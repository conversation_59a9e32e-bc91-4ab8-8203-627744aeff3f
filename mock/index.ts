import type { Request, Response } from '@umijs/deps/compiled/express';
import mockjs from 'mockjs';

export default {
  'GET /api/pollution-warning-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;

    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              sn: /BJ\d{10}/,
              date: '@date',
              region: '@province',
              address: '@city(true)',
              'level|0-3': 1,
              status: mockjs.Random.pick([0, 1, 2, 3, 4]),
            },
          ],
        }),
      );
    }, 500);
  },

  'GET /api/pollution-detail-list': (req: Request, res: Response) => {
    setTimeout(() => {
      res.json(
        mockjs.mock({
          'list|5': [
            {
              id: '@guid',
              sn: /WR\d{10}/,
              name: '@cword(10)',
              'type|0-2': 1,
              'cat|0-3': 1,
              'level|0-1': 1,
              'status|0-4': 0,
            },
          ],
        }),
      );
    }, 500);
  },

  'GET /api/pollution-early-warning-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              sn: /WR\d{10}/,
              region: '@region',
              name: '@cword(10)',
              'type|0-2': 1,
              'cat|0-3': 1,
              'level|0-1': 1,
              'status|0-4': 0,
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/pollution-source-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              sn: /WRY-\d{10}/,
              name: '@cword(10)',
              region: '@region',
              'type|0-2': 1,
              'cat|0-3': 1,
              'level|0-1': 1,
              'status|0-4': 0,
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/regional-assessment-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              sn: /C-\d{10}/,
              region: '@region',
              'val1|150-200': 200,
              'rank1|1-10': 10,
              'val2|150-200': 200,
              'rank2|1-10': 10,
              'val3|150-200': 200,
              'rank3|1-10': 10,
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/user-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              name: '@cname',
              region: '@province',
              mobile: /13\d{9}/,
              'status|0-1': 1,
              'loginCount|1-100': 1,
              lastLogin: '@datetime',
              'rules|0-2': 1,
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/monitoring-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              'name|0-3': 3,
              'source|0-2': 1,
              'category|0-2': 1,
              'format|0-1': 1,
              status: 1,
              time: '',
              receiveTime: '@datetime',
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/log-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              'name|0-3': 3,
              'source|0-2': 1,
              'category|0-2': 1,
              'format|0-1': 1,
              status: 1,
              time: '',
              receiveTime: '@datetime',
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/rules-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              'category|0-1': 1,
              'pollution|0-4': 1,
              rule1: '',
              rule2: '',
              rule3: '',
              rule4: '',
              status: 1,
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/batch-rules-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              region: '@county',
              pm25: '',
              pm10: '',
              o3: '',
              no2: '',
              hcho: '',
            },
          ],
        }),
      );
    }, 500);
  },
  'GET /api/data-download-list': (req: Request, res: Response) => {
    const {
      query: { page },
    } = req;
    setTimeout(() => {
      res.json(
        mockjs.mock({
          page: +(page || 1),
          total: 87,
          'list|10': [
            {
              id: '@guid',
              name: '',
              'size|1-10': 1,
            },
          ],
        }),
      );
    }, 500);
  },
};
