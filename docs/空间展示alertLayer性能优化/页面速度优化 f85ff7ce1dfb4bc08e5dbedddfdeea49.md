# 页面速度优化

## 现象描述

随着选择不同的筛选日期，页面性能也会随之改变，从日到月到年不断增高，具体性能指标为：

日：组件更新时间为10ms以下，cpu使用率为5%左右，此时页面不怎么掉帧

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled.png)

月：组件更新时间为20ms左右，cpu使用率为50%左右，此时页面有轻微的掉现象

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%201.png)

年：组件更新时间波动较大，平均在100ms以上，cpu使用率为100%，所以此时界面基本属于不可操作状态，需要页面反应一会之后才会流畅一些

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%202.png)

## 分析原因

由于选择不同的时间导致性能不一致，所以只需要判断不同的时间所造成的不一致的因素即可。

页面中，时间是用来请求接口的，不同的时间会造成数据的不一致，因此我们从数据角度入手。

## 排查原因

地图组件中，数据的获取都是在图层里进行的，所以先从图层入手，找出图层造成的影响有多大。

第一步，把图层代码全部注释掉，看性能的提升程度：

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%203.png)

上图可以看出，CPU使用率显著降低，但是组件更新时间还是100ms以上，只不过比之前更稳定了一些。

由此可以推断，造成CUP性能占用过高的原因是图层渲染，过多的图层渲染导致CUP使用率过高。但是组件更新依旧很慢，推测原因是图层hooks在每次组件更新的时候都要执行，拖慢了渲染效率。

第二步，注释掉所有图层执行的逻辑，看渲染时间会不会有所提高：

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%204.png)

结果与推测一致，去掉所有图层执行的逻辑之后，大大提高了页面性能。

这样一来，优化重点就落到了图层上，只需找到哪些图层拖慢了性能即可。

## 优化步骤

第一步，先把所有代码恢复，把没用的图层删除。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%205.png)

删除了一些业务上没有用到的图层之后，组件更新时间没有明显降低，证明删掉的图层不是性能瓶颈所在。

第二步，使用二分法的方式删除layer，寻找性能瓶颈。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%206.png)

运气比较好，在删除了一半的图层之后，发现渲染时间以及CPU使用率都有显著提升，由此断定，性能瓶颈就在这几个layer之中。

第三步，逐个放开layer，找到罪魁祸首。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%207.png)

罪魁祸首已找到，当放开alertLayer图层之后，页面展现出了以前的样子。

## 代码优化

定位到原因之后就可以着手优化了，首先看一下代码是如何实现的。

代码比较简单，最终定位到以下代码：

```tsx
const stationRimGeoLayers = stationRimGeoList.map(({ id, geojson }) => {
    return new GeoJsonLayer({
      id,
      // @ts-ignore
      data: geojson || [],
      stroked: false,
      filled: true,
      visible: circleVisible && visible,
      getFillColor: [255, 0, 0, 255 * 0.3],
    });
  });
```

这段代码单独看是没啥问题，但恰巧这段代码在react hook里，这就导致了每一次组件更新，hook都会执行，这段代码也会执行，每次执行的时候都会 new 很多个 GeoJsonLayer，具体数量根据数据的多少来计算，这也印证了一开始我们排查时的猜想，确实是跟数据的多少有关。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%208.png)

注释掉代码之后，组件更新速度提升到了ms级，CPU使用率稳定在30%左右，虽然还有优化空间，但是不至于页面卡得不能动了。

接下来需要根据业务需求及代码执行逻辑对这段代码进行优化。

### 利用缓存

优化的常用方案就是缓存，我们用 react 的 useMemo 缓存这两份数据，得到以下效果。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%209.png)

由上图可以看出，加上缓存之后，组件更新时间已经到了ms级，但是cpu使用率还是100%，还得想办法把CPU使用率降下来。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%2010.png)

由于这些GeoJsonLayer默认是不显示的，所以我们在其不显示的时候不初始化对象就可以了，加上这段判断后，CPU使用率降到了20%左右。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%2011.png)

但是当打开图层开关之后，CPU使用率又飙升到了100%，业务上，这些GeoJsonLayer只是渲染了一些圆圈而已，如下图：

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%2012.png)

这些红色的半透明圆圈就是GeoJsonLayer渲染出来的效果，那可否用其他图层代替呢？

业务上来说，这些图层在地图上表示一定的范围，必须要用GeoJson才能精确表示，所以不能改造成scatterLayer。如上文提到的，代码会根据数据的数量创造出同样数量图层，如果只创建一个图层是不是就能解决问题呢？

### 减少图层数量

我们对代码做如下改造：

```tsx
const goeJsonCollection = useMemo(() => {
    return {
      type: 'FeatureCollection',
      features: stationRimGeoList.map((item) => {
        return {
          ...item.geojson,
          id: item.id,
        };
      }),
    };
  }, [stationRimGeoList]);

  const stationRimGeoLayers = useMemo(() => {
    // 返回空数组是为了性能优化
    if (!(circleVisible && visible)) {
      return [];
    }
    return new GeoJsonLayer({
      id: 'station-rim-geo-json-layer',
      // @ts-ignore
      data: goeJsonCollection || [],
      stroked: false,
      filled: true,
      visible: circleVisible && visible,
      getFillColor: [255, 0, 0, 255 * 0.3],
    });
  }, [circleVisible, goeJsonCollection, visible]);
```

改造后，CUP使用率稳定在20%左右。

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%2013.png)

## 优化结果

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%202.png)

![Untitled](%E9%A1%B5%E9%9D%A2%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96%20f85ff7ce1dfb4bc08e5dbedddfdeea49/Untitled%2014.png)

对比优化前后，用一张表总结：

|  | 优化前 | 优化后 |
| --- | --- | --- |
| 组件更新时间 | 200ms左右 | 10ms以下 |
| CPU使用率 | 100% | 20% |
| 内存 | 1400MB | 400MB |

## 经验总结

通过此次性能优化，可以总结出以下方法：

1. 对性能排查方面，要通过减少、增加、再减少的方式逐步定位原因，做到有法可依；
2. 对于React的开发，还需要注意对基础的理解，特别对于数据处理方面，要时刻盯防性能问题的发生
3. 地图的开发本身具有数据量大的特点，在开发的过程中要做好压测，防止极端情况的发生。