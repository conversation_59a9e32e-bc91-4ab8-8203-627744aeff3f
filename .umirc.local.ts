import { defineConfig } from 'umi';

export default defineConfig({
  analyze: {
    analyzerMode: 'server',
    analyzerPort: 8888,
    openAnalyzer: true,
    // generate stats file while ANALYZE_DUMP exist
    generateStatsFile: false,
    statsFilename: 'stats.json',
    logLevel: 'info',
    defaultSizes: 'parsed', // stat  // gzip
  },
  proxy: {
    '/apbapi': {
      target: 'http://apbapi.test.ytcsc.cn:20055/api',
      changeOrigin: true,
      pathRewrite: { '^/apbapi': '' },
    },
    '/imgApi': {
      target: 'http://dev.guangxi.openrj.cn:20067',
      changeOrigin: true,
      pathRewrite: { '^/imgApi': '' },
    },
  },
});
