.has-triangle-down::after {
  position: absolute;
  bottom: -6px;
  left: 50%;
  display: block;
  border-top: 8px solid white;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  transform: translate(-50% 0);
  content: '';
}
.scoll-bar {
  overflow: auto;
}
.scoll-bar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
.scoll-bar::-webkit-scrollbar-track {
  background: #f7f7f9;
}
.scoll-bar::-webkit-scrollbar-thumb {
  background: #c6c6c6;
  border-radius: 2px;
}
.scoll-bar-hidden {
  overflow: auto;
}
.scoll-bar-hidden::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.scoll-bar-hidden::-webkit-scrollbar-track {
  background: #f7f7f9;
}
.scoll-bar-hidden::-webkit-scrollbar-thumb {
  background: #c6c6c6;
}
.long-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-overflow-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.square {
  width: 100%;
  aspect-ratio: 1 / 1;
}
.grid-center {
  display: grid;
  place-items: center;
}

.flex-center {
  @apply flex items-center justify-center;
}

.tag {
  @apply 
}
