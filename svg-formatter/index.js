const fs = require('fs');
const path = require('path');
const yargs = require('yargs');

const argv = yargs
  .command('remove', 'Remove XML declarations from SVG files', {
    dir: {
      description: 'The directory containing SVG files',
      alias: 'd',
      type: 'string',
      demandOption: true,
    },
  })
  .help().argv;

const svgDirectory = argv.dir; // 从命令行参数获取文件夹路径

fs.readdir(svgDirectory, (err, files) => {
  if (err) {
    console.error('读取文件夹时出错:', err);
    return;
  }

  files.forEach((file) => {
    const filePath = path.join(svgDirectory, file);

    // 只处理 SVG 文件
    if (path.extname(file) === '.svg') {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          console.error(`读取文件 ${file} 时出错:`, err);
          return;
        }

        // 正则表达式匹配 <? ... ?>
        const xmlDeclarationRegex = /<\?xml.*\?>/;
        if (xmlDeclarationRegex.test(data)) {
          const updatedData = data.replace(xmlDeclarationRegex, '');

          fs.writeFile(filePath, updatedData, 'utf8', (err) => {
            if (err) {
              console.error(`写入文件 ${file} 时出错:`, err);
            } else {
              console.log(`文件 ${file} 中的 XML 声明已删除！`);
            }
          });
        } else {
          console.log(`文件 ${file} 中没有找到 XML 声明。`);
        }
      });
    }
  });
});
