import { FlyToInterpolator, RGBAColor } from 'deck.gl';

export const DEFAULT_VIEW_STATE = {
  latitude: 32.639291444974816,
  longitude: 106.60277834154844,
  zoom: 3,
  minZoom: 3,
  maxZoom: 14,
  transitionInterpolator: new FlyToInterpolator(),
  transitionDuration: 500,
};

export const hiddenCloudLayerType: DUST.DataTypes[] = [
  'PM25',
  'PM10',
  'AOD',
  'O3',
  'O3TCD',
  'NO2TCD',
  'HCHO',
  'SO2',
  'CO',
  'DCOLOR',
];

export const SpaceAlertColors: Record<number, RGBAColor> = {
  4: [24, 35, 104],
  3: [100, 49, 141],
  2: [112, 107, 219],
  1: [119, 163, 255],
};

export const pollutionLevelList: {
  name: string;
  type: DUST.pollutionLevelType;
  color: string;
  level: number;
  range: Partial<Record<DUST.DataTypes, [number, number]>>;
}[] = [
    {
      level: 4,
      name: '严重',
      type: 'seriousCount',
      color: '#182368',
      range: {
        PM25: [200, 500],
        PM10: [420, 600],
        CO: [1.0, 3],
        HCHO: [4, 5],
        O3: [350, 500],
        SO2: [8.5, 10],
        NO2TCD: [2.8, 5],
      },
    },
    {
      level: 3,
      name: '重度',
      type: 'heavyCount',
      color: '#64318D',
      range: {
        PM25: [150, 200],
        PM10: [350, 420],
        CO: [0.8, 1],
        HCHO: [3.6, 4],
        O3: [250, 350],
        SO2: [7.5, 8.5],
        NO2TCD: [2.6, 2.8],
      },
    },
    {
      level: 2,
      name: '中度',
      type: 'moderateCount',
      color: '#706BDB',
      range: {
        PM25: [125, 150],
        PM10: [250, 350],
        CO: [0.6, 0.8],
        HCHO: [3.2, 3.6],
        O3: [160, 250],
        SO2: [6.5, 7.5],
        NO2TCD: [2.4, 2.6],
      },
    },
    {
      level: 1,
      name: '轻度',
      type: 'lightCount',
      color: '#73A3FF',
      range: {
        PM25: [115, 125],
        PM10: [150, 250],
        CO: [0.5, 0.6],
        HCHO: [3.0, 3.2],
        O3: [150, 160],
        SO2: [5.5, 6.5],
        NO2TCD: [2.2, 2.4],
      },
    },
  ];

export const showHightValueType: DUST.DataTypes[] = [
  'PM25',
  'PM10',
  'NO2TCD',
  'O3',
  'HCHO',
  'SO2',
  'CO',
];

export const showStationLayerType: DUST.DataTypes[] = [
  'PM25',
  'PM10',
  'O3',
  'NO2TCD',
  'SO2',
  'CO',
];

export const showDColorLegendType: DUST.DataTypes[] = ['DCOLOR', 'DMASK'];

export const stationOptions: {
  label: string;
  value: DUST.DataTypes | 'AQI';
}[] = [
    {
      label: 'PM₁₀',
      value: 'PM10',
    },
    {
      label: 'PM₂.₅',
      value: 'PM25',
    },
    {
      label: 'AQI',
      value: 'AQI',
    },
    // {
    //   label: 'NO₂',
    //   value: 'NO2',
    // },
    // {
    //   label: 'O₃',
    //   value: 'O3',
    // },
    // {
    //   label: 'SO₂',
    //   value: 'SO2',
    // },
    // {
    //   label: 'CO',
    //   value: 'CO',
    // },
  ];

export const remoteSensingOptions: {
  label: string;
  value: DUST.RemoteSensingType;
}[] = [
    {
      label: 'PM₁₀',
      value: 'PM10',
    },
    {
      label: 'PM₂.₅',
      value: 'PM25',
    },
    {
      label: 'AOD',
      value: 'AOD',
    },
    // {
    //   label: 'O₃_S5P',
    //   value: 'O3',
    // },
    // {
    //   label: 'O₃_GK2B',
    //   value: 'GK2B_O3',
    // },
    // {
    //   label: 'NO₂_GK2B',
    //   value: 'GK2B_NO2',
    // },
    // {
    //   label: 'NO₂_S5P',
    //   value: 'NO2',
    // },
    // {
    //   label: 'HCHO_S5P',
    //   value: 'HCHO',
    // },
    // {
    //   label: 'HCHO_GK2B',
    //   value: 'GK2B_HCHO',
    // },
    // {
    //   label: 'SO₂',
    //   value: 'SO2',
    // },
    // {
    //   label: 'CO',
    //   value: 'CO',
    // },


  ];

export const tweenFrameCount = 4;
