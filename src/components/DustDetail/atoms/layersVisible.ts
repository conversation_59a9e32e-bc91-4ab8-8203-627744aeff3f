import { atom } from 'jotai';
import { stationTypeAtom, textureTypeAtom } from './base';
import { Position2D } from 'deck.gl';
import { Position3D } from 'deck.gl';

// 区划名称
export const isAnnotationVisibleAtom = atom(false);

// 影响范围
export const isSphereOfInfluenceAtom = atom(true);

// 风场
export const isWindLayerVisibleAtom = atom(false);

// 卫星云图
export const isCloudLayerVisibleAtom = atom(false);

// 国控站点
export const isStationLayerVisibleAtom = atom(
  (get) => {
    const type = get(stationTypeAtom);
    return !!type;
  },
  (get, set, value: boolean | ((previousValue: boolean) => boolean)) => {
    const stationType = get(stationTypeAtom);
    const currentValue =
      typeof value === 'function' ? value(!!stationType) : value;
    // 默认选中 PM10
    const type = currentValue ? stationType || 'PM10' : undefined;
    set(stationTypeAtom, type);
  },
);

// 遥感
export const isRemoteSensingLayerVisibleAtom = atom(
  (get) => {
    const type = get(textureTypeAtom);
    return !!type && ['PM10', 'PM25', 'AOD','O3','GK2B_O3','GK2B_NO2','NO2','HCHO','GK2B_HCHO','SO2','CO'].includes(type);
  },
  (get, set, value: boolean | ((previousValue: boolean) => boolean)) => {
    const textureType = get(textureTypeAtom);
    const previousValue =
      !!textureType && ['PM10', 'PM25', 'AOD','O3','GK2B_O3','GK2B_NO2','NO2','HCHO','GK2B_HCHO','SO2','CO'].includes(textureType);

    const currentValue =
      typeof value === 'function' ? value(previousValue) : value;
    // 默认选中 PM10
    const type = currentValue ? 'PM10' : undefined;
    set(textureTypeAtom, type);
  },
);

// 沙尘影像
export const isDColorLayerVisibleAtom = atom(
  (get) => {
    const type = get(textureTypeAtom);
    return !!type && type === 'DCOLOR';
  },
  (get, set, value: boolean | ((previousValue: boolean) => boolean)) => {
    const textureType = get(textureTypeAtom);
    const previousValue = textureType === 'DCOLOR';

    const currentValue =
      typeof value === 'function' ? value(previousValue) : value;
    const type = currentValue ? 'DCOLOR' : undefined;

    set(textureTypeAtom, type);
  },
);

// 沙尘范围
export const isDustRangeLayerVisibleAtom = atom<boolean>(true);

// 沙源地
export const isSandSourceLayerVisibleAtom = atom<boolean>(false);
// 鼠标hover地图时拾取经纬度
export const coordinateAtom = atom<Position2D | Position3D | []>([]);
