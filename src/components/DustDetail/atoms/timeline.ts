import { getDatetimeFormatter } from '@/utils/dust';
import { regionList } from '@/utils/dust/regionList';
import dayjs from 'dayjs';
import { atom } from 'jotai';
import { TimelineListItem } from '../components/TimelineWithRegion/Timeline/TimelineItem';

export const regionInfoAtom = atom<DUST.RegionInfo | undefined>(regionList[0]);

export const regionCodeAtom = atom((get) => {
  return get(regionInfoAtom)?.code;
});

export const activeTimelineItemAtom = atom<TimelineListItem | undefined>(
  undefined
);

export const activeTimePointAtom = atom((get) => {
  const timelineItem = get(activeTimelineItemAtom);
  if (timelineItem) {
    return dayjs(timelineItem.date).format(getDatetimeFormatter());
  }
  return undefined;
});

export const activeTimePointHourlyAtom = atom((get) => {
  const timelineItem = get(activeTimelineItemAtom);
  if (timelineItem) {
    return dayjs(timelineItem.date).format(getDatetimeFormatter('/', true));
  }
  return undefined;
});

export const isSelectTheLastPointOfTimelineAtom = atom(false);

export const timePointsAtom = atom<TimelineListItem[]>([]);

export const timePointsRangeAtom = atom((get) => {
  const timePoints = get(timePointsAtom);
  if (timePoints.length > 0) {
    return [timePoints[0].date, timePoints[timePoints.length - 1].date];
  }
  return ['', ''];
});

export const activeTimePointPercentageAtom = atom((get) => {
  const timePoints = get(timePointsAtom);
  const activeTimelineItem = get(activeTimelineItemAtom);
  if (activeTimelineItem) {
    const index = timePoints.findIndex(
      (item) => item.date === activeTimelineItem.date
    );
    return index / timePoints.length;
  }
  return 0;
});

export const frameAtom = atom(0);

export const startPollAtom = atom(false);
