import { LazyDeckGLRef } from '@/components/LazyDeckGL';
import { stationPollutionValuesAndColors } from '@/utils/dust/stationLegendConfig';
import { ViewStateProps } from '@deck.gl/core/lib/deck';
import { atom } from 'jotai';

export const mapTypeAtom = atom<'vec' | 'img' | 'ter'>('img');

// lazy deckGl 实例
export const deckInstanceRefAtom = atom<React.MutableRefObject<LazyDeckGLRef | null> | null>(
  null
);

export const zoomScaleGreater100KMAtom = atom(false);

export const textureTypeAtom = atom<
  DUST.RemoteSensingType | 'DCOLOR' | 'TCOLOR' | undefined
>('DCOLOR');

export const stationTypeAtom = atom<undefined | DUST.DataTypes | 'AQI'>(
  undefined
);

export const stationUnitAtom = atom((get) => {
  const type = get(stationTypeAtom);
  if (type) {
    const lowerType = type.toLocaleLowerCase();
    const pollutionType = lowerType === 'no2tcd' ? 'no2' : lowerType;
    const stationType = ['o3', 'co', 'no2', 'so2'].includes(pollutionType)
      ? `${pollutionType}-hour`
      : pollutionType;

    const stationInfo = stationPollutionValuesAndColors[stationType];
    return stationInfo?.unit;
  }
  return undefined;
});

export const selectedSandSourceDataAtom = atom<
  | {
      coordinate: [number, number];
      properties: any;
    }
  | undefined
>(undefined);

export const hoveredSandRangeDataAtom = atom<
  | {
      position: [number, number];
      properties: any;
    }
  | undefined
>(undefined);

export const isSubscribeModalOpenAtom = atom(false);

export const isFullScreenAtom = atom(false);
export const openDownloadModalAtom = atom(false);
export type SelectedSandSourceData = APIV2.TStationDetailModel & {
  color: [number, number, number];
  value: number;
};

export const selectedStationDataAtom = atom<SelectedSandSourceData | undefined>(
  undefined
);
export const loadingTileAtom = atom<boolean>(false);
export const loadingCloundTileAtom = atom<boolean>(false);
export const loadingPointsAtom = atom<boolean>(false);
export const loadingRangeAtom = atom<boolean>(false);

export const viewStateAtom = atom<ViewStateProps>({
  longitude: 111.62056100000001,
  latitude: 45.3719785,
  zoom: 7,
  maxZoom: 18,
  minZoom: 4,
});
