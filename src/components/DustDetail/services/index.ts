import { request } from '@/utils';
import { stringify } from 'qs';

/** 获取沙尘时间节点 */
export const getDustRecordTimePoint = (params: APIDUST.TGetDustRecordTimePointParams) => {
  return request(`/api/dust/record/time/point?${stringify(params)}`) as Promise<APIDUST.TDustDataTimePointModel[]>;
};

/** 获取当前时间点的监测结果 */
export const getDustRecordRegionInfos = (params: APIDUST.TGetDustRecordRegionInfosParams) => {
  return request(`/api/dust/record/region/infos?${stringify(params)}`) as Promise<APIDUST.TDustRecordRegionModel>;
};

/** 获取当前时间点的所有沙团信息 */
// 沙尘事件这里走的是新接口 /api/dust/event/record/infos  与首页的不一样/api/dust/record/infos
export const getDustRecordInfos = (params: APIDUST.TGetDustRecordInfosParams) => {
  return request(`/api/dust/event/record/infos?${stringify(params)}`) as Promise<APIDUST.TDustRecordInfoModel[]>;
};


/** 获取沙尘事件影响行政区 */
export const getDustRecordDateRegions = (params: APIDUST.TGetDustRecordDateRegionsParams) => {
  return request(`/api/dust/record/date/regions?${stringify(params)}`) as Promise<APIDUST.TDateLabelsModelDustRecordRegionInfoModel[]>;
};

/** 获取沙尘事件沙团面积 */
export const getDustRecordDateArea = (params: APIDUST.TGetDustRecordDateAreaParams) => {
  return request(`/api/dust/record/date/area?${stringify(params)}`) as Promise<APIDUST.TDateValueModel[]>;
};

// 站点监测模块

/** 站点列表 这个接口好像没有用*/
export const getStationMonitorList = (params: APIDUST.TGetStationMonitorListParams) => {
  return request(`/api/station/monitor/list?${stringify(params)}`) as Promise<APIDUST.TStationModel[]>;
};

/** 站点数据 */
export const getStationMonitorData = (params: APIDUST.TGetStationMonitorDataParams) => {
  return request(`/api/station/monitor/data?${stringify(params)}`) as Promise<APIDUST.TStationDetailModel>;
};

/** 站点数据列表 */
export const getStationMonitorDataList = (params: APIDUST.TGetStationMonitorDataListParams) => {
  return request(`/api/station/monitor/data/list?${stringify(params)}`) as Promise<APIDUST.TStationDetailModel[]>;
};
