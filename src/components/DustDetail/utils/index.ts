import { getWeekDay } from '@/utils';
import dayjs from 'dayjs';

interface Group {
  groupName: string;
  points: {
    label: string;
    value: string;
  }[];
}
export const groupPointsByAgg = (points: DUST.TimePoint[], agg: DUST.Agg) => {
  const groups = points.reduce((acc, cur) => {
    const { timePoints } = cur;
    const weekDay = getWeekDay(timePoints);
    const groupName =
      agg === 'none'
        ? `周${weekDay}-${dayjs(timePoints).format('DD')}`
        : `${dayjs(timePoints).format('MM月')}`;

    const find = acc.find((item) => item.groupName === groupName);
    const label =
      agg === 'none'
        ? dayjs(timePoints).format('MM月DD日 HH:mm')
        : dayjs(timePoints).format('MM月DD日') + ` 周${weekDay}`;

    if (find) {
      return acc.map((item) => {
        if (item.groupName === groupName) {
          return {
            ...item,
            points: [
              ...item.points,
              {
                value: timePoints,
                label,
              },
            ],
          };
        }
        return item;
      });
    } else {
      return [
        ...acc,
        {
          groupName,
          points: [
            {
              value: timePoints,
              label,
            },
          ],
        },
      ];
    }
  }, [] as Group[]);

  return groups;
};
