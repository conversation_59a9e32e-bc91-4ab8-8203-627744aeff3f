import { regionList } from '@/utils/dust/regionList';
import { animated, useTransition } from '@react-spring/web';
import { Switch } from 'antd';
import { useAtom } from 'jotai';
import { memo, useState } from 'react';
import { createPortal } from 'react-dom';
import { isSubscribeModalOpenAtom } from '../../atoms';
import Input from './Input';
import Select from './Select';

function SubscribeModal() {
  const [open, setOpen] = useAtom(isSubscribeModalOpenAtom);
  const [formValue, setFormValue] = useState<{
    areaInfo:
      | {
          code: number;
          name: string;
        }
      | undefined;
    areaCount: string | '';
    notification: 'string' | boolean;
  }>({
    areaInfo: undefined,
    areaCount: '',
    notification: true,
  });

  const handleCancel = (val: boolean) => {
    setOpen(val);
  };

  const transition = useTransition(open, {
    from: {
      scale: 0,
      opacity: 0,
    },
    enter: {
      scale: 1,
      opacity: 1,
    },
    leave: {
      scale: 0,
      opacity: 0,
    },
  });

  return (
    <>
      {transition((style, isOpen) => {
        return (
          isOpen &&
          createPortal(
            <animated.div
              className="fixed flex items-center justify-center top-0 left-0 right-0 bottom-0 z-[100] bg-[#000000]/70"
              style={{ opacity: style.opacity }}
            >
              <animated.div
                className="relative w-[662px] min-h-[415px] bg-white rounded-[20px]"
                style={style}
              >
                {/* header */}
                <div className="absolute top-[-22px] left-0 right-0">
                  <img
                    src="/images/home/<USER>"
                    alt="订阅消息"
                  />
                  <div className="absolute top-[61px] left-[64px] w-[381px] text-white">
                    <h2 className="text-[30px] leading-[42px]">订阅消息</h2>
                    <div className="text-[14px] leading-[20px] mt-[20px]">
                      提示：扫码关注公众号后，在消息通知开启状态下，系统将会把监测区域最新沙尘监测消息推送至微信公众号。
                    </div>
                  </div>
                </div>

                {/* content */}
                <div className="px-[64px] pt-[183px] text-[14px] text-[#5E677C]">
                  <div className="flex gap-[20px]">
                    <div className="w-[201px] h-[201px] rounded-[8px] bg-[#F1F6F8]"></div>

                    {/* form */}
                    <div className="flex flex-col gap-[20px] flex-grow">
                      <div className="flex items-center gap-[7px] ">
                        <div className="w-[96px] text-right">
                          监测区域<span className="text-[#ff0000]">*</span>
                        </div>
                        <Select
                          className="flex-grow"
                          value={formValue.areaInfo}
                          options={regionList}
                          onChange={(value) => {
                            setFormValue((prev) => {
                              return {
                                ...prev,
                                areaInfo: value,
                              };
                            });
                          }}
                        ></Select>
                      </div>

                      <div className="flex items-center gap-[7px]">
                        <div className="w-[96px] text-right">
                          沙尘面积大于<span className="text-[#ff0000]">*</span>
                        </div>
                        <Input
                          value={formValue.areaCount}
                          onChange={(val) => {
                            const reg = /^\+?[1-9][0-9]*$/;
                            if (reg.test(val))
                              setFormValue((prev) => {
                                return {
                                  ...prev,
                                  areaCount: val,
                                };
                              });
                          }}
                        />
                      </div>

                      <div className="flex items-center gap-[7px]">
                        <div className="w-[96px] text-right">消息通知</div>
                        <Switch
                          defaultChecked
                          onChange={(checked: boolean) => {
                            setFormValue((prev) => {
                              return {
                                ...prev,
                                notification: checked,
                              };
                            });
                          }}
                        />
                      </div>

                      <div className="flex items-center justify-end gap-[6px]">
                        <div
                          className="flex items-center justify-center w-[77px] h-[38px] border-[1px] border-[#D5DDE1] rounded-[8px] cursor-pointer hover:bg-black/10"
                          onClick={() => handleCancel(false)}
                        >
                          取消
                        </div>
                        <div className="flex items-center justify-center w-[77px] h-[38px] rounded-[8px] text-white bg-primary cursor-pointer hover:bg-primary/80">
                          确定
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </animated.div>
            </animated.div>,
            document.body,
          )
        );
      })}
    </>
  );
}

export default memo(SubscribeModal);
