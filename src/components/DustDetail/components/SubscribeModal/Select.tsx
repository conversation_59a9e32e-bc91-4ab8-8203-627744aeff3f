import { Popover } from 'antd';
import { memo, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import NavigationItem from '../NavigationItem';

type Props<T> = {
  className?: string;
  options: T[];
  value: T;
  onChange: (value: T) => void;
};

function Select<T extends DUST.RegionInfo | undefined>({
  className,
  options,
  value,
  onChange,
}: Props<T>) {
  const [open, setOpen] = useState(false);

  return (
    <div className={twMerge('flex items-center', className)}>
      <Popover
        placement="top"
        overlayClassName="rounded-[14px] overflow-hidden z-[999] shadow-card"
        overlayInnerStyle={{
          padding: '10px 4px',
          background: '#ffffff',
        }}
        arrow={false}
        content={
          <div className="flex flex-col max-h-[220px] max-w-[200px] overflow-y-auto overflow-x-hidden scrollbar-thin">
            {options.map((option) => {
              return (
                <NavigationItem
                  onClick={() => {
                    onChange(option);
                    setOpen(false);
                  }}
                  key={option!.code + option!.name}
                >
                  {() => (
                    <span
                      title={option?.name}
                      className="text-[14px] px-[13px] text-overflow-ellipsis"
                    >
                      {option?.name}
                    </span>
                  )}
                </NavigationItem>
              );
            })}
          </div>
        }
        trigger="click"
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <div className="flex items-center justify-between gap-[10px] min-w-[99px] w-full h-[38px] px-[14px] rounded-[8px] cursor-pointer select-none border-[1px] border-[#D5DDE1]">
          <span
            className="text-[14px] leading-[22px] text-overflow-ellipsis"
            title={value?.name}
          >
            {value?.name ?? <span className="text-[#999999]">请选择</span>}
          </span>
          <iconpark-icon
            name="xiala"
            width="13"
            height="13"
            color="#B9C9DB"
            style={{
              transform: open ? 'rotate(180deg)' : 'none',
              transition: 'all 0.5s',
            }}
          ></iconpark-icon>
        </div>
      </Popover>
    </div>
  );
}

export default memo(Select) as <T>(props: Props<T>) => JSX.Element;
