import { memo } from 'react';

type Props = {
  value: string | '';
  onChange: (val: string | '') => void;
};

function Input({ value, onChange }: Props) {
  return (
    <div className="flex items-center justify-between text-[14px] px-[14px] gap-[10px] h-[38px] border-[1px] border-[#D5DDE1] rounded-[8px]">
      <input
        className="placeholder:text-[#999999]"
        type="number"
        placeholder="请输入"
        value={value}
        onInput={(e) => onChange(e.target?.value)}
      />
      <span className="text-[#979FB2] shrink-0">k㎡</span>
    </div>
  );
}

export default memo(Input);
