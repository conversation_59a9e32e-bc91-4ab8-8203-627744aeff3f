
import DownloadAnimation from '@/components/DownloadAnimation';
import { useRouter } from '@/hooks';
import { useAtom } from 'jotai';
import { useQuery } from 'react-query';
import { openDownloadModalAtom } from '../../atoms';
import useTimePoints from '../TimelineWithRegion/useTimePoints';
import { getDustEventInfo } from '@/pages/DustEvent/services';

const DownloadModal = () => {
  const { query } = useRouter();
  const { data } = useQuery(
    ['getDustEventInfo', query.id],
    () => getDustEventInfo({ dustEventId: query.id }),
    { enabled: Boolean(query.id) },
  );
  const [open, setOpen] = useAtom(openDownloadModalAtom);
  const timePoints = useTimePoints();
  
  return (
    <DownloadAnimation
      regionCode={data?.regions.map((region) => region.key) as number[]}
      dustEventId={query.id}
      dustName={data?.code as string}
      timePoints={timePoints.map((t) => ({
        time: t.date,
        haveDust: t.value > 0,
      }))}
      open={open}
      onCancle={() => setOpen(false)}
    />
  );
};
export default DownloadModal;
