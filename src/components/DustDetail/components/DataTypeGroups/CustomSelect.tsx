import { Popover } from 'antd';
import { memo, useMemo, useState } from 'react';
import NavigationItem from '../NavigationItem';

type Props<T> = {
  options: {
    label: string;
    value: T;
  }[];
  value: T;
  onChange: (value: T) => void;
};

function CustomSelect<T>({ options, value, onChange }: Props<T>) {
  const [open, setOpen] = useState(false);
  const label = useMemo(() => {
    return options.find((v) => v.value === value)?.label || '';
  }, [options, value]);

  return (
    <div className="flex items-center ">
      <Popover
        placement="top"
        overlayClassName="backdrop-filter-blur-card"
        overlayInnerStyle={{
          padding: '10px 4px',
          background: 'rgba(16,21,48,0.5)',
        }}
        arrow={false}
        content={
          <div className="flex flex-col gap-2 text-white">
            {options.map((option) => {
              return (
                <NavigationItem
                  style={{
                    background: option.value === value ? '#E1EAF5' : '',
                    color: option.value === value ? '#FFBA17' : ''
                  }}
                  onClick={() => {
                    onChange(option.value);
                    setOpen(false);
                  }}
                  key={option.value + option.label}
                >
                  {() => (
                    <span className="text-[16px] px-5">{option.label}</span>
                  )}
                </NavigationItem>
              );
            })}
          </div>
        }
        trigger="click"
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <div className="flex items-center justify-center gap-[10px]  h-[44px] bg-[#101530]/50 rounded-[24px] cursor-pointer select-none backdrop-filter-blur-card"
          style={{ minWidth: "99px", padding: '0 5px' }}>
          <span className="text-[16px] text-white leading-[22px]">{label}</span>
          <iconpark-icon
            name="xiala"
            width="13"
            height="13"
            color="#B9C9DB"
            style={{
              transform: open ? 'rotate(180deg)' : 'none',
              transition: 'all 0.5s',
            }}
          ></iconpark-icon>
        </div>
      </Popover>
    </div>
  );
}

export default memo(CustomSelect) as <T>(props: Props<T>) => JSX.Element;
