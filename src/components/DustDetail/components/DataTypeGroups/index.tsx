import { useGlobalContext } from '@/components/GlobalProvider';
import { animated, useSpring } from '@react-spring/web';
import { useAtom } from 'jotai';
import { useRef } from 'react';
import { twMerge } from 'tailwind-merge';
import {
  isAnnotationVisibleAtom,
  isCloudLayerVisibleAtom,
  isDColorLayerVisibleAtom,
  isDustRangeLayerVisibleAtom,
  isRemoteSensingLayerVisibleAtom,
  isSandSourceLayerVisibleAtom,
  isSphereOfInfluenceAtom,
  isStationLayerVisibleAtom,
  isWindLayerVisibleAtom,
  stationTypeAtom,
  textureTypeAtom,
} from '../../atoms';
import {
  hiddenCloudLayerType,
  remoteSensingOptions,
  stationOptions,
} from '../../config';
import CustomSelect from './CustomSelect';
import GroupBtn from './GroupBtn';

function DataTypeGroups() {
  const [stationType, setStationType] = useAtom(stationTypeAtom);
  const [isStationLayerVisible, setIsStationLayerVisible] = useAtom(
    isStationLayerVisibleAtom,
  );
  const [textureType, setTextureType] = useAtom(textureTypeAtom);
  const [isRemoteSensingLayerVisible, setIsRemoteSensingLayerVisible] = useAtom(
    isRemoteSensingLayerVisibleAtom,
  );
  const [isDColorLayerVisible, setIsDColorLayerVisible] = useAtom(
    isDColorLayerVisibleAtom,
  );
  const [cloudLayerVisible, setCloudLayerVisible] = useAtom(
    isCloudLayerVisibleAtom,
  );
  const [isDustRangeLayerVisible, setIsDustRangeLayerVisible] = useAtom(
    isDustRangeLayerVisibleAtom,
  );
  const [isSandSourceLayerVisible, setIsSandSourceLayerVisible] = useAtom(
    isSandSourceLayerVisibleAtom,
  );
  const [windLayerVisible, setWindLayerVisible] = useAtom(
    isWindLayerVisibleAtom,
  );

  const [annotationVisible, setAnnotationVisible] = useAtom(
    isAnnotationVisibleAtom,
  );

  const [isSphereOfInfluence, setIsSphereOfInfluence] = useAtom(
    isSphereOfInfluenceAtom,
  );

  const leftContainerRef = useRef<HTMLDivElement>(null);

  // 隐藏互斥图层
  const hideExclusiveLayer = () => {
    if (textureType && hiddenCloudLayerType.includes(textureType)) {
      setTextureType(undefined);
    }
  };

  const { isFullScreen } = useGlobalContext();
  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    right: isFullScreen ? -200 : 20,
  });

  const positionClass = 'absolute top-[20px] bottom-[228px] z-10';

  return (
    <>
      <animated.div
        className={twMerge(
          positionClass,
          'right-[20px]',
          'rounded-[16px] overflow-y-auto scrollbar',
        )}
        style={styles}
        ref={leftContainerRef}
      >
        <GroupBtn
          iconParkName="shachenyingxiang"
          active={isDColorLayerVisible}
          className="mb-2.5"
          onClick={() => {
            setCloudLayerVisible(false);
            setIsDColorLayerVisible((prev) => !prev);
          }}
          label="沙尘影像"
        ></GroupBtn>
        <GroupBtn
          iconParkName="shachenfanwei"
          active={isDustRangeLayerVisible}
          className="mb-2.5"
          onClick={() => {
            setIsDustRangeLayerVisible((prev) => !prev);
          }}
          label="沙尘范围"
        ></GroupBtn>
        <GroupBtn
          iconParkName="guokongzhandian-d4j3jkkl"
          active={isStationLayerVisible}
          className="mb-2.5"
          onClick={() => {
            setIsStationLayerVisible((prev) => !prev);
          }}
          label="国控站"
        ></GroupBtn>
        {isStationLayerVisible && (
          <div className="flex justify-end mb-2.5">
            <CustomSelect
              value={stationType}
              onChange={(val) => setStationType(val)}
              options={stationOptions}
            />
          </div>
        )}
        <GroupBtn
          iconParkName="weixingyaogan"
          active={isRemoteSensingLayerVisible}
          className="mb-2.5"
          onClick={() => {
            setCloudLayerVisible(false);
            setIsRemoteSensingLayerVisible((prev) => !prev);
          }}
          label="卫星遥感"
        ></GroupBtn>
        {isRemoteSensingLayerVisible && (
          <div className="flex justify-end mb-2.5">
            <CustomSelect
              value={textureType}
              onChange={setTextureType}
              options={remoteSensingOptions}
            />
          </div>
        )}
        <GroupBtn
          active={windLayerVisible}
          onClick={() => {
            setWindLayerVisible((prev) => !prev);
          }}
          className="mb-2.5"
          iconParkName="fengchang-cka9elp2"
          label="风场"
        ></GroupBtn>
        <GroupBtn
          iconParkName="weixingyuntu-d4j3jkjk"
          active={cloudLayerVisible}
          className="mb-2.5"
          onClick={() => {
            hideExclusiveLayer();
            setCloudLayerVisible((prev) => !prev);
          }}
          label="卫星云图"
        ></GroupBtn>
        <GroupBtn
          iconParkName="shayuandi"
          active={isSandSourceLayerVisible}
          className="mb-2.5"
          onClick={() => {
            setIsSandSourceLayerVisible((prev) => !prev);
          }}
          label="沙源地"
        ></GroupBtn>
        <GroupBtn
          iconParkName="hangzhengquyu-d4j3o2pp"
          active={isSphereOfInfluence}
          className="mb-2.5"
          onClick={() => {
            setIsSphereOfInfluence((prev) => !prev);
          }}
          label="影响范围"
        ></GroupBtn>
        <GroupBtn
          iconParkName="quyumingcheng-d6jc2if1"
          active={annotationVisible}
          className="mb-2.5"
          onClick={() => {
            setAnnotationVisible((prev) => !prev);
          }}
          label="区划名称"
        ></GroupBtn>
      </animated.div>
    </>
  );
}

export default DataTypeGroups;
