import { FC, HTMLAttributes } from 'react';
import { twMerge } from 'tailwind-merge';

interface GroupBtnProps {
  active?: boolean;
  onClick?: HTMLAttributes<HTMLDivElement>['onClick'];
  children?: React.ReactNode;
  className?: string;
  iconParkName?: string;
  label?: string;
}
const GroupBtn: FC<GroupBtnProps> = ({
  active,
  onClick,
  className,
  iconParkName = 'fengchang-cka9elp2',
  label = '',
}) => {
  return (
    <div
      className={twMerge(
        'relative flex items-center w-[130px] h-[44px] px-[10px] rounded-full text-[16px] gap-[10px] bg-[#101530]/50 backdrop-filter-blur-card',
        !active && 'hover:bg-[#101530]/40',
        active && 'text-[#286CFF]',
        className,
      )}
      onClick={(e) => {
        if (onClick) {
          onClick(e);
        }
      }}
    >
      <div
        className={twMerge(
          'w-7 h-7 rounded-full bg-[#6F8395]',
          active && 'bg-[#286CFF]',
        )}
      >
        <iconpark-icon
          width="28"
          height="28"
          name={iconParkName}
          color={'#ffffff'}
        ></iconpark-icon>
      </div>
      <div
        className={twMerge(
          'cursor-pointer flex-1 z-10 select-none text-white',
          active && 'text-[#286CFF]',
        )}
      >
        {label}
      </div>
    </div>
  );
};

export default GroupBtn;
