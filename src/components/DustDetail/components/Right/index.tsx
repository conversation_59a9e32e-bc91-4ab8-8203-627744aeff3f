import { useGlobalContext } from '@/components/GlobalProvider';
import { animated, useSpring } from '@react-spring/web';
import ChangeMapStyleBtn from './ChangeMapStyleBtn';
import FullScreenButton from './FullScreenButton';
import ResetButton from './ResetButton';

const Right = () => {
  const { isFullScreen } = useGlobalContext();

  const styles = useSpring({
    right: isFullScreen ? -60 : 20,
  });

  return (
    <animated.div
      style={styles}
      className="absolute right-[20px] bottom-[107px] z-10 flex flex-col items-center"
    >
      <div className="flex-1"></div>
      <div className="flex flex-col gap-[10px] items-end">
        <FullScreenButton />
        <ChangeMapStyleBtn />
        <ResetButton />
      </div>
    </animated.div>
  );
};

export default Right;
