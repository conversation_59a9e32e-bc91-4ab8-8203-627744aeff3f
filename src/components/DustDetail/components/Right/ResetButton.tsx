import { calcCenterAndZoomByGeojson } from '@/utils/dust/geojson/calcCenterAndZoomByGeojson';
import { bbox } from '@turf/turf';
import { FlyToInterpolator } from 'deck.gl';
import { useAtomValue } from 'jotai';
import { deckInstanceRefAtom } from '../../atoms';
import SwitchButton from './SwitchButton';
import { useRouter } from '@/hooks';
import { useQuery } from 'react-query';
import { getDustEventInfo } from '@/pages/DustEvent/services';


const ResetButton = () => {
  const deckInstance = useAtomValue(deckInstanceRefAtom);
  const { query } = useRouter();
  const { data } = useQuery(
    ['getDustEventInfo', query.id],
    () => getDustEventInfo({ dustEventId: query.id }),
    { enabled: Boolean(query.id) },
  );
  return (
    <SwitchButton
      className="w-[32px] flex items-center justify-center"
      title="复位"
      onClick={() => {
        const width = deckInstance?.current?.deckGLInstance?.width || 0;
        const height = deckInstance?.current?.deckGLInstance?.height || 0;
        if (data?.geoJson && width > 0 && height > 0) {
          const [minLon, minLat, maxLon, maxLat] = bbox(
            JSON.parse(data?.geoJson),
          );

          const centerAndZoom = calcCenterAndZoomByGeojson(
            minLon,
            maxLon,
            minLat,
            maxLat,
            width,
            height,
            160,
          );
          deckInstance?.current?.setViewState({
            longitude: centerAndZoom?.longitude,
            latitude: centerAndZoom?.latitude,
            zoom: centerAndZoom?.zoom,
            transitionInterpolator: new FlyToInterpolator(),
            transitionDuration: 500,
          });
        }
      }}
    >
      <iconpark-icon
        width="20"
        height="20"
        name="fuwei-d4j3nag4"
      ></iconpark-icon>
    </SwitchButton>
  );
};

export default ResetButton;
