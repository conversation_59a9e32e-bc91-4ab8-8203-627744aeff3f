import { FC } from 'react';
import { twMerge } from 'tailwind-merge';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  active?: boolean;
  disable?: boolean;
  children?: React.ReactNode;
}

const SwitchButton: FC<Props> = ({ className, disable, children, ...rest }) => {
  const { onClick, ...r } = rest;
  return (
    <div
      className={twMerge(
        'h-[32px] min-w-[32px] flex items-center select-none cursor-pointer gap-[8px] bg-white rounded-full',
        'hover:bg-[#eeeeee] transition-colors',
        disable && 'bg-opacity-80 text-[#666]',
        className,
      )}
      onClick={(e) => {
        if (disable) return;
        if (onClick) {
          onClick(e);
        }
      }}
      {...r}
    >
      {children}
    </div>
  );
};

export default SwitchButton;
