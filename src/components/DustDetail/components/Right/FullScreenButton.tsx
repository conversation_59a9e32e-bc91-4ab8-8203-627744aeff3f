import { useGlobalContext } from '@/components/GlobalProvider';
import { memo } from 'react';
import SwitchButton from './SwitchButton';

function FullScreenButton() {
  const { isFullScreen, setIsFullScreen } = useGlobalContext();

  return (
    <>
      {isFullScreen && (
        <div
          className="px-[12px] py-[6px] text-white text-[14px] fixed top-[20px] right-[20px] z-[200] cursor-pointer rounded-full bg-[#101530]/50 backdrop-filter-blur-card select-none"
          onClick={() => {
            setIsFullScreen(false);
          }}
        >
          退出演示模式
        </div>
      )}
      <SwitchButton
        className="w-[32px] flex items-center justify-center"
        title="全屏"
        onClick={() => {
          setIsFullScreen((prev) => !prev);
        }}
      >
        <iconpark-icon
          width="20"
          height="20"
          name="quanping-d4j3o2pl"
        ></iconpark-icon>
      </SwitchButton>
    </>
  );
}

export default memo(FullScreenButton);
