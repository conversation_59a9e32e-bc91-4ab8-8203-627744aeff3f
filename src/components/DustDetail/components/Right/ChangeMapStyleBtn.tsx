import { animated, useSpring } from '@react-spring/web';
import { useAtom } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import { useOnClickOutside } from 'usehooks-ts';
import { mapTypeAtom } from '../../atoms';
import Checkbox from '../Checkbox';

const options = [
  {
    label: '标准地图',
    value: 'vec',
  },
  {
    label: '卫星影像',
    value: 'img',
  },
] as const;
const WIDTH = 112 * options.length;
const INIT = {
  width: 0,
  opacity: 0,
  paddingLeft: 0,
};
const ChangeMapStyleBtn = () => {
  const [tileStyle, setTileStyle] = useAtom(mapTypeAtom);
  const [open, setOpen] = useState(false);
  const [style, api] = useSpring(() => INIT);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (open) {
      api.start({
        opacity: 1,
        width: WIDTH,
        paddingLeft: 12,
      });
    } else {
      api.start(INIT);
    }
  }, [api, open]);
  const handler = (value: typeof tileStyle) => {
    setTileStyle(value);
    setOpen(false);
  };

  useOnClickOutside(ref, () => {
    setOpen(false);
  });

  return (
    <div
      className="flex items-center justify-end rounded-full shadow-toolbar overflow-hidden backdrop-filter-blur-card"
      style={{
        background:
          'linear-gradient( 270deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0.5) 100%)',
      }}
      ref={ref}
    >
      <animated.div
        className="flex items-center gap-[12px] flex-shrink-0 overflow-hidden"
        style={style}
      >
        {options.map((option) => {
          return (
            <div className="flex items-center" key={option.value}>
              <Checkbox
                checked={tileStyle === option.value}
                onChange={() => {
                  handler(option.value);
                }}
              >
                <div
                  className="text-[#002a58] cursor-pointer"
                  style={{
                    whiteSpace: 'nowrap',
                  }}
                  onClick={() => {
                    handler(option.value);
                  }}
                >
                  {option.label}
                </div>
              </Checkbox>
            </div>
          );
        })}
      </animated.div>
      <div
        className="flex items-center justify-center w-[32px] h-[32px] rounded-full cursor-pointer relative bg-white hover:bg-[#eeeeee] transition-colors"
        onClick={() => setOpen((p) => !p)}
        title="切换底图"
      >
        <iconpark-icon
          width="20"
          height="20"
          name="dituqiehuan"
        ></iconpark-icon>
      </div>
    </div>
  );
};

export default ChangeMapStyleBtn;
