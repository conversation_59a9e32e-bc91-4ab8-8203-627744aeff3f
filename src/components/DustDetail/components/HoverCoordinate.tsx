import GeoUtils from '@/utils/geo';
import { useAtomValue } from 'jotai';
import { coordinateAtom } from '../atoms';
import { InfoItem, ItemHead, ItemText } from '@/pages/Overview/components/ui';

const HoverCoordinate: React.FC = () => {
  const coordinate = useAtomValue(coordinateAtom);
  const [lng = 0, lat = 0] = coordinate;

  return (
    <>
      {coordinate.length > 0 && (
        <InfoItem>
          <ItemHead size={20}>
            <i className="icomoon icon-coordinate" />
          </ItemHead>
          <ItemText style={{minWidth:'250px'}}>
            <span>东经{GeoUtils.format(lng)}</span>
            <span className='ml-[10px]'>北纬{GeoUtils.format(lat)}</span>
          </ItemText>
        </InfoItem>
      )}
    </>
  );
};

export default HoverCoordinate;
