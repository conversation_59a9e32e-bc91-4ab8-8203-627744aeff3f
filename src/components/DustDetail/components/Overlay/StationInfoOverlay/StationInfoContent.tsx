import type { SelectedSandSourceData } from '@/components/DustDetail/atoms';
import { ensureNumber } from '@/utils/dust';
import { Viewport } from 'deck.gl/typed';
import { memo, useMemo } from 'react';

type Props = {
  data: SelectedSandSourceData;
  stationUnit: string | undefined;
  viewport: Viewport;
};

export default memo(function StationInfoOverlay({
  data,
  stationUnit,
  viewport,
}: Props) {
  const [r, g, b] = data.color ?? [0, 0, 0];

  const [left, top] = useMemo(() => {
    const coordinate = [data.lon, data.lat];
    return viewport.project([
      ensureNumber(coordinate[0]),
      ensureNumber(coordinate[1]),
    ]);
  }, [data.lat, data.lon, viewport]);

  return (
    <div className="absolute" style={{ left, top: top - 66 }}>
      <div
        style={{
          background: 'rgb(11, 21, 39)',
          borderRadius: '6px 6px 6px 0px',
          color: 'rgb(255, 255, 255)',
          padding: '4px 7px',
          fontSize: '12px',
        }}
      >
        <div className="flex items-center gap-[4px]">
          <div
            style={{
              width: '8px',
              height: '8px',
              borderRadius: '8px',
              background: `rgba(${r}, ${g}, ${b}, 1)`,
            }}
          ></div>
          <span>{data.stationName}</span>
        </div>
        <div>
          {data.value ?? '-'} {stationUnit}
        </div>
      </div>
      <div
        style={{
          width: '28px',
          height: '20px',
          background: 'rgb(11, 21, 39)',
          clipPath: `path("M 0 0 L 0 20.151 C 3.3333 7.151 10.8333 0.4843 22.546 0.048 Z")`,
        }}
      ></div>
      <div
        style={{
          position: 'absolute',
          bottom: '-2px',
          left: '-2px',
          width: '5px',
          height: '5px',
          borderRadius: '4px',
          background: 'rgb(11, 21, 39)',
        }}
      ></div>
    </div>
  );
});
