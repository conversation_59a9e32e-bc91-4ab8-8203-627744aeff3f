import {
  selectedStationDataAtom,
  stationUnitAtom,
} from '@/components/DustDetail/atoms';
import { Viewport } from 'deck.gl/typed';
import { useAtomValue } from 'jotai';
import { memo } from 'react';
import StationInfoContent from './StationInfoContent';

export default memo(function StationInfoOverlay({
  viewport,
}: {
  viewport: Viewport;
}) {
  const selectedStationData = useAtomValue(selectedStationDataAtom);
  const stationUnit = useAtomValue(stationUnitAtom);

  return (
    <>
      {selectedStationData && (
        <StationInfoContent
          data={selectedStationData}
          stationUnit={stationUnit}
          viewport={viewport}
        />
      )}
    </>
  );
});
