import { hoveredSandRangeDataAtom } from '@/components/DustDetail/atoms';
import calcSandRangeDirection from '@/components/DustDetail/utils/calcSandRangeDirection';
import { ensureArray } from '@/utils/dust';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';

export default function usePredictionData() {
  const hoveredSandRangeData = useAtomValue(hoveredSandRangeDataAtom);

  const directionText = useMemo(() => {
    return calcSandRangeDirection(
      hoveredSandRangeData?.properties.warningModel?.direction,
    );
  }, [hoveredSandRangeData?.properties.warningModel?.direction]);

  const regionsText = useMemo(() => {
    return ensureArray(hoveredSandRangeData?.properties.warningModel?.regions)
      .map((item: any) => {
        return item.value;
      })
      .join('、');
  }, [hoveredSandRangeData?.properties.warningModel?.regions]);

  return {
    directionText,
    regionsText,
  };
}
