import {
  isSandSourceLayerVisibleAtom,
  selectedSandSourceDataAtom,
} from '@/components/DustDetail/atoms';
import { useAtom, useAtomValue } from 'jotai';
import { useEffect, useMemo } from 'react';
import type { SandSourceOverlayProps } from './SandSourceOverlay';
import SandSourceOverlay from './SandSourceOverlay';

export default function SandSourceOverlayWrapper(
  props: SandSourceOverlayProps,
) {
  const isSandSourceLayerVisible = useAtomValue(isSandSourceLayerVisibleAtom);
  const [selectedSandSourceData, setSelectedSandSourceData] = useAtom(
    selectedSandSourceDataAtom,
  );

  useEffect(() => {
    if (!isSandSourceLayerVisible) {
      setSelectedSandSourceData(undefined);
    }
  }, [isSandSourceLayerVisible, setSelectedSandSourceData]);

  const show = useMemo(() => {
    return isSandSourceLayerVisible && selectedSandSourceData;
  }, [isSandSourceLayerVisible, selectedSandSourceData]);

  return <>{show && <SandSourceOverlay {...props} />}</>;
}
