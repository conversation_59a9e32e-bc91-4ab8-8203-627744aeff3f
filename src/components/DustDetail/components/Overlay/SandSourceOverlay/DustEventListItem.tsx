import { ensureArray } from '@/utils/dust';
import { formatAreaNumber, formatAreaUnit } from '@/utils/dust/formatArea';
import dayjs from 'dayjs';
import { memo, useMemo } from 'react';

function DustEventListItem({ content }: { content: APIV2.TDustEventModel }) {
  const {
    startTime,
    endTime,
    area,
    duration,
    regionCount,
    sandSourceName,
    sandSourceFlag,
  } = useMemo(() => {
    return {
      startTime: dayjs(content?.startTime).format('MM.DD HH:mm'),
      endTime: dayjs(content?.endTime).format('MM.DD HH:mm'),
      area: content.area,
      duration: (Number(content?.duration) / 60 / 60).toFixed(2),
      regionCount: ensureArray(content?.regions).length,
      sandSourceName: ensureArray(content?.sendSites)[0]?.value,
      sandSourceFlag: content.code.includes('JN') ? '境内' : '境外',
    };
  }, [
    content.area,
    content.code,
    content?.duration,
    content?.endTime,
    content?.regions,
    content?.sendSites,
    content?.startTime,
  ]);

  return (
    <div className="rounded-[8px] bg-[#FFFCE724] p-[12px]">
      <div className="flex items-center justify-between gap-[20px] text-[14px]">
        <div className="flex items-center gap-[2px]">
          <p>{startTime}</p>
          <iconpark-icon
            width="12"
            height="12"
            color="#ffffff"
            name="bianzu"
          ></iconpark-icon>
          <p>{endTime}</p>
        </div>
        <div>{content.code}</div>
      </div>

      <div className="flex items-center justify-between px-[10px] py-[4px] my-[12px] text-[12px] bg-[#FFFCE724] rounded-full">
        <div className="flex items-center gap-[4px]">
          <iconpark-icon
            width="12"
            height="12"
            color="#F4A115"
            name="feng"
          ></iconpark-icon>
          <span>沙尘源头</span>
        </div>
        <div>
          {sandSourceName}({sandSourceFlag})
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-end justify-center">
            <span className="text-[16px] leading-[18px] text-[#FFC935]">
              {formatAreaNumber(area)}
            </span>
            <span className="text-[12px] leading-[14px] text-white">{formatAreaUnit(area)}km²</span>
          </div>
          <div className="text-center text-[14px]">影响面积</div>
        </div>
        <div>
          <div className="flex items-end justify-center">
            <span className="text-[16px] leading-[18px] text-[#FFC935]">
              {duration}
            </span>
            <span className="text-[12px] leading-[14px] text-white">h</span>
          </div>
          <div className="text-center text-[14px]">持续时间</div>
        </div>
        <div>
          <div className="flex items-end justify-center">
            <span className="text-[16px] leading-[18px] text-[#FFC935]">
              {regionCount}
            </span>
            <span className="text-[12px] leading-[14px] text-white">个</span>
          </div>
          <div className="text-center text-[14px]">影响省份</div>
        </div>
      </div>
    </div>
  );
}

export default memo(DustEventListItem);
