import { Popover } from 'antd';
import { memo, useState } from 'react';
import NavigationItem from '../../NavigationItem';

type Props<T> = {
  options: T[];
  value: T;
  onChange: (value: T) => void;
};

function Select<T extends DUST.RegionInfo | undefined>({
  options,
  value,
  onChange,
}: Props<T>) {
  const [open, setOpen] = useState(false);

  return (
    <div className="flex items-center ">
      <Popover
        placement="top"
        overlayClassName="rounded-[14px] overflow-hidden"
        overlayInnerStyle={{
          padding: '10px 4px',
          background: '#394461',
        }}
        arrow={false}
        content={
          <div className="flex flex-col max-h-[220px] max-w-[110px] text-white overflow-y-auto overflow-x-hidden scrollbar-thin">
            {options.map((option) => {
              return (
                <NavigationItem
                  onClick={() => {
                    onChange(option);
                    setOpen(false);
                  }}
                  key={option!.code + option!.name}
                >
                  {() => (
                    <span
                      title={option?.name}
                      className="text-[14px] px-[13px] text-overflow-ellipsis"
                    >
                      {option?.name}
                    </span>
                  )}
                </NavigationItem>
              );
            })}
          </div>
        }
        trigger="click"
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <div className="flex items-center justify-center gap-[10px] w-[99px] h-[34px] px-2 bg-[#394461] rounded-[24px] cursor-pointer select-none backdrop-filter-blur-card">
          <span
            className="text-[14px] text-white leading-[22px] text-overflow-ellipsis"
            title={value?.name}
          >
            {value?.name}
          </span>
          <iconpark-icon
            name="xiala"
            width="13"
            height="13"
            color="#B9C9DB"
            style={{
              transform: open ? 'rotate(180deg)' : 'none',
              transition: 'all 0.5s',
            }}
          ></iconpark-icon>
        </div>
      </Popover>
    </div>
  );
}

export default memo(Select) as <T>(props: Props<T>) => JSX.Element;
