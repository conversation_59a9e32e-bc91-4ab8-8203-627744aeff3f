import { regionInfoAtom } from '@/components/DustDetail/atoms';
import { regionList } from '@/utils/dust/regionList';
import { useAtom } from 'jotai';
import { memo } from 'react';
import Select from './Select';

function RegionSelect() {
  const [regionInfo, setRegionInfo] = useAtom(regionInfoAtom);

  return (
    <Select
      value={regionInfo}
      options={regionList}
      onChange={(val) => setRegionInfo(val)}
    />
  );
}

export default memo(RegionSelect);
