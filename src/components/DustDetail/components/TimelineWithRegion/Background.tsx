import { memo, useEffect, useMemo, useRef, useState } from 'react';

// 基准宽度
const BaseWidth = 1880;
// 切图中 path 右侧基准点位置
const BaseRightPoint = 1855;
// 切图中 path 左侧基准点位置
const BaseLeftPoint = -1830;

export default memo(function Background() {
  const [offSetX, setOffSetX] = useState(0);
  const headerRef = useRef<HTMLDivElement | null>(null);

  const rightPoint = useMemo(() => {
    return BaseRightPoint + offSetX;
  }, [offSetX]);

  const leftPoint = useMemo(() => {
    return BaseLeftPoint - offSetX;
  }, [offSetX]);

  const path = useMemo(() => {
    return `m ${rightPoint} 1 
    c 6.6964 0 12.7589 2.7143 17.1473 7.1027 
    c 4.3884 4.3884 7.1027 10.4509 7.1027 17.1473 
    c 0 6.6965 -2.7143 12.759 -7.1027 17.1473 
    c -4.3884 4.3884 -10.4509 7.1027 -17.1473 7.1027 l ${leftPoint} 0 
    c -6.6965 0 -12.759 -2.7143 -17.1473 -7.1027 
    c -4.3884 -4.3884 -7.1027 -10.4509 -7.1027 -17.1473 
    c 0 -6.6965 2.7143 -12.759 7.1027 -17.1473 
    c 4.3884 -4.3884 10.4509 -7.1027 17.1473 -7.1027 
    z`;
  }, [rightPoint, leftPoint]);

  useEffect(() => {
    // 创建 ResizeObserver 实例
    const observer = new ResizeObserver((entries) => {
      // 遍历观察到的所有条目
      for (const entry of entries) {
        // 获取目标 <div> 元素的宽度
        const divWidth = entry.contentRect.width;
        const offSetX = divWidth - BaseWidth;
        setOffSetX(offSetX);
      }
    });

    // 开始观察目标 <div> 元素的大小变化
    observer.observe(headerRef.current!);
  }, []);

  return (
    <div
      className="absolute top-0 left-0 right-0 z-[-1] select-none"
      ref={headerRef}
    >
      <svg height="54" width="1900">
        <defs xmlns="http://www.w3.org/2000/svg">
          <linearGradient
            x1="50%"
            y1="0%"
            x2="50%"
            y2="100%"
            id="linearGradient"
          >
            <stop stopColor="#FEAA38" offset="0%" />
            <stop stopColor="#FFCE00" stopOpacity="0.178649476" offset="100%" />
          </linearGradient>
        </defs>
        <path
          stroke="url(#linearGradient)"
          fill="#ffffff00"
          strokeWidth="2"
          d={path}
        ></path>
      </svg>
    </div>
  );
});
