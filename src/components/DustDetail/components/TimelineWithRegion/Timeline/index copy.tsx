import {
  frameAtom,
  isCloudLayerVisibleAtom,
  isDColorLayerVisibleAtom,
  isDustRangeLayerVisibleAtom,
  isStationLayerVisibleAtom,
  loadingCloundTileAtom,
  loadingPointsAtom,
  loadingRangeAtom,
  loadingTileAtom,
  startPollAtom,
} from '@/components/DustDetail/atoms';
import { tweenFrameCount } from '@/components/DustDetail/config';
import { LoadingOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import React, { createRef, memo, useCallback, useEffect, useMemo, useState } from 'react';
import { twJoin, twMerge } from 'tailwind-merge';
import BasicTimeline from './BasicTimeline';
import { type TimelineListItem } from './TimelineItem';
import { TimelineControl } from './ui';
import { useSpring } from 'react-spring';

const actionIconStyle = twJoin(
  'flex items-center justify-center w-[26px] h-[26px] border-[1px] border-white  bg-[#000000]/20 rounded-full flex-shrink-0 grow-0 cursor-pointer select-none',
);

const findIndex = (
  list: TimelineListItem[],
  id: number | undefined | string,
) => {
  return list.findIndex((v) => v.id === id);
};

let intervalId: any;
type Props = {
  /**
   * 当前选中的时间点
   *
   * 初始时如果传undefined，则默认选中列表最后一个
   */
  activeItem?: TimelineListItem;
  /**
   * 设置当前选中的时间点
   */
  setActiveItem: React.Dispatch<
    React.SetStateAction<TimelineListItem | undefined>
  >;
  /**
   * 需要展示的时间点列表
   */
  list: TimelineListItem[];
};

function Timeline({ list, activeItem, setActiveItem }: Props) {
  const [startPoll, setStartPoll] = useAtom(startPollAtom);
  const [frame, setFrame] = useAtom(frameAtom);
  const isDColorLayerVisible = useAtomValue(isDColorLayerVisibleAtom);
  const isCloudLayerVisible = useAtomValue(isCloudLayerVisibleAtom);
  const isStationLayerVisible = useAtomValue(isStationLayerVisibleAtom);
  const isDustRangeLayerVisible = useAtomValue(isDustRangeLayerVisibleAtom);
  const loadingTile = useAtomValue(loadingTileAtom);
  const loadingCloundTile = useAtomValue(loadingCloundTileAtom);
  const loadingPoints = useAtomValue(loadingPointsAtom);
  const loadingRange = useAtomValue(loadingRangeAtom);
  const isLoading = useMemo(() => {
    if ((isDColorLayerVisible && loadingTile) || (isCloudLayerVisible && loadingCloundTile) || (isStationLayerVisible && loadingPoints) || (isDustRangeLayerVisible && loadingRange)) {
      return true;
    } else {
      return false;
    }
  }, [isCloudLayerVisible, isDColorLayerVisible, isDustRangeLayerVisible, isStationLayerVisible, loadingCloundTile, loadingPoints, loadingRange, loadingTile]);
  useEffect(() => {
    if (isLoading) {
      setStartPoll(false);
    }
  }, [isLoading, setStartPoll])
  const totalFrame = useMemo(() => {
    return (list.length - 1) * (tweenFrameCount + 1);
  }, [list.length]);

  const getActiveTimePointPercentage = useCallback(
    (activeTimelineItem: TimelineListItem) => {
      if (activeTimelineItem) {
        const index = list.findIndex(
          (item) => item.value === activeTimelineItem.value,
        );
        return index / (list.length - 1);
      }
      return 0;
    },
    [list],
  );
  const setActiveItemAndFrame = useCallback(
    (data: TimelineListItem) => {
      setActiveItem(data);
      const percentage = getActiveTimePointPercentage(data);
      const currentFrame = Math.round(totalFrame * percentage);
      setFrame(currentFrame);
    },
    [setActiveItem, getActiveTimePointPercentage, totalFrame, setFrame],
  );

  const handleActiveItemChange = (data: TimelineListItem) => {
    // 点击时停止轮播
    setStartPoll(false);
    setActiveItemAndFrame(data);
  };

  // const toPrevious = useCallback(() => {
  //   const index = findIndex(list, activeItem?.id);
  //   if (index > 0) {
  //     setActiveItemAndFrame(list[index - 1]);
  //   }
  // }, [activeItem?.id, list, setActiveItemAndFrame]);

  // const toNext = useCallback(() => {
  //   const index = findIndex(list, activeItem?.id);
  //   if (index > -1 && index < list.length - 1) {
  //     setActiveItemAndFrame(list[index + 1]);
  //   }
  // }, [activeItem?.id, list, setActiveItemAndFrame]);

  const poll = useCallback(() => {
    const index = findIndex(list, activeItem?.id);
    if (index >= list.length - 1) {
      setActiveItem(list[0]);
    } else {
      setActiveItem(list[index]);
    }
  }, [activeItem?.id, list, setActiveItem]);

  const handlePollStateChange = useCallback(() => {
    // 初始化的时立即执行一次播放
    if (!startPoll) {
      poll();
    }
    setStartPoll((prev) => !prev);
  }, [poll, setStartPoll, startPoll]);

  useEffect(() => {
    // 初始化时间轴
    if (
      !list.find((active) => active.date === activeItem?.date) &&
      Array.isArray(list) &&
      list.length > 0
    ) {
      setActiveItemAndFrame(list[0]);
    }
  }, [activeItem, list, setActiveItemAndFrame]);
  // 轮播控制
  useEffect(() => {
    if (startPoll) {
      intervalId = setInterval(() => {
        // 两个时间点之间的总帧数
        const frameInTwoPoints = tweenFrameCount + 1;
        if (frame % frameInTwoPoints === 0) {
          const index = Math.round(frame / frameInTwoPoints);
          setActiveItem(list[index]);
        }

        setFrame((prev) => (prev < totalFrame ? prev + 1 : 0));
      }, 100);
    } else {
      clearInterval(intervalId);
    }
    return () => {
      clearInterval(intervalId);
    };
  }, [frame, list, poll, setActiveItem, setFrame, startPoll, totalFrame]);
  // 退出时暂停
  useEffect(() => {
    return () => {
      setStartPoll(false);
      setActiveItem(list[0]);
      setActiveItemAndFrame(list[0]);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [list]);



  const timelineWrapperRef = createRef<HTMLDivElement>();
  const [dialWidth, setDialWidth] = useState(10);
  const [scrollLeft, setScrollLeft] = useState(0);


  const stylesScroll = useSpring({
    scrollLeft,
  });

  
  return (
    <div className="relative z-[1] flex items-center gap-[8px] overflow-hidden grow">
      {isLoading ? (
        <div
          className={twMerge(actionIconStyle, 'bg-white')}
          onClick={() => {
            message.warning('请等待资源加载完毕');
          }}
          title={'资源加载中'}
        >
          <LoadingOutlined />
        </div>
      ) : (
        <TimelineControl
          active
          onClick={handlePollStateChange}
          title={startPoll ? '暂停' : '播放'}
        >
          {startPoll ? (
            <iconpark-icon
              width="24"
              height="24"
              name="zanting-d51o5mk0"
              fill='#fff'
            ></iconpark-icon>
          ) : (
            <iconpark-icon
              width="24"
              height="24"
              name="bofang-d51o5962"
               fill='#fff'
            ></iconpark-icon>
          )}
          <p>{startPoll ? '暂停' : '播放'}</p>
        </TimelineControl>
      )}
      <div className="flex h-[54px] rounded-[4px] bg-[#25262D] items-center justify-between px-[6px] grow gap-[20px] overflow-hidden">
        {/* <div className={actionIconStyle} onClick={toPrevious}>
          <iconpark-icon
            width="24"
            height="24"
            name="zuoqiehuan"
          ></iconpark-icon>
        </div> */}
        {/* 基础时间轴模块 */}
        <BasicTimeline
          value={activeItem}
          options={list}
          onChange={handleActiveItemChange}
        />
        {/* <div className={actionIconStyle} onClick={toNext}>
          <iconpark-icon
            width="24"
            height="24"
            name="youqiehuan"
          ></iconpark-icon>
        </div> */}
      </div>
    </div>
  );
}

export default memo(Timeline);
