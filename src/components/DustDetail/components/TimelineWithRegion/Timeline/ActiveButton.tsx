import { forwardRef, memo } from 'react';
import { twMerge } from 'tailwind-merge';

type Props = {
  haveDust: boolean;
  children?: React.ReactNode;
};

export default memo(
  forwardRef(function ActiveButton(
    { haveDust, children }: Props,
    ref: React.Ref<HTMLDivElement>,
  ) {
    return (
      <div
        className={twMerge(
          'relative w-[16px] h-[16px] flex items-center justify-center rounded-full',
          haveDust ? 'bg-[#FFB600]/30' : 'bg-[#3DE7B1]/30',
        )}
      >
        {/* absolute 布局，不受flex影响 */}
        <div
          ref={ref}
          className={twMerge(
            'absolute top-[-0px] left-[-0px]',
            `flex items-center justify-center w-[16px] h-[16px] rounded-full`,
            haveDust ? 'bg-[#FFB600]/40' : 'bg-[#3DE7B1]/40',
          )}
          style={{ animation: 'bubble 2s linear infinite' }}
        >
          <div
            className={twMerge(
              'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
              'flex items-center justify-center w-[12px] h-[12px] bg-[#FFB600]/40 rounded-full',
              haveDust ? 'bg-[#FFB600]/60' : 'bg-[#3DE7B1]/60',
            )}
          >
            <div
              className={twMerge(
                'w-[6px] h-[6px] border-[1px] border-white bg-[#FFB600] rounded-full',
                haveDust ? 'bg-[#FFB600]' : 'bg-[#3DE7B1]',
              )}
            ></div>
          </div>
        </div>
        {children}
      </div>
    );
  }),
);
