import { Center, Flex, scrollBarStyles } from "@/components/ui";
import { getColorFromTheme } from "@/components/ui/utils";
import { animated } from "react-spring";
import styled, { css } from "styled-components";

export const TimelineControl = styled(Center)<{
  active?: boolean;
  disabled?: boolean;
}>`
  flex-direction: column;
  width: 36px;
  height: 54px;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;

  ${(props) =>
    props.disabled &&
    css`
      opacity: 0.7;
      pointer-events: none;
    `}

  color: ${(props) =>
    props.active ? props.theme.colors.white : props.theme.colors.gray['400']};
  background: ${(props) =>
    props.active ? props.theme.colors.primary : props.theme.colors.gray['800']};

  .icomoon {
    font-size: 16px;
  }

  p {
    margin: 4px 0 0 !important;
    font-size: 12px;
  }
`;
export const TimelineContainer = styled(Flex)`
  flex: 1;
  align-items: center;

  ${TimelineControl} {
    &:last-child {
    }
  }
`;

export const TimelineBodyWrapper = styled(animated.div)`
  flex: 1;
  padding-top: 5px;
  padding-left: 3px;
  margin-left: 10px;
  overflow-x: auto;
  border-radius: 4px;

  ${scrollBarStyles};
`;

export const TimelineBody = styled(Flex)`
  width: 0px;
  position: relative;
  height: 54px;
  align-items: flex-end;
  border-radius: 4px;
  background: ${getColorFromTheme('gray.800')};
`;

export const Nonius = styled.div`
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  width: 1px;
  background: ${getColorFromTheme('primary')};

  &::after {
    position: absolute;
    top: -5px;
    left: -2px;
    width: 5px;
    height: 5px;
    background: ${getColorFromTheme('primary')};
    border-radius: 3px;
    content: '';
  }
`;
