import React, { FC, memo, useEffect, useRef, useState } from 'react';
import Item, { type TimelineListItem } from './TimelineItem';

interface Props {
  /**
   * 列表数据
   */
  options: TimelineListItem[];

  /**
   * 数据变动的回调函数
   *
   * @param data
   * @param event
   * @returns
   */
  onChange: (data: TimelineListItem, event: React.MouseEvent) => void;

  /**
   * 当前选中的数据
   */
  value?: TimelineListItem;
}

const BasicTimeline: FC<Props> = ({ options, onChange, value }) => {
  const scrollContainer = useRef<HTMLDivElement>(null);
  const [, setOutlineWidth] = useState(0);
  const [activeItem, setActiveItem] = useState<TimelineListItem | undefined>(
    undefined,
  );

  useEffect(() => {
    // 更新 activeValue
    if (value) {
      setActiveItem(value);
      return;
    }
  }, [value]);
  const maxArea = options.reduce((prev, curr) => {
    if (curr.value > prev) {
      return curr.value;
    } else {
      return prev;
    }
  }, 0);
  return (
    <div
      className="flex grow h-full overflow-x-auto justify-between relative scrollbarDustDetail"
      ref={scrollContainer}
      style={{
        overscrollBehavior: 'contain',
      }}
    >
      {options.map((item) => {
        return (
          <Item
            key={item.id}
            data={item}
            maxArea={maxArea}
            isActive={item.id === activeItem?.id}
            handleClick={(data, e) => onChange(data, e)}
            containerRef={scrollContainer}
            setOutlineWidth={(width) => setOutlineWidth(width)}
          />
        );
      })}
    </div>
  );
};

export default memo(BasicTimeline);
