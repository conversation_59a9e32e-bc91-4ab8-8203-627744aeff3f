import { formatAreaNumber, formatAreaUnit } from '@/utils/dust/formatArea';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';

/**
 * 生成 10 位的唯一 ID
 *
 * @returns 唯一 ID
 */
function genUniqueID(): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let autoId = '';
  for (let i = 0; i < 10; i++) {
    autoId += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return autoId;
}

/**
 * 将一个元素在其父元素内水平居中
 *
 * @param container 父元素
 * @param innerItem 需要居中的元素
 */
export function scrollToHorizontalCenter(
  container: HTMLElement | null,
  innerItem: HTMLElement | null,
) {
  if (container && innerItem) {
    const containerWidth = container.offsetWidth;
    const innerItemWidth = innerItem.offsetWidth;
    const scrollLeft =
      innerItem.offsetLeft - (containerWidth - innerItemWidth) / 2;

    container.scrollTo({
      left: scrollLeft,
      behavior: 'smooth',
    });
  }
}

export type TimelineListItem = {
  id: number | string;
  date: string;
  value: number;
};

type Props<T> = {
  /**
   * 当前项是否被选中
   */
  isActive?: boolean;
  /**
   * 当前项数据
   */
  data: T;
  /**
   * 监听点击事件
   *
   * @param data
   * @param event
   * @returns
   */
  maxArea: number;
  handleClick: (data: T, event: React.MouseEvent) => void;
  containerRef: React.RefObject<HTMLDivElement>;
  setOutlineWidth: (width: number) => void;
};

function Item<T extends TimelineListItem>({
  isActive = false,
  data,
  maxArea,
  handleClick,
  containerRef,
  setOutlineWidth,
}: Props<T>) {
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const [isItemVisible, setIsItemVisible] = useState(true);
  const iconStyle = twMerge(
    'block rounded-full box-content',
    `w-[4px]`,
    'bg-[#FFB600]',
  );

  const itemRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);

  const domId = useMemo(() => genUniqueID(), []);
  const currentDayjs = useMemo(() => dayjs(data?.date), [data?.date]);

  useEffect(() => {
    if (isActive) {
      const outerContainer = containerRef.current;
      const clickedItem = itemRef.current;

      // 自动滚动到父元素中间
      scrollToHorizontalCenter(outerContainer, clickedItem);
      if (clickedItem) {
        setOutlineWidth(clickedItem.offsetLeft + clickedItem.offsetWidth / 2);
      }
    }
  }, [containerRef, isActive, setOutlineWidth]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 元素进入视口
            setIsItemVisible(true);
          } else {
            // 元素不在视口中
            setIsItemVisible(false);
          }
        });
      },
      {
        root: containerRef.current!,
      },
    );
    observer.observe(iconRef.current!);
    return () => observer.disconnect();
    //迁移
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const area = useMemo(() => {
    // if (data.value > 10000) {
    //   return (data.value / 10000).toFixed(1) + '万km²';
    // } else {
    //   return data.value.toFixed(1) + 'km²';
    // }
    return formatAreaNumber(data.value) + formatAreaUnit(data.value) + 'km²';
  }, [data.value]);
  return (
    <Tooltip
      placement="top"
      title={
        <div className="flex flex-col justify-center">
          <div className="text-[12px] leading-[17px] text-center">
            {currentDayjs.format('YYYY/MM/DD HH:mm')}
          </div>
          <div className="text-[14px] leading-[20px] text-center">
            面积：{area}
          </div>
        </div>
      }
      open={isItemVisible && isTooltipOpen || isActive}
      onOpenChange={(val) => setIsTooltipOpen(val)}
    >
      <div
        ref={itemRef}
        id={domId}
        className="py-[3px] flex flex-col items-center justify-end min-w-[12px] h-full relative flex-shrink-0 cursor-pointer scroll-snap-align-center select-none overflow-hidden"
        style={{ background: isActive ? 'rgba(255, 182, 0, 0.3)' : 'none' }}
        onClick={(e) => handleClick(data, e)}
      >
        <div
          className="flex items-center justify-center min-w-[12px]"
          ref={iconRef}
        >
          <span
            className={iconStyle}
            style={{
              height: `${((data.value / maxArea) * 20 + 4).toFixed(0)}px`,
            }}
          ></span>
        </div>
      </div>
    </Tooltip>
  );
}

export default memo(Item);
