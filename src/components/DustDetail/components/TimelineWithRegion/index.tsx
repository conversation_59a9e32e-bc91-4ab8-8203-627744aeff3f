import { useGlobalContext } from '@/components/GlobalProvider';
import { animated, useSpring } from '@react-spring/web';
import { useAtom } from 'jotai';
import { createRef, memo, useState } from 'react';
import { activeTimelineItemAtom } from '../../atoms';
import Timeline from './Timeline';
import useTimePoints from './useTimePoints';

export default memo(function TimelineWithRegion() {
  const timePoints = useTimePoints();
  const [activeTimelineItem, setActiveTimelineItem] = useAtom(
    activeTimelineItemAtom,
  );

  const { isFullScreen } = useGlobalContext();

  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    bottom: isFullScreen ? -80 : 12,
  });



  return (
    <animated.div
      className="absolute bottom-3 right-[32px] left-[20px] z-[20] flex items-center overflow-hidden h-[94px] px-[10px] gap-[8px] backdrop-filter-blur-card"
      style={{
        ...styles,
        background: '#1C1D24',
        borderRadius: '6px',
      }}
    >
      <Timeline
        activeItem={activeTimelineItem}
        list={timePoints}
        setActiveItem={(item) => {
          if (item) {
            setActiveTimelineItem(item);
          }
        }}
      />
    </animated.div>
  );
});
