
import { useRouter } from '@/hooks';
import { ensureArray } from '@/utils/dust';
import { useAtom, useSetAtom } from 'jotai';
import { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import {
  activeTimelineItemAtom,
  frameAtom,
  isSelectTheLastPointOfTimelineAtom,
  timePointsAtom,
} from '../../atoms';
import { TimelineListItem } from './Timeline/TimelineItem';
import { getDustRecordDateArea } from '../../services';

export default function useTimePoints() {
  // const regionCode = useAtomValue(regionCodeAtom);
  const { query } = useRouter();
  const [activeTimelineItem, setActiveTimelineItem] = useAtom(
    activeTimelineItemAtom,
  );
  const setFrame = useSetAtom(frameAtom);
  const setIsSelectTheLastPointOfTimeline = useSetAtom(
    isSelectTheLastPointOfTimelineAtom,
  );
  const setTimePoints = useSetAtom(timePointsAtom);

  const { data } = useQuery(
    ['getDustRecordDateArea', query.id],
    () => getDustRecordDateArea({ dustEventId: query.id }),
    {
      enabled: !!query.id,
    },
  );

  useEffect(() => {
    const isSelectTheLast =
      Array.isArray(data) &&
      data.length > 0 &&
      data[data.length - 1].date === activeTimelineItem?.date;

    setIsSelectTheLastPointOfTimeline(isSelectTheLast);
  }, [activeTimelineItem?.date, data, setIsSelectTheLastPointOfTimeline]);

  const timePoints: TimelineListItem[] = useMemo(() => {
    return ensureArray(
      data?.map((d) => ({
        ...d,
        id: `${d.date}-${d.value}`,
      })),
    );
  }, [data]);
  useEffect(() => {
    if (timePoints.length > 0) {
      setActiveTimelineItem(timePoints[0]);
      setFrame(0);
    }
     //迁移
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setActiveTimelineItem, timePoints]);
  useEffect(() => {
    setTimePoints(timePoints);
  }, [setTimePoints, timePoints]);
  return timePoints;
}
