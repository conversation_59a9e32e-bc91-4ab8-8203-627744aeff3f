import { useClickOutside } from '@/hooks/dust';
import { InfoItem, ItemHead, RectContainer } from '@/pages/Overview/components/ui';
import { animated, useTransition } from '@react-spring/web';
import React, { FC, useState } from 'react';
import { twMerge } from 'tailwind-merge';

interface Props {
  label?: React.ReactNode;
  children?: React.ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  contentClassName?: string;
  popup?: React.ReactNode;
}

const Legend: FC<Props> = ({
  label,
  children,
  containerClassName,
  labelClassName,
  contentClassName,
  popup,
}) => {
  const [visible, setVisible] = useState(false);
  const transition = useTransition(visible, {
    from: { opacity: 0, bottom: 60 },
    enter: { opacity: 1, bottom: 30 },
    leave: { opacity: 0, bottom: 60 },
  });
  const ref = useClickOutside(() => {
    if (visible) {
      setVisible(false);
    }
  });

  return (
    <div
      className={twMerge(
        'relative flex items-center h-5 bg-[#1C1D24] rounded-[2px] select-none shadow-card',
        containerClassName,
        popup && 'cursor-pointer',
      )}
      ref={ref}
      onClick={() => {
        if (popup) {
          setVisible((p) => !p);
        }
      }}
    >
      {label && (
        // <div
        //   className={twMerge(
        //     'text-xs px-[7px] bg-[#286CFF] text-white leading-[20px] rounded-[2px]',
        //     labelClassName,
        //   )}
        // >
        //   {label}
        // </div>
        <ItemHead>{label}</ItemHead>
      )}
      <div
        className={twMerge(
          'h-5 px-[10px] text-xs leading-[20px]',
          contentClassName,
        )}
      >
        {children}
      </div>
      {transition(
        (style, item) =>
          item &&
          popup && (
            <animated.div
              style={style}
              className="absolute bottom-5 left-1/2 transform -translate-x-1/2 z-50"
            >
              {popup}
            </animated.div>
          ),
      )}
    </div>
  
  );
};

export default Legend;
