import { useGlobalContext } from '@/components/GlobalProvider';
import { animated, useSpring } from '@react-spring/web';
import { useAtomValue } from 'jotai';
import { memo } from 'react';
import {
  isDColorLayerVisibleAtom,
  isDustRangeLayerVisibleAtom,
  isRemoteSensingLayerVisibleAtom,
  isStationLayerVisibleAtom,
} from '../../atoms';
import DownloadButton from './DownloadButton';
import ClearSkyLegend from './Legend/ClearSkyLegend';
import CloudLegend from './Legend/CloudLegend';
import DustLegend from './Legend/DustLegend';
import DustRangeLegend from './Legend/DustRangeLegend';
import PollutionLegend from './Legend/PollutionLegend';
import StationLegend from './Legend/StationLegend';
import HoverCoordinate from '../HoverCoordinate';

export const plottingScaleContainerId = 'map-bottom-bar-plotting-scale';

function BottomBar() {
  const isDColorLayerVisible = useAtomValue(isDColorLayerVisibleAtom);
  const isDustRangeLayerVisible = useAtomValue(isDustRangeLayerVisibleAtom);
  const isRemoteSensingLayerVisible = useAtomValue(
    isRemoteSensingLayerVisibleAtom,
  );
  const isStationLayerVisible = useAtomValue(isStationLayerVisibleAtom);
  const { isFullScreen } = useGlobalContext();

  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    bottom: isFullScreen ? -100 : 113,
  });

  return (
    <animated.div
      className="absolute bottom-[113px] right-[32px] left-[20px]  z-10 flex gap-x-4 items-end"
      style={styles}
    >
      <div className='flex flex-1  justify-between'>
        <DownloadButton />
        <div className="flex-1 flex items-center justify-end flex-wrap gap-x-4 gap-y-1">
          {isDColorLayerVisible && <DustLegend />}
          {isDColorLayerVisible && <CloudLegend />}
          {isDColorLayerVisible && <ClearSkyLegend />}
          {isDustRangeLayerVisible && <DustRangeLegend />}
          {isRemoteSensingLayerVisible && <PollutionLegend />}
          {isStationLayerVisible && <StationLegend />}
          <HoverCoordinate />
          <div id={plottingScaleContainerId}></div>

        </div>
      </div>


    </animated.div>
  );
}

export default memo(BottomBar);
