import { Viewport } from 'deck.gl/typed';
import { memo, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { plottingScaleContainerId } from '.';

function PlottingScale({ viewport }: { viewport: Viewport }) {
  const val = useMemo(() => {
    return Math.round(viewport.metersPerPixel * 50);
  }, [viewport.metersPerPixel]);

  const renderContent = (
    <div className="flex flex-col text-xs items-center w-[70px] h-[20px] rounded-[2px] shadow-card bg-[#1C1D24] ">
      <div className="border-b border-[#31333D] text-[#C1C1C4]" >
        {val > 5000 ? `${Math.round(val / 1000)}KM` : `${val}M`}
      </div>
      <div className="flex items-center -mt-0.5">
        <span className="w-[1px] h-[3px] bg-[#31333D]"></span>
        <span className="w-[50px] h-[1px] bg-[#31333D]"></span>
        <span className="w-[1px] h-[3px] bg-[#31333D]"></span>
      </div>
    </div>
  );
  return (
    <>
      {createPortal(
        renderContent,
        document.getElementById(plottingScaleContainerId) as HTMLElement,
      )}
    </>
  );
}

export default memo(PlottingScale);
