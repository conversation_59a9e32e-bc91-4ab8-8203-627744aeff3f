import { textureTypeAtom } from '@/components/DustDetail/atoms';
import { dataConfig } from '@/utils/dust';
import { useAtomValue } from 'jotai';
import { memo, useCallback, useMemo } from 'react';
import Legend from '../Legend';
import { useCustomLegend } from '@/hooks';

function PollutionLegend() {
  const type = useAtomValue(textureTypeAtom);
  const typeLowerCase =type?.toLowerCase()
  const {pollutionType} =useCustomLegend(typeLowerCase as string)

  const pollutionInfo = useMemo(() => {
    const info = !!type ? dataConfig[type] : null;
    return info;
  }, [type]);

  const legendOptions = useMemo(() => {
    if (!pollutionInfo) {
      return [];
    }
    return pollutionType?.values || [] // 一张图里的自定义
    // return pollutionInfo.values;  默认的
  }, [pollutionInfo, pollutionType?.values]);

  const getColorBlockStyle = useCallback(
    (index: number) => {
      if (index === 0) {
        return {
          borderRadius: '12px 12px 0 0',
        };
      }
      if (index === legendOptions.length - 1) {
        return {
          borderRadius: '0 0 12px 12px',
        };
      }
    },
    [legendOptions.length],
  );

  return (
    <>
      <Legend
        label={<span className="text-[12px] px-[4px]">卫星遥感</span>}
        popup={
          <div className="bg-white p-4 rounded-md flex flex-col items-center text-[12px]">
            <div className="whitespace-nowrap">卫星遥感</div>
            <div>{pollutionInfo?.chemicalFormula}</div>
            <div className="whitespace-nowrap">{pollutionInfo?.unit}</div>
            <div className="py-[10px]">
              {legendOptions.map((v, index) => {
                return (
                  <div key={index} className="flex">
                    <div
                      className="w-[10px] h-[24px] mr-[8px]"
                      style={{
                        background: v.color,
                        ...getColorBlockStyle(index),
                      }}
                    ></div>
                    <div className="relative top-[12px] text-[12px]">
                      {v.min}
                    </div>
                    {
                      index === 0 && <div className="relative top-[-7px] left-[-17px] text-[12px]">{v.max}</div>
                    }

                  </div>
                );
              })}
            </div>
          </div>
        }
      >
        <div className="flex items-center">
          <div className="pr-1 text-[#C1C1C4]">{pollutionInfo?.chemicalFormula}</div>
          {legendOptions.map((v, index) => {
            return (
              <div key={index} className="mr-px flex items-center">
                <div
                  className="w-[3px] h-[10px] "
                  style={{
                    background: v.color,
                  }}
                />
              </div>
            );
          })}
        </div>
      </Legend>
    </>
  );
}

export default memo(PollutionLegend);
