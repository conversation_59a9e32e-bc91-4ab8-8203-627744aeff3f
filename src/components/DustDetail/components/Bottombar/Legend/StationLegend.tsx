import { stationTypeAtom } from '@/components/DustDetail/atoms';
import { stationPollutionValuesAndColors } from '@/utils/dust/stationLegendConfig';
import { useAtomValue } from 'jotai';
import { memo, useCallback, useMemo } from 'react';
import Legend from '../Legend';

function StationLegend() {
  const type = useAtomValue(stationTypeAtom);
  const lowercaseType = useMemo(() => type?.toLowerCase() || '', [type]);

  const pollutionTypeForLegend = useMemo(() => {
    const pollutionType = lowercaseType === 'no2tcd' ? 'no2' : lowercaseType;
    return ['o3', 'co', 'no2', 'so2'].includes(pollutionType)
      ? `${pollutionType}-hour`
      : pollutionType;
  }, [lowercaseType]);

  const pollutionInfo = useMemo(() => {
    const info = !!pollutionTypeForLegend
      ? stationPollutionValuesAndColors[pollutionTypeForLegend]
      : null;
    return info;
  }, [pollutionTypeForLegend]);

  const legendOptions = useMemo(() => {
    if (!pollutionInfo) {
      return [];
    }
    return pollutionInfo.values;
  }, [pollutionInfo]);

  const getColorBlockStyle = useCallback(
    (index: number) => {
      if (index === 0) {
        return {
          borderRadius: '12px 12px 0 0',
        };
      }
      if (index === legendOptions.length - 1) {
        return {
          borderRadius: '0 0 12px 12px',
        };
      }
    },
    [legendOptions.length],
  );

  return (
    <>
      <Legend
        label={<span className="text-[12px] px-[4px]">国控站</span>}
        popup={
          <div className="bg-white p-4 rounded-md flex flex-col items-center text-[12px]">
            <div className="whitespace-nowrap">国控站</div>
            <div>{pollutionInfo?.formula}</div>
            <div className="whitespace-nowrap">{pollutionInfo?.unit}</div>
            <div className="py-[10px]">
              {legendOptions.map((v, index) => {
                return (
                  <div key={index} className="flex">
                    <div
                      className="w-[10px] h-[24px] mr-[8px]"
                      style={{
                        background: v.color,
                        ...getColorBlockStyle(index),
                      }}
                    ></div>
                    <div className="relative top-[12px] text-[12px]">
                      {v.min}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        }
      >
        <div className="flex items-center">
          <div className="pr-1 text-[#C1C1C4]">{pollutionInfo?.formula}</div>
          {legendOptions.map((v, index) => {
            return (
              <div key={index} className="mr-px flex items-center">
                <div
                  className="w-[3px] h-[10px] "
                  style={{
                    background: v.color,
                  }}
                />
              </div>
            );
          })}
        </div>
      </Legend>
    </>
  );
}

export default memo(StationLegend);
