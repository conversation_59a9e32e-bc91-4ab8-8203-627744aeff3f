import { loadingTileAtom } from '@/components/DustDetail/atoms';
import { tweenFrameCount } from '@/components/DustDetail/config';
import { getTileUrl } from '@/components/DustDetail/utils/getTileUrl';
import useTextureToken from '@/hooks/dust/useTextureToken';
import DynamicTextureLayer from '@/layers/dust/dynamic-texture-layer';
import DynamicTileLayer from '@/layers/dust/dynamic-texture-layer/dynamic-tile-layer';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { EXTENT } from '@/utils/dust/config';
import { useSetAtom } from 'jotai';
import { useMemo } from 'react';
import { TimelineListItem } from '../../../TimelineWithRegion/Timeline/TimelineItem';

export const useDynamicTextureTileLayer = ({
  timePoints = [],
  frame = 0,
  visible,
}: {
  frame: number;
  timePoints: TimelineListItem[];
  visible: boolean;
}) => {
  const token = useTextureToken('DCOLOR');
  const fps = 2;
  const setLoadingTile = useSetAtom(loadingTileAtom);
  const timeList = useMemo(() => {
    return timePoints.map((item) => item.date);
  }, [timePoints]);
  const data = timeList
    .map((time) =>
      getTileUrl(
        {
          agg: 'none',
          time,
          token,
          type: 'DCOLOR',
        },
        true,
      ),
    )
    .join(',');
  // const loadNext = useCallback(() => {
  //   setLoading(true);
  //   if (!loadingTile || !loadingTile[0]) return;
  //   new Promise((inject) => {
  //     Promise.all(
  //       data.split(',').map((url) => {
  //         const [x, y, z] = loadingTile[0];
  //         return getTranslateImageData(
  //           url.replace('{x}', x).replace('{y}', y).replace('{z}', z)
  //         );
  //       })
  //     ).then((d) => {
  //       inject(d);
  //       setLoading(false);
  //       setLoadingTile((tiles) => tiles?.filter((t) => t !== loadingTile[0]));
  //     });
  //   });
  // }, [data, loadingTile, setLoadingTile]);
  // useEffect(() => {
  //   if (!loading && loadingTile && loadingTile?.length > 0) {
  //     loadNext();
  //   }
  // }, [loadNext, loading, loadingTile]);
  const dynamicTextureTileLayer = useMemo(() => {
    if (!token) return;
    return new DynamicTileLayer({
      ...tileLayerBaseConfig,
      id: `dynamic-texture-tile-DCOLOR`,
      data,
      visible,
      extent: EXTENT,
      maxZoom: 3,
      minZoom: 3,
      tileSize: 256,
      colorFormat: 'RGBA',
      pickable: true,
      min: 0,
      max: 100,
      fps: fps,
      frame: frame,
      normalFrame: tweenFrameCount,
      onReady: () => {
        setLoadingTile(false);
      },
      onLoading: () => {
        setLoadingTile(true);
      },
      // 色带更改后，重新渲染图层
      shouldUpdate: (prevProps: any, nextProps: any) => {
        return (
          prevProps.colorRamp !== nextProps.colorRamp ||
          prevProps.dataUrl !== nextProps.dataUrl ||
          prevProps.visible !== nextProps.visible ||
          prevProps.min !== nextProps.min ||
          prevProps.max !== nextProps.max ||
          prevProps.filters !== nextProps.filters ||
          prevProps.filtersChannel !== nextProps.filtersChannel
        );
      },
      // @ts-ignore
      renderSubLayers: (props) => {
        const {
          bbox: { west, south, east, north },
          //   index: { x, y, z },
        } = props.tile;
        return props?.data
          ? new DynamicTextureLayer(props, {
              pickable: true,
              data: null,
              imageList: props.data,
              bounds: [west, south, east, north],
              // smooth: true,
            })
          : null;
      },
      updateTriggers: {
        renderSubLayers: [frame, fps],
      },
      //@ts-ignore
      getTileData: (tile) => {
        // setLoadingTile((tiles) =>
        //   tiles === undefined
        //     ? [tile.id]
        //     : tiles?.includes(tile.id)
        //     ? tiles
        //     : tiles?.concat(tile.id)
        // );
        return tile?.url?.split(',');
      },
    });
  }, [data, frame, setLoadingTile, token, visible]);

  return { dynamicTextureTileLayer };
};
