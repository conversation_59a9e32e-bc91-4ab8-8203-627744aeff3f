
import { deckInstanceRefAtom } from '@/components/DustDetail/atoms';
import { getDustSourceList } from '@/pages/DustEvent/services';
import { ensureArray } from '@/utils/dust';
import { PathStyleExtension } from '@deck.gl/extensions';
import { center } from '@turf/turf';
import { GeoJsonLayer, TextLayer } from 'deck.gl';
import { WebMercatorViewport } from 'deck.gl/typed';
import { useAtomValue } from 'jotai';
import { SetStateAction, useCallback, useMemo } from 'react';
import { useQuery } from 'react-query';

type Params = {
  isSandSourceLayerVisible: boolean;
  activeTimePoint: string | undefined;
  setSelectedSandSourceData: (
    args: SetStateAction<
      | {
          coordinate: [number, number];
          properties: any;
        }
      | undefined
    >
  ) => void;
};
export default function useSandSourceLayer({
  isSandSourceLayerVisible,
  setSelectedSandSourceData,
}: Params) {
  const { data } = useQuery(['/api/dust/source/list'], () =>
    getDustSourceList()
  );

  const sandSourceGeojson = useMemo(() => {
    const features = ensureArray(data).map((item) => {
      const geojson = JSON.parse(item.geom);
      const centerGeojson = center(geojson);
      let centroid = centerGeojson?.geometry?.coordinates;
      if (item.code === 113) {
        centroid[1] -= 2;
      }
      if (item.code === 111) {
        centroid[0] += 0.5;
        centroid[1] -= 1;
      }
      return {
        geometry: geojson,
        properties: { ...item, centroid },
        type: 'Feature',
      };
    });
    return {
      type: 'FeatureCollection',
      features,
    };
  }, [data]);
  const deckInstance = useAtomValue(deckInstanceRefAtom);

  const centerMapAtMousePointer = useCallback(
    (coordinate: [number, number]) => {
      try {
        const modalHeight = 460;
        const viewState = deckInstance?.current?.viewState;
        const viewport = new WebMercatorViewport(viewState);
        const [left, top] = viewport.project(coordinate);

        const [longitude, latitude] = viewport.unproject([
          left,
          top + modalHeight / 2,
        ]);
        deckInstance?.current?.setViewState((p) => ({
          ...p,
          longitude,
          latitude,
          transitionDuration: 500,
        }));
      } catch (e) {
        console.error(e);
      }
    },
    [deckInstance]
  );

  const labelList = useMemo(() => {
    return sandSourceGeojson.features.map((item) => {
      return item.properties;
    });
  }, [sandSourceGeojson.features]);

  const sandSourceLabelLayer = useMemo(
    () =>
      new TextLayer({
        id: 'sand-source-label-layer',
        data: labelList,
        getText: (d: any) => {
          return `${d.name ?? '-'}`;
        },
        characterSet: 'auto',
        // @ts-ignore
        getPosition: (d) => d.centroid,
        pickable: false,
        getSize: () => 12,
        sizeMinPixels: 12,
        sizeMaxPixels: 18,
        getColor: [255, 255, 255],
        getPixelOffset: () => [0, 0],
        fontFamily: 'Arial',
        fontWeight: 600,
        visible: isSandSourceLayerVisible,
        fontSettings: {
          sdf: true,
        },
        outlineColor: [255, 255, 255, 100],
      }),
    [isSandSourceLayerVisible, labelList]
  );

  const sandSourceGeojsonLayer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'sand-source-geojson-layer',
      lineWidthUnits: 'pixels',
      pickable: true,
      stroked: true,
      getLineWidth: 2,
      lineWidthMinPixels: 2,
      // @ts-ignore
      data: sandSourceGeojson || [],
      getLineColor: [23, 0, 227, 255],
      filled: true,
      getFillColor: [23, 91, 140, 102],
      updateTriggers: {},
      getDashArray: [5, 3],
      visible: isSandSourceLayerVisible,
      extensions: [new PathStyleExtension({ dash: true })],
      onClick: (d: any) => {
        if (d.object) {
          centerMapAtMousePointer(d.coordinate);
          setSelectedSandSourceData({
            properties: d.object.properties,
            coordinate: d.coordinate,
          });
        }
        console.log('🚀 ~ sandSourceGeojsonLayer ~ d:', d);
      },
    });
  }, [
    centerMapAtMousePointer,
    isSandSourceLayerVisible,
    sandSourceGeojson,
    setSelectedSandSourceData,
  ]);

  return [sandSourceGeojsonLayer, sandSourceLabelLayer];
}
