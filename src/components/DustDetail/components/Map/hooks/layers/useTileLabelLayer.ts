import { wmts } from '@/utils';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { TileLayer } from 'deck.gl/typed';
import { useMemo } from 'react';

type Params = {
  isAnnotationVisible: boolean;
};

/**
 * 基础瓦片图层
 */
const useTileLabelLayer = ({ isAnnotationVisible }: Params) => {
  const tileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: `tianditu-tile-label-layer`,
      data: wmts('cia'),
      visible: isAnnotationVisible,
      pickable: false,
      zoomOffset: 1,
    });
  }, [isAnnotationVisible]);
  return tileLayer;
};

export default useTileLabelLayer;
