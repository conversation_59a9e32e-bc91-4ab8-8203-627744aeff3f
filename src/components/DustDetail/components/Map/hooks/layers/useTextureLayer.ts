import { getTileUrl } from '@/components/DustDetail/utils/getTileUrl';
import useTextureToken from '@/hooks/dust/useTextureToken';
import {dataConfig,getColorRamps,getDecoder} from '@/utils/dust';
import { createTextureLayer } from '@/utils/dust/layers/createTextureLayers';
import { useMemo } from 'react';

type Params = {
  type: DUST.RemoteSensingType | 'DCOLOR' | 'TCOLOR' | undefined;
  activeTimePoint: string | undefined;
};

export default function useTextureLayer({ type, activeTimePoint }: Params) {
  const textureToken = useTextureToken();

  const decoder = useMemo(() => {
    return !!type ? getDecoder(type) : '';
  }, [type]);

  const colorRamp = useMemo(() => {
    return !!type ? getColorRamps(dataConfig[type]?.values) : {};
  }, [type]);

  const visible = useMemo(
    () => !!type && activeTimePoint && !['DCOLOR', 'TCOLOR'].includes(type),
    [activeTimePoint, type],
  );

  const tileUrl = useMemo(() => {
    return getTileUrl(
      {
        agg: 'none',
        time: activeTimePoint ? activeTimePoint : '',
        token: textureToken || '',
        type: type!,
      },
      true,
    );
  }, [activeTimePoint, textureToken, type]);

  const layer = useMemo(() => {
    if (!visible) {
      return null;
    }

    return createTextureLayer({
      id: `data-texture-layer-${type}`,
      dataUrl: tileUrl,
      decoder: decoder,
      colorRamp: colorRamp,
      visible: true,
      opacity: 1,
      smooth: true,
    });
  }, [colorRamp, decoder, tileUrl, type, visible]);

  return layer;
}
