import { ViewStateProps } from "@deck.gl/core/lib/deck";
import { useEffect, useRef, useState } from "react";

export const useDebouncedFilterLayerData = (viewState: ViewStateProps, data: any[],commonVisible:boolean) => {
  const [filteredData, setFillLayerData] = useState<any[]>([])
  function filterByDistanceCluster(
    data: { lon: number, lat: number, value: number }[],
    clusterRadius: number, // 聚类半径（度）
    minClusterSize: number = 2, // 最小聚类大小
    maxClusterSize: number = 10 // 最大聚类大小
  ) {
    const clusters: Array<{ points: any[], center: [number, number] }> = [];
    const processed = new Set<number>();

    // 1. 距离计算函数
    const calculateDistance = (p1: any, p2: any) => {
      const dx = p1.lon - p2.lon;
      const dy = p1.lat - p2.lat;
      return Math.sqrt(dx * dx + dy * dy);
    };

    // 2. 聚类算法
    data.forEach((point, index) => {
      if (processed.has(index)) return;

      const cluster = [point];
      processed.add(index);

      // 寻找距离内的所有点
      data.forEach((otherPoint, otherIndex) => {
        if (processed.has(otherIndex)) return;

        if (calculateDistance(point, otherPoint) <= clusterRadius) {
          cluster.push(otherPoint);
          processed.add(otherIndex);
        }
      });

      // 只保留符合大小要求的聚类
      if (cluster.length >= minClusterSize && cluster.length <= maxClusterSize) {
        const centerLon = cluster.reduce((sum, p) => sum + p.lon, 0) / cluster.length;
        const centerLat = cluster.reduce((sum, p) => sum + p.lat, 0) / cluster.length;
        clusters.push({
          points: cluster,
          center: [centerLon, centerLat]
        });
      }
    });

    // 3. 处理聚类内的点
    const result: any[] = [];

    // 先处理聚类内的点
    clusters.forEach(cluster => {
      const values = cluster.points.map(p => p.value);
      const maxValue = Math.max(...values);
      // const minValue = Math.min(...values);
      let maxCount = 0;
      cluster.points.forEach((point) => {
        let alpha = 50;
        if (point.value === maxValue) {
          maxCount++;
          if (maxCount === 1) {
            alpha = 255;
          } else {
            alpha = 50;
          }
        }

        result.push({
          ...point,
          color: [33, 33, 33, alpha],
          alpha,
          isClustered: true,
          clusterSize: cluster.points.length,
        });
      });
    });

    data.forEach((point) => {
      if (!result.some(p => p.lon === point.lon && p.lat === point.lat && p.value === point.value)) {
        result.push({
          ...point,
          color: [33, 33, 33, 255],
          alpha: 255,
          isClustered: false,
          clusterSize: 1,
        });
      }
    });
    return result;
  }

  const getClusterRadiusByZoom = (zoom: number) => {
    const ceilZoom = Math.ceil(zoom);
    console.log(ceilZoom, 'zoom')
    if (ceilZoom <= 8) return 0.1;      // 缩放级别低时，聚类半径大
    if (ceilZoom < 10) return 0.05;
    if (ceilZoom < 12) return 0.02;
    if (ceilZoom < 15) return 0.01;
    return 0.005;                       // 缩放级别高时，聚类半径小
  };

  const timerRef = useRef();
  useEffect(() => {
    if(!commonVisible) return
    if (timerRef.current) clearTimeout(timerRef.current);
    // @ts-ignore
    timerRef.current = setTimeout(() => {
      const clusterRadius = getClusterRadiusByZoom(viewState.zoom || 6);
      const result = filterByDistanceCluster(
        data,
        clusterRadius,
        2,
        30
      );
      setFillLayerData(result);
    }, 200);
    return () => clearTimeout(timerRef.current);
  }, [viewState, data, commonVisible]);


  return { filteredData }
};

export const useDebouncedFilterLayerLonglatData = (viewState: ViewStateProps, data: any[],commonVisible:boolean) => {
  const [fillLayerData, setFillLayerData] = useState<any[]>([])
  function filterByLngLatGrid(data: { lon: number, lat: number, value: number }[], cellSize = 0.05, maxPerCell = 1) {
    const grid = new Map();
    data.forEach((d: { lon: number, lat: number }) => {
      const key = `${Math.floor(d.lon / cellSize)}_${Math.floor(d.lat / cellSize)}`;
      if (!grid.has(key)) grid.set(key, []);
      const arr = grid.get(key);
      arr.push(d);
      // @ts-ignore
      arr.sort((a, b) => b.value - a.value);
      if (arr.length > maxPerCell) arr.length = maxPerCell;
    });
    return Array.from(grid.values()).flat();
  }

  const getMaxPerCellZoom = (zoom: number) => {
    const ceilZoom = Math.ceil(zoom);
    if (ceilZoom < 8) {
      return 1
    } else if (ceilZoom >= 8 && ceilZoom < 10) {
      return 1;
    } else if (ceilZoom >= 10 && ceilZoom < 12) {
      return 3;
    } else if (ceilZoom >= 12 && ceilZoom < 13) {
      return 4;
    } else if (ceilZoom >= 13 && ceilZoom < 15) {
      return 5;
    } else if (ceilZoom > 15) {
      return 2;
    }
    else {
      return 1;
    }
  }

  const getLngLatCellSizeZoom = (zoom: number) => {
    const ceilZoom = Math.ceil(zoom);
    console.log(ceilZoom)
    if (ceilZoom <= 8) {
      return 0.8
    } else if (ceilZoom > 8 && ceilZoom < 10) {
      return 0.6;
    } else if (ceilZoom >= 10 && ceilZoom < 12) {
      return 0.2;
    } else if (ceilZoom >= 12 && ceilZoom < 13) {
      return 0.04;
    } else if (ceilZoom >= 13 && ceilZoom < 15) {
      return 0.001;
    } else if (ceilZoom > 15) {
      return 0.001;
    }
    else {
      return 0.05;
    }
  }

  const timerRef = useRef();
  useEffect(() => {
    if(!commonVisible) return
    if (timerRef.current) clearTimeout(timerRef.current);
    // @ts-ignore
    timerRef.current = setTimeout(() => {
      const filtered = filterByLngLatGrid(
        data,
        getLngLatCellSizeZoom(viewState.zoom || 4),
        getMaxPerCellZoom(viewState.zoom || 4)
      );
      setFillLayerData(filtered);
    }, 200);
    return () => clearTimeout(timerRef.current);
  }, [viewState, data, commonVisible]);

  return { fillLayerNameData: fillLayerData }
};