import { wmts } from '@/utils';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { BitmapLayer, TileLayer } from 'deck.gl/typed';
import { useMemo } from 'react';

/**
 * 基础瓦片图层
 */
const useTileLayer = () => {
  const tileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: `tianditu-tile-layer`,
      data: wmts('img'),
    });
  }, []);
  return tileLayer;
};

export default useTileLayer;

type Params = {
  mapType: 'vec' | 'img' | 'ter';
};

export const useTileLayerHooks = ({ mapType }: Params) => {
  const tileLayer = useMemo(() => {
    if (mapType === 'vec') {
      return new TileLayer({
        ...tileLayerBaseConfig,
        id: `tianditu-tile-layer-${mapType}`,
        data: wmts(mapType),
      });
    } else {
      return new TileLayer({
        ...(tileLayerBaseConfig as any),
        minZoom: 0,
        maxZoom: 18,
        id: 'tianditu-tile-layer-' + mapType,
        data: wmts(mapType),
        // data: 'https://api.mapbox.com/styles/v1/saury1029/clmfyko0m019y01r98y7ghfca/tiles/256/{z}/{x}/{y}@2x?access_token=pk.eyJ1Ijoic2F1cnkxMDI5IiwiYSI6ImNrb3RvajY4cjBiaHkydnBmbHUwMWRsZHMifQ.byJtXxjso2A1kCiKWbeD1w',
        tileSize: 256,
        pickable: false,
        renderSubLayers: (props: any) => {
          const {
            bbox: { west, south, east, north },
          } = props.tile;
          return new BitmapLayer(props, {
            data: null,
            image: props.data,
            transparentColor: [255, 255, 255, 0],
            bounds: [west, south, east, north],
          });
        },
      });
    }
  }, [mapType]);
  return tileLayer;
};
