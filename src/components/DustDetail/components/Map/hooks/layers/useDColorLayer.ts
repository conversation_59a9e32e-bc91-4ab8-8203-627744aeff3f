import { getTileUrl } from '@/components/DustDetail/utils/getTileUrl';
import useTextureToken from '@/hooks/dust/useTextureToken';
import { ColorFilterBitmapLayer } from '@/layers/dust/ColorFilterBitmapLayer';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { EXTENT } from '@/utils/dust/config';
// @ts-ignore
import { TileLayer } from 'deck.gl';
import { useMemo } from 'react';

type Params = {
  isDColorLayerVisible: boolean;
  activeTimePoint: string | undefined;
};

export default function useDColorLayer({ isDColorLayerVisible, activeTimePoint }: Params) {
  const textureToken = useTextureToken('DCOLOR');

  const url = useMemo(() => {
    if (!activeTimePoint) {
      return null;
    }
    return getTileUrl(
      {
        token: textureToken || '',
        agg: 'none',
        type: 'DCOLOR',
        time: activeTimePoint,
      },
      true,
    );
  }, [activeTimePoint, textureToken]);

  const visible = useMemo(() => isDColorLayerVisible && !!url, [isDColorLayerVisible, url]);
  const layer = useMemo(() => {
    return new TileLayer({
      ...(tileLayerBaseConfig as any),
      id: `dMask-color-image-layer`,
      data: url,
      visible,
      extent: EXTENT,
      maxZoom: 7,
      minZoom: 0,
      renderSubLayers: (props: any) => {
        const {
          bbox: { west, south, east, north },
        } = props.tile;
        return new ColorFilterBitmapLayer(props, {
          data: undefined,
          image: props.data,
          bounds: [west, south, east, north],
          discardColor: [0, 0, 0],
        });
      },
    });
  }, [url, visible]);

  return layer;
}
