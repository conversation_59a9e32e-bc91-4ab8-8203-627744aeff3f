import { getTileUrl } from '@/components/DustDetail/utils/getTileUrl';
import useTextureToken from '@/hooks/dust/useTextureToken';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { EXTENT } from '@/utils/dust/config';
import { getTranslateImageData } from '@/utils/image';
// @ts-ignore
import { MaskExtension } from '@deck.gl/extensions';
import { TileLayer } from 'deck.gl';
import { useMemo } from 'react';

type Params = {
  isCloudLayerVisible: boolean;
  activeTimePoint: string | undefined;
};

export default function useCloudLayer({ isCloudLayerVisible, activeTimePoint }: Params) {
  const textureToken = useTextureToken();
  const tileUrl = useMemo(() => {
    if (!activeTimePoint) {
      return null;
    }
    return getTileUrl(
      {
        agg: 'none',
        time: activeTimePoint,
        token: textureToken || '',
        type: 'TCOLOR',
      },
      true,
    );
  }, [activeTimePoint, textureToken]);

  const visible = useMemo(() => isCloudLayerVisible && tileUrl, [isCloudLayerVisible, tileUrl]);

  const layer = useMemo(() => {
    if (!visible) {
      return null;
    }
    return new TileLayer({
      ...(tileLayerBaseConfig as any),
      id: `cloud-image-layer`,
      data: tileUrl,
      getTileData: (tile) => {
        // 兼容瓦片可能没数据的情况
        return getTranslateImageData(tile.url).then((val) => {
          return val || false;
        });
      },
      visible: visible,
      extensions: [new MaskExtension()],
      maskId: 'mask-area',
      extent: EXTENT,
      maxZoom: 7,
    });
  }, [tileUrl, visible]);

  return layer;
}
