import { userInfoAtom } from '@/atoms';
import { fetchGeojsonIncludeChild } from '@/services/global';
import { getLevelByRegionCode } from '@/utils';
import { request } from '@/utils/request';
import { GeoJsonLayer, TextLayer } from 'deck.gl/typed';
import { useAtomValue } from 'jotai';
import { stringify } from 'qs';
import { useMemo } from 'react';
import { useQuery } from 'react-query';

/** 获取指定行政区下属区域 */
export const getRegionList = (params: any) => {
  return request(`/api/region/list?${stringify(params)}`) as Promise<any>;
};

type Params = {
  regionCode: number[] | undefined;
};

// GeoJSON图层
export default function useGeojsonLayer({ regionCode }: Params) {
  const userInfo = useAtomValue(userInfoAtom);
  const provinceCode = useMemo(() => {
    if (userInfo?.regionCode) {
      return `${`${userInfo.regionCode}`.substring(0, 2)}0000`;
    }
    return '';
  }, [userInfo?.regionCode]);

  const { data: geojson } = useQuery(
    ['geojson-layer-data'],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(provinceCode),
        level: 1,
      }),
    {
      enabled: Boolean(provinceCode),
    },
  );

  const highlightedGeojson = useMemo(() => {
    const level = getLevelByRegionCode(Number(userInfo?.regionCode));
    if ((regionCode === userInfo?.regionCode || regionCode?.includes(userInfo?.regionCode ?? 0) || regionCode?.includes(0)) && level === 1) {
      return geojson?.features;
    }
    return geojson?.features?.filter((item) => regionCode?.includes(Number(item.properties.adcode))) ?? [];
  }, [geojson?.features, regionCode, userInfo?.regionCode]);

  // TODO: 据产品是否需要展示名称而定
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const regionLayer = useMemo(
    () =>
      new TextLayer({
        id: 'region-text-layer',
        data: [
          {
            adcode: 510100,
            name: '成都市',
            centroid: [103.93169327483824, 30.651207193588107],
          },
        ],
        getPosition: (d) => {
          return d?.centroid;
        },
        getText: (d) => {
          return d.name;
        },
        pickable: true,
        onClick: () => {},
      }),
    [],
  );

  const geojsonLayer = useMemo(
    () =>
      new GeoJsonLayer({
        id: 'geojson-layer',
        pickable: true,
        stroked: true,
        lineWidthUnits: 'pixels',
        getLineWidth: 2,
        lineWidthMinPixels: 2,
        // @ts-ignore
        data: geojson || [],
        getLineColor: [88, 88, 88, 255],
        filled: false,
        getFillColor: () => [0, 0, 0, 0],
        updateTriggers: {
          getLineColor: [regionCode],
        },
      }),
    [geojson, regionCode],
  );

  const highlightGeojsonLayer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'geojson-layer-highlight',
      lineWidthUnits: 'pixels',
      pickable: true,
      stroked: true,
      getLineWidth: 2,
      lineWidthMinPixels: 2,
      // @ts-ignore
      data: highlightedGeojson || [],
      getLineColor: () => {
        return [255, 255, 255, 255];
      },
      filled: false,
      getFillColor: () => [0, 0, 0, 0],
      updateTriggers: {
        getLineColor: [regionCode],
      },
    });
  }, [highlightedGeojson, regionCode]);

  return [geojsonLayer, highlightGeojsonLayer];
}
