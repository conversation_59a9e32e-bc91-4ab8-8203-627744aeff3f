
import {
  SelectedSandSourceData,
  loadingPointsAtom,
  viewStateAtom,
} from '@/components/DustDetail/atoms';
import { ensureArray, ensureNumber } from '@/utils/dust';
import { stationPollutionValuesAndColors } from '@/utils/dust/stationLegendConfig';
// import { CollisionFilterExtension } from '@deck.gl/extensions';
import {
  IconLayer,
  Position,
  RGBAColor,
  ScatterplotLayer,
  TextLayer,
} from 'deck.gl';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { useAtomValue, useSetAtom } from 'jotai';
import { getStationMonitorDataList } from '@/components/DustDetail/services';
import { useDebouncedFilterLayerData, useDebouncedFilterLayerLonglatData } from './useStationLayerFilterDate';
import { getAqiList } from '@/pages/Overview/services';

type Params = {
  isStationLayerVisible: boolean;
  activeTimePoint: string | undefined;
  type: DUST.DataTypes | 'AQI' | undefined;
  zoomScaleGreater100KM: boolean;
  regionCode: number | undefined;
  timePoints: string[];
};

export default function useStationLayer({
  isStationLayerVisible,
  activeTimePoint,
  type,
  zoomScaleGreater100KM,
  regionCode,
  timePoints,
  setSelectedStationData,
}: Params) {
  const [pointData, setPointData] = useState<
    Record<string, APIDUST.TStationDetailModel[]>
  >({});
  const loadingPoints = useSetAtom(loadingPointsAtom);
  const lowercaseType = useMemo(() => type?.toLowerCase() || '', [type]);
  const viewState = useAtomValue(viewStateAtom);
  const pollutionTypeForLegend = useMemo(() => {
    const pollutionType = lowercaseType === 'no2tcd' ? 'no2' : lowercaseType;
    return ['o3', 'co', 'no2', 'so2'].includes(pollutionType)
      ? `${pollutionType}-hour`
      : pollutionType;
  }, [lowercaseType]);

  const legendInfo = useMemo(() => {
    return stationPollutionValuesAndColors?.[pollutionTypeForLegend];
  }, [pollutionTypeForLegend]);

  const values = useMemo(() => ensureArray(legendInfo?.values), [
    legendInfo?.values,
  ]);

  const commonVisible = useMemo(
    () => zoomScaleGreater100KM && isStationLayerVisible,
    [isStationLayerVisible, zoomScaleGreater100KM]

  );
  // 站点图层走一张图里的数据
  const { mutate } = useMutation((dataTime: string) =>
    // getStationMonitorDataList({ regionCode, dataTime })
    getAqiList({ type: 'nation', regionCode:150000, date: dataTime })
  );
  const loadMore = useCallback(
    async (frame, loadList) => {
      if (loadList[frame]) {
        const d = await new Promise((inject) => {
          mutate(loadList[frame], {
            onSuccess: (d) => {
              // 国控站数据
              const nationData = d.map((item)=>{
                return {
                  ...item,
                  stationName: `${item.region}${item.placeName}`,
                }
              })?.filter((item) => item.type === 'nation')
              inject(nationData);
            },
          });
        });
        // setPointData((prev) => ({ ...prev, [loadList[frame].date]: d }));
        const next = await loadMore(frame + 1, loadList);
        return {
          [loadList[frame]]: d,
          ...next,
        };
      } else {
        return {};
      }
    },
    [mutate]
  );
  useEffect(() => {
    setPointData({});
  }, [regionCode]);
  useEffect(() => {
    if (!!regionCode && isStationLayerVisible) {
      let hasPoint = false;
      for (let key in pointData) {
        if (activeTimePoint === key) {
          hasPoint = true;
          break;
        }
      }
      const frame = timePoints.findIndex((t) => t === activeTimePoint);
      const loadList = timePoints
        .concat()
        .slice(frame, timePoints.length)
        .concat(timePoints.concat().slice(0, frame));
      if (hasPoint) {
        const loadingPoint = loadList.find((t) => !pointData[t]);
        if (loadingPoint) {
          mutate(loadingPoint, {
            onSuccess: (d) => {
              // .filter((item) => item.type === 'nation')
              setPointData((prev) => ({ ...prev, [loadingPoint]: d }));
            },
          });
        }
      } else {
      
        loadingPoints(true);
        loadMore(
          0,
          loadList.length > 10 ? loadList.concat().slice(0, 10) : loadList
        ).then((d) => {
          loadingPoints(false);
          setPointData((prev) => ({ ...prev, ...d }));
        });
      }
    }
  }, [
    activeTimePoint,
    isStationLayerVisible,
    loadMore,
    loadingPoints,
    mutate,
    pointData,
    regionCode,
    timePoints,
  ]);
  const handleHover = useCallback(
    (info: any) => {
      if (info?.object) {
        setSelectedStationData(info.object as SelectedSandSourceData);
      } else {
        setSelectedStationData(undefined);
      }
    },
    [setSelectedStationData]
  );

  const findColor = useCallback(
    (val: number | null | undefined): RGBAColor => {
      if (val === null || val === undefined) {
        return [169, 169, 169];
      }

      const find = values.find((e) => val >= ensureNumber(e?.min));
      if (find) {
        const color = find?.color;
        if (/#.{6}/.test(color)) {
          const r = color.slice(1, 3);
          const g = color.slice(3, 5);
          const b = color.slice(5, 7);
          return [parseInt(r, 16), parseInt(g, 16), parseInt(b, 16)];
        }
        const colorStr = String(color).replace(/rgb\((.*?)\)/, '$1');
        const t = colorStr.split(',');
        return t.map((e) => ensureNumber(e)) as RGBAColor;
      } else {
        return [0, 255, 0, 255];
      }
    },
    [values]
  );

  // const params = useMemo(() => {
  //   return {
  //     dataTime: activeTimePoint!,
  //     regionCode: regionCode!,
  //   };
  // }, [activeTimePoint, regionCode]);

  // const { data } = useQuery(
  //   ['/api/station/monitor/data/list', params],
  //   () => getStationMonitorDataList(params),
  //   {
  //     enabled: !!activeTimePoint && !!regionCode && isStationLayerVisible,
  //     keepPreviousData: true,
  //   }
  // );

  const layerData = useMemo(() => {
    return (
      (activeTimePoint &&
        ensureArray(pointData[activeTimePoint]).map((item) => {
          // @ts-ignore
          const value = item[lowercaseType as keyof APIV2.TStationDetailModel];
          return {
            ...item,
            value,
            // @ts-ignore
            color: findColor(value),
          };
        })) ||
      []
    );
  }, [activeTimePoint, findColor, lowercaseType, pointData]);

  const { filteredData } = useDebouncedFilterLayerData(viewState, layerData, commonVisible);
  const { fillLayerNameData } = useDebouncedFilterLayerLonglatData(viewState, layerData, commonVisible);
  const bg = useMemo(
    () =>
      new IconLayer({
        id: 'station-icon-layer',
        data: layerData,
        getIcon: () => {
          return {
            url: `/assets/images/station.png`,
            width: 68 * 2,
            height: 78 * 2,
            mask: true,
          };
        },
        getColor: (d: any) => d.color,
        getPixelOffset: () => [0, 0],
        getPosition: (d: any) => [d.lon, d.lat],
        onHover(info) {
          handleHover(info);
        },
        pickable: true,
        getSize: 28,
        sizeScale: 1,
        visible: commonVisible,
        updateTriggers: {
          getColor: [layerData],
        },
      }),
    [commonVisible, handleHover, layerData]
  );

  const bgBorder = useMemo(
    () =>
      new IconLayer({
        id: 'station-icon-border-layer',
        data: layerData,
        getIcon: () => {
          return {
            url: `/assets/images/stationBorder.png`,
            width: 68 * 2,
            height: 78 * 2,
          };
        },
        getPixelOffset: () => [0, 0],
        // @ts-ignore
        getPosition: (d: { lon: any; lat: any }) => [d.lon, d.lat] as Position,
        pickable: true,
        getSize: 29,
        sizeScale: 1,
        visible: commonVisible,
        updateTriggers: {
          getColor: [layerData],
        },
      }),
    [commonVisible, layerData]
  );

  const scatter = useMemo(
    () =>
      new ScatterplotLayer({
        id: 'station-scatterplot-layer',
        data: layerData,
        pickable: true,
        opacity: 1,
        stroked: true,
        filled: true,
        radiusScale: 7,
        radiusMinPixels: 7,
        radiusMaxPixels: 100,
        lineWidthMinPixels: 2,
        visible: isStationLayerVisible && !zoomScaleGreater100KM,
        getLineColor: () => [255, 255, 255],
        getRadius: 8,
        getFillColor: (d: any) => d.color,
        getPixelOffset: () => [0, 0],
        getPosition: (d: any) => [d.lon, d.lat] as Position,
        onHover(info) {
          handleHover(info);
        },
      }),
    [layerData, isStationLayerVisible, zoomScaleGreater100KM, handleHover]
  );

  const text = useMemo(
    () =>
      new TextLayer({
        id: 'station-value-text-layer',
        data: filteredData,
        // @ts-ignore
        getColor: (d) => d.color,
        getText: (d) => {
          // @ts-ignore
          return `${d?.value ?? '-'}`;
        },
        // @ts-ignore
        getPosition: (d: { lon: any; lat: any }) => [d.lon, d.lat],
        pickable: false,
        getSize: () => 12,
        sizeMinPixels: 12,
        sizeMaxPixels: 18,
        fontFamily: 'Arial',
        fontWeight: 500,
        visible: commonVisible,
        collisionEnabled: true,
        collisionGroup: '222',
        collisionTestProps: { sizeScale: 4 },
        getCollisionPriority: (d: any) => d.value,
        // 一会看看这里有问题 8.8.20 版本没有这个扩展，暂时注释掉
        // extensions: [new CollisionFilterExtension()],

      }),
    [commonVisible, filteredData]
  );

  const name = useMemo(() => {
    return new TextLayer({
      id: `station-text-layer`,
      data: fillLayerNameData,
      // @ts-ignore
      // getColor: (d) => d.color,
      // @ts-ignore
      getPosition: (d: { lon: any; lat: any }) => [d.lon, d.lat],
      // @ts-ignore
      getText: (d) => d.stationName,
      getSize: () => 12,
      characterSet: 'auto',
      getPixelOffset: () => [0, 25],
      sizeMinPixels: 12,
      sizeMaxPixels: 18,
      getColor: (d) => {
        return [255, 255, 255]
      },
      outlineWidth: 5,
      outlineColor: [33, 33, 33],
      fontWeight: 600,
      fontSettings: {
        sdf: true,
      },
      visible: commonVisible,
      // CollisionFilterExtension props
      collisionGroup: '222',
      collisionEnabled: true,
      collisionTestProps: { sizeScale: 2 },
      getCollisionPriority: (d: any) => d.value,
      // 一会看看这里有问题 8.8.20 版本没有这个扩展，暂时注释掉
      // extensions: [new CollisionFilterExtension()],
    });
  }, [commonVisible, fillLayerNameData]);

  const layers = useMemo(() => {
    return commonVisible ? [bg, bgBorder, text, name, scatter] : [scatter];
  }, [bg, bgBorder, commonVisible, name, scatter, text]);

  return layers;
}
