import { deckInstanceRefAtom, isSphereOfInfluenceAtom } from '@/components/DustDetail/atoms';
import { useRouter } from '@/hooks';
import { getDustEventInfo } from '@/pages/DustEvent/services';
import { calcCenterAndZoomByGeojson } from '@/utils/dust/geojson/calcCenterAndZoomByGeojson';
import { PathStyleExtension } from '@deck.gl/extensions';
import { bbox } from '@turf/turf';
import { FlyToInterpolator, GeoJsonLayer, IconLayer } from 'deck.gl';
import { useAtomValue } from 'jotai';
import { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';

const useSphereOfInfluenceLayer = () => {
  const isSphereOfInfluence = useAtomValue(isSphereOfInfluenceAtom);
  const { query } = useRouter();
  const { data, remove } = useQuery(['getDustEventInfo', query.id], () => getDustEventInfo({ dustEventId: query.id }), {
    enabled: Boolean(query.id),
  });
  useEffect(() => {
    return () => {
      remove();
    };
  }, [remove]);
  const deckInstanceRef = useAtomValue(deckInstanceRefAtom);
  // 自动聚焦所选行政区域
  useEffect(() => {
    const width = deckInstanceRef?.current?.deckGLInstance?.width || 0;
    const height = deckInstanceRef?.current?.deckGLInstance?.height || 0;

    if (data?.geoJson && width > 0 && height > 0) {
      const [minLon, minLat, maxLon, maxLat] = bbox(JSON.parse(data?.geoJson));

      const centerAndZoom = calcCenterAndZoomByGeojson(minLon, maxLon, minLat, maxLat, width, height, 160);
      deckInstanceRef?.current?.setViewState({
        longitude: centerAndZoom?.longitude,
        latitude: centerAndZoom?.latitude,
        zoom: centerAndZoom?.zoom,
        transitionInterpolator: new FlyToInterpolator(),
        transitionDuration: 500,
      });
    }
  }, [data, deckInstanceRef, query.id]);
  const sphereOfInfluence = useMemo(() => {
    return new GeoJsonLayer({
      id: 'sphere-of-influence-layer',
      lineWidthUnits: 'pixels',
      pickable: false,
      stroked: true,
      getLineWidth: 2,
      lineWidthMinPixels: 2,
      // @ts-ignore
      data: (data?.geoJson && JSON.parse(data.geoJson)) || [],
      getLineColor: [172, 0, 181],
      filled: true,
      getFillColor: [162, 52, 96, 51],
      updateTriggers: {},
      getDashArray: [5, 3],
      visible: isSphereOfInfluence,
      extensions: [new PathStyleExtension({ dash: true })],
    });
  }, [data, isSphereOfInfluence]);
  const startPoint = useMemo(
    () =>
      new IconLayer({
        id: 'sphere-of-influence-point-layer',
        data: data?.sendSitePoints,
        getIcon: () => {
          return {
            // eslint-disable-next-line max-len
            url: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAsCAYAAADvjwGMAAAAAXNSR0IArs4c6QAABIFJREFUWEfNmFtsFGUUx/9ntrvF0t0Vb9U+qJEHeVD7YCoakxUvAdPZ4A2hIlbxVjURowYTwwsxMSZqNHgpVORiKVAttSpdagkxyMsGCQ99ERLjgyQWaq1tp1u6s5c55szuTGeHNmkn0y7z+M13/ud3/ud8+2WWstUNMVaoi8HJSu3IapTxoUwkvo3Bm4SBoNwTBP274DyUH6axxAhlIuqXDLy24AClCQ0wvUvpiNpCwKtlhgGBx8SZFr4sYJCkdKRhO4FeKaczBIwYoOWUDqs7iNBcRpi8AqhBLdFHmbC6g10wSqwOgY+akf/0EIyOX+aVkxmbF40nPjZPczqsthLhZWdGJwwGhhFofQtUe3VhS2oSube3Q7n3NtBDdyLf/AmME/3mq8CWDVBW1SMbe2O2BbRXaolnrM3iTCs7YCo+3wSlaaUtxmfPIbt86uQH+z5Efs/PpmNm8rUrkP/gAAJvrgH/dhZUt3RWMAQ6FdRSMcLxtBPmKya8NJMz7jYJjJH8Hcq6+8HHTiP3+meQAnBTDfjkGdMZ7v8TdNeykiJKrGKcD+WoniZ7/nauUzoa30nML8qi2R5nSwDwwDCQyYJuvr6kTQIZPLENRt8pKLE7TLdoaa3dJgF0t9EUYGQU0IrgeE/S3UvKROM7uQjjfFnx0/vAX4Nm5Ze0xjEX0iq6/RbkGt8rmRlnvCvpuZA2cauzPXab0lH1a2K8YC0ojQ8gsPFhsJ6Fcl9dwZmhUeRbfrTnxEruTmgNsLRJ2pZbvWXaQSagPeQYXBtGj6q7wHjeWrAFR1NTzpxsAS270RY22o5e4pj1UgCpZsnM81LcqDA2B4tHemqAo+oudsCImDgij5V0uvIsB7Or3jFfW6fQ+LV/RkdcOvaPncOZht1g2mgtWPOhrH/QhnLDyHHnwRHTOXPwm1bC/RMwbX9ci3INBOUa0Hr+kFcywLuZ2YaZjYjPe86EKoy76b9eTW7tPQw853OCOcpRT0jreYT0iLoXwLNzjJ6H7bxVnNnLlwEMAaOkRxq+AahpHkqdkyQBSdIj8TaArZuznRgTc1LxYTMpGM5kc1/I10EbF2FyuVzt4ot9533Q9yQhA7wPwAaJzub5huqJIxc8KfkQJAO8j4swISNUQ6nuf3zQ9SQhMO0MPC3RIcO4jlK9Q56UfAiSAd4P8HoThpVrafzwwn9RFguRAd7PNkzoGhrvHvahSE8SMsAHADxlOkO4Sr55PSn5ECQwBwE0mjBKcAmN/jDqg64nCRngg2zBBPQraeTYmCclH4LkOugAaJ3pTIURlavcB11PEjLAHQwuwFRWhWmoM+VJyYcgadO3DKw1Ya7IVtPg0QW/m6w6SA+r34HwpAlTrSymgcMXfSjSk0QpjFZVReic9KTkQ5DAdIKwxnRGMxYRenUfdD1JkB6OHwLxEwWYqkpCZ8aTkg9BLpgLIcLprA+6niSkTV0gPF5wZiJIOJ7zpORDEOlRtQtswdQHCFsNH3Q9SQjM92A8VnAmoZD8aVGmh/RoQzeYHpX8lVqCysRhpv0fA8v0YZEk7KoAAAAASUVORK5CYII=`,
            width: 35,
            height: 44,
          };
        },
        getPixelOffset: () => [17.5, -22],
        getPosition: (d: any) => [d.lon, d.lat],
        pickable: false,
        getSize: 44,
        sizeScale: 1,
        visible: isSphereOfInfluence,
      }),
    [data?.sendSitePoints, isSphereOfInfluence],
  );
  return [sphereOfInfluence, startPoint];
};
export default useSphereOfInfluenceLayer;
