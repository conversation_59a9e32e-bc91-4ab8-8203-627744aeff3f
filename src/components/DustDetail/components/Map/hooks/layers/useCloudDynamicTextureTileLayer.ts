import { useSetAtom } from 'jotai';
import { TimelineListItem } from '../../../TimelineWithRegion/Timeline/TimelineItem';
import { loadingCloundTileAtom } from '@/components/DustDetail/atoms';
import useTextureToken from '@/hooks/dust/useTextureToken';
import { useMemo } from 'react';
import { getTileUrl } from '@/components/DustDetail/utils/getTileUrl';
import DynamicTileLayer from '@/layers/dust/dynamic-texture-layer/dynamic-tile-layer';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { EXTENT } from '@/utils/dust/config';
import { tweenFrameCount } from '@/components/DustDetail/config';
import DynamicTextureLayer from '@/layers/dust/dynamic-texture-layer';
// @ts-ignore
import { MaskExtension } from '@deck.gl/extensions';

export const useCloudDynamicTextureTileLayer = ({
  timePoints = [],
  frame = 0,
  visible,
}: {
  frame: number;
  timePoints: TimelineListItem[];
  visible: boolean;
}) => {
  const setLoadingTile = useSetAtom(loadingCloundTileAtom);
  // const [loading, setLoading] = useState(false);
  const token = useTextureToken('TCOLOR');
  const fps = 2;

  const timeList = useMemo(() => {
    return timePoints.map((item) => item.date);
  }, [timePoints]);

  const data = timeList
    .map((time) =>
      getTileUrl(
        {
          agg: 'none',
          time,
          token,
          type: 'TCOLOR',
        },
        true
      )
    )
    .join(',');
  const cloudDynamicTextureTileLayer = useMemo(() => {
    if (!token) return;
    return new DynamicTileLayer({
      ...tileLayerBaseConfig,
      id: `dynamic-texture-tile-TCOLOR`,
      data,
      visible,
      extent: EXTENT,
      maxZoom: 3,
      minZoom: 3,
      tileSize: 256,
      colorFormat: 'RGBA',
      pickable: false,
      min: 0,
      max: 100,
      fps: fps,
      frame: frame,
      normalFrame: tweenFrameCount,
      extensions: [new MaskExtension()],
      // 云图不加遮罩
      // maskId: 'mask-area',
      onReady: () => {
        setLoadingTile(false);
      },
      onLoading: () => {
        setLoadingTile(true);
      },
      // 色带更改后，重新渲染图层
      shouldUpdate: (prevProps: any, nextProps: any) => {
        return (
          prevProps.colorRamp !== nextProps.colorRamp ||
          prevProps.dataUrl !== nextProps.dataUrl ||
          prevProps.visible !== nextProps.visible ||
          prevProps.min !== nextProps.min ||
          prevProps.max !== nextProps.max ||
          prevProps.filters !== nextProps.filters ||
          prevProps.filtersChannel !== nextProps.filtersChannel
        );
      },
      // @ts-ignore
      renderSubLayers: (props) => {
        const {
          bbox: { west, south, east, north },
          //   index: { x, y, z },
        } = props.tile;
        return props?.data
          ? new DynamicTextureLayer(props, {
            pickable: true,
            data: null,
            imageList: props.data,
            bounds: [west, south, east, north],
            // smooth: true,
          })
          : null;
      },
      updateTriggers: {
        renderSubLayers: [frame, fps],
      },
      //@ts-ignore
      getTileData: (tile) => {
        // setLoadingTile((tiles) =>
        //   tiles === undefined
        //     ? [tile.id]
        //     : tiles?.includes(tile.id)
        //     ? tiles
        //     : tiles?.concat(tile.id)
        // );
        return tile?.url?.split(',');
      },
    });
  }, [data, frame, setLoadingTile, token, visible]);

  return { cloudDynamicTextureTileLayer };
};
