import {
  deckInstanceRefAtom,
  regionCodeAtom,
} from '@/components/DustDetail/atoms';
import { DEFAULT_VIEW_STATE } from '@/components/DustDetail/config';
import geojson from '@/utils/dust/geojson/10.json';
import { calcCenterAndZoomByGeojson } from '@/utils/dust/geojson/calcCenterAndZoomByGeojson';
import { bbox } from '@turf/turf';
import { FlyToInterpolator } from 'deck.gl';
import { useAtomValue } from 'jotai';
import { useEffect, useMemo } from 'react';

export default function useFitBoundsToGeojson({
  mapViewWith,
  mapViewHeight,
}: {
  mapViewWith: number;
  mapViewHeight: number;
}) {
  const regionCode = useAtomValue(regionCodeAtom);
  const deckInstanceRef = useAtomValue(deckInstanceRefAtom);

  const highlightedGeojson = useMemo(() => {
    return geojson.features.find(
      (item) => item.properties.adcode === regionCode,
    );
  }, [regionCode]);

  // 自动聚焦所选行政区域
  useEffect(() => {
    if (highlightedGeojson && mapViewWith > 0 && mapViewHeight > 0) {
      const [minLon, minLat, maxLon, maxLat] = bbox(highlightedGeojson);

      const centerAndZoom = calcCenterAndZoomByGeojson(
        minLon,
        maxLon,
        minLat,
        maxLat,
        mapViewWith,
        mapViewHeight,
        120,
      );

      deckInstanceRef?.current?.setViewState({
        longitude: centerAndZoom?.longitude,
        latitude: centerAndZoom?.latitude,
        zoom: centerAndZoom?.zoom,
        transitionInterpolator: new FlyToInterpolator(),
        transitionDuration: 500,
      });
    }
  }, [deckInstanceRef, highlightedGeojson, mapViewHeight, mapViewWith]);

  useEffect(() => {
    if (regionCode === 100000) {
      deckInstanceRef?.current?.setViewState(DEFAULT_VIEW_STATE);
    }
  }, [deckInstanceRef, regionCode]);
}
