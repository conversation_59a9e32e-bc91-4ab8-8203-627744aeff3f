import {
  activeTime<PERSON><PERSON><PERSON><PERSON>,
  activeTimePoint<PERSON>ourly<PERSON><PERSON>,
  frameAtom,
  isAnnotationVisibleAtom,
  isCloudLayerVisibleAtom,
  isDColorLayerVisibleAtom,
  isDustRangeLayerVisibleAtom,
  isSandSourceLayerVisibleAtom,
  isSelectTheLastPointOfTimelineAtom,
  isStationLayerVisibleAtom,
  isWindLayerVisibleAtom,
  loadingRangeAtom,
  mapTypeAtom,
  selectedSandSourceDataAtom,
  selectedStationDataAtom,
  startPollAtom,
  stationTypeAtom,
  textureTypeAtom,
  timePointsAtom,
  zoomScaleGreater100KMAtom,
} from '@/components/DustDetail/atoms';
import { useRouter } from '@/hooks';
import { getDustEventInfo } from '@/pages/DustEvent/services';
import { useAtomValue, useSetAtom } from 'jotai';
import moment from 'moment';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { useCloudDynamicTextureTileLayer } from './layers/useCloudDynamicTextureTileLayer';
import useCloudLayer from './layers/useCloudLayer';
import useDColorLayer from './layers/useDColorLayer';
import useDustRangeLayer from './layers/useDustRangeLayer';
import { useDynamicTextureTileLayer } from './layers/useDynamicTextureTileLayer';
import useGeojsonLayer from './layers/useGeojsonLayer';
import useMaskGeojsonLayer from './layers/useMaskGeojsonLayer';
import useSandSourceLayer from './layers/useSandSourceLayer';
import useSphereOfInfluenceLayer from './layers/useSphereOfInfluenceLayer';
import useStationLayer from './layers/useStationLayer';
import useTextureLayer from './layers/useTextureLayer';
import useTileLabelLayer from './layers/useTileLabelLayer';
import { useTileLayerHooks } from './layers/useTileLayer';
import useWindLayer from './layers/useWindLayer';

export default function useLayers() {
  const isCloudLayerVisible = useAtomValue(isCloudLayerVisibleAtom);
  const isDColorLayerVisible = useAtomValue(isDColorLayerVisibleAtom);
  const activeTimePoint = useAtomValue(activeTimePointAtom);
  const isDustRangeLayerVisible = useAtomValue(isDustRangeLayerVisibleAtom);
  const isSandSourceLayerVisible = useAtomValue(isSandSourceLayerVisibleAtom);
  const setLoadingRange = useSetAtom(loadingRangeAtom);
  const setSelectedSandSourceData = useSetAtom(selectedSandSourceDataAtom);
  const stationType = useAtomValue(stationTypeAtom);
  const zoomScaleGreater100KM = useAtomValue(zoomScaleGreater100KMAtom);
  const isStationLayerVisible = useAtomValue(isStationLayerVisibleAtom);
  const isAnnotationVisible = useAtomValue(isAnnotationVisibleAtom);
  const mapType = useAtomValue(mapTypeAtom);
  const isWindLayerVisible = useAtomValue(isWindLayerVisibleAtom);
  const activeTimePointHourly = useAtomValue(activeTimePointHourlyAtom);
  const setSelectedStationData = useSetAtom(selectedStationDataAtom);
  const isSelectTheLastPointOfTimeline = useAtomValue(isSelectTheLastPointOfTimelineAtom);
  const frame = useAtomValue(frameAtom);
  const timePoints = useAtomValue(timePointsAtom);
  const startPoll = useAtomValue(startPollAtom);

  const textureType = useAtomValue(textureTypeAtom);

  const { query } = useRouter();
  const { data } = useQuery(['getDustEventInfo', query.id], () => getDustEventInfo({ dustEventId: query.id }), { enabled: Boolean(query.id) });
  const maskGeojsonLayer = useMaskGeojsonLayer();
  const tileLayer = useTileLayerHooks({ mapType });
  const textureLayer = useTextureLayer({
    type: textureType,
    activeTimePoint: activeTimePointHourly,
  });
  const dMaskLayer = useDColorLayer({
    isDColorLayerVisible: isDColorLayerVisible && !startPoll,
    activeTimePoint,
  });

  const cloudLayer = useCloudLayer({
    isCloudLayerVisible: isCloudLayerVisible && !startPoll,
    activeTimePoint,
  });
  const windLayer = useWindLayer({
    isWindLayerVisible: isWindLayerVisible && !startPoll,
    activeTimePoint: activeTimePointHourly,
  });
  const hourTimeList = useMemo(
    () => Array.from(new Set(timePoints?.map((point) => moment(point.date).set('minute', 0).format('YYYY/MM/DD HH:mm:ss')))),
    [timePoints],
  );
  const stationLayer = useStationLayer({
    type: stationType,
    regionCode: 100000,
    isStationLayerVisible,
    activeTimePoint: activeTimePointHourly,
    timePoints: hourTimeList,
    zoomScaleGreater100KM,
    setSelectedStationData,
  });
  const tileLabelLayer = useTileLabelLayer({ isAnnotationVisible });
  const geojsonLayer = useGeojsonLayer({
    regionCode: data?.regions.map((region) => region.key),
  });
  const timeList = useMemo(() => timePoints?.map((t) => t.date) || [], [timePoints]);
  const dustRangeLayer = useDustRangeLayer({
    isDustRangeLayerVisible,
    activeTimePoint,
    dustEventId: query.id,
    isSelectTheLastPointOfTimeline,
    timeList,
    setRangeLoading: setLoadingRange,
  });
  const sandSourceLayer = useSandSourceLayer({
    isSandSourceLayerVisible,
    setSelectedSandSourceData,
    activeTimePoint,
  });

  const { dynamicTextureTileLayer } = useDynamicTextureTileLayer({
    timePoints,
    frame,
    visible: isDColorLayerVisible,
  });
  const { cloudDynamicTextureTileLayer } = useCloudDynamicTextureTileLayer({
    timePoints,
    frame,
    visible: isCloudLayerVisible,
  });
  const [sphereOfInfluence] = useSphereOfInfluenceLayer();

  const layers = useMemo(() => {
    return [
      maskGeojsonLayer,
      tileLayer,
      cloudDynamicTextureTileLayer,
      cloudLayer,
      textureLayer,
      dynamicTextureTileLayer,
      dMaskLayer,
      geojsonLayer[0],
      tileLabelLayer,
      geojsonLayer[1],
      sandSourceLayer,
      ...dustRangeLayer,
      sphereOfInfluence,
      ...stationLayer,
      ...windLayer,
    ];
  }, [
    maskGeojsonLayer,
    tileLayer,
    cloudDynamicTextureTileLayer,
    cloudLayer,
    textureLayer,
    dynamicTextureTileLayer,
    dMaskLayer,
    geojsonLayer,
    tileLabelLayer,
    sandSourceLayer,
    dustRangeLayer,
    sphereOfInfluence,
    stationLayer,
    windLayer,
  ]);

  return layers;
}
