import LazyDeckGL, { LazyDeckGLRef } from '@/components/LazyDeckGL';
import { TIANDITU_KEY } from '@/utils/dust/config';
import { MapView } from 'deck.gl';
import { useSetAtom } from 'jotai';
import { memo, useEffect, useRef } from 'react';
import {
  coordinateAtom,
  deckInstanceRefAtom,
  viewStateAtom,
  zoomScaleGreater100KMAtom,
} from '../../atoms';
import { DEFAULT_VIEW_STATE } from '../../config';
import PlottingScale from '../Bottombar/PlottingScale';
import SandRangeOverlay from '../Overlay/SandRangeOverlay';
import SandSourceOverlay from '../Overlay/SandSourceOverlay';
// import useFitBoundsToGeojson from './hooks/useFitBoundsToGeojson';
import useLayers from './hooks/useLayers';
import StationInfoOverlay from '../Overlay/StationInfoOverlay';

function Map() {
  const mapContainerRef = useRef<HTMLDivElement | null>(null);
  const deckRef = useRef<LazyDeckGLRef | null>(null);
  const setZoomScaleGreater100KM = useSetAtom(zoomScaleGreater100KMAtom);
  const setViewState = useSetAtom(viewStateAtom);
  const setDeckGL = useSetAtom(deckInstanceRefAtom);
  const setCoordinate = useSetAtom(coordinateAtom);

  const layers = useLayers();

  useEffect(() => {
    setDeckGL(deckRef);
  }, [deckRef, setDeckGL]);

  // useFitBoundsToGeojson({
  //   mapViewWith: mapContainerRef.current?.clientWidth || 0,
  //   mapViewHeight: mapContainerRef.current?.clientHeight || 0,
  // });

  return (
    <div
      className="h-full relative"
      id="map-container"
      ref={mapContainerRef}
      onMouseLeave={() => { }}
    >
      <LazyDeckGL
        containerCfg={{
          className: 'w-full h-full',
        }}
        ref={deckRef}
        deckCfg={{
          controller: true,
          // @ts-ignore
          views: [new MapView({ repeat: true })],
          useDevicePixels: true,
          glOptions: { preserveDrawingBuffer: true },
          // @ts-ignore
          layers,
          initialViewState: { ...DEFAULT_VIEW_STATE, zoom: 7 },
          onViewStateChange: (info: any) => {
            setZoomScaleGreater100KM(info.viewState.zoom > 7);
            if (!info.interactionState.isDragging) {
              setViewState(info.viewState)
            }
          },
          baseMap: {
            provider: 'tianditu',
            type: 'img',
            key: [TIANDITU_KEY],
          },
          onHover: (info: any) => {
            if (info.coordinate) {
              setCoordinate(info.coordinate);
            }
          }
        }}
        renderOnViewportChange={({ viewport }) => {
          return (
            <>
              <PlottingScale viewport={viewport} />
              <SandSourceOverlay
                viewport={viewport}
                mapContainer={mapContainerRef.current}
              />
              <StationInfoOverlay viewport={viewport} />
              <SandRangeOverlay />
            </>
          );
        }}
      ></LazyDeckGL>
    </div>
  );
}

export default memo(Map);
