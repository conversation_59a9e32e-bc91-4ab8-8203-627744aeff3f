import { useState } from 'react';

type Props = {
  children: (isHover: boolean) => React.ReactNode;
  onClick?: () => void;
};

export default function NavigationItem({ children, ...rest }: Props) {
  const [isHover, setIsHover] = useState(false);

  return (
    <div
      className="flex items-center h-[30px] rounded-[4px] cursor-pointer hover:bg-[#FFFFFF64]"
      onMouseEnter={() => {
        setIsHover(true);
      }}
      onMouseLeave={() => {
        setIsHover(false);
      }}
      {...rest}
    >
      {children(isHover)}
    </div>
  );
}
