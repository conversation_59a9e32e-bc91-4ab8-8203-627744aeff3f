import React, { FC } from 'react';

interface CheckboxProps {
  color?: string;
  checked?: boolean;
  onChange?: () => void;
  children?: React.ReactNode;
  style?: React.CSSProperties;
}
const Checkbox: FC<CheckboxProps> = ({
  color = '#0C59DB',
  checked,
  onChange,
  style,
  children,
}) => {
  return (
    <div
      className="inline-flex items-center gap-[8px]"
      style={style}
      onClick={onChange}
    >
      <div
        className="inline-flex items-center justify-center flex-shrink-0 w-[16px] h-[16px] rounded-[4px] border cursor-pointer"
        style={{ borderColor: color }}
      >
        {checked && (
          <div
            className="w-[12px] h-[12px] rounded-[2px]"
            style={{ backgroundColor: color }}
          ></div>
        )}
      </div>
      {children}
    </div>
  );
};

export default Checkbox;
