import { memo } from 'react';
import CardContainer from './CardContainer';

function PredictionResult() {
  return (
    <CardContainer>
      <div className="px-[10px] py-[16px] text-white text-[14px]">
        <div className="flex items-center justify-between px-[9px]">
          <p className="text-[14px] text-[#FFBA17]">预测结果</p>
          <p className="text-[12px] ">仅供参考</p>
        </div>

        <div className="flex items-center justify-between mt-[9px] pb-[7px] px-[9px] border-b-[1px] border-dashed border-white/20">
          <p>传播方向</p>
          <p className="text-[16px]">西南方向</p>
        </div>

        <div className="flex items-center justify-between mt-[10px] px-[9px] gap-[8px]">
          <div className="shrink-0">受影响省份</div>
          <div className="text-[16px] ">青海省、甘肃省 青海省、甘肃省</div>
        </div>
      </div>
    </CardContainer>
  );
}

export default memo(PredictionResult);
