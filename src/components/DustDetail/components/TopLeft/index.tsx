import { useGlobalContext } from '@/components/GlobalProvider';
import { SHOW_SUBSCRIBE_OVERLAY_KEY } from '@/config';
import { animated, useSpring } from '@react-spring/web';
import { memo, useMemo } from 'react';
import MonitoringResults from './MonitoringResults';
import SubscribeToMessages from './SubscribeToMessages';

function TopLeft() {
  const { isFullScreen } = useGlobalContext();

  const showSubscribeLayer = useMemo(() => {
    return (
      (localStorage.getItem(SHOW_SUBSCRIBE_OVERLAY_KEY) ?? 'yes') === 'yes'
    );
  }, []);

  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    left: isFullScreen ? -300 : 20,
  });

  return (
    <animated.div
      className="absolute top-[20px] left-[20px]"
      style={{
        ...styles,
        display: 'grid',
        gridAutoFlow: 'row dense',
        gridTemplateColumns: 'max-content',
        gap: '8px',
        gridTemplateRows: 'min-content min-content min-content',
        gridTemplateAreas: `
				"."
				"."
				"."
			`,
      }}
    >
      <MonitoringResults />
      {showSubscribeLayer && <SubscribeToMessages />}
    </animated.div>
  );
}

export default memo(TopLeft);
