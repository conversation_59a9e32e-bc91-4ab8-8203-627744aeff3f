
import { activeTimePointAtom } from '@/components/DustDetail/atoms';
import { getDustRecordRegionInfos } from '@/components/DustDetail/services';
import { ensureArray } from '@/utils/dust';
import dayjs from 'dayjs';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { useQuery } from 'react-query';

export function useMonitoringResultInfo() {
  const activeTimePoint = useAtomValue(activeTimePointAtom);

  const { data } = useQuery(
    ['/api/dust/record/region/infos', activeTimePoint],
    () => getDustRecordRegionInfos({ dataTime: activeTimePoint! }),
    {
      enabled: !!activeTimePoint,
      keepPreviousData: true,
    },
  );

  const activeTime = useMemo(() => {
    return dayjs(activeTimePoint).format('HH:mm');
  }, [activeTimePoint]);

  const activeDate = useMemo(() => {
    return dayjs(activeTimePoint).format('YYYY-MM-DD');
  }, [activeTimePoint]);

  const formattedData = useMemo(() => {
    return {
      ...data,
      infoModels: ensureArray(data?.infoModels).map((item) => {
        return {
          ...item,
          intArea: Number(item.area).toFixed(1),
        };
      }),
    };
  }, [data]);

  return { data: formattedData, activeTime, activeDate };
}
