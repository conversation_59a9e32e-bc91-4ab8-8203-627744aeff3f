import CardContainer from '../CardContainer';
import { useMonitoringResultInfo } from './useMonitoringResultInfo';

export default function MonitoringResults() {
  const { data, activeDate, activeTime } = useMonitoringResultInfo();
  return (
    <CardContainer>
      <div className="px-[10px] py-[16px] text-white text-[14px]">
        <div className="flex items-center px-[9px]">
          <iconpark-icon
            class="mr-[6px]"
            width="26"
            height="26"
            name="shijian-d51nc9em"
          ></iconpark-icon>
          <div className="align-bottom">
            <span className="text-[30px] leading-[30px] mr-[9px]">
              {activeTime}
            </span>
            <span className="text-[14px] leading-[16px]">{activeDate}</span>
          </div>
        </div>

        <div className="h-0 border-t-[1px] mt-[6px] mb-[10px] border-t-white/20"></div>

        <div className="text-[#FFBA17] px-[9px]">监测结果</div>

        <div className="divide-y divide-dashed divide-white/20">
          {data.infoModels.map((item, index) => {
            return (
              <div
                key={index}
                className="flex items-end justify-between pt-[3px] pb-[6px] px-[9px] gap-[13px]"
              >
                <div className="flex items-center">
                  <iconpark-icon
                    class="mr-[3px]"
                    color="#FFBA17"
                    name="dili-6of0o4f2"
                  ></iconpark-icon>
                  <p className="text-[14px] leading-[20px] shrink-0">
                    {item.region}
                  </p>
                </div>
                <div className="flex items-end">
                  <div className="text-[26px] leading-[26px] mr-[3px] shrink-0 font-bold">
                    {item.intArea}
                  </div>
                  <div className="text-[12px] leading-[14px] shrink-0">k㎡</div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex items-center bg-[#FFB600]/40 rounded-full h-[20px] text-[12px] px-[10px] mt-[3px]">
          沙尘开始时间：{data?.startTime || '-'}
        </div>
      </div>
    </CardContainer>
  );
}
