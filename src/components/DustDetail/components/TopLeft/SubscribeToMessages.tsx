import { SHOW_SUBSCRIBE_OVERLAY_KEY } from '@/config';
import { useSetAtom } from 'jotai';
import { memo, useRef, useState } from 'react';
import { useResizeObserver } from 'usehooks-ts';
import { isSubscribeModalOpenAtom } from '../../atoms';

function SubscribeToMessages() {
  const setIsSubscribeModalOpen = useSetAtom(isSubscribeModalOpenAtom);
  const contentRef = useRef<HTMLSpanElement>(null);
  const [show, setShow] = useState(true);

  const { height = 80 } = useResizeObserver({
    ref: contentRef,
    box: 'border-box',
  });

  return (
    <>
      {show && (
        <div className="relative px-[19px] py-[16px] rounded-[14px] bg-[#101530]/60 backdrop-filter-blur-card text-[0px]">
          <span
            style={{
              display: 'inline-block',
              height,
            }}
          ></span>
          <span
            className="absolute right-0 left-0 text-white px-[19px] text-[12px]"
            ref={contentRef}
          >
            点击【
            <span
              className="text-[#24D6AA] cursor-pointer hover:opacity-80"
              onClick={() => setIsSubscribeModalOpen(true)}
            >
              订阅消息
            </span>
            】可以实时接收区域的最新沙尘监测消息。
          </span>

          <div
            className="absolute top-[-6px] right-[-6px] flex items-center justify-center w-[22px] h-[22px] rounded-full bg-[#566E84] cursor-pointer select-none"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              localStorage.setItem(SHOW_SUBSCRIBE_OVERLAY_KEY, 'no');
              setShow(false);
            }}
          >
            <iconpark-icon
              width="10"
              height="10"
              color="#ffffff"
              name="guanbi"
            ></iconpark-icon>
          </div>
        </div>
      )}
    </>
  );
}

export default memo(SubscribeToMessages);
