import { ReactNode, useEffect, useRef, useState } from 'react';

type Props = {
  children: ReactNode;
};

export default function CardContainer({ children }: Props) {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [svgDimensions, setSvgDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    // 创建 ResizeObserver 实例
    const observer = new ResizeObserver((entries) => {
      // 遍历观察到的所有条目
      for (const entry of entries) {
        // 获取目标 <div> 元素的宽度
        const { width, height } = entry.contentRect;
        setSvgDimensions({ width, height });
      }
    });

    // 开始观察目标 <div> 元素的大小变化
    observer.observe(containerRef.current!);
    return () => observer.disconnect();
  }, []);

  return (
    <div
      ref={containerRef}
      className="backdrop-filter-blur-card relative min-w-[220px] min-h-[20px] bg-[#101530]/60 rounded-[20px]"
    >
      <svg
        className="absolute top-0 left-0 right-0 bottom-0 z-[0]"
        xmlns="http://www.w3.org/2000/svg"
        version="1.1"
        width={svgDimensions.width}
        height={svgDimensions.height}
      >
        <defs>
          <linearGradient
            x1="54.2105567%"
            y1="50%"
            x2="94.7043551%"
            y2="12.0386946%"
            id="linearGradient-1"
          >
            <stop stopColor="#25350E" stopOpacity="0" offset="0%" />
            <stop
              stopColor="#DE9F21"
              stopOpacity="0.597929414"
              offset="99.9590253%"
            />
          </linearGradient>
          <linearGradient
            x1="97.8210309%"
            y1="9.70825967%"
            x2="47.6227489%"
            y2="62.5129614%"
            id="linearGradient-2"
          >
            <stop stopColor="#FFAC39" offset="0%" />
            <stop stopColor="#FFA92A" stopOpacity="0" offset="100%" />
          </linearGradient>
        </defs>
        <g
          id="页面-1"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
        >
          <rect
            id="矩形备份"
            stroke="url(#linearGradient-2)"
            strokeWidth="2"
            fill="url(#linearGradient-1)"
            x="1"
            y="1"
            width={svgDimensions.width - 2}
            height={svgDimensions.height - 2}
            rx="14"
          />
        </g>
      </svg>
      <div className="relative z-2">{children}</div>
    </div>
  );
}
