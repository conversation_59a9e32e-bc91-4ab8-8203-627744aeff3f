import type React from 'react';
import { useEffect } from 'react';
import { useRef } from 'react';
import { createPortal } from 'react-dom';

interface Props {
  groupId: string;
  containerId?: string;
}

const MapMarkerPortal: React.FC<Props> = ({
  groupId,
  children,
  containerId = 'map-container',
}) => {
  const ref = useRef<HTMLDivElement>(document.createElement('div'));
  const mapContainer = document.getElementById(containerId);

  ref.current.id = groupId;
  mapContainer?.appendChild(ref.current);

  useEffect(() => {
    return () => {
      const el = document.getElementById(groupId);

      if (el) {
        el.remove();
      }
    };
  }, [groupId]);

  return createPortal(children, ref.current);
};

export default MapMarkerPortal;
