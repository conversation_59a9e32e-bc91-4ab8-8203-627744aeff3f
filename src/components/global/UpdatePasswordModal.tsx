import type { UpdatePasswordParams } from '@/services/global';
import { updatePassword } from '@/services/global';
import formRules from '@/utils/formRules';
import { Form, Input, message } from 'antd';
import type { Rule } from 'antd/lib/form';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { Flex, ModalContent, PoCreateTitle, StyledButton } from '../ui';
import DarkModal from './DarkModal';

interface Props {
  visible?: boolean;
  logout: () => void;
  onCancel: () => void;
}
const UpdatePasswordModal: React.FC<Props> = ({
  visible,
  logout,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [disabled, setDisabled] = useState(true);
  const mutation = useMutation((params: UpdatePasswordParams) =>
    updatePassword(params),
  );

  const rule: (msg: string) => Rule[] = (msg: string) => {
    return [
      ...formRules.required(msg),
      {
        type: 'string',
        min: 8,
        message: '密码长度最少8位，由字母、数字、符号组成',
      },
    ];
  };
  const originRule = useMemo(() => formRules.required('请输入原密码'), []);
  const newRule = useMemo(() => rule('新密码不能为空'), []);

  const handleSubmit = useCallback(() => {
    if (disabled) return;

    form.validateFields().then((values) => {
      const { confirmPassword, ...rest } = values;
      mutation.mutate(rest, {
        onSuccess() {
          message.success('修改成功，请重新登录');
          setTimeout(() => {
            onCancel();
            logout();
          }, 1500);
        },
      });
    });
  }, [disabled, form, logout, mutation, onCancel]);

  const handleValuesChange = useCallback((_, allValues) => {
    const keys = Object.keys(allValues);
    const allIsInput = keys.every((key) => !!allValues[key]);

    setDisabled(!allIsInput);
  }, []);

  useEffect(() => {
    return () => {
      form.resetFields();
    };
  }, [form, visible]);

  return (
    <DarkModal visible={visible}>
      <PoCreateTitle>
        <span>修改密码</span>
      </PoCreateTitle>
      <ModalContent>
        <Form form={form} onValuesChange={handleValuesChange}>
          <Form.Item name="originalPassword" rules={originRule}>
            <Input
              type="password"
              className="dark-form-item"
              placeholder="请输入原密码"
            />
          </Form.Item>
          <Form.Item name="newPassword" rules={newRule}>
            <Input
              type="password"
              className="dark-form-item"
              placeholder="请输入新密码"
            />
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            rules={[
              {
                validator: async (_, value) => {
                  if (value !== form.getFieldValue('newPassword')) {
                    return Promise.reject('与新密码不一致');
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              type="password"
              className="dark-form-item"
              placeholder="请确认新密码"
            />
          </Form.Item>
        </Form>
        <Flex style={{ justifyContent: 'flex-end', marginTop: 12, gap: 12 }}>
          <StyledButton onClick={onCancel}>取消</StyledButton>
          <StyledButton
            variant="primary"
            disabled={disabled}
            style={{
              opacity: disabled ? 0.5 : 1,
            }}
            onClick={handleSubmit}
          >
            提交
          </StyledButton>
        </Flex>
      </ModalContent>
    </DarkModal>
  );
};

export default UpdatePasswordModal;
