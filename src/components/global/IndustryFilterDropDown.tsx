import React from 'react';
import { But<PERSON>, Tree } from 'antd';
import type { Industry } from '@/types';
import { useMemo } from 'react';
import { FilterTreeBtns, FilterTreeContainer } from '@/components/ui';

interface Props {
  data: Industry[] | undefined;
  setSelectedKeys: any;
  selectedKeys: any;
  confirm: any;
  clearFilters: any;
}

interface TreeDataItem {
  title: string;
  key: number;
}

export interface TreeData extends TreeDataItem {
  title: string;
  key: number;
  children: TreeDataItem[];
}

const IndustryFilterDropDown: React.FC<Props> = ({
  data,
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
}) => {
  const treeData = useMemo(() => {
    if (data && data.length > 0) {
      return data.reduce((prev: TreeData[], cur: Industry) => {
        const findFirstLevel = prev.find(
          (item) => item.key === cur.firstLevelCode,
        );

        if (findFirstLevel) {
          return prev.map((item) =>
            item.key === cur.firstLevelCode
              ? {
                  ...item,
                  children: [
                    ...item.children,
                    {
                      title: cur.secondLevelName,
                      key: cur.secondLevelCode,
                    },
                  ],
                }
              : item,
          );
        }
        return [
          ...prev,
          {
            title: cur.firstLevelName,
            key: cur.firstLevelCode,
            children: [
              {
                key: cur.secondLevelCode,
                title: cur.secondLevelName,
              },
            ],
          },
        ];
      }, [] as TreeData[]);
    }

    return [];
  }, [data]);

  return (
    <div>
      <FilterTreeContainer>
        <Tree
          checkable
          treeData={treeData}
          checkedKeys={selectedKeys}
          onCheck={(keys) => {
            setSelectedKeys(keys);
          }}
        />
      </FilterTreeContainer>
      <FilterTreeBtns>
        <Button
          size="small"
          type="link"
          disabled={selectedKeys.length === 0}
          onClick={() => {
            clearFilters();
            setSelectedKeys([]);
          }}
        >
          重置
        </Button>
        <Button
          size="small"
          type="primary"
          onClick={() => {
            confirm();
          }}
        >
          确定
        </Button>
      </FilterTreeBtns>
    </div>
  );
};

export default IndustryFilterDropDown;
