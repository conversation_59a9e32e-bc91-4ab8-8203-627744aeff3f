import React, { useState } from 'react';
import { Menu, Dropdown } from 'antd';
import {
  Avatar,
  PAvatarContainer,
  PBreadcrumb,
  PHContainer,
  PhLeft,
  PTitle,
  PUserInfo,
  Spacer,
} from '../ui';
import type { ReactNode } from 'react';
import { useQuery } from 'react-query';
import { fetchUserInfo } from '@/services/global';
import { useEffect } from 'react';
import { useSetAtom as useUpdateAtom } from 'jotai';
import { userInfoAtom } from '@/atoms';
import type { LoginRespData } from '@/types';
import UpdatePasswordModal from './UpdatePasswordModal';
import { useLogOut } from '@/hooks';

const { Item } = Menu;

interface Props {
  title: string;
  children: ReactNode;
  type?: 'between';
  style?: React.CSSProperties;
}

const PageHead: React.FC<Props> = ({ title, children, type,style }) => {
  const { data } = useQuery<LoginRespData>(['user-info'], fetchUserInfo);
  const updateUserInfo = useUpdateAtom(userInfoAtom);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (data) {
      updateUserInfo(data);
    }
  }, [data, updateUserInfo]);

  const logout = useLogOut();

  const menu = (
    <Menu
      onClick={({ key }) => {
        if (key === '1') {
          setVisible(true);
        }
        if (key === '2') {
          logout();
        }
      }}
    >
      <Item key="1">修改密码</Item>
      <Item danger key="2">
        退出登录
      </Item>
    </Menu>
  );
  return (
    <div>
      <PHContainer>
        {
          type === 'between' ?
            <>
              <PhLeft className="flex flex-1 justify-between" style={style}>
                <PTitle>{title}</PTitle>
                <PBreadcrumb>{children}</PBreadcrumb>
              </PhLeft>
            </>
            :
            <>
              <PhLeft>
                <PTitle>{title}</PTitle>
                <PBreadcrumb>{children}</PBreadcrumb>
              </PhLeft>
              <Spacer />
            </>
        }
        <PAvatarContainer>
          <Avatar>
            <img src="/assets/images/avatar.jpg" alt="" />
          </Avatar>
          <PUserInfo>
            <p>管理员</p>
            <Dropdown overlay={menu} trigger={['hover']}>
              <h1>
                {data?.name} <i className="icomoon icon-arrow-down" />
              </h1>
            </Dropdown>
          </PUserInfo>
        </PAvatarContainer>
      </PHContainer>
      <UpdatePasswordModal
        visible={visible}
        onCancel={() => setVisible(false)}
        logout={logout}
      />
    </div>
  );
};

export default PageHead;
