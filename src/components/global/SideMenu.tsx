import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  StyledAntdMenu as <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from '@/components/ui';
import { fullScreenAtom, userInfoAtom } from '@/atoms';
import { useAtomValue } from 'jotai';
import { useRouter, useUserRegionCode } from '@/hooks';
import { useEffect, useMemo } from 'react';
import { getProvinceCode } from '@/utils';
import { useAtom } from 'jotai';
import { menuCollapsedAtom } from '@/atoms/menu';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { useQuery } from 'react-query';

const { SubMenu } = Menu;

interface Route {
  component?: string;
  hiddenInMenu?: boolean;
  icon?: string;
  key: string;
  path?: string;
  redirect?: string;
  regionCode: number | string;
  title: string;
  children?: Route[];
  permission?: string;
  tagKey?: string;
}

const getRoutes: () => Promise<Route[]> = () => {
  return new Promise((resolve) => {
    resolve([
      {
        path: '',
        regionCode: '',
        component: 'Overview',
        title: '大气一张图',
        key: '2',
        icon: 'da<PERSON>yiz<PERSON>tu',
      },
      {
        path: 'dust-event',
        regionCode: '',
        component: 'DustEvent',
        title: '沙尘事件',
        key: '9',
        icon: 'fengchang-gjmbn420',
        permission: '1007',
        children: [
          {
            path: 'dust-event-detail',
            regionCode: '',
            component: 'DustEventDetail',
            title: '沙尘事件详情',
            key: '9-1',
            hiddenInMenu: true,
          }
        ]
      },
      {
        path: 'thematic-map',
        regionCode: '',
        component: 'ThematicMap',
        title: '创建专题图',
        key: '4',
        icon: 'chuangjianzhuantitu',
        permission: '1004',
      },
      // {
      //   path: 'thematic-map',
      //   regionCode: '',
      //   component: 'ThematicMap',
      //   title: '专题图',
      //   key: '3',
      //   icon: 'icon-menu-2',
      //   hiddenInMenu: true,
      // },
      {
        path: 'thematic-map-tpl',
        regionCode: '',
        component: 'ThematicMapTpl',
        title: '专题图模板',
        key: '3',
        hiddenInMenu: true,
      },
      {
        path: 'thematic-map-list',
        regionCode: '',
        component: 'ThematicMapList',
        title: '专题产品',
        icon: 'zhuantichanpin',
        key: '3',
        permission: '1004',
      },
      {
        path: 'thematic-map-tpl-list',
        regionCode: '',
        component: 'ThematicMapTplList',
        title: '模板列表',
        key: '3',
        hiddenInMenu: true,
      },
      {
        path: 'report',
        regionCode: '',
        component: 'Report',
        title: '分析报告',
        key: '11',
        icon: 'shujuguanli',
        permission: '1008',
        children: [
          {
            path: 'report-detail',
            regionCode: '',
            component: 'ReportDetail',
            title: '查看报告',
            key: '11-1',
            hiddenInMenu: true,
          }
        ]
      },
      {
        regionCode: '',
        title: '综合评估',
        key: '6',
        icon: 'zonghefenxi',
        permission: '1006',
        children: [
          {
            path: 'comprehensive-assessment',
            regionCode: '',
            component: 'ComprehensiveAssessment',
            title: '大气综合评估',
            key: '6-1',
          },
          {
            regionCode: '',
            path: 'dust-analyze',
            component: 'DustAnalyze',
            title: '沙尘综合评估',
            key: '6-2',
          }
        ],
      },
      {
        title: '系统管理',
        key: '8',
        regionCode: '',
        icon: 'xitongguanli',
        permission: '1005',
        children: [
          {
            path: 'user-management',
            regionCode: '',
            component: 'UserManagement',
            title: '用户管理',
            key: '8-1',
          },
          {
            regionCode: 'data8',
            path: 'receive-monitoring',
            component: 'ReceiveMonitoring',
            title: '数据管理',
            key: '8-2',
            tagKey:'8-2'
          },
          {
            regionCode: 'data8',
            path: 'data-download',
            component: 'DataDownload',
            title: '数据下载',
            key: '8-3',
            hiddenInMenu: true,
             tagKey:'8-2'
          },
          {
            regionCode: 'data8',
            path: 'receiving-log',
            component: 'ReceivingLog',
            title: '数据接收日志',
            key: '8-4',
            hiddenInMenu: true,
             tagKey:'8-2'
          },
          {
            regionCode: '',
            path: 'system-log',
            component: 'SystemLog',
            title: '系统日志',
            key: '8-5',
          },
        ],
      },
    ]);
  });
};

const SideMenu: React.FC = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const userRegionCode = useUserRegionCode();
  const { history, pathname } = useRouter();
  const [collapsed, setCollapsed] = useAtom(menuCollapsedAtom);
  const { data } = useQuery<Route[]>(['route-data'], getRoutes, {
    enabled: Boolean(localStorage.getItem('__NMENG_TOKEN__')),
  });
  const userInfo = useAtomValue(userInfoAtom);


  const keys = useMemo(() => {
    let key: string[] = [];

    (data ?? []).forEach((route) => {
      const { regionCode, path, children } = route;
      const target = regionCode ? `/${regionCode}/${path}` : `/${path}`;
      if (pathname === target) {
        key = [route.key];
      } else {
        (children || []).forEach((childRoute) => {
          const { regionCode: childRegionCode, path: childPath } = childRoute;
          const childTarget = childRegionCode
            ? `/${childRegionCode}/${childPath}`
            : `/${childPath}`;
          if (pathname === childTarget) {
            key = [route.key, childRoute.key];
          }
          if (childTarget.includes(pathname)&&childRoute.tagKey&&pathname!=='/') {
            key = [route.key, childRoute.tagKey as string];
          }
        });
      }
    });

    return [...key];
  }, [data, pathname]);

  useEffect(() => {
    setCollapsed(1);
  }, [setCollapsed, pathname]);

  return isFullScreen ? null : (
    <MenuWrapper collapsed={collapsed}>
      <Brand>
        {userRegionCode && (
          <img
            style={{
              width: 296,
              height: 120
            }}
            src={`/assets/images/${getProvinceCode(
              userRegionCode,
            )}-menu-logo.png`}
            alt=""
          />
        )}
      </Brand>
      <Menu
        selectedKeys={keys}
        mode="inline"
        // @ts-ignore
        theme="dark"
        inlineCollapsed={!!collapsed}
      >
        {(data || []).map((route) => {
          const { children, hiddenInMenu } = route;
          const hasVisibleChildren = children && children.some(child => !child.hiddenInMenu);

          if (
            route.permission &&
            !userInfo?.permissions.includes(route.permission)
          ) {
            return null;
          }

          if (children && !hiddenInMenu && hasVisibleChildren) {
            return (
              <SubMenu
                key={route.key}
                icon={
                  <div className="w-[20px] h-[20px] mr-[8px]">
                    <iconpark-icon size="30px" name={route.icon} color="currentColor" />
                  </div>
                }
                title={route.title}
              >
                {children.map((childRoute) => {
                  return childRoute.hiddenInMenu ? null : (
                    <Menu.Item
                      key={childRoute.key}
                      onClick={() => {
                        const { regionCode, path,tagKey } = childRoute;
                        history.push(
                          regionCode&&!tagKey ? `/${regionCode}/${path}` : `/${path}`,
                        );
                      }}
                    >
                      {childRoute.title}
                    </Menu.Item>
                  );
                })}
              </SubMenu>
            );
          }

          return route.hiddenInMenu ? null : (
            <Menu.Item
              key={route.key}
              icon={<div className="w-[20px] h-[20px] mr-[8px]">
                <iconpark-icon size="30px" name={route.icon} color="currentColor" />
              </div>}
              onClick={() => {
                const { regionCode, path,tagKey } = route;
                history.push(
                  regionCode&&!tagKey ? `/${regionCode}/${path}` : `/${path}`,
                );
              }}
            >
              {route.title}
            </Menu.Item>
          );
        })}
      </Menu>
      <MenuToggler onClick={() => setCollapsed((prev) => (prev === 0 ? 1 : 0))}>
        <Center>
          {collapsed === 1 ? (
            <MenuUnfoldOutlined style={{ fontSize: 24, opacity: 0.7 }} />
          ) : (
            <MenuFoldOutlined style={{ fontSize: 24, opacity: 0.7 }} />
          )}
        </Center>
      </MenuToggler>
    </MenuWrapper>
  );
};

export default SideMenu;
