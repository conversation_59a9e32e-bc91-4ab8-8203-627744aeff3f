import { userInfo<PERSON><PERSON> } from '@/atoms';
import {
  DdModalBody,
  DdModalControls,
  DdModalCtrlLeft,
  DdModalCtrlRight,
  DdModalCtrlRightBottom,
  DdModalFooter,
  DdModalInputContent,
  DdModalInputGroup,
  DdModalInputList,
  DdModalSubTitle,
  DdModalTitle,
  DdModalUserBox,
  DueDiModal,
  HorCenter,
  PreviewModal,
  PreviewModalBody,
  PreviewModalFooter,
  PreviewModalTitle,
  Spacer,
  StyledButton,
} from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { uploadFile } from '@/services/global';
import type { CreateOrEditCheckParams, RecordItemInterface } from '@/types';
import { getBase64, pollutionStatus } from '@/utils';
import formRules from '@/utils/formRules';
import { PlusOutlined } from '@ant-design/icons';
import { DatePicker, Form, Input, message, Select, Switch, Upload } from 'antd';
import type { UploadFile } from 'antd/lib/upload/interface';
import { useAtomValue } from 'jotai';
import moment from 'moment';
import { parse } from 'querystring';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';

interface Props {
  title?: string;
  inPoDetails?: boolean;
  current?: RecordItemInterface;
  status?: number;
  visible: boolean;
  onCancel: () => void;
  onOk: (params: CreateOrEditCheckParams) => void;
}

interface UploadFileResponse {
  uid: string;
  id: string;
  name: string;
  size?: number;
  url: string;
}

const arr = [
  {
    label: 'PM₂.₅',
    value: 'pm25',
  },
  {
    label: 'PM₁₀',
    value: 'pm10',
  },
  {
    label: 'CO',
    value: 'co',
  },
  {
    label: 'BC',
    value: 'bc',
  },
  {
    label: 'NH₃',
    value: 'nh3',
  },
  {
    label: 'NOx',
    value: 'nox',
  },
  {
    label: 'OC',
    value: 'oc',
  },
  {
    label: 'VOC',
    value: 'voc',
  },
  {
    label: 'SO₂',
    value: 'so2',
  },
];
const DueDiligenceModal: React.FC<Props> = ({ title, visible, status, current, inPoDetails, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const [imageList, setImageList] = useState<UploadFileResponse[]>([]);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | undefined>('');
  const userInfo = useAtomValue(userInfoAtom);
  const uploadMutation = useMutation((file: FormData) => uploadFile(file));

  const onSubmit = useCallback(() => {
    form
      .validateFields()
      .then((values) => {
        onOk({
          ...values,
          imageIds: imageList.map((item) => item.uid),
        });
      })
      .catch((err) => {
        const { errorFields } = err;
        const msg = errorFields[0].errors[0];

        message.error(msg);
      });
  }, [form, imageList, onOk]);

  const uploadButton = (
    <HorCenter className="text-primary" style={{ fontSize: 12 }}>
      <PlusOutlined />
      <div>上传照片</div>
    </HorCenter>
  );

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('请上传JPG或者PNG类型的文件!');
    }
    const isLt1M = file.size / 1024 / 1024 < 1;
    if (!isLt1M) {
      message.error('图片必须小于1M');
    }
    return isJpgOrPng && isLt1M;
  };
  const initialValues = useMemo(() => {
    return {
      auditor: userInfo?.name,
      related: true,
      date: moment(),
      pollutionSourceStatus: status,
      checkType: inPoDetails ? 2 : 1,
      ...arr.reduce((prev, cur) => {
        return {
          ...prev,
          [cur.value]: '0',
        };
      }, {} as Record<string, any>),
    };
  }, [inPoDetails, status, userInfo?.name]);

  useEffect(() => {
    if (visible && !current) {
      setTimeout(() => {
        form.setFieldsValue({
          ...initialValues,
        });
      }, 10);
    }

    if (current && visible) {
      setTimeout(() => {
        const { alertCheckRecordModels, datetime, type, images, status: checkStatus, ...rest } = current;
        const poValues = (alertCheckRecordModels || []).reduce((prev, cur) => {
          return {
            ...prev,
            [cur.pollutionType.replace('.', '').toLowerCase()]: `${cur.actualValue}`,
          };
        }, {});
        form.setFieldsValue({
          ...rest,
          ...initialValues,
          date: moment(datetime),
          pollutionSourceStatus: status,
          checkType: inPoDetails ? 2 : 1,
          checkStatus: checkStatus === 5,
          related: current.related,
          ...(inPoDetails
            ? poValues
            : {
                ...['bc', 'co', 'nh3', 'nox', 'oc', 'pm10', 'pm25', 'so2', 'voc'].reduce(
                  (prev, cur) => ({
                    ...prev,
                    [cur]: (current[cur] as number) || 0,
                  }),
                  {},
                ),
              }),
        });
      }, 10);
    }

    if (current) {
      setImageList(
        current.images
          ? current.images.map((image) => {
              const parsed = parse(image);

              return {
                uid: parsed.id as string,
                id: parsed.id as string,
                name: image,
                url: image,
              };
            })
          : [],
      );
    }

    return () => {
      form.resetFields();
      setImageList([]);
    };
  }, [current, form, inPoDetails, initialValues, status, visible]);

  return (
    <>
      <DueDiModal width={640} visible={visible} onCancel={onCancel}>
        <DdModalTitle>
          <span>{inPoDetails ? '例行' : '实地'}核查</span>
        </DdModalTitle>
        <Form form={form}>
          <DdModalBody>
            <DdModalSubTitle>
              <h1>{title}</h1>
              <Spacer />
              <div>
                {inPoDetails && current?.type !== 1 ? (
                  <>
                    <span>
                      是否异常
                      <span className="text-danger"> *</span>
                    </span>
                    <Form.Item name="checkStatus" noStyle valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </>
                ) : (
                  <>
                    <span>
                      污染源与该报警是否相关
                      <span className="text-danger"> *</span>
                    </span>
                    <Form.Item name="related" noStyle valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </>
                )}
              </div>
            </DdModalSubTitle>
            <DdModalInputList>
              {arr.map((item) => {
                return (
                  <DdModalInputGroup key={item.value}>
                    <span>{item.label}</span>
                    <Form.Item
                      key={item.value}
                      name={item.value}
                      noStyle
                      rules={[...formRules.required(`${item.label}为值必填项,或以0代替`), ...formRules.numberWithDecimal(`${item.label}值格式错误`)]}
                    >
                      <Input style={{ paddingLeft: 50, color: 'white' }} />
                    </Form.Item>
                  </DdModalInputGroup>
                );
              })}
            </DdModalInputList>
            <DdModalInputContent>
              <div>
                污染源核查内容<span className="text-danger">*</span>
              </div>
              <Form.Item noStyle name="content" rules={formRules.required('请输入核查内容')}>
                <Input.TextArea
                  style={{ outline: 'none', boxShadow: 'none' }}
                  className="dark-form-item"
                  placeholder="请在此处填写核查内容/无关原因"
                  rows={4}
                />
              </Form.Item>
            </DdModalInputContent>
            <DdModalControls>
              <DdModalCtrlLeft>
                <Upload
                  className="dark-form-item"
                  listType="picture-card"
                  fileList={imageList}
                  accept="image/*"
                  beforeUpload={beforeUpload}
                  onRemove={(file: UploadFile<UploadFileResponse>) => {
                    setImageList((prev) => prev.filter((item) => item.uid !== file.uid));
                  }}
                  customRequest={({ file }) => {
                    const fmData = new FormData();
                    fmData.append('file', file);
                    uploadMutation.mutate(fmData, {
                      onSuccess(data: UploadFileResponse) {
                        setImageList((prev) => [
                          ...prev,
                          {
                            ...data,
                            uid: data.id,
                          },
                        ]);
                      },
                    });
                  }}
                  onPreview={async (file) => {
                    const f = file;
                    // @ts-ignore
                    if (!f.url && !f.preview) {
                      // @ts-ignore
                      f.preview = await getBase64(f.originFileObj);
                    }

                    setPreviewImage(f.url || f.preview);
                    setPreviewModalVisible(true);
                  }}
                  // @ts-ignore
                >
                  {imageList.length < 3 ? uploadButton : null}
                </Upload>
              </DdModalCtrlLeft>
              <DdModalCtrlRight>
                <div style={{ marginBottom: 10 }}>
                  <Form.Item noStyle name="checkType" rules={formRules.required('请选择核查类型')}>
                    <Select
                      style={{ width: '100%', height: 36 }}
                      className="dark-form-item"
                      bordered={false}
                      placeholder="核查类型"
                      getPopupContainer={() => document.getElementById('dark-container') as HTMLDivElement}
                      options={
                        inPoDetails
                          ? [
                              {
                                label: '例行检查',
                                value: 2,
                              },
                            ]
                          : [
                              {
                                label: '报警核查',
                                value: 1,
                              },
                            ]
                      }
                    />
                  </Form.Item>
                </div>
                <div>
                  <Form.Item noStyle name="date" rules={formRules.required('请选择核查日期')}>
                    <DatePicker
                      className="dark-form-item"
                      style={{
                        width: '100%',
                        height: 36,
                      }}
                      disabledDate={(cur) => {
                        return cur.isAfter(moment());
                      }}
                      getPopupContainer={getDarkContainer}
                    />
                  </Form.Item>
                </div>
                <DdModalCtrlRightBottom>
                  <Form.Item name="pollutionSourceStatus" rules={formRules.required('请选择污染源状态')} noStyle>
                    <Select
                      style={{ width: '100px', height: 36 }}
                      className="dark-form-item"
                      bordered={false}
                      placeholder="污染源状态"
                      getPopupContainer={() => document.getElementById('dark-container') as HTMLDivElement}
                      options={Object.keys(pollutionStatus).map((key) => ({
                        label: pollutionStatus[key],
                        value: +key,
                      }))}
                    />
                  </Form.Item>
                  <DdModalUserBox>
                    <i className="icomoon icon-cct" />
                    <Form.Item noStyle name="auditor" rules={formRules.required('请填写审核员')}>
                      <Input className="dark-form-item" placeholder="审核员" />
                    </Form.Item>
                  </DdModalUserBox>
                </DdModalCtrlRightBottom>
              </DdModalCtrlRight>
            </DdModalControls>
          </DdModalBody>
        </Form>
        <DdModalFooter>
          <span>历史版本：{current?.count || 0}版</span>
          <Spacer />
          <StyledButton onClick={onCancel}>以后再说</StyledButton>
          <StyledButton variant={current && current.id ? 'danger' : 'primary'} onClick={onSubmit}>
            {current && current.id ? '修改' : '提交'}
          </StyledButton>
        </DdModalFooter>
      </DueDiModal>

      {/* 图片预览弹窗 */}
      <PreviewModal width={800} visible={previewModalVisible} onCancel={() => setPreviewModalVisible(false)}>
        <PreviewModalTitle>
          <span>图片预览</span>
        </PreviewModalTitle>
        <PreviewModalBody>
          <img src={previewImage} style={{ width: '100%' }} alt="预览图片" />
        </PreviewModalBody>
        <PreviewModalFooter>
          <StyledButton variant="danger" onClick={() => setPreviewModalVisible(false)}>
            关闭
          </StyledButton>
        </PreviewModalFooter>
      </PreviewModal>
    </>
  );
};

export default DueDiligenceModal;
