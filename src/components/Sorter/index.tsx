import React, { Key } from 'react';
import './index.css';

export type SortInfo<V> = {
  type: V | undefined;
  direction: 'up' | 'down' | undefined;
};

export type Props<V> = {
  /**
   * 需要被筛选的类型类别，类似于 select 组件的 options
   */
  options: {
    label: string;
    value: V;
  }[];
  /**
   * 排序信息
   */
  sortInfo: SortInfo<V>;
  /**
   * 设置排序信息
   */
  setSortInfo: React.Dispatch<React.SetStateAction<SortInfo<V>>>;
};

export default function Sorter<V extends Key>({
  options,
  sortInfo,
  setSortInfo,
}: Props<V>) {
  const color = '#FFAA17';
  return (
    <div className="flex items-center gap-[25px]">
      {options.map((item) => {
        const { label, value } = item;
        return (
          <div className="flex items-center gap-[4px]" key={value}>
            <div className="flex items-center gap-[4px] cursor-pointer">
              <span className="text-[14px]">{label}</span>
            </div>
            <div className="flex flex-col gap-[1px] pl-[8px]">
              <div
                className="cursor-pointer"
                onClick={() => {
                  setSortInfo((prev) => {
                    return {
                      type: value,
                      direction:
                        prev.type === value && prev.direction === 'up'
                          ? undefined
                          : 'up',
                    };
                  });
                }}
              >
                <div
                  className="up-icon"
                  style={{
                    backgroundColor:
                      sortInfo.type === value && sortInfo.direction === 'up'
                        ? color
                        : '#979FB2',
                  }}
                />
              </div>

              <div
                className="cursor-pointer"
                onClick={() => {
                  setSortInfo((prev) => {
                    return {
                      type: value,
                      direction:
                        prev.type === value && prev.direction === 'down'
                          ? undefined
                          : 'down',
                    };
                  });
                }}
              >
                <div
                  className="down-icon"
                  style={{
                    backgroundColor:
                      sortInfo.type === value && sortInfo.direction === 'down'
                        ? color
                        : '#979FB2',
                  }}
                />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
