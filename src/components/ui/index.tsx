import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import { animated } from 'react-spring';
import styled, { createGlobalStyle, css } from 'styled-components';
import DarkModal from '../global/DarkModal';
import { darkContainerStyles, darkFormItemStyles } from './dark';
import theme from './theme';
import { getColorFromTheme } from './utils';

// GlobalStyle
export const GlobalStyle = createGlobalStyle`
  body {
    min-width: 1300px;
    overflow-x:hidden;
    font-family: -apple-system, BlinkMacSystemFont, Arial,
     'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
      'Open Sans', 'Helvetica Neue', sans-serif !important;

    &#index {
      .ant-menu {
        background:${getColorFromTheme('gray.1c1d24')} !important;
        box-shadow: none;
      }
    }
  }

  ul {
    padding: 0;

  }
  li {
    list-style: none;
  }

  a {
    color: inherit;
  }

  svg {
    fill:currentColor;
  }

  .svg-wrapper {
    svg {
      fill:currentColor;
    }

    div {
      width: 100%;
      height:100%;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: inherit;
  }

  .ant-tooltip-inner,
  .ant-tooltip-arrow-content {
    background: ${getColorFromTheme('gray.900')}
  }

  .ant-menu-dark .ant-menu-sub, .ant-menu.ant-menu-dark .ant-menu-sub {
    background: ${getColorFromTheme('gray.900')}
  }

  .text-primary {
    color: ${getColorFromTheme('primary')} !important;
  }
  .text-danger {
    color: ${getColorFromTheme('danger')} !important;
  }

  .distance-marker {
    position: absolute;
    display: flex;
    align-items:center;
    padding: 4px;
    font-size: 12px;
    line-height: 1;
    color: white;
    background: ${getColorFromTheme('primary')};
    border-radius: 3px;
    cursor: pointer;

    .icomoon {
      margin-left: 4px;
    }
  }

  .measure-area {
    position: absolute;
    z-index: 10;
    background: rgba(40, 108, 255, .2);
    pointer-events: none;
    border: 2px dotted ${getColorFromTheme('primary')};

    .icomoon {
      margin-left: 4px;
    }

    .content {
      position: absolute;
      display: flex;
      align-items: center;
      padding: 4px;
      font-size: 12px;
      line-height: 1;
      background: ${getColorFromTheme('primary')};
      top:-20px;
      left: 0;
      border-radius: 2px 2px 0 0;
      pointer-events: initial;
      cursor: pointer;
    }
  }

  .w-full {
    width: 100% !important;
  }

  .flex-1 {
    flex:1;
  }

  .mt-12 {
    margin-bottom: 12px;
  }
  .mb-12 {
    margin-bottom: 12px;
  }



  .ant-form-item-has-error .ant-input:not(.ant-input-disabled),
  .ant-form-item-has-error
    .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled),
  .ant-form-item-has-error .ant-input:not(.ant-input-disabled):hover,
  .ant-form-item-has-error
    .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    background: #1c1d24;
  }

  /* 黑色表单控件样式 */
  .dark-form-item {
    ${darkFormItemStyles};
  }


  /* select等弹出容器内控件样式 */
  #dark-container {
    color: #c1c1c4;
    ${darkContainerStyles};
  }

  .ellipsis {
     overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  }

  .dark-empty {
    .ant-empty-img-default-ellipse {
    fill: #fff;
    fill-opacity: 0.08;
  }
  .ant-empty-img-default-path-1 {
    fill: #262626;
  }
  .ant-empty-img-default-path-2 {
    fill: url(#linearGradient-1);
  }
  .ant-empty-img-default-path-3 {
    fill: #595959;
  }
  .ant-empty-img-default-path-4 {
    fill: #434343;
  }
  .ant-empty-img-default-path-5 {
    fill: #595959;
  }
  .ant-empty-img-default-g {
    fill: #434343;
  }
  .ant-empty-img-simple-ellipse {
    fill: #fff;
    fill-opacity: 0.08;
  }
  .ant-empty-img-simple-g {
    stroke: #434343;
  }
  .ant-empty-img-simple-path {
    fill: #262626;
    stroke: #434343;
  }
  }

.ant-table .active {
  border:1px solid blue;

  td {
    background: #F2F6FD !important;
  }
}
`;
// Global Components
export const Grid = styled.div`
  display: grid;
`;
export const Flex = styled.div`
  display: flex;
`;

export const WrapFlex = styled(Flex)`
  flex-wrap: wrap;
`;

export const FlexCol = styled(Flex)`
  flex-direction: column;
`;

export const HorCenter = styled(Flex)`
  align-items: center;
`;

export const VerCenter = styled(Flex)`
  justify-content: center;
`;

export const Center = styled(Flex)`
  justify-content: center;
  align-items: center;
`;

export const Spacer = styled.div`
  flex: 1;
`;
export const ellipsisCss = css`
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
`;

export const scrollBarStyles = css`
  &::-webkit-scrollbar {
    width: 4px;
    height: 6px;
  }
  /* 滚动槽 */
  &::-webkit-scrollbar-track {
    width: 2px;
    border-radius: 10px;
  }
  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    width: 2px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb:window-inactive {
    width: 2px;
    background: rgba(255, 255, 255, 0.2);
  }
`;

// layout
export const MenuWrapper = styled(animated.div)<{ collapsed: 0 | 1 }>`
  & {
    position: relative;
    z-index: 999;
    display: flex;
    flex-direction: column;
    width: ${(props) => {
      return props.collapsed === 1 ? '80px' : '296px';
    }};
    height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    color: white;
    background: ${getColorFromTheme('gray.900', '#000')};

    .icomoon {
      display: block !important;
      width: 32px !important;
      font-size: 32px !important;
    }

    ${scrollBarStyles}
  }
`;

export const MenuToggler = styled(Flex)`
  margin-bottom: 40px;
  padding: 4px;
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }

  ${Center} {
    width: 80px;
  }
`;

export const EllipsisTextInTable = styled.div<{
  width?: string;
}>`
  max-width: ${({ width }) => width || 'auto'};
  ${ellipsisCss};
`;

export const StyledAntdMenu = styled(Menu)`
  flex: 1;
  overflow-y: auto;
  ${scrollBarStyles};

  * {
    transition: none !important;
  }

  &.ant-menu.ant-menu-dark {
    background: ${theme.colors.gray['900']} !important;

    & > .ant-menu-item,
    & > .ant-menu-submenu {
      margin-bottom: 24px !important;
    }

    & > .ant-menu-item,
    & > .ant-menu-submenu .ant-menu-submenu-title {
      border-left-color: transparent;
      border-left-width: 4px;
      border-left-style: solid;
    }

    & > .ant-menu-item,
    .ant-menu-submenu-title {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.ant-menu.ant-menu-inline-collapsed > .ant-menu-item .ant-menu-item-icon + span,
    &.ant-menu.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title .ant-menu-item-icon + span {
      display: none;
    }
  }

  &.ant-menu-dark .ant-menu-sub {
    background: ${theme.colors.gray['900']} !important;
  }

  & > .ant-menu-item-selected {
    color: ${theme.colors.primary} !important;
    background: transparent !important;
    border-radius: 0 !important;
  }

  & > .ant-menu-submenu-selected {
    .ant-menu-submenu-title {
      border-left-color: ${theme.colors.primary} !important;
      border-radius: 0 !important;
    }
  }

  &.ant-menu-inline-collapsed {
    & > .ant-menu-item,
    .ant-menu-submenu-title {
      padding: 0 !important;
    }
  }

  .ant-menu-submenu-selected,
  .ant-menu-item-selected:not(.ant-menu-item-only-child) {
    color: ${theme.colors.primary};
    border-left-color: ${theme.colors.primary} !important;
  }
  &.ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon {
    color: ${theme.colors.primary};
  }
`;

export const Main = styled.div`
  position: relative;
  flex: 1;
  height: 100vh;
  overflow-y: auto;

  ${scrollBarStyles};

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
  }
  &::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(0, 0, 0, 0.2);
  }
`;

export const Brand = styled(HorCenter)`
  height: 120px;
  margin-bottom: 40px;
  font-size: 18px;
  color: white;
  background: ${getColorFromTheme('primary')};
  transition: all 0.3s;
`;

export const LogoText = styled(Center)`
  flex: 1;
  font-weight: bold;
`;

export const Tag = styled(Center)<{ type?: 'default' | 'primary' | 'danger' }>`
  display: inline-flex;
  padding: 2px 4px;
  font-size: 14px;
  line-height: 1;
  color: white;
  vertical-align: middle;
  border-radius: 2px;

  background: ${(props) => {
    return props.theme.colors[props.type || 'other'] || props.theme.colors.gray['838485'];
  }};
`;

export const TriangleIcon = styled.div<{
  size?: number;
  borderColor: string;
  type: 'up' | 'down';
}>`
  display: inline-block;
  width: 0;
  height: 0;
  border-left: ${(props) => props.size || 10}px solid transparent;
  border-right: ${(props) => props.size || 10}px solid transparent;
  transition: transform 0.3s;

  ${(props) => {
    return props.type === 'up'
      ? css`
          border-bottom: ${(props.size || 10) * 1.5}px solid ${props.borderColor};
        `
      : css`
          border-top: ${(props.size || 10) * 1.5}px solid ${props.borderColor};
        `;
  }}
`;

export const StyledModal = styled(Modal)`
  .ant-modal-close-x {
    display: none;
  }

  .ant-modal-content {
    color: ${getColorFromTheme('gray.c1c1c4')};
    background-color: ${getColorFromTheme('gray.25262d')};
    border-radius: 10px;
  }

  .ant-modal-body {
    padding: 0;
  }

  input::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
`;

export const BaseButton = styled.button`
  position: relative;
  display: inline-block;
  min-width: 72px;
  min-height: 32px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  line-height: 1.5;
  white-space: nowrap;
  color: #000;
  background: white;
  border-radius: 4px;
  user-select: none;
  cursor: pointer;
  transition: all 0.2s ease-in;
`;

export const StyledButton = styled(BaseButton)<{
  variant?: 'primary' | 'danger';
  outline?: boolean;
}>`
  color: ${(props) => {
    if (props.variant) {
      return props.theme.colors.white;
    }

    return props.theme.colors.gray.c1c1c4;
  }};
  background: ${(props) => {
    if (props.variant) {
      return props.theme.colors[props.variant || 'primary'];
    }

    return props.theme.colors.gray['31333d'];
  }};

  &:not(:disabled):hover {
    background: ${(props) => {
      if (props.variant) {
        return props.variant === 'primary' ? props.theme.colors.hoveredPrimary : props.theme.colors.hoveredDanger;
      }

      return props.theme.colors.hoveredGray;
    }};
  }
`;

export const SmStyledButton = styled(StyledButton)`
  min-height: 24px;
  min-width: 40px;
  font-size: 12px;
  padding: 4px 8px;
`;

export const OutlinedButton = styled(BaseButton)<{
  variant?: 'primary' | 'white' | 'danger';
}>`
  ${({ theme: themeProps, variant }) => css`
    width: 108px;
    height: 32px;
    color: ${themeProps.colors[variant || 'primary']};
    border-color: ${themeProps.colors[variant || 'primary']};
    background-color: transparent;
    border-radius: 18px;

    &:hover {
      color: ${variant === 'white' ? '#333' : 'white'};
      background-color: ${themeProps.colors[variant || 'primary']};
    }
  `}
`;

OutlinedButton.defaultProps = {
  variant: 'primary',
};

export const OutlineIconBtn = styled(OutlinedButton)`
  width: 32px;
  height: 32px;
  min-width: 32px;
  min-height: 32px;
  padding: 0;
`;

export const StyledCheckbox = styled.div<{
  checked: boolean;
}>`
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 2px;
  background: ${(props) => {
    return props.checked ? props.theme.colors.primary : props.theme.colors.white;
  }};
  border: 1px solid
    ${(props) => {
      return props.checked ? props.theme.colors.primary : props.theme.colors.white;
    }};
`;

// 页面title、面包屑及头像区域
export const PHContainer = styled(HorCenter)``;
export const PhLeft = styled(HorCenter)``;
export const PTitle = styled.h1`
  margin: 0 8px 0 0;
  font-size: 20px;
  font-weight: 700;
  color: #333;
`;
export const PBreadcrumb = styled(HorCenter)`
  .icomoon {
    padding: 0 6px;
    color: #aaa;
    font-size: 10px;
    transform: scale(0.8);
  }
  .active {
    color: ${getColorFromTheme('primary')};
  }
`;

export const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;

  img {
    display: block;
    width: 100%;
  }
`;

export const PAvatarContainer = styled(HorCenter)`
  cursor: pointer;

  ${Avatar} {
    margin-right: 16px;
  }
`;

export const PUserInfo = styled.div`
  p {
    margin: 0;
    color: #999;
    font-size: 12px;
  }

  h1 {
    margin-top: 2px;
    margin-bottom: 0;
    font-weight: 700;
    font-size: 16px;

    .icomoon {
      margin-left: 12px;
    }
  }
`;

// 除首页外main
export const PageMain = styled.div`
  min-height: 100vh;
  flex: 1;
  padding: 30px 30px 0 30px;
`;

export const Text = styled.span<{
  fontSize?: string;
  bold?: number;
  color?: '';
}>`
  color: ${(props) => props.color || 'inherit'};

  ${(props) => {
    return (
      props.fontSize &&
      css`
        font-size: ${props.fontSize};
      `
    );
  }}
  ${(props) => {
    return (
      props.bold &&
      css`
        font-weight: 700;
      `
    );
  }};
`;

export const SoftTag = styled(Tag)<{
  colorType?: 'danger' | 'orange';
}>`
  background: ${(props) => {
    return props.colorType === 'orange' ? props.theme.colors.lightOrange : props.theme.colors.lightDanger;
  }};
  color: ${(props) => {
    return props.colorType === 'orange' ? props.theme.colors.orange : props.theme.colors.danger;
  }};
`;

/**
 * 带有蓝色左边框的标题
 */
export const MuTitle = styled(HorCenter)<{
  paddingLeft?: string;
}>`
  margin: 0;
  padding-left: ${(props) => props.paddingLeft || '12px'};
  font-size: 18px;
  line-height: 18px;
  font-weight: 700;
  border-left: 3px solid ${getColorFromTheme('primary')};
`;

export const BaseGrayContainer = styled.div`
  background: ${getColorFromTheme('gray.f7f7f9')};
  border-radius: 4px;
`;

export const SpinContainer = styled(Center)<{ padding?: string }>`
  padding: ${(props) => (props.padding ? props.padding : '20px')}}
`;

export const TableActionLink = styled.span<{
  color?: string;
  disabled?: boolean;
}>`
  margin-right: 12px;
  color: ${({ color, theme: themeProps, disabled }) => {
    if (disabled) {
      return '#999';
    }
    return color ? themeProps.colors[color] : '#333';
  }};
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
`;

export const TabLinkInTitle = styled.span<{ active?: boolean }>`
  padding-left: 20px;
  color: ${({ theme: themeProps, active }) => (active ? themeProps.colors.primary : '#333')};
  cursor: pointer;
`;

export const Block = styled.div<{
  padding?: string;
  margin?: string;
}>`
  ${({ padding, margin }) => css`
    padding: ${padding};
    margin: ${margin};
  `}
`;

export const GoToFireAdmin = styled(Flex)`
  padding: 8px 4px 0;
  cursor: pointer;
  opacity: 0.8;

  &:hover {
    color: white;
    opacity: 1;
  }

  ${Center} {
    width: 80px;
    margin-bottom: 40px;
    padding-left: 4px;

    .icomoon {
      width: 30px !important;
      font-size: 30px !important;
    }
  }
`;

export const TitleWithDownload = styled(HorCenter)`
  margin-bottom: 20px;

  a {
    line-height: 1;
  }

  .icomoon {
    margin-left: 30px;
  }
`;

export const TagInTable = styled(Tag)`
  flex-shrink: 0;
  margin-left: 8px;
`;

export const FilterContainer = styled(Row)`
  margin: 30px 0 20px;
`;

export const FormContainer = styled.div`
  padding: 0 10px;
  margin-bottom: 30px;
`;

export const Bcard = styled(animated.div)`
  background: ${getColorFromTheme('gray.900')};
  border-radius: 6px;
  position: absolute;
  z-index: 1000;
`;

export const DarkContainer = styled.div`
  padding: 12px 20px;
  background: #1c1d24;

  .dark-form-item.ant-input,
  .dark-form-item .ant-input,
  .dark-form-item.ant-cascader-picker,
  .dark-form-item.ant-select:not(.ant-select-customize-input) .ant-select-selector,
  .dark-form-item.ant-picker {
    background: #25262d !important;
  }
`;

export const FailedMessage = styled.div<{
  width?: string;
}>`
  padding-left: 8px;
  color: #999;
  margin-left: 8px;
  border-left: 1px solid #ccc;
  line-height: 1;
  cursor: default;

  ${({ width }) => {
    return width
      ? css`
          width: ${width};

          ${ellipsisCss};
        `
      : null;
  }}
`;

export const CompassContainer = styled(Bcard)`
  right: 42px;
  bottom: 400px;
  width: 60px;
  height: 60px;
  background: transparent;
  cursor: pointer;

  img {
    width: 100%;
  }
`;

export const IconBtn = styled(Center)<{
  active?: boolean;
}>`
  user-select: none;
  cursor: pointer;

  color: ${(props) => (props.active ? props.theme.colors.primary : getColorFromTheme('gray.500'))};

  &:hover {
    color: ${(props) => (props.active ? props.theme.colors.primary : getColorFromTheme('white'))};
  }

  .icomoon {
    font-size: 20px;

    &.icon-zoomin,
    &.icon-zoomout {
      font-size: 16px;
    }
  }
`;
export const BaseContainer = styled(Bcard)`
  padding: 0 4px;
  border-radius: 4px;

  ${IconBtn} {
    width: 32px;
    height: 40px;

    &:last-child {
      border-top: 1px solid ${getColorFromTheme('gray.800')};
    }
  }
`;

export const MeasureContainer = styled(BaseContainer)`
  bottom: 304px;
  right: 52px;
`;

export const ZoomContainer = styled(BaseContainer)`
  bottom: 164px;
  right: 52px;

  ${IconBtn} {
    &:nth-child(2) {
      border-top: 1px solid ${getColorFromTheme('gray.800')};
    }
  }
`;

export const MapCoor = styled(Bcard)`
  display: flex;
  left: 10px;
  bottom: 10px;
  z-index: 99;
  color: white;
  border-radius: 2px;
  overflow: hidden;

  span {
    padding: 0 10px;
    color: ${getColorFromTheme('gray.c1c1c4')};
    font-size: 12px;
  }
`;
export const MapCoorIcon = styled(Center)`
  width: 20px;
  height: 20px;
  background: ${getColorFromTheme('primary')};
  border-radius: 2px;
`;

export const AreaRect = styled.div`
  position: absolute;
  z-index: 10;
  background: rgba(40, 108, 255, 0.2);
  pointer-events: none;
  color: white;
  border: 2px dotted ${getColorFromTheme('primary')};

  .icomoon {
    margin-left: 4px;
  }

  .content {
    position: absolute;
    top: -20px;
    left: 0;
    display: flex;
    align-items: center;
    padding: 4px;
    font-size: 12px;
    line-height: 1;
    white-space: nowrap;
    background: ${getColorFromTheme('primary')};
    border-radius: 2px 2px 0 0;
    cursor: pointer;
    pointer-events: initial;
  }
`;

export const FilterTreeContainer = styled.div`
  padding: 12px 12px 12px 4px;
  max-height: 400px;
  overflow-y: auto;
  transition: all 0.3s;

  ${scrollBarStyles};

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
  }
  &::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(0, 0, 0, 0.2);
  }
`;

export const FilterTreeBtns = styled(Flex)`
  justify-content: space-between;
  padding: 7px 8px 7px 3px;
  overflow: hidden;
  background-color: inherit;
  border-top: 1px solid #f0f0f0;
`;

export const MapLocMarker = styled.div<{ active?: boolean }>`
  position: absolute;
  z-index: 11;
  width: 34px;
  height: 44px;
  font-size: 16px;
  font-family: Arial, Helvetica, sans-serif;
  line-height: 34px;
  text-align: center;
  color: white;
  transform: translate(-50%, -100%);

  ${(props) => {
    const color = props.active ? 'red' : 'blue';
    return css`
      background: url('/assets/images/${color}-marker.png');
    `;
  }}
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
`;

export const MapOverlay = styled.div<{
  width?: string;
}>`
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 12;
  min-width: 220px;
  padding: 10px;
  background: ${getColorFromTheme('gray.25262d')};
  border-radius: 4px;
  color: ${getColorFromTheme('gray.c1c1c4')};
  transform: translate(-50%, -120%);
  pointer-events: none;

  ${({ width }) =>
    width &&
    css`
      width: ${width};
    `}

  * {
    flex-shrink: 0;
  }

  &::after {
    position: absolute;
    bottom: -8px;
    left: 50%;
    display: block;
    width: 0;
    height: 0;
    border-top: 8px solid ${getColorFromTheme('gray.25262d')};
    border-right: 8px solid transparent;
    border-left: 8px solid transparent;
    transform: translateX(-50%);
    content: '';
  }
`;

export const OverlayTop = styled.div`
  .region,
  .time {
    color: white;
  }

  h1 {
    margin: 0 12px 0 0;
    color: white;
    font-weight: 700;
    font-size: 28px;
  }

  span {
    color: ${getColorFromTheme('gray.c1c1c4')};
    font-size: 12px;

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 6px;
      background: linear-gradient(180deg, #ff6784 0%, #f6903d 100%);
      border-radius: 3px;
    }
  }
`;
export const OverlayBottom = styled(HorCenter)`
  padding-top: 12px;
  font-size: 12px;
  border-top: 1px solid ${getColorFromTheme('gray.1c1d24')};

  * + * {
    margin-left: 6px;
  }

  .danger {
    color: ${getColorFromTheme('danger')};
  }

  ${Tag} {
    padding: 4px;
    font-size: 12px;
  }
`;

export const DisMarker = styled(HorCenter)`
  position: absolute;
  z-index: 10;
  padding: 4px;
  background: ${getColorFromTheme('primary')};
  color: white;
  font-size: 12px;
  line-height: 1;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;

  .icomoon {
    margin-left: 8px;
  }
`;

export const PoCreateTitle = styled(MuTitle)`
  padding-top: 24px;
  padding-left: 24px;
  border-left: none;

  span {
    display: block;
    height: 18px;
    padding-left: 12px;
    color: white;
    font-size: 18px;
    border-left: 3px solid #286cff;
  }
`;

export const DdModalTitle = styled(PoCreateTitle)`
  padding: 30px;
`;

export const MapPoMarker = styled.div<{ active?: boolean }>`
  position: absolute;
  z-index: 11;
  width: 34px;
  height: 44px;
  font-size: 22px;
  line-height: 30px;
  text-align: center;
  color: white;
  ${(props) => {
    const color = props.active ? 'red' : 'blue';
    return css`
      background: url('/assets/map/route-${color}-marker.png');
    `;
  }}
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  transform: translate(-50%, -100%);
  transform-origin: center bottom;
  transition: transform 0.2s;
`;

// @ts-ignore
export const StyledCascader = styled(Cascader)`
  width: 160px;
  color: white;

  .ant-cascader-menu-item {
    color: white !important;
  }

  .ant-cascader-menu-item-active:not(#index .ant-cascader-menu-item-disabled) {
    color: white !important;

  }

  &,
  .ant-cascader-input,
  .ant-cascader-picker-label {
    font-size: 16px;
  }
`;

export const DdModalBody = styled.div`
  padding: 0 30px;
`;

export const DueDiModal = styled(DarkModal)``;

export const DdModalFooter = styled(HorCenter)`
  padding: 24px 30px;

  ${StyledButton} {
    margin-left: 20px;
  }
`;
export const DdModalInputGroup = styled(HorCenter)`
  position: relative;
  width: 136px;
  height: 32px;
  margin-right: 12px;
  margin-bottom: 12px;

  &:nth-child(4n + 0) {
    margin-right: 0;
  }

  span {
    position: absolute;
    top: 50%;
    left: 12px;
    z-index: 12;
    display: block;
    width: 42px;
    color: #999;
    font-size: 12px;
    transform: translateY(-50%);
  }

  .ant-input {
    background: ${getColorFromTheme('gray.1c1d24')};
    border-color: transparent;
    border-radius: 4px;

    &:focus {
      border: 1px solid ${getColorFromTheme('primary')};
    }
  }
`;

export const DdModalInputList = styled(Flex)`
  margin-top: 20px;
  margin-bottom: 12px;
  flex-wrap: wrap;

  .item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 136px;
    height: 32px;
    margin-right: 12px;
    margin-bottom: 12px;

    &:nth-child(4n + 0) {
      margin-right: 0;
    }

    span {
      position: absolute;
      top: 50%;
      left: 12px;
      z-index: 12;
      display: block;
      width: 42px;
      color: #999;
      font-size: 12px;
      transform: translateY(-50%);
    }

    .ant-input {
      background: ${getColorFromTheme('gray.1c1d24')};
      border-color: transparent;
      border-radius: 4px;

      &:focus {
        border: 1px solid ${getColorFromTheme('primary')};
      }
    }
  }
`;

export const DdModalInputContent = styled.div`
  padding: 12px;
  background: ${getColorFromTheme('gray.1c1d24')};
  color: #c1c1c4;

  div {
    font-size: 12px;
  }

  textarea {
    width: 100%;
    padding: 12px 0;
    background: transparent;
    border: none;
    outline: none;
    resize: none;

    ${scrollBarStyles};
  }
`;

export const DdModalControls = styled(HorCenter)`
  padding-top: 12px;
`;

export const DdModalCtrlLeft = styled.div`
  flex: 1;
  height: 90px;
  overflow: hidden;

  .ant-upload.ant-upload-select-picture-card {
    width: 80px;
    height: 80px;
  }

  .ant-upload-list-picture-card-container {
    width: 80px;
    height: 80px;
  }
`;

export const DdModalCtrlRight = styled.div`
  flex: 1;
`;

export const DdModalCtrlRightBottom = styled(Flex)`
  margin-top: 10px;
`;
export const DdModalUserBox = styled(HorCenter)`
  flex: 1;
  height: 36px;
  padding: 12px;
  margin-left: 12px;
  background: ${getColorFromTheme('gray.1c1d24')};
  border-radius: 2px;
  color: white;

  input {
    margin-left: 8px;
    background: transparent;
    border: none;
    outline: none;
  }
`;

export const PreviewModal = styled(DarkModal)``;
export const PreviewModalBody = styled.div`
  padding: 8px 30px;
`;
export const PreviewModalTitle = styled(DdModalTitle)``;
export const PreviewModalFooter = styled.div`
  padding: 24px 30px;
  text-align: right;
`;

export const MenuContainer = styled(animated.div)`
  position: absolute;
  bottom: 110px;
  left: 50%;
  font-size: 12px;
  border-radius: 5px;
  background: ${getColorFromTheme('gray.25262d')};
  transform: translateX(-50%);
  z-index: 5;

  &::after {
    position: absolute;
    bottom: -8px;
    left: 50%;
    z-index: 1;
    display: block;
    width: 0;
    height: 0;
    border-top: 20px solid ${getColorFromTheme('gray.25262d')};
    border-right: 14px solid transparent;
    border-left: 14px solid transparent;
    transform: translateX(-50%);
    content: '';
  }

  ${Center} {
    padding: 10px 0;
    color: ${getColorFromTheme('gray.c1c1c4')};
    background: ${getColorFromTheme('gray.1c1d24')};
    border-radius: 5px 5px 0 0;

    .icomoon {
      margin-right: 4px;
    }
  }

  ul {
    padding: 0 8px;

    li {
      border-radius: 1px;
    }
  }
`;

export const LayerMenu = styled.div`
  padding: 0 8px;
`;

export const LayerMenuItem = styled.div<{ active?: boolean }>`
  position: relative;
  z-index: 10;
  display: block;
  width: 60px;
  height: 20px;
  margin: 8px 0;
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  color: ${(props) => (props.active ? props.theme.colors.white : props.theme.colors.gray.c1c1c4)};
  background: ${(props) => (props.active ? props.theme.colors.primary : 'transparent')};
  border-radius: 1px;

  &:hover {
    color: ${(p) => (p.active ? p.theme.colors.white : p.theme.colors.primary)};
  }
`;

export const LayerBtn = styled(Center)<{ active?: boolean }>`
  position: relative;
  width: 36px;
  height: 36px;
  margin-right: 8px;
  font-size: 20px;
  border-radius: 4px;
  color: ${(props) => {
    return props.active ? 'white' : props.theme.colors.gray['400'];
  }};
  background-color: ${(props) => {
    return props.active ? props.theme.colors.primary : props.theme.colors.gray['800'];
  }};
  cursor: pointer;

  &:last-child {
    margin-right: 0;
  }
`;

export const DdModalSubTitle = styled(HorCenter)`
  h1 {
    margin: 0;
    color: white;
    font-size: 18px;
  }

  span {
    padding-right: 12px;
    color: ${getColorFromTheme('gray.c1c1c4')};
    font-size: 12px;
  }
`;

export const InspectModalBody = styled.div`
  padding: 24px 24px 0;
  color: white;

  .title,
  .desc,
  .inq,
  .sub-title {
    text-align: center;
  }

  .title {
    margin-top: 8px;
    margin-bottom: 8px;
    font-size: 20px;
  }

  .desc {
    color: ${getColorFromTheme('gray.c1c1c4')};
    font-size: 12px;
  }

  .inq {
    margin-top: 12px;
    margin-bottom: 24px;
    font-size: 16px;
  }

  .sub-title {
    margin-bottom: 12px;
    font-size: 16px;
  }

  .count {
    margin-bottom: 12px;
    color: ${getColorFromTheme('gray.c1c1c4')};
    text-align: right;
  }
`;

export const InspectModalFooter = styled(Flex)`
  padding: 20px 24px;
  border-top: 1px solid ${getColorFromTheme('gray.1c1d24')};

  ${BaseButton} {
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }
`;

export const InspectModalTextarea = styled.textarea`
  width: 100%;
  resize: none;
  border: none;
  background: transparent;
  outline: none;

  ${scrollBarStyles}
`;
export const TextareaErrHelper = styled.p`
  font-size: 14px;
  color: red;
`;

export const ItemContainer = styled.div`
  margin-bottom: 12px;
`;

export const PageNotFoundContainer = styled(Center)`
  min-height: 100vh;
  font-size: 160px;
  font-weight: 700;
  color: #ccc;
  padding-bottom: 100px;
`;

export const ModalContent = styled.div`
  padding: 30px;
`;
