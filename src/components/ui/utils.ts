import get from 'lodash/get';
import type { DefaultTheme } from 'styled-components';

/**
 * styled-components theme 中取值
 * @param path 取值路径
 * @param fallback 取值失败默认值
 * @returns
 */
export const getValFromTheme: (
  path: string,
  fallback?: string | number,
) => any =
  (path: string, fallback?: string | number) =>
  ({ theme }: { theme: DefaultTheme }) => {
    const result = get(theme, path, fallback);
    return result;
  };

export const getColorFromTheme: (
  path: string,
  fallback?: string | number,
) => any = (path: string, fallback?: string | number) => {
  const result = getValFromTheme(`colors.${path}`, fallback);
  return result;
};

export const getDarkContainer = () =>
  document.getElementById('dark-container') as HTMLDivElement;
