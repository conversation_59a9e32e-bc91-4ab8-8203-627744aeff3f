/* eslint-disable max-len */
import { css } from 'styled-components';

export const darkContainerStyles = css`
  .ant-picker-time-panel {
    width: auto;
    min-width: auto;
  }
  .ant-picker-time-panel .ant-picker-content {
    display: flex;
    flex: auto;
    height: 224px;
  }
  .ant-picker-time-panel-column {
    flex: 1 0 auto;
    width: 56px;
    margin: 0;
    padding: 0;
    overflow-y: hidden;
    text-align: left;
    list-style: none;
    transition: background 0.3s;
  }
  .ant-picker-time-panel-column::after {
    display: block;
    height: 196px;
    content: '';
  }
  .ant-picker-time-panel-column:not(:first-child) {
    border-left: 1px solid #303030;
  }
  .ant-picker-time-panel-column:hover {
    overflow-y: auto;
  }
  .ant-picker-time-panel-column > li {
    margin: 0;
    padding: 0;
  }
  .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
    display: block;
    width: 100%;
    height: 28px;
    margin: 0;
    padding: 0 0 0 14px;
    color: rgba(255, 255, 255, 0.85);
    line-height: 28px;
    border-radius: 0;
    cursor: pointer;
    transition: background 0.3s;
  }
  .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background: #111b26;
  }

  .ant-picker-week-panel-row:hover td {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-select {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-select-dropdown {
    color: rgba(255, 255, 255, 0.85);

    background-color: #1f1f1f;

    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  .ant-select-item {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    background-color: rgba(255, 255, 255, 0.08);
  }
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color: rgba(255, 255, 255, 0.85);
    background-color: #172d5c;
  }
  .ant-dropdown {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-list {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-list-item {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-menu {
    color: rgba(255, 255, 255, 0.85);
    background: #141414;
  }
  .ant-menu-item:active {
    background: #172d5c;
  }
  .ant-menu-item a {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-menu-item a:hover {
    color: #286cff;
  }
  .ant-menu-item a::before {
    background-color: transparent;
  }
  .ant-menu-item:focus-visible {
    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
  }
  .ant-cascader {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-cascader-menus {
    position: absolute;
    z-index: 1050;
    font-size: 14px;
    white-space: nowrap;
    background: #1f1f1f;
    border-radius: 2px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  .ant-cascader-menus ul {
    margin: 0;
    list-style: none;
  }
  .ant-cascader-menus-hidden {
    display: none;
  }
  .ant-cascader-menu {
    border-right: 1px solid #303030;
  }
  .ant-cascader-menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    color: #fff !important;
    background-color: #172d5c;
  }
  .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-cascader-menu-item-disabled {
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
  }
  .ant-cascader-menu-item-disabled:hover {
    background: transparent;
  }
  .ant-cascader-menu-empty .ant-cascader-menu-item {
    color: rgba(255, 255, 255, 0.3);
    cursor: default;
    pointer-events: none;
  }
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    font-weight: 600;
    color: #fff !important;

  }

  .ant-dropdown {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-btn {
    color: rgba(255, 255, 255, 0.85);
    border: 1px solid transparent;
    border-color: #434343;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
  }
  .ant-btn > a:only-child {
    color: currentColor;
  }
  .ant-btn:focus,
  .ant-btn:hover {
    color: #165996;
    background: 0 0;
    border-color: #165996;
  }
  .ant-btn:focus > a:only-child,
  .ant-btn:hover > a:only-child {
    color: currentColor;
  }
  .ant-btn:focus > a:only-child::after,
  .ant-btn:hover > a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: 0 0;
    content: '';
  }
  .ant-btn:active {
    color: #388ed3;
    background: 0 0;
    border-color: #388ed3;
  }
  .ant-btn:active > a:only-child {
    color: currentColor;
  }
  .ant-btn:active > a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: 0 0;
    content: '';
  }
  .ant-btn[disabled],
  .ant-btn[disabled]:active,
  .ant-btn[disabled]:focus,
  .ant-btn[disabled]:hover {
    color: rgba(255, 255, 255, 0.3);
    text-shadow: none;
    background: rgba(255, 255, 255, 0.08);
    border-color: #434343;
    box-shadow: none;
  }
  .ant-btn[disabled]:active > a:only-child,
  .ant-btn[disabled]:focus > a:only-child,
  .ant-btn[disabled]:hover > a:only-child,
  .ant-btn[disabled] > a:only-child {
    color: currentColor;
  }
  .ant-btn[disabled]:active > a:only-child::after,
  .ant-btn[disabled]:focus > a:only-child::after,
  .ant-btn[disabled]:hover > a:only-child::after,
  .ant-btn[disabled] > a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: 0 0;
    content: '';
  }
  .ant-btn:active,
  .ant-btn:focus,
  .ant-btn:hover {
    text-decoration: none;
    background: 0 0;
  }
  .ant-btn > span {
    display: inline-block;
  }
  .ant-btn::before {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    z-index: 1;
    display: none;
    background: #141414;
    border-radius: inherit;
    opacity: 0.35;
    transition: opacity 0.2s;
    content: '';
    pointer-events: none;
  }
  .ant-btn:active > span,
  .ant-btn:focus > span {
    position: relative;
  }
  .ant-btn:empty {
    display: inline-block;
    width: 0;
    visibility: hidden;
    content: '\a0';
  }
  a.ant-btn {
    padding-top: 0.01px !important;
    line-height: 30px;
  }
  .ant-picker {
    color: rgba(255, 255, 255, 0.85);
    border: 1px solid #434343;
  }
  .ant-picker:hover {
    border-color: #165996;
  }
  .ant-picker-dropdown {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-picker-panel-container {
    background: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }

  .ant-picker-panel {
    background: #1f1f1f;
    border: 1px solid #303030;
  }

  .ant-picker-header {
    color: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid #303030;
  }

  .ant-picker-header button {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-header > button:hover {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-picker-header-view button:hover {
    color: #286cff;
  }
  .ant-picker-next-icon::before,
  .ant-picker-prev-icon::before,
  .ant-picker-super-next-icon::before,
  .ant-picker-super-prev-icon::before {
    /* border: 0 solid currentColor; */
  }
  .ant-picker-content th {
    height: 30px;
    color: rgba(255, 255, 255, 0.85);
    line-height: 30px;
  }
  .ant-picker-cell {
    padding: 3px 0;
    color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
  }
  .ant-picker-cell-in-view {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(
      .ant-picker-cell-range-hover-start
    ):not(.ant-picker-cell-range-hover-end)
    .ant-picker-cell-inner {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    border: 1px solid #286cff;
  }
  .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner::after,
  .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner::after {
    background: #286cff;
  }
  .ant-picker-panel .ant-picker-footer {
    border-top: 1px solid #303030;
  }
  .ant-picker-today-btn {
    color: #286cff;
  }
  .ant-picker-decade-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
  .ant-picker-year-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
  .ant-picker-quarter-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
  .ant-picker-month-panel .ant-picker-cell-disabled .ant-picker-cell-inner {
    background: #303030;
  }
  .ant-picker-today-btn:hover {
    color: #165996;
  }
  .ant-picker-today-btn:active {
    color: #388ed3;
  }
  .ant-table {
    color: rgba(255, 255, 255, 0.85);
    background: #141414;
  }
  .ant-table-footer {
    color: rgba(255, 255, 255, 0.85);
    background: rgba(255, 255, 255, 0.04);
  }
  .ant-table-thead > tr > th {
    color: rgba(255, 255, 255, 0.85);
    background: #1d1d1d;
    border-bottom: 1px solid #303030;
  }
  .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
    background-color: rgba(255, 255, 255, 0.08);
    transition: background-color 0.3s;
  }
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #303030;
    transition: background 0.3s;
  }

  .ant-dropdown {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-dropdown-placement-topLeft > .ant-dropdown-arrow {
    border-right-color: #1f1f1f;
    border-bottom-color: #1f1f1f;
  }

  .ant-btn {
    color: rgba(255, 255, 255, 0.85);
    background: 0 0;
    border-color: #434343;
  }
  .ant-btn:focus,
  .ant-btn:hover {
    color: #165996;
    border-color: #165996;
  }
  .ant-btn:active {
    color: #388ed3;
    border-color: #388ed3;
  }
  .ant-btn[disabled],
  .ant-btn[disabled]:active,
  .ant-btn[disabled]:focus,
  .ant-btn[disabled]:hover {
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: #434343;
  }

  .ant-btn::before {
    background: #141414;
  }

  .ant-picker {
    color: rgba(255, 255, 255, 0.85);

    border: 1px solid #434343;
  }
  .ant-picker:hover {
    border-color: #165996;
  }

  .ant-picker-dropdown {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-picker-range-arrow {
    box-shadow: 2px -2px 6px rgba(0, 0, 0, 0.06);
  }
  .ant-picker-range-arrow::after {
    border: 5px solid #303030;
    border-color: #1f1f1f #1f1f1f transparent transparent;
  }
  .ant-picker-panel-container {
    background: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }

  .ant-picker-panel {
    background: #1f1f1f;
    border: 1px solid #303030;
  }

  .ant-picker-header {
    color: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid #303030;
  }
  .ant-picker-header button {
    color: rgba(255, 255, 255, 0.3);
  }

  .ant-picker-header > button:hover {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-picker-header-view button:hover {
    color: #286cff;
  }

  .ant-picker-content th {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-picker-cell {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-cell-in-view {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(
      .ant-picker-cell-range-hover-start
    ):not(.ant-picker-cell-range-hover-end)
    .ant-picker-cell-inner {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-cell-in-view.ant-picker-cell-in-range {
    position: relative;
  }
  .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
    background: #172d5c;
  }
  .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner {
    color: #fff;
    background: #286cff;
  }
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before {
    background: #172d5c;
  }

  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start::before {
    background: #06213a;
  }

  .ant-table {
    color: rgba(255, 255, 255, 0.85);
    background: #141414;
  }

  .ant-table-thead > tr > th {
    color: rgba(255, 255, 255, 0.85);
    background: #1d1d1d;
    border-bottom: 1px solid #303030;
  }
  .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
    background-color: rgba(255, 255, 255, 0.08);
  }
  .ant-table-thead > tr:not(:last-child) > th[colspan] {
    border-bottom: 0;
  }
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #303030;
  }
  .ant-picker-cell-disabled .ant-picker-cell-inner {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-cell-disabled::before {
    background: #303030;
  }

  .ant-checkbox {
    position: relative;
    top: 0.2em;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    line-height: 1;
    white-space: nowrap;
    list-style: none;
    outline: none;
    cursor: pointer;
    font-feature-settings: 'tnum';
  }
  .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .ant-checkbox:hover .ant-checkbox-inner,
  .ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: #286cff;
  }
  .ant-checkbox-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #286cff;
    border-radius: 2px;
    visibility: hidden;
    -webkit-animation: antCheckboxEffect 0.36s ease-in-out;
    animation: antCheckboxEffect 0.36s ease-in-out;
    -webkit-animation-fill-mode: backwards;
    animation-fill-mode: backwards;
    content: '';
  }
  .ant-checkbox:hover::after,
  .ant-checkbox-wrapper:hover .ant-checkbox::after {
    visibility: visible;
  }
  .ant-checkbox-inner {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    direction: ltr;
    background-color: transparent;
    border: 1px solid #434343;
    border-radius: 2px;
    border-collapse: separate;
    transition: all 0.3s;
  }
  .ant-checkbox-inner::after {
    position: absolute;
    top: 50%;
    left: 22%;
    display: table;
    width: 5.71428571px;
    height: 9.14285714px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(0) translate(-50%, -50%);
    opacity: 0;
    transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    content: ' ';
  }
  .ant-checkbox-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }
  .ant-checkbox-checked .ant-checkbox-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #286cff;
    border-color: #286cff;
  }
  .ant-checkbox-disabled {
    cursor: not-allowed;
  }
  .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: rgba(255, 255, 255, 0.3);
    -webkit-animation-name: none;
    animation-name: none;
  }
  .ant-checkbox-disabled .ant-checkbox-input {
    cursor: not-allowed;
  }
  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: rgba(255, 255, 255, 0.08);
    border-color: #434343 !important;
  }
  .ant-checkbox-disabled .ant-checkbox-inner::after {
    border-color: rgba(255, 255, 255, 0.08);
    border-collapse: separate;
    -webkit-animation-name: none;
    animation-name: none;
  }
  .ant-checkbox-disabled + span {
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
  }
  .ant-checkbox-disabled:hover::after,
  .ant-checkbox-wrapper:hover .ant-checkbox-disabled::after {
    visibility: hidden;
  }
  .ant-checkbox-wrapper {
    display: inline-flex;
    align-items: baseline;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    line-height: unset;
    list-style: none;
    cursor: pointer;
    font-feature-settings: 'tnum';
  }
  .ant-checkbox-wrapper::after {
    display: inline-block;
    width: 0;
    overflow: hidden;
    content: '\a0';
  }
  .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled {
    cursor: not-allowed;
  }
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 8px;
  }
  .ant-checkbox + span {
    padding-right: 8px;
    padding-left: 8px;
  }
  .ant-checkbox-group {
    display: inline-block;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
  }
  .ant-checkbox-group-item {
    margin-right: 8px;
  }
  .ant-checkbox-group-item:last-child {
    margin-right: 0;
  }
  .ant-checkbox-group-item + .ant-checkbox-group-item {
    margin-left: 0;
  }
  .ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: transparent;
    border-color: #434343;
  }
  .ant-checkbox-indeterminate .ant-checkbox-inner::after {
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background-color: #286cff;
    border: 0;
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    content: ' ';
  }
  .ant-checkbox-indeterminate.ant-checkbox-disabled .ant-checkbox-inner::after {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
  }
  .ant-checkbox-rtl {
    direction: rtl;
  }
  .ant-checkbox-group-rtl .ant-checkbox-group-item {
    margin-right: 0;
    margin-left: 8px;
  }
  .ant-checkbox-group-rtl .ant-checkbox-group-item:last-child {
    margin-left: 0 !important;
  }
  .ant-checkbox-group-rtl .ant-checkbox-group-item + .ant-checkbox-group-item {
    margin-left: 8px;
  }

  .ant-tree-checkbox {
    position: relative;
    top: 0.2em;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    line-height: 1;
    white-space: nowrap;
    list-style: none;
    outline: none;
    cursor: pointer;
    font-feature-settings: 'tnum';
  }
  .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
  .ant-tree-checkbox:hover .ant-tree-checkbox-inner,
  .ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {
    border-color: #286cff;
  }
  .ant-tree-checkbox-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #286cff;
    border-radius: 2px;
    visibility: hidden;
    -webkit-animation: antCheckboxEffect 0.36s ease-in-out;
    animation: antCheckboxEffect 0.36s ease-in-out;
    -webkit-animation-fill-mode: backwards;
    animation-fill-mode: backwards;
    content: '';
  }
  .ant-tree-checkbox:hover::after,
  .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox::after {
    visibility: visible;
  }
  .ant-tree-checkbox-inner {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    direction: ltr;
    background-color: transparent;
    border: 1px solid #434343;
    border-radius: 2px;
    border-collapse: separate;
    transition: all 0.3s;
  }
  .ant-tree-checkbox-inner::after {
    position: absolute;
    top: 50%;
    left: 22%;
    display: table;
    width: 5.71428571px;
    height: 9.14285714px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(0) translate(-50%, -50%);
    opacity: 0;
    transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    content: ' ';
  }
  .ant-tree-checkbox-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }
  .ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
  }
  .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
    background-color: #286cff;
    border-color: #286cff;
  }
  .ant-tree-checkbox-disabled {
    cursor: not-allowed;
  }
  .ant-tree-checkbox-disabled.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
    border-color: rgba(255, 255, 255, 0.3);
    -webkit-animation-name: none;
    animation-name: none;
  }
  .ant-tree-checkbox-disabled .ant-tree-checkbox-input {
    cursor: not-allowed;
  }
  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
    background-color: rgba(255, 255, 255, 0.08);
    border-color: #434343 !important;
  }
  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {
    border-color: rgba(255, 255, 255, 0.08);
    border-collapse: separate;
    -webkit-animation-name: none;
    animation-name: none;
  }
  .ant-tree-checkbox-disabled + span {
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
  }
  .ant-tree-checkbox-disabled:hover::after,
  .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-disabled::after {
    visibility: hidden;
  }
  .ant-tree-checkbox-wrapper {
    display: inline-flex;
    align-items: baseline;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    line-height: unset;
    list-style: none;
    cursor: pointer;
    font-feature-settings: 'tnum';
  }
  .ant-tree-checkbox-wrapper::after {
    display: inline-block;
    width: 0;
    overflow: hidden;
    content: '\a0';
  }
  .ant-tree-checkbox-wrapper.ant-tree-checkbox-wrapper-disabled {
    cursor: not-allowed;
  }
  .ant-tree-checkbox-wrapper + .ant-tree-checkbox-wrapper {
    margin-left: 8px;
  }
  .ant-tree-checkbox + span {
    padding-right: 8px;
    padding-left: 8px;
  }
  .ant-tree-checkbox-group {
    display: inline-block;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
  }
  .ant-tree-checkbox-group-item {
    margin-right: 8px;
  }
  .ant-tree-checkbox-group-item:last-child {
    margin-right: 0;
  }
  .ant-tree-checkbox-group-item + .ant-tree-checkbox-group-item {
    margin-left: 0;
  }
  .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner {
    background-color: transparent;
    border-color: #434343;
  }
  .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background-color: #286cff;
    border: 0;
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    content: ' ';
  }
  .ant-tree-checkbox-indeterminate.ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .ant-tree {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    background: transparent;
    border-radius: 2px;
    transition: background-color 0.3s;
    font-feature-settings: 'tnum';
  }

  .ant-tree .ant-tree-node-content-wrapper:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }

  .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: transparent;
  }

  .ant-dropdown-menu {
    background-color: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }

  .ant-dropdown-menu-title-content > a {
    color: inherit;
  }
  .ant-dropdown-menu-title-content > a:hover {
    color: inherit;
  }

  .ant-dropdown-menu-item.ant-dropdown-menu-item-danger {
    color: #a61d24;
  }
  .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:hover {
    color: #fff;
    background-color: #a61d24;
  }
  .ant-dropdown-menu-item {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-dropdown-menu-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }

  .ant-popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1030;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 400;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    white-space: normal;
    text-align: left;
    list-style: none;
    cursor: auto;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    font-feature-settings: 'tnum';
  }
  .ant-popover::after {
    position: absolute;
    background: rgba(255, 255, 255, 0.01);
    content: '';
  }
  .ant-popover-hidden {
    display: none;
  }
  .ant-popover-placement-top {
    padding-bottom: 10px;
  }
  .ant-popover-placement-right {
    padding-left: 10px;
  }
  .ant-popover-placement-bottom {
    padding-top: 10px;
  }
  .ant-popover-placement-left,
  .ant-popover-placement-leftBottom {
    padding-right: 10px;
  }
  .ant-popover-inner {
    background-color: #1f1f1f;
    background-clip: padding-box;
    border-radius: 2px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  .ant-popover-title {
    min-width: 177px;
    min-height: 32px;
    margin: 0;
    padding: 5px 16px 4px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    border-bottom: 1px solid #303030;
  }
  .ant-popover-inner-content {
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-popover-arrow {
    position: absolute;
    display: block;
    width: 8.48528137px;
    height: 8.48528137px;
    color: transparent;
    background: 0 0;
    border-style: solid;
    border-width: 4.24264069px;
    transform: rotate(45deg);
  }
  .ant-popover-placement-top > .ant-popover-content > .ant-popover-arrow {
    bottom: 6.2px;
    border-top-color: transparent;
    border-right-color: #1f1f1f;
    border-bottom-color: #1f1f1f;
    border-left-color: transparent;
    box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
  }
  .ant-popover-placement-top > .ant-popover-content > .ant-popover-arrow {
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }
  .ant-popover-placement-right > .ant-popover-content > .ant-popover-arrow {
    left: 6px;
    border-top-color: transparent;
    border-right-color: transparent;
    border-bottom-color: #1f1f1f;
    border-left-color: #1f1f1f;
    box-shadow: -3px 3px 7px rgba(0, 0, 0, 0.07);
  }
  .ant-popover-placement-right > .ant-popover-content > .ant-popover-arrow {
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
  }
  .ant-popover-placement-bottom > .ant-popover-content > .ant-popover-arrow {
    top: 6px;
    border-top-color: #1f1f1f;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: #1f1f1f;
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
  }
  .ant-popover-placement-bottom > .ant-popover-content > .ant-popover-arrow {
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }
  .ant-popover-placement-left > .ant-popover-content > .ant-popover-arrow,
  .ant-popover-placement-leftBottom > .ant-popover-content > .ant-popover-arrow {
    right: 6px;
    border-top-color: #1f1f1f;
    border-right-color: #1f1f1f;
    border-bottom-color: transparent;
    border-left-color: transparent;
    box-shadow: 3px -3px 7px rgba(0, 0, 0, 0.07);
  }
  .ant-popover-placement-left > .ant-popover-content > .ant-popover-arrow {
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
  }
  .ant-popover-placement-leftBottom > .ant-popover-content > .ant-popover-arrow {
    bottom: 12px;
  }

  .sketch-picker {
    padding: 0 !important;
    color: white !important;
    background: #1f1f1f !important;
    box-shadow: none !important;

    label {
      color: #999 !important;
    }

    input {
      background: #111 !important;
      outline: none;
      box-shadow: none !important;
    }

    .flexbox-fix {
      border-color: #333 !important;
    }
  }
`;

export const darkFormItemStyles = css`
  &.ant-cascader-picker {
    background: #1c1d24;
  }

  &.ant-input,
  .ant-input {
    color: white !important;
    background: #1c1d24 !important;
    border-color: #1c1d24 !important;
  }
  &.ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper,
  .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:hover,
  .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input,
  .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover {
    color: white !important;
    background: #1c1d24 !important;
  }
  .ant-cascader-picker-label {
    color: #c1c1c4;
  }
  .anticon {
    color: #c1c1c4;
    background: transparent;
  }

  &.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    background: #1c1d24 !important;
    border-color: #1c1d24;
  }

  &.ant-select-multiple .ant-select-selection-item {
    color: #c1c1c4;
    background: #31333d;
    border-color: #31333d;
  }

  &.ant-select {
    color: white !important;
  }
  & .ant-select-selection-item{
    color: white !important;
  }

  .ant-picker-input > input {
    color: #c1c1c4;
  }
  &.ant-picker {
    background: #1c1d24;
    border-color: #1c1d24;
  }

  .ant-select-arrow {
    color: #c1c1c4;
  }

  .ant-select-selector {
    border-color: transparent !important;
    box-shadow: none !important;
  }

  .ant-picker-clear {
    color: rgba(255, 255, 255, 0.3);
    background: #141414 !important;
  }
  .ant-picker-clear:hover {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-upload {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-upload.ant-upload-select-picture-card {
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px dashed #434343;
  }

  .ant-upload.ant-upload-select-picture-card:hover {
    border-color: #286cff;
  }

  .ant-upload-list {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-upload-list-item-card-actions .anticon {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-upload-list-item-info .ant-upload-text-icon .anticon {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-upload-list-item:hover .ant-upload-list-item-info {
    background-color: rgba(255, 255, 255, 0.08);
  }

  .ant-upload-list-picture .ant-upload-list-item,
  .ant-upload-list-picture-card .ant-upload-list-item {
    border: 1px solid #434343;
  }
  .ant-upload-list-picture-card .ant-upload-list-item-info::before {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete,
  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete:hover,
  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye:hover {
    color: #fff;
  }

  .ant-upload .anticon-plus {
    color: ${({ theme }) => theme.colors.primary};
  }

  &.ant-input-number {
    color: rgba(255, 255, 255, 0.85);
    background-color: transparent;
    border: 1px solid #434343;
  }

  .ant-input-number:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-input-number::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-input-number:-moz-placeholder-shown {
    text-overflow: ellipsis;
  }
  .ant-input-number:-ms-input-placeholder {
    text-overflow: ellipsis;
  }
  .ant-input-number:placeholder-shown {
    text-overflow: ellipsis;
  }
  .ant-input-number:hover {
    border-color: #165996;
    border-right-width: 1px !important;
  }
  .ant-input-number:focus {
    border-color: #177ddc;
    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
  }
  .ant-input-number-disabled {
    color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.08);
    cursor: not-allowed;
    opacity: 1;
  }
  .ant-input-number-disabled:hover {
    border-color: #434343;
    border-right-width: 1px !important;
  }
  .ant-input-number[disabled] {
    color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.08);
    cursor: not-allowed;
    opacity: 1;
  }
  .ant-input-number[disabled]:hover {
    border-color: #434343;
    border-right-width: 1px !important;
  }
  .ant-input-number-sm {
    padding: 0 7px;
  }
  .ant-input-number-handler {
    position: relative;
    display: block;
    width: 100%;
    height: 50%;
    overflow: hidden;
    color: rgba(255, 255, 255, 0.45);
    font-weight: 700;
    line-height: 0;
    text-align: center;
    transition: all 0.1s linear;
  }
  .ant-input-number-handler:active {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-input-number-handler:hover .ant-input-number-handler-down-inner,
  .ant-input-number-handler:hover .ant-input-number-handler-up-inner {
    color: #165996;
  }
  .ant-input-number-handler-down-inner,
  .ant-input-number-handler-up-inner {
    color: rgba(255, 255, 255, 0.45);
  }
  .ant-input-number-handler-down-inner > *,
  .ant-input-number-handler-up-inner > * {
    line-height: 1;
  }
  .ant-input-number-handler-down-inner svg,
  .ant-input-number-handler-up-inner svg {
    display: inline-block;
  }
  .ant-input-number-handler-down-inner::before,
  .ant-input-number-handler-up-inner::before {
    display: none;
  }
  .ant-input-number-handler-down-inner .ant-input-number-handler-down-inner-icon,
  .ant-input-number-handler-down-inner .ant-input-number-handler-up-inner-icon,
  .ant-input-number-handler-up-inner .ant-input-number-handler-down-inner-icon,
  .ant-input-number-handler-up-inner .ant-input-number-handler-up-inner-icon {
    display: block;
  }
  .ant-input-number:hover {
    border-color: #165996;
    border-right-width: 1px !important;
  }
  .ant-input-number-disabled {
    color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.08);
    cursor: not-allowed;
    opacity: 1;
  }
  .ant-input-number-disabled:hover {
    border-color: #434343;
    border-right-width: 1px !important;
  }
  .ant-input-number-disabled .ant-input-number-input {
    cursor: not-allowed;
  }
  .ant-input-number-disabled .ant-input-number-handler-wrap {
    display: none;
  }
  .ant-input-number-input {
    width: 100%;
    height: 30px;
    padding: 0 11px;
    text-align: left;
    background-color: transparent;
    border: 0;
    border-radius: 2px;
    outline: 0;
    transition: all 0.3s linear;
    -moz-appearance: textfield !important;
  }
  .ant-input-number-input::-moz-placeholder {
    opacity: 1;
  }
  .ant-input-number-input:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-input-number-input::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-input-number-input:-moz-placeholder-shown {
    text-overflow: ellipsis;
  }
  .ant-input-number-input:-ms-input-placeholder {
    text-overflow: ellipsis;
  }
  .ant-input-number-input:placeholder-shown {
    text-overflow: ellipsis;
  }
  .ant-input-number-input[type='number']::-webkit-inner-spin-button,
  .ant-input-number-input[type='number']::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none;
  }
  .ant-input-number-sm {
    padding: 0;
  }
  .ant-input-number-sm input {
    height: 22px;
    padding: 0 7px;
  }
  .ant-input-number-handler-wrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 22px;
    height: 100%;
    background: #141414;
    border-left: 1px solid #434343;
    border-radius: 0 2px 2px 0;
    opacity: 0;
    transition: opacity 0.24s linear 0.1s;
  }
  .ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-down-inner,
  .ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-up-inner {
    min-width: auto;
    margin-right: 0;
    font-size: 7px;
  }
  .ant-input-number-handler-wrap:hover .ant-input-number-handler {
    height: 40%;
  }
  .ant-input-number:hover .ant-input-number-handler-wrap {
    opacity: 1;
  }
  .ant-input-number-handler-up {
    border-top-right-radius: 2px;
    cursor: pointer;
  }
  .ant-input-number-handler-up-inner {
    top: 50%;
    margin-top: -5px;
    text-align: center;
  }
  .ant-input-number-handler-up:hover {
    height: 60% !important;
  }
  .ant-input-number-handler-down {
    top: 0;
    border-top: 1px solid #434343;
    border-bottom-right-radius: 2px;
    cursor: pointer;
  }
  .ant-input-number-handler-down-inner {
    top: 50%;
    text-align: center;
    transform: translateY(-50%);
  }
  .ant-input-number-handler-down:hover {
    height: 60% !important;
  }
  .ant-input-number-handler-down-disabled,
  .ant-input-number-handler-up-disabled {
    cursor: not-allowed;
  }
  .ant-input-number-handler-down-disabled:hover .ant-input-number-handler-down-inner,
  .ant-input-number-handler-up-disabled:hover .ant-input-number-handler-up-inner {
    color: rgba(255, 255, 255, 0.3);
  }
`;
