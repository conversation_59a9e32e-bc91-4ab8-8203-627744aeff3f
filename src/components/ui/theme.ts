import type { DefaultTheme } from 'styled-components';

const theme: DefaultTheme = {
  colors: {
    black: '#000',
    white: '#fff',
    primary: '#286cff',
    hoveredPrimary: '#1c5de8',
    danger: '#f44336',
    hoveredDanger: '#dc3226',
    hoveredGray: '#2d2f3a',
    success: '',
    warning: '',
    orange: '#F48736',
    lightDanger: 'rgba(244, 67, 54, 0.06)',
    lightOrange: 'rgba(244, 135, 54, 0.06)',
    gray: {
      '1c1d24': '#1c1d24',
      '25262d': '#25262d',
      '31333d': '#31333d',
      '69696d': '#69696d',
      '838485': '#838485',
      333: '#333',
      400: '#c1c1c4',
      500: '#838485',
      666: '#666',
      800: '#25262d',
      900: '#1c1d24',
      999: '#999',
      b5b5b8: '#b5b5b8',
      c1c1c4: '#c1c1c4',
      e6e6e6: '#e6e6e6',
      efefef: '#efefef',
      f7f7f9: '#f7f7f9',
      ececef: '#ececef',
    },
  },
};

export default theme;
