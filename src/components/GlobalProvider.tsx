import { isSubscribeModalOpenAtom } from '@/atoms';
import { useSetAtom } from 'jotai';
import React, { createContext, useContext, useState } from 'react';

type States = {
  isFullScreen: boolean;
  setIsFullScreen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsSubscribeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

export const GlobalContext = createContext<States | null>(null);
export const GlobalConsumer = GlobalContext.Consumer;

export function useGlobalContext() {
  return useContext(GlobalContext)!;
}

export default function GlobalProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const setIsSubscribeModalOpen = useSetAtom(isSubscribeModalOpenAtom);

  return (
    <GlobalContext.Provider
      value={{
        isFullScreen,
        setIsFullScreen,
        setIsSubscribeModalOpen,
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
}
