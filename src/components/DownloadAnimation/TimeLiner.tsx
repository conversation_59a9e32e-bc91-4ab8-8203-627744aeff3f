import { useEffect, useMemo, useState } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import useScratch from 'react-use/lib/useScratch';
let interverl: NodeJS.Timeout | null = null;
let timePointInterverl: NodeJS.Timeout | null = null;
const barWidth = 8;
const TimeLiner = ({
  play,
  normalFrame = 19, // 补间帧
  fps = 2, // 关键帧帧率
  setTimeLineOptions,
  timeList,
  startTimePoint,
  endTimePoint,
}) => {
  const [left, setLeft] = useState(0);
  const [right, setRight] = useState(0);
  const [timePointPosition, setTimePointPosition] = useState(0);
  // 可视区长度
  const { width: scrollWidth, ref: scrollRef } = useResizeDetector();
  // 整体长度
  const { width, ref } = useResizeDetector();
  const [refLeft, stateLeft] = useScratch();
  const [refRight, stateRight] = useScratch();
  const perTimeWidth = useMemo(() => {
    return (width ?? 0) / timeList.length;
  }, [width, timeList.length]);
  // 时间列表变化
  useEffect(() => {
    if (timeList) {
      setLeft(perTimeWidth / 2 - barWidth / 2);
      setRight(perTimeWidth / 2 - barWidth / 2);
      setTimeLineOptions((p) => ({
        ...p,
        startTimePoint: timeList[0]?.time.format('YYYY/MM/DD HH:mm'),
        endTimePoint:
          timeList[timeList.length - 1]?.time.format('YYYY/MM/DD HH:mm'),
        time: timeList[0]?.time.format('YYYY/MM/DD HH:mm'),
      }));
    }
  }, [perTimeWidth, setTimeLineOptions, timeList]);
  // 拖动时滚动条滚动
  useEffect(() => {
    if (
      stateLeft.dx &&
      left - (barWidth - 1) < (scrollRef.current?.scrollLeft || 0)
    ) {
      interverl = setInterval(() => {
        scrollRef.current?.scroll({ left: scrollRef.current?.scrollLeft - 1 });
      }, 10);
    }
    if (
      stateLeft.dx &&
      left - (scrollWidth || 0) + barWidth >=
      (scrollRef.current?.scrollLeft || 0)
    ) {
      interverl = setInterval(() => {
        scrollRef.current?.scroll({ left: scrollRef.current?.scrollLeft + 1 });
      }, 10);
    }
    return () => {
      if (interverl) clearInterval(interverl);
    };
  }, [left, scrollWidth, scrollRef, stateLeft.dx]);
  useEffect(() => {
    if (
      stateRight.dx &&
      right > (width ?? 0) - scrollRef.current?.scrollLeft - barWidth
    ) {
      interverl = setInterval(() => {
        scrollRef.current?.scroll({
          left: scrollRef.current?.scrollLeft - 1,
        });
      }, 10);
    }
    if (
      stateRight.dx &&
      right < (width ?? 0) - (scrollWidth ?? 0) - scrollRef.current?.scrollLeft
    ) {
      interverl = setInterval(() => {
        scrollRef.current?.scroll({
          left: scrollRef.current?.scrollLeft + 1,
        });
      }, 10);
    }
    return () => {
      if (interverl) clearInterval(interverl);
    };
  }, [left, scrollWidth, scrollRef, stateLeft.dx, stateRight.dx, right, width]);
  // 吸附时间点同时把时间指针归零
  useEffect(() => {
    if (!stateLeft.isScratching && !stateRight.isScratching && interverl) {
      clearInterval(interverl);
    }
    const offset = (perTimeWidth - barWidth) / 2;
    if (!stateLeft.isScratching && perTimeWidth > 0) {
      setLeft((l) => {
        setTimeLineOptions((p) => ({
          ...p,
          startTimePoint:
            timeList[Math.round((l - offset) / perTimeWidth)]?.time.format(
              'YYYY/MM/DD HH:mm',
            ),
          time: timeList[Math.round((l - offset) / perTimeWidth)]?.time.format(
            'YYYY/MM/DD HH:mm',
          ),
          frame: 0,
        }));
        setTimePointPosition(0);
        return (
          (perTimeWidth &&
            Math.round((l - offset) / perTimeWidth) * perTimeWidth + offset) ||
          l
        );
      });
    }
    if (!stateRight.isScratching && perTimeWidth > 0) {
      setRight((r) => {
        setTimeLineOptions((p) => ({
          ...p,
          frame: 0,
          endTimePoint:
            timeList[
              timeList.length - 1 - Math.round((r - offset) / perTimeWidth)
            ]?.time.format('YYYY/MM/DD HH:mm'),
        }));
        setTimePointPosition(0);
        return (
          (perTimeWidth &&
            Math.round((r - offset) / perTimeWidth) * perTimeWidth + offset) ||
          r
        );
      });
    }
  }, [
    perTimeWidth,
    setTimeLineOptions,
    stateLeft.isScratching,
    stateRight.isScratching,
    timeList,
    width,
  ]);
  // 左右拖动
  useEffect(() => {
    if (
      stateLeft.isScratching &&
      stateLeft.start &&
      stateLeft.dx &&
      width &&
      !play
    ) {
      setLeft((l) =>
        Math.min(
          Math.max(
            perTimeWidth / 2 - barWidth / 2,
            l + (stateLeft.dx ?? 0),
            (scrollRef.current?.scrollLeft &&
              scrollRef.current?.scrollLeft - 1) ||
            0,
          ),
          width - right - 8,
          scrollRef.current?.scrollLeft + scrollWidth - (barWidth - 1),
        ),
      );
    }
  }, [
    perTimeWidth,
    play,
    right,
    scrollRef,
    scrollWidth,
    stateLeft.dx,
    stateLeft.isScratching,
    stateLeft.start,
    width,
  ]);
  useEffect(() => {
    if (
      stateRight.isScratching &&
      stateRight.start &&
      stateRight.dx &&
      width &&
      !play
    ) {
      setRight((r) =>
        Math.min(
          Math.max(
            perTimeWidth / 2 - barWidth / 2,
            r - (stateRight.dx ?? 0),
            width - (scrollWidth ?? 0) - scrollRef.current?.scrollLeft - 1,
          ),
          width - left - 8,
          width - scrollRef.current?.scrollLeft - (barWidth - 1),
        ),
      );
    }
  }, [
    left,
    perTimeWidth,
    play,
    scrollRef,
    scrollWidth,
    stateRight.dx,
    stateRight.isScratching,
    stateRight.start,
    width,
  ]);
  // 开始结束时间变化
  useEffect(() => {
    setLeft(
      perTimeWidth / 2 -
      barWidth / 2 +
      timeList.findIndex(
        (t) => t.time.format('YYYY/MM/DD HH:mm') === startTimePoint,
      ) *
      perTimeWidth,
    );
  }, [perTimeWidth, startTimePoint, timeList]);
  useEffect(() => {
    setRight(
      perTimeWidth / 2 -
      barWidth / 2 +
      timeList
        .concat()
        .reverse()
        .findIndex(
          (t) => t.time.format('YYYY/MM/DD HH:mm') === endTimePoint,
        ) *
      perTimeWidth,
    );
  }, [perTimeWidth, endTimePoint, timeList]);
  // 播放初始化滚动条位置
  useEffect(() => {
    if (play) {
      if (
        timePointPosition + left >
        scrollWidth + scrollRef.current?.scrollLeft
      ) {
        scrollRef.current.scroll({
          left: timePointPosition + left,
        });
      }
      if (timePointPosition + left < scrollRef.current?.scrollLeft) {
        scrollRef.current.scroll({
          left: timePointPosition + left,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [left, play, scrollRef, scrollWidth]);
  // 时间点位置
  useEffect(() => {
    if (play) {
      const perFramePixel = perTimeWidth / (normalFrame + 1);
      timePointInterverl = setInterval(
        () => {
          setTimePointPosition((prev) => {
            if ((width ?? 0) - left - right - barWidth > prev) {
              if (
                prev + perFramePixel + left >
                scrollWidth + scrollRef.current?.scrollLeft
              ) {
                scrollRef.current.scroll({
                  left: prev + perFramePixel + left,
                });
              }
              setTimeLineOptions((p) => ({
                ...p,
                frame: Math.round((prev + perFramePixel) / perFramePixel),
                time: timeList[
                  timeList.findIndex(
                    (t) =>
                      t.time.format('YYYY/MM/DD HH:mm') === p.startTimePoint,
                  ) +
                  Math.floor(
                    (prev + perFramePixel) /
                    perFramePixel /
                    (normalFrame + 1),
                  )
                ]?.time.format('YYYY/MM/DD HH:mm'),
              }));
              return prev + perFramePixel;
            } else {
              if (left < scrollRef.current?.scrollLeft) {
                scrollRef.current.scroll({
                  left,
                });
              }
              setTimeLineOptions((p) => ({
                ...p,
                frame: 0,
                time: p.startTimePoint,
              }));
              return 0;
            }
          });
        },
        1000 / fps / (normalFrame + 1),
      );
    }
    return () => {
      if (timePointInterverl) {
        clearInterval(timePointInterverl);
      }
    };
  }, [
    fps,
    left,
    normalFrame,
    perTimeWidth,
    play,
    right,
    scrollRef,
    scrollWidth,
    setTimeLineOptions,
    timeList,
    width,
  ]);
  return (
    <div className="rounded-full h-[36px] w-full bg-[#394461] px-[20px] relative">
      <div
        className="w-full overflow-y-hidden white-scrollbar h-full"
        style={{
          overflowX: play ? 'hidden' : 'auto',
        }}
        ref={scrollRef}
      >
        <div
          className="relative h-full"
          style={{
            width:
              Math.max(20, (scrollWidth ?? 0) / timeList.length) *
              timeList.length,
          }}
        >
          <div
            className="flex w-max min-w-full h-full absolute z-[3]"
            ref={ref}
          >
            {timeList.map((timeInfo) => (
              <div
                key={timeInfo.time.format('YYYY/MM/DD HH:mm')}
                className="flex-1 min-w-[20px] h-full flex items-center justify-center"
              >
                {timeInfo.time.get('minute') === 0 ? (
                  <div
                    className="rounded-full w-[10px] h-[10px]"
                    style={{
                      background: timeInfo.haveDust ? '#FFB600' : '#3DE7B1',
                    }}
                  />
                ) : (
                  <div
                    className="rounded-full w-[6px] h-[6px]"
                    style={{
                      background: timeInfo.haveDust ? '#FFB600' : '#3DE7B1',
                    }}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="absolute left-0 right-0 h-full flex z-[1] items-center overflow-visible pointer-events-none select-none">
            <div
              className="h-full"
              style={{
                width: left + 4,
              }}
            ></div>
            <div className="h-[36px] flex-1 bg-[#4353C5] border-1 border-solid border-[#206CFF]"></div>
            <div
              className="h-full"
              style={{
                width: right + 4,
              }}
            ></div>
          </div>
          <div className="absolute left-0 right-0 h-full flex z-[4] items-center overflow-visible pointer-events-none select-none">
            <div
              className="h-full"
              style={{
                width: left + 4,
                background: play ? '#394461aa' : 'none',
              }}
            ></div>
            <div className="h-full flex-1 relative overflow-hidden">
              {play && (
                <>
                  <div
                    className="absolute w-[1px] h-full bg-[#ffffff]"
                    style={{ left: timePointPosition }}
                  ></div>
                  <img
                    className="absolute w-[11px] h-[11px] bottom-0"
                    src="/assets/images/download/timePoint.png"
                    alt=""
                    style={{ left: timePointPosition - 5 }}
                  />
                </>
              )}
            </div>
            <div
              className="h-full"
              style={{
                width: right + 4,
                background: play ? '#394461aa' : 'none',
              }}
            ></div>
          </div>
          <div
            className="flex absolute justify-center items-center w-[8px] h-full cursor-[w-resize]"
            style={{
              left,
              zIndex: right === perTimeWidth / 2 - barWidth / 2 ? 6 : 5,
            }}
            ref={refLeft}
          >
            <div className="w-full h-[30px] bg-[#206CFF] rounded-full py-[3px] px-[3px]">
              <div className="w-full h-full bg-[#ffffff]"></div>
            </div>
          </div>
          <div
            className="flex absolute justify-center items-center w-[8px] h-full cursor-[w-resize]"
            style={{
              right,
              zIndex: right === perTimeWidth / 2 - barWidth / 2 ? 5 : 6,
            }}
            ref={refRight}
          >
            <div className="w-full h-[30px] bg-[#206CFF] rounded-full py-[3px] px-[3px]">
              <div className="w-full h-full bg-[#ffffff]"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default TimeLiner;
