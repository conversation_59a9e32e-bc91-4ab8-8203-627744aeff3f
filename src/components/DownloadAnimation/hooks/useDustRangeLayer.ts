import {
  hoveredSandRangeDataAtom,
  // isRemoteSensingLayerVisibleAtom,
  isSphereOfInfluenceAtom,
} from '@/components/DustDetail/atoms';
import { getDustRecordInfos } from '@/components/DustDetail/services';
import { TranslatedPathLayer } from '@/layers/dust/TranslatedPathLayer';
import { getDustRecordInfosWithoutEventId } from '@/pages/Overview/hooks/layers/useDustForecastLayer';
import { ensureArray } from '@/utils/dust';

import { GeoJsonLayer, IconLayer } from 'deck.gl';
import { Position } from 'deck.gl/typed';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
type Params = {
  isDustRangeLayerVisible: boolean;
  activeTimePoint: string | undefined;
  regionCode?: number;
  dustEventId?: number;
  isSelectTheLastPointOfTimeline: boolean;
  timeList: string[];
  setRangeLoading: (v: boolean) => void;
};

export default function useDustRangeLayer({
  activeTimePoint,
  isDustRangeLayerVisible,
  regionCode,
  dustEventId,
  isSelectTheLastPointOfTimeline,
  timeList,
  setRangeLoading,
}: Params) {
  const [pointData, setPointData] = useState<Record<string, APIDUST.TDustRecordInfoModel[]>>({});
  const setHoveredSandRangeData = useSetAtom(hoveredSandRangeDataAtom);
  const isSphereOfInfluence = useAtomValue(isSphereOfInfluenceAtom);
  // const isRemoteSensingLayerVisible = useAtomValue(
  //   isRemoteSensingLayerVisibleAtom
  // );
  const { mutate } = useMutation((dataTime: string) =>
    dustEventId
      ? getDustRecordInfos({
          dataTime,
          regionCode,
          eventId: dustEventId,
        })
      : getDustRecordInfosWithoutEventId({
          dataTime,
          regionCode: 100000,
        }),
  );
  const loadMore = useCallback(
    async (frame, loadList) => {
      if (loadList[frame]) {
        const d = await new Promise((inject) => {
          mutate(loadList[frame], {
            onSuccess: (d) => {
              inject(d);
            },
          });
        });
        // setPointData((prev) => ({ ...prev, [loadList[frame].date]: d }));
        const next = await loadMore(frame + 1, loadList);
        return {
          [loadList[frame]]: d,
          ...next,
        };
      } else {
        return {};
      }
    },
    [mutate],
  );
  useEffect(() => {
    setPointData({});
  }, [regionCode, dustEventId]);
  useEffect(() => {
    if ((!!regionCode || !!dustEventId) && isDustRangeLayerVisible && activeTimePoint) {
      let hasPoint = false;
      for (let key in pointData) {
        if (activeTimePoint === key) {
          hasPoint = true;
          break;
        }
      }
      const frame = timeList.findIndex((t) => t === activeTimePoint);
      const loadList = timeList.concat().slice(frame, timeList.length).concat(timeList.concat().slice(0, frame));
      if (hasPoint) {
        const loadingPoint = loadList.find((t) => !pointData[t]);
        if (loadingPoint) {
          mutate(loadingPoint, {
            onSuccess: (d) => {
              setPointData((prev) => ({ ...prev, [loadingPoint]: d }));
            },
          });
        }
      } else {
        setRangeLoading(true);
        loadMore(0, loadList.length > 10 ? loadList.concat().slice(0, 10) : loadList).then((d) => {
          setRangeLoading(false);
          setPointData((prev) => ({ ...prev, ...d }));
        });
      }
    }
  }, [activeTimePoint, dustEventId, isDustRangeLayerVisible, loadMore, mutate, pointData, regionCode, setRangeLoading, timeList]);
  // const { data } = useQuery(
  //   [
  //     '/api/dust/record/infos-dustDetail',
  //     activeTimePoint,
  //     regionCode,
  //     dustEventId,
  //   ],
  //   () =>
  //     getDustRecordInfos({
  //       dataTime: activeTimePoint!,
  //       regionCode,
  //       eventId: dustEventId,
  //     }),
  //   {
  //     enabled: !!activeTimePoint && (!!regionCode || !!dustEventId),
  //     keepPreviousData: true,
  //   }
  // );
  const points = useMemo(() => {
    return (
      activeTimePoint &&
      ensureArray(pointData[activeTimePoint])
        .filter((d) => (dustEventId && d.eventID === Number(dustEventId)) || !dustEventId)
        .filter((d) => d.sendSite)
        ?.map((d) => d.pointModel)
    );
  }, [activeTimePoint, dustEventId, pointData]);
  const dustGeojson = useMemo(() => {
    const formattedData =
      (activeTimePoint &&
        ensureArray(pointData[activeTimePoint])
          .filter((d) => (dustEventId && d.eventID === Number(dustEventId)) || !dustEventId)
          .map((item) => {
            return {
              geometry: JSON.parse(item.geom),
              properties: item,
              type: 'Feature',
            };
          })) ||
      [];
    return formattedData;
  }, [activeTimePoint, dustEventId, pointData]);

  // 使用PathLayer实现，需要对geojson数据进行简单格式化
  const pathData = useMemo(() => {
    return dustGeojson?.map((item) => {
      return {
        path: item.geometry.coordinates[0],
      };
    });
  }, [dustGeojson]);

  const dustRangeLayer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'dust-range-geojson-layer',
      lineWidthUnits: 'pixels',
      pickable: true,
      stroked: true,
      getLineWidth: 2,
      lineWidthMinPixels: 2,
      // @ts-ignore
      data: dustGeojson || [],
      getLineColor: [255, 240, 13, 255],

      filled: true,
      getFillColor: () => [0, 0, 0, 0],
      updateTriggers: {
        onHover: [isSelectTheLastPointOfTimeline],
      },
      visible: isDustRangeLayerVisible,
      onHover: (info: any) => {
        // 只有选择时间轴上最新的时间的时候才触发
        if (info && info.object && isSelectTheLastPointOfTimeline) {
          setHoveredSandRangeData({
            position: [info.x, info.y],
            properties: info.object.properties,
          });
        } else {
          setHoveredSandRangeData(undefined);
        }
      },
    });
  }, [dustGeojson, isDustRangeLayerVisible, isSelectTheLastPointOfTimeline, setHoveredSandRangeData]);

  const dustRangeShadowLayer = useMemo(() => {
    return new TranslatedPathLayer<{
      path: Position[];
    }>({
      id: 'dust-range-geojson-shadow-layer',
      data: pathData,
      getPath: (d) => d.path,
      getWidth: 4,
      widthMinPixels: 4,
      getColor: [0, 0, 0],
      opacity: 0.3,
      jointRounded: true,
      offsetY: -0.01,
      visible: isDustRangeLayerVisible,
    });
  }, [isDustRangeLayerVisible, pathData]);
  const startPoint = useMemo(
    () =>
      new IconLayer({
        id: 'sphere-of-influence-point-layer',
        data: points,
        getIcon: () => {
          return {
            // eslint-disable-next-line max-len
            url: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAsCAYAAADvjwGMAAAAAXNSR0IArs4c6QAABIFJREFUWEfNmFtsFGUUx/9ntrvF0t0Vb9U+qJEHeVD7YCoakxUvAdPZ4A2hIlbxVjURowYTwwsxMSZqNHgpVORiKVAttSpdagkxyMsGCQ99ERLjgyQWaq1tp1u6s5c55szuTGeHNmkn0y7z+M13/ud3/ud8+2WWstUNMVaoi8HJSu3IapTxoUwkvo3Bm4SBoNwTBP274DyUH6axxAhlIuqXDLy24AClCQ0wvUvpiNpCwKtlhgGBx8SZFr4sYJCkdKRhO4FeKaczBIwYoOWUDqs7iNBcRpi8AqhBLdFHmbC6g10wSqwOgY+akf/0EIyOX+aVkxmbF40nPjZPczqsthLhZWdGJwwGhhFofQtUe3VhS2oSube3Q7n3NtBDdyLf/AmME/3mq8CWDVBW1SMbe2O2BbRXaolnrM3iTCs7YCo+3wSlaaUtxmfPIbt86uQH+z5Efs/PpmNm8rUrkP/gAAJvrgH/dhZUt3RWMAQ6FdRSMcLxtBPmKya8NJMz7jYJjJH8Hcq6+8HHTiP3+meQAnBTDfjkGdMZ7v8TdNeykiJKrGKcD+WoniZ7/nauUzoa30nML8qi2R5nSwDwwDCQyYJuvr6kTQIZPLENRt8pKLE7TLdoaa3dJgF0t9EUYGQU0IrgeE/S3UvKROM7uQjjfFnx0/vAX4Nm5Ze0xjEX0iq6/RbkGt8rmRlnvCvpuZA2cauzPXab0lH1a2K8YC0ojQ8gsPFhsJ6Fcl9dwZmhUeRbfrTnxEruTmgNsLRJ2pZbvWXaQSagPeQYXBtGj6q7wHjeWrAFR1NTzpxsAS270RY22o5e4pj1UgCpZsnM81LcqDA2B4tHemqAo+oudsCImDgij5V0uvIsB7Or3jFfW6fQ+LV/RkdcOvaPncOZht1g2mgtWPOhrH/QhnLDyHHnwRHTOXPwm1bC/RMwbX9ci3INBOUa0Hr+kFcywLuZ2YaZjYjPe86EKoy76b9eTW7tPQw853OCOcpRT0jreYT0iLoXwLNzjJ6H7bxVnNnLlwEMAaOkRxq+AahpHkqdkyQBSdIj8TaArZuznRgTc1LxYTMpGM5kc1/I10EbF2FyuVzt4ot9533Q9yQhA7wPwAaJzub5huqJIxc8KfkQJAO8j4swISNUQ6nuf3zQ9SQhMO0MPC3RIcO4jlK9Q56UfAiSAd4P8HoThpVrafzwwn9RFguRAd7PNkzoGhrvHvahSE8SMsAHADxlOkO4Sr55PSn5ECQwBwE0mjBKcAmN/jDqg64nCRngg2zBBPQraeTYmCclH4LkOugAaJ3pTIURlavcB11PEjLAHQwuwFRWhWmoM+VJyYcgadO3DKw1Ya7IVtPg0QW/m6w6SA+r34HwpAlTrSymgcMXfSjSk0QpjFZVReic9KTkQ5DAdIKwxnRGMxYRenUfdD1JkB6OHwLxEwWYqkpCZ8aTkg9BLpgLIcLprA+6niSkTV0gPF5wZiJIOJ7zpORDEOlRtQtswdQHCFsNH3Q9SQjM92A8VnAmoZD8aVGmh/RoQzeYHpX8lVqCysRhpv0fA8v0YZEk7KoAAAAASUVORK5CYII=`,
            width: 35,
            height: 44,
          };
        },
        getPixelOffset: () => [17.5, -22],
        getPosition: (d: any) => [d.lon, d.lat],
        pickable: false,
        getSize: 44,
        sizeScale: 1,
        visible: isSphereOfInfluence,
      }),
    [isSphereOfInfluence, points],
  );
  return [dustRangeShadowLayer, dustRangeLayer, startPoint];
}
