
import useTextureToken from '@/hooks/dust/useTextureToken';
import DynamicTextureLayer from '@/layers/dust/dynamic-texture-layer';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { useAtomValue, useSetAtom } from 'jotai';
import moment from 'moment';
import { useMemo } from 'react';
import {
  dynamicTileLayerStateAtom,
  fpsAtom,
  normalFrameAtom,
  timeLineInfoAtom,
} from '../atoms';
import DynamicTileLayer from '@/layers/dust/dynamic-texture-layer/dynamic-tile-layer';
import { getTileUrl } from '@/components/DustDetail/utils/getTileUrl';

export const useDynamicTextureTileLayer = ({ timeList }:{timeList:string[]}) => {
  const token = useTextureToken('DCOLOR');
  const timeLineInfo = useAtomValue(timeLineInfoAtom);
  const fps = useAtomValue(fpsAtom);
  const normalFrame = useAtomValue(normalFrameAtom);
  const setDynamicTileLayerState = useSetAtom(dynamicTileLayerStateAtom);

  const data = timeList
    .filter(
      (time:string) =>
        !moment(time).isBefore(moment(timeLineInfo.startTimePoint)) &&
        !moment(time).isAfter(moment(timeLineInfo.endTimePoint))
    )
    .map((time:string) =>
      getTileUrl(
        {
          agg: 'none',
          time,
          token,
          type: 'DCOLOR',
        },
        true
      )
    )
    .join(',');
  const dynamicTextureTileLayer = useMemo(() => {
    if (!token) return;
    return new DynamicTileLayer({
      ...tileLayerBaseConfig,
      id: `dynamic-texture-tile-DCOLOR`,
      data,
      visible: true,
      extent: [73.502355, 3.39716187, 135.09567, 53.563269],
      maxZoom: 3,
      minZoom: 3,
      tileSize: 256,
      colorFormat: 'RGBA',
      pickable: true,
      min: 0,
      max: 100,
      fps: fps,
      frame: timeLineInfo.frame,
      normalFrame: normalFrame,
      onLoading: () => {
        setDynamicTileLayerState('loading');
      },
      onReady: () => {
        setDynamicTileLayerState('ready');
      },
      // 色带更改后，重新渲染图层
      shouldUpdate: (prevProps: any, nextProps: any) => {
        return (
          prevProps.colorRamp !== nextProps.colorRamp ||
          prevProps.dataUrl !== nextProps.dataUrl ||
          prevProps.visible !== nextProps.visible ||
          prevProps.min !== nextProps.min ||
          prevProps.max !== nextProps.max ||
          prevProps.filters !== nextProps.filters ||
          prevProps.filtersChannel !== nextProps.filtersChannel
        );
      },
      renderSubLayers: (props) => {
        const {
          bbox: { west, south, east, north },
          //   index: { x, y, z },
        } = props.tile;
        return props?.data
          ? new DynamicTextureLayer(props, {
              pickable: true,
              data: null,
              imageList: props.data,
              bounds: [west, south, east, north],
              // smooth: true,
            })
          : null;
      },
      updateTriggers: {
        renderSubLayers: [timeLineInfo.frame, normalFrame, fps],
      },
      getTileData: async function (tile) {
        return tile?.url?.split(',');
      },
    });
  }, [
    data,
    fps,
    normalFrame,
    setDynamicTileLayerState,
    timeLineInfo.frame,
    token,
  ]);
  return { dynamicTextureTileLayer };
};
