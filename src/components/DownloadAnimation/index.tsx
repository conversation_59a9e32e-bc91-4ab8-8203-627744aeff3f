import { downloadUseLink } from '@/utils';
import { But<PERSON>, message, Modal, Spin } from 'antd';
import DeckGl, { MapView, Viewport, WebMercatorViewport } from 'deck.gl';
import html2canvas from 'html2canvas';
import { useAtom, useAtomValue } from 'jotai';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import useGeojsonLayer from '../DustDetail/components/Map/hooks/layers/useGeojsonLayer';
import useSandSourceLayer from '../DustDetail/components/Map/hooks/layers/useSandSourceLayer';
import { useTileLayerHooks } from '../DustDetail/components/Map/hooks/layers/useTileLayer';
import { DEFAULT_VIEW_STATE } from '../DustDetail/config';
import { LazyDeckGLRef } from '../LazyDeckGL';
import { dynamicTileLayerState<PERSON>tom, fpsAtom, normalFrame<PERSON>tom, rangeLoadingAtom, timeLineInfoAtom } from './atoms';
import { useDynamicTextureTileLayer } from './hooks';
// import TimeLiner from './TimeLiner';
import { TIANDITU_KEY } from '@/utils/dust/config';
import { LoadingOutlined } from '@ant-design/icons';
import { twMerge } from 'tailwind-merge';
import useDustRangeLayer from './hooks/useDustRangeLayer';
const formatDegree = (degree: number, type: 'longitude' | 'latitude') => {
  const isLongitude = type === 'longitude';
  const isPositive = degree >= 0;
  const degreeAbs = Math.abs(degree);
  const degreeInt = Math.floor(degreeAbs);
  const degreeDecimal = degreeAbs - degreeInt;
  const degreeMinute = degreeDecimal * 60;
  const degreeMinuteInt = Math.floor(degreeMinute);
  const degreeMinuteDecimal = degreeMinute - degreeMinuteInt;
  const degreeSecondInt = degreeMinuteDecimal.toFixed(2).slice(2);
  const degreeStr = `${degreeInt}°${degreeMinuteInt}′${degreeSecondInt}″`;
  let direction = '';

  if (isLongitude) {
    direction = isPositive ? 'E' : 'W';
  } else {
    direction = isPositive ? 'N' : 'S';
  }

  return `${degreeStr}${direction}`;
};
const minWidth = 808;
const minHeight = 678;
const downloadWidth = 808;
const downloadHeight = 626;

let timePointInterverl: NodeJS.Timeout | null = null;

interface DownloadAnimationProps {
  timePoints: { time: string; haveDust: boolean }[];
  regionCode: number[];
  dustName: string;
  open: boolean;
  onCancle: () => void;
  dustEventId?: number;
  // 首页打开时，需要同步viewport
  bounds?: [number, number, number, number];
}

const DownloadAnimation = ({ timePoints, regionCode, dustName, open, onCancle, dustEventId, bounds }: DownloadAnimationProps) => {
  const [viewport, setViewport] = useState<Viewport | undefined>(() => {
    let viewport = new WebMercatorViewport({
      ...DEFAULT_VIEW_STATE,
      width: 700,
      height: 418,
    });

    return viewport;
  });

  const [waitFrame, setWaitFrame] = useState(-1);
  const [gifImages, setGifImages] = useState([]);
  const [waitExport, setWaitExport] = useState(false);
  const [exportType, setExportType] = useState('');
  const [exporting, setExporting] = useState(false);
  const fps = useAtomValue(fpsAtom);
  const normalFrame = useAtomValue(normalFrameAtom);
  const dynamicTileLayerState = useAtomValue(dynamicTileLayerStateAtom);
  const [timeLineOptions, setTimeLineOptions] = useAtom(timeLineInfoAtom);
  const { height, ref } = useResizeDetector();
  const [rangeLoading, setRangeLoading] = useAtom(rangeLoadingAtom);
  const deckRef = useRef<LazyDeckGLRef>(null);
  const isLoading = useMemo(() => dynamicTileLayerState !== 'ready' || rangeLoading, [dynamicTileLayerState, rangeLoading]);

  useEffect(() => {
    if (bounds) {
      const [minLng, minLat, maxLng, maxLat] = bounds;
      setViewport((prev) => {
        const newViewport = prev?.fitBounds(
          [
            [minLng, minLat],
            [maxLng, maxLat],
          ],
          {
            padding: 10,
          },
        );
        return newViewport;
      });
    }
  }, [bounds]);

  useEffect(() => {
    if (isLoading) {
      setTimeLineOptions((prev) => ({ ...prev, play: false }));
    }
  }, [isLoading, setTimeLineOptions]);
  const { domWidth, domHeight } = useMemo(
    () => ({
      domWidth: minWidth,
      domHeight: Math.min(height || 0, minHeight),
    }),
    [height],
  );
  const tileLayer = useTileLayerHooks({ mapType: 'img' });
  const { dynamicTextureTileLayer } = useDynamicTextureTileLayer({
    timeList: timePoints?.map((t) => t.time) || [],
  });
  const [geojsonLayer, highlightGeojsonLayer] = useGeojsonLayer({ regionCode });
  const sandSourceLayer = useSandSourceLayer({
    isSandSourceLayerVisible: true,
    activeTimePoint: timeLineOptions.time,
    setSelectedSandSourceData: () => {},
  });
  const dustTimeList = useMemo(() => timePoints?.map((t) => t.time) || [], [timePoints]);
  const dustTime = useMemo(() => (timeLineOptions.time && moment(timeLineOptions.time).format('YYYY/MM/DD HH:mm:ss')) || '', [timeLineOptions.time]);
  const [dustRangeShadowLayer, dustRangeLayer] = useDustRangeLayer({
    isDustRangeLayerVisible: true,
    activeTimePoint: dustTime,
    dustEventId,
    regionCode: dustEventId ? undefined : regionCode[0],
    isSelectTheLastPointOfTimeline: true,
    timeList: dustTimeList,
    setRangeLoading,
  });
  const unitInfo = useMemo(() => {
    const meters = (viewport?.metersPerPixel ?? 0) * 180;
    if (meters > 1000 * 8) {
      return {
        unit: '千米',
        value: 1000,
      };
    } else {
      return { unit: '米', value: 1 };
    }
  }, [viewport?.metersPerPixel]);
  useEffect(() => {
    if (timePoints && timePoints.length > 0) {
      setTimeLineOptions((prev) => ({
        ...prev,
        startTimePoint: timePoints[0].time,
        endTimePoint: timePoints[timePoints.length - 1].time,
      }));
    }
  }, [setTimeLineOptions, timePoints]);

  const timeList = useMemo(
    () =>
      timePoints.map((point) => ({
        ...point,
        time: moment(point.time),
      })),
    [timePoints],
  );
  const showTimes = useMemo(() => {
    const times = timePoints
      ?.map((t) => t.time)
      .filter(
        (time) => !moment(time).isBefore(moment(timeLineOptions.startTimePoint)) && !moment(time).isAfter(moment(timeLineOptions.endTimePoint)),
      );
    return times;
  }, [timeLineOptions.endTimePoint, timeLineOptions.startTimePoint, timePoints]);

  const frameNumber = useMemo(() => (showTimes.length - 1) * (normalFrame + 1), [normalFrame, showTimes.length]);
  const drawImages = useCallback(
    (frame: number) => {
      setTimeLineOptions((prev) => ({
        ...prev,
        frame,
        time: showTimes[Math.floor(Number((frame / (normalFrame + 1)).toFixed(2)))],
      }));
      setWaitFrame(frame);
    },
    [normalFrame, setTimeLineOptions, showTimes],
  );
  const doDraw = useCallback(
    async (frame: number) => {
      const container = document.getElementById('dust-download');
      await new Promise((inject) => {
        setTimeout(() => {
          inject(true);
        }, 500);
      });
      setWaitFrame(-1);
      if (container) {
        const canvas = await html2canvas(container, {
          scale: 1,
          onclone: (d) => {
            const downloadDomMT10 = d.getElementsByClassName('d-m-t-10');
            if (downloadDomMT10) {
              for (const dom of downloadDomMT10) {
                // @ts-ignore
                dom.style.marginTop = '10px';
              }
            }

            const downloadDomT8 = d.getElementsByClassName('d-mt-8');
            if (downloadDomT8) {
              for (const dom of downloadDomT8) {
                // @ts-ignore
                dom.style.marginTop = '-8px';
              }
            }
            const downloadDomT10 = d.getElementsByClassName('d-mt-10');
            if (downloadDomT10) {
              for (const dom of downloadDomT10) {
                // @ts-ignore
                dom.style.marginTop = '-10px';
                // @ts-ignore
                dom.style.float = 'left';
              }
            }
            const downloadDomL8 = d.getElementsByClassName('d-ml-8');
            if (downloadDomL8) {
              for (const dom of downloadDomL8) {
                // @ts-ignore
                dom.style.marginLeft = '-8px';
              }
            }
            const downloadDomT6 = d.getElementsByClassName('d-mt-6');
            if (downloadDomT6) {
              for (const dom of downloadDomT6) {
                // @ts-ignore
                dom.style.marginTop = '-6px';
              }
            }
            const show = d.getElementsByClassName('export-show');
            if (show) {
              for (const dom of show) {
                // @ts-ignore
                dom.style.display = 'block';
              }
            }
            const hidden = d.getElementsByClassName('export-hidden');
            if (hidden) {
              for (const dom of hidden) {
                // @ts-ignore
                dom.style.display = 'none';
              }
            }
            const downloadWidth = d.getElementsByClassName('export-w-[808px]');
            if (downloadWidth) {
              for (const dom of downloadWidth) {
                // @ts-ignore
                dom.style.width = 808;
              }
            }
          },
        });
        const ctx = canvas.getContext('2d');
        if (ctx) {
          if (frame + normalFrame + 1 > frameNumber) {
            // @ts-ignore
            setGifImages((prev) => prev.concat(canvas));
            setTimeout(() => {
              setWaitExport(true);
            });
          } else {
            // @ts-ignore
            setGifImages((prev) => prev.concat(canvas));
            drawImages(frame + normalFrame + 1);
          }
        } else if (frame + normalFrame + 1 > frameNumber) {
          setWaitExport(true);
        } else {
          drawImages(frame + normalFrame + 1);
        }
      } else if (frame + normalFrame + 1 > frameNumber) {
        setWaitExport(true);
      } else {
        drawImages(frame + normalFrame + 1);
      }
    },
    [drawImages, frameNumber, normalFrame],
  );
  useEffect(() => {
    let timer = setTimeout(() => {
      if (dynamicTileLayerState === 'ready' && !rangeLoading && waitFrame >= 0 && open) {
        doDraw(waitFrame);
      }
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [doDraw, dynamicTileLayerState, open, rangeLoading, waitFrame]);

  useEffect(() => {
    if (waitExport && exportType === 'GIF') {
      // @ts-ignore
      const gif = new window.GIF({
        workers: 12,
        quality: 1,
        dither: 'Atkinson-serpentine',
        width: downloadWidth,
        height: downloadHeight,
        workerScript: '/assets/js/gif.worker.js',
      });
      gifImages.forEach((canvas: any) => {
        gif.addFrame(canvas.getContext('2d').getImageData(0, 0, downloadWidth, downloadHeight), {
          width: downloadWidth,
          height: downloadHeight,
          delay: 500,
        });
      });
      gif.on('finished', function (blob: Blob) {
        setExporting(false);
        downloadUseLink(URL.createObjectURL(blob), `卫星遥感监测沙尘传输过程.gif`);
        setTimeLineOptions((prev) => ({
          ...prev,
          play: false,
          time: prev.startTimePoint,
          frame: 0,
        }));
      });
      gif.render();
    }
    if (waitExport && exportType === 'VIDEO') {
      const canvas = document.createElement('canvas');
      canvas.width = downloadWidth;
      canvas.height = downloadHeight;
      const ctx = canvas.getContext('2d')!;
      const stream = canvas.captureStream
        ? canvas.captureStream()
        : // @ts-ignore
          canvas.mozCaptureStream(); //浏览器兼容处理
      let videoType = 'video/webm';
      if (MediaRecorder.isTypeSupported('video/mp4')) {
        videoType = 'video/mp4';
      }
      const recorder = new MediaRecorder(stream, {
        mimeType: videoType, // 编码格式
      });
      const draw = (i = 0) => {
        // ctx.drawImage(images[Math.floor(i / 20)], 0, 0);
        ctx.clearRect(0, 0, downloadWidth, downloadHeight);
        ctx.save();
        ctx.drawImage(gifImages[Math.floor(i / 20)], 0, 0);
        if (gifImages.length * 20 - 1 > i) {
          setTimeout(() => {
            draw(i + 1);
          }, 1000 / 40);
        } else {
          recorder.stop();
        }
      };
      // @ts-ignore
      const data = [];
      recorder.ondataavailable = function (event) {
        if (event.data && event.data.size) {
          data.push(event.data);
        }
      };
      recorder.onstop = () => {
        setTimeout(() => {
          const url = URL.createObjectURL(
            // @ts-ignore
            new Blob(data, {
              type: videoType,
            }),
          );
          // 创建一个 a 标签用于下载
          const link = document.createElement('a');
          link.href = url;
          link.download = '卫星遥感监测沙尘传输过程.' + (MediaRecorder.isTypeSupported('video/mp4') ? 'mp4' : 'webm'); // 设置下载的文件名
          // 触发下载
          document.body.appendChild(link); // 需要将 a标签添加到DOM中，否则在一些浏览器中不会触发下载
          link.click(); // 模拟点击a标签来触发下载
          // 清理，移除创建的 URL 对象
          URL.revokeObjectURL(url); // 释放之前创建的 URL 对象，防止内存泄漏
          // 清理，从 DOM 中移除 a 标签（如果需要）
          document.body.removeChild(link);
          setExporting(false);
          setTimeLineOptions((prev) => ({
            ...prev,
            play: false,
            time: prev.startTimePoint,
            frame: 0,
          }));
        }, 1000);
      };
      recorder.start();
      draw();
    }
    setWaitExport(false);
  }, [exportType, gifImages, setTimeLineOptions, waitExport]);
  const exportGif = useCallback(() => {
    try {
      // @ts-ignore
      setExporting(true);
      setTimeLineOptions((prev) => ({
        ...prev,
        play: false,
      }));
      setGifImages([]);
      setExportType('GIF');
      setWaitExport(false);
      drawImages(0);
    } catch (error) {
      message.error('导出失败');
    }
  }, [drawImages, setTimeLineOptions]);
  const exportVideo = useCallback(() => {
    try {
      // @ts-ignore
      setExporting(true);
      setTimeLineOptions((prev) => ({
        ...prev,
        play: false,
      }));
      setGifImages([]);
      setExportType('VIDEO');
      setWaitExport(false);
      drawImages(0);
    } catch (error) {
      message.error('导出失败');
    }
  }, [drawImages, setTimeLineOptions]);

  // 点击播放按钮
  useEffect(() => {
    if (timeLineOptions?.play && !isLoading) {
      // 最大帧数 = 时间点的数量 - 1（因为起始帧为0） * (normalFrame + 1)。
      const maxFrame = (timeList.length - 1) * (normalFrame + 1);
      timePointInterverl = setInterval(() => {
        // 更新 frame 和 time
        setTimeLineOptions((p) => {
          const newFrame = p.frame + 1;

          if (newFrame > maxFrame + normalFrame) {
            clearInterval(timePointInterverl!);
            // 播放完毕
            return {
              ...p,
              frame: 0,
              play: false,
              time: timeList[0]?.time.format('YYYY/MM/DD HH:mm'),
            };
          } else {
            return {
              ...p,
              frame: newFrame,
              time: timeList[Math.floor(newFrame / (normalFrame + 1))]?.time.format('YYYY/MM/DD HH:mm'),
            };
          }
          // if (newFrame <= maxFrame) {
          // } else {
          // }
        });
      }, 1000 / fps / (normalFrame + 1));
    }
    return () => {
      if (timePointInterverl) {
        clearInterval(timePointInterverl);
      }
    };
  }, [fps, isLoading, normalFrame, setTimeLineOptions, timeLineOptions?.play, timeList]);
  // 时间列表变化
  useEffect(() => {
    if (timeList) {
      setTimeLineOptions((p) => ({
        ...p,
        startTimePoint: timeList[0]?.time.format('YYYY/MM/DD HH:mm'),
        endTimePoint: timeList[timeList.length - 1]?.time.format('YYYY/MM/DD HH:mm'),
        time: timeList[0]?.time.format('YYYY/MM/DD HH:mm'),
      }));
    }
  }, [setTimeLineOptions, timeList]);

  // 关闭时重置
  useEffect(() => {
    if (open) {
      setWaitFrame(-1);
      setWaitExport(false);
      setExporting(false);
    }
    return () => {
      if (!open) {
        setWaitFrame(-1);
        setWaitExport(false);
        setGifImages([]);
        setExporting(false);
        setTimeLineOptions((prev) => ({
          ...prev,
          play: false,
          time: prev.startTimePoint,
          frame: 0,
        }));
      }
    };
  }, [open, setTimeLineOptions]);

  return (
    <div
      id="container"
      className={twMerge(' w-full h-full fixed  left-0 top-0  select-none', !open && 'pointer-events-none')}
      style={{ minWidth: minWidth, zIndex: 9999 }}
      ref={ref}
    >
      <Modal
        className="download-ant-modal-close"
        getContainer={false}
        width={domWidth}
        zIndex={9999}
        style={{ top: ((height || 0) - domHeight) / 2 }}
        classNames={{
          content: 'scrollbar',
        }}
        closeIcon={<img src="/assets/images/download/close.png" />}
        styles={{
          content: {
            borderRadius: 20,
            background: '#ECF1F3',
            padding: 0,
            width: domWidth,
            height: domHeight,
            overflowY: 'auto',
            overflowX: 'hidden',
          },
          wrapper: {
            overflowY: 'hidden',
          },
        }}
        open={open}
        maskClosable={false}
        footer={null}
        onCancel={() => {
          // setTimeLineOptions((prev) => ({
          //   ...prev,
          //   play: false,
          //   time: timeLineOptions?.startTimePoint,
          //   frame: 0,
          // }));
          // setWaitFrame(-1);
          // setTimeout(() => {
          onCancle();
          // });
        }}
      >
        <Spin spinning={exporting} tip="加载中">
          <div className="flex flex-col  items-center" style={{ width: domWidth, height: minHeight }}>
            <div id="dust-download" className="w-[808px] export-w-[808px]  bg-[#ffffff] h-[678px]">
              <div className="w-[808px] h-[520px] flex flex-col pt-[40px]">
                <div className="ml-[47px] flex h-[30px] items-center">
                  <div className="w-[4px] h-[20px] bg-[#286CFF] rounded-full" />
                  <div className="font-semibold text-[22px] leading-[30px] h-[30px] ml-[9px] text-[#333]">{dustName}卫星遥感监测沙尘传输过程</div>
                  <div className="font-normal leading-[22px] h-[22px] ml-[10px] mt-[5px] text-[#333]">
                    {moment(timeLineOptions.time).format('YYYY年MM月DD日HH时mm分')}
                  </div>
                </div>
                <div className="px-[50px] w-[808px] h-[13px] mt-[25px] flex">
                  {new Array(7).fill('').map((_, index) => (
                    <div
                      key={index * 1}
                      className="flex-1 h-full translate-x-1/2 font-semibold flex justify-center items-end text-xs leading-[13px] d-mt-6 text-[#333]"
                    >
                      {formatDegree(viewport?.unproject([(viewport.width / 7) * index, 0])[0], 'longitude')}
                    </div>
                  ))}
                </div>
                <div className="px-[50px] w-[808px] h-[4px] flex">
                  {new Array(7).fill(<div className="flex-1 border-r-[2px] h-full border-solid border-[#000000]" />)}
                </div>
                <div className="px-[4px] w-[808px] h-[398px] flex">
                  <div className="w-[42px] h-full flex flex-col">
                    {new Array(4).fill('').map((_, index) => (
                      <div
                        key={index * 1}
                        className="flex-1 translate-y-1/2 translate-x-[10px] font-semibold flex items-center text-xs origin-center -rotate-90 justify-center d-ml-8 text-[#333]"
                      >
                        {formatDegree(viewport?.unproject([0, (viewport.height / 5) * (index + 1)])[1], 'latitude')}
                      </div>
                    ))}
                    <div className="flex-1 h-full translate-y-1/2 font-semibold flex items-center origin-center -rotate-90 justify-center"></div>
                  </div>
                  <div className="w-[4px] h-full flex flex-col">
                    {new Array(4).fill(<div className="flex-1 border-b-[2px] w-full border-solid border-[#000000]" />)}
                    <div className="flex-1 border-none w-full" />
                  </div>
                  <div className="flex-1 h-full border-[2px] border-solid border-[#000000] relative">
                    <DeckGl
                      containerCfg={{
                        className: 'w-full h-full',
                      }}
                      ref={deckRef}
                      views={[new MapView({ repeat: true })]}
                      viewState={{
                        ...DEFAULT_VIEW_STATE,
                        zoom: 2.95,
                        latitude: 42.82708032485706,
                        longitude: 105.6250901521798,
                        ...(bounds ? viewport : {}),
                      }}
                      glOptions={{
                        preserveDrawingBuffer: true,
                      }}
                      layers={[
                        tileLayer,
                        dynamicTextureTileLayer,
                        geojsonLayer,
                        highlightGeojsonLayer,
                        sandSourceLayer,
                        dustRangeShadowLayer,
                        dustRangeLayer,
                      ]}
                      baseMap={{
                        provider: 'tianditu',
                        type: 'vec',
                        key: [TIANDITU_KEY],
                      }}
                      // onViewStateChange={({ viewState }) => {
                      //   const viewport = new WebMercatorViewport({
                      //     ...viewState,
                      //     width: 668,
                      //     height: 398,
                      //   });
                      //   setViewport(viewport);
                      // }}
                    ></DeckGl>
                    <div className="w-[16px] mr-[18px] h-[42px] mt-[6px] absolute right-[-5px] top-[5px]">
                      <div className="text-sm font-normal text-[#fff] leading-[20px] h-[20px] w-[16px] text-center" style={{}}>
                        <div className="d-mt-10 w-full text-center">N</div>
                      </div>
                      <div className="mt-[-2px] w-[16px] h-[24px]">
                        <img className="w-full h-full" src="/assets/images/download/north.png" alt="" />
                      </div>
                    </div>
                  </div>
                  <div className="w-[4px] h-full flex flex-col">
                    {new Array(4).fill(<div className="flex-1 border-b-[2px] w-full border-solid border-[#000000]" />)}
                    <div className="flex-1 border-none w-full" />
                  </div>
                  <div className="w-[42px] h-full flex flex-col">
                    {new Array(4).fill('').map((_, index) => (
                      <div
                        key={index * 1}
                        className="flex-1 translate-y-1/2 translate-x-[-10px] font-semibold flex items-center text-xs origin-center -rotate-90 justify-center d-ml-8 text-[#333]"
                      >
                        {formatDegree(viewport?.unproject([viewport.width, (viewport.height / 5) * (index + 1)])[1], 'latitude')}
                      </div>
                    ))}
                    <div className="flex-1 h-full translate-y-1/2 font-semibold flex items-center origin-center -rotate-90 justify-center"></div>
                  </div>
                </div>
                <div className="px-[50px] w-[808px] h-[4px] flex">
                  {new Array(7).fill(<div className="flex-1 border-r-[2px] h-full border-solid border-[#000000]" />)}
                </div>
                <div className="px-[50px] w-[808px] h-[13px] flex mb-[19px] d-m-t-10">
                  {new Array(7).fill('').map((_, index) => (
                    <div
                      key={index * 1}
                      className="flex-1 h-full translate-x-1/2 font-semibold flex justify-center items-end text-xs leading-[13px] d-mt-6 text-[#333]"
                    >
                      {formatDegree(viewport?.unproject([(viewport.width / 7) * index, viewport.height])[0], 'longitude')}
                    </div>
                  ))}
                </div>
              </div>
              {/* 右侧 */}
              <div className="">
                {/* 导出的 */}
                <div className="export-show hidden">
                  <div className="flex justify-between items-center ml-[30px] mr-[10px] mt-[10px]  mb-[20px]">
                    {/* 沙尘图例 */}
                    <div className="flex">
                      <div className="w-[100px] h-[20px] mr-[6px] flex">
                        <div className="flex justify-center items-center">
                          <div className="w-[30px] h-[10px] bg-[#FFF00D]"></div>
                        </div>
                        <div className="w-[64px] h-[20px] text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">沙尘范围</div>
                        </div>
                      </div>
                      <div className="w-[100px] h-[20px] mr-[6px] rounded-full ] flex">
                        <div className="flex justify-center items-center">
                          <div
                            className="w-[45px] h-[10px]"
                            style={{
                              background: `linear-gradient( 270deg, #FF001C 0%, #ED259E 100%)`,
                            }}
                          ></div>
                        </div>
                        <div className="w-[40px] h-[20px]  rounded-full text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">沙尘</div>
                        </div>
                      </div>
                      <div className="w-[100px] h-[20px] mr-[6px] rounded-full  flex">
                        <div className="flex justify-center items-center">
                          <div
                            className="w-[45px] h-[10px]"
                            style={{
                              background: `linear-gradient( 270deg, #220416 0%, #09065B 17%, #57152C 38%, #7E2B0C 56%, #8F7133 71%, #8B8BC6 86%, #784725 100%)`,
                            }}
                          ></div>
                        </div>
                        <div className="w-[40px] h-[20px] rounded-full text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">云</div>
                        </div>
                      </div>
                      <div className="w-[100px] h-[20px] mr-[6px] rounded-full  flex">
                        <div className="flex justify-center items-center">
                          <div
                            className="w-[45px] h-[10px]"
                            style={{
                              background: `linear-gradient( 270deg, #9CDE7E 0%, #2AA1DC 30%, #3FDADE 51%, #B6D341 79%, #70E32D 100%)`,
                            }}
                          ></div>
                        </div>
                        <div className="w-[45px] h-[20px]  rounded-full text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">晴空</div>
                        </div>
                      </div>
                    </div>
                    {/* 比例尺 */}
                    <div className="flex-1 flex justify-end items-center">
                      {/* 比例尺 */}
                      <div className="flex h-[20px] items-end justify-end">
                        <div className="mr-[3px] flex flex-col w-[210px]">
                          <div className="flex  h-[30px] text-xs justify-end flex-row-reverse">
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2 leading-[17px]">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 180) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="mt-[-6px] ml-[5px] w-full h-[17px] text-xs leading-[17px] text-left">
                                <div className="d-mt-8 mt-[-20px] text-[#333]">{unitInfo.unit}</div>
                              </div>
                            </div>
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 135) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] bg-[#000000] border-[1px] border-solild border-[#000000]" />
                            </div>
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 90) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] bg-[#ffffff] border-[1px] border-solild border-[#000000]" />
                            </div>
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 45) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] bg-[#000000] border-[1px] border-solild border-[#000000]" />
                            </div>
                            <div className="w-[23px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 22.5) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] flex border-[1px] border-solild border-[#000000]">
                                <div className="flex-1 bg-[#000000] h-full"></div>
                                <div className="flex-1 bg-[#ffffff] h-full"></div>
                              </div>
                            </div>
                            <div className="w-[22px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{0}</div>
                              </div>
                              <div className="w-full h-[7px] flex border-[1px] border-solild border-[#000000]">
                                <div className="flex-1 bg-[#000000] h-full"></div>
                                <div className="flex-1 bg-[#ffffff] h-full"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* 显示的部分 */}
                <div className="export-hidden ">
                  <div className="flex justify-between items-center ml-[30px] mr-[10px] mt-[10px]  mb-[20px]">
                    {/* 沙尘图例 */}
                    <div className="flex">
                      <div className="w-[100px] h-[20px] mr-[6px] flex">
                        <div className="flex justify-center items-center">
                          <div className="w-[30px] h-[10px] bg-[#FFF00D]"></div>
                        </div>
                        <div className="w-[64px] h-[20px] text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">沙尘范围</div>
                        </div>
                      </div>
                      <div className="w-[100px] h-[20px] mr-[6px] rounded-full ] flex">
                        <div className="flex justify-center items-center">
                          <div
                            className="w-[45px] h-[10px]"
                            style={{
                              background: `linear-gradient( 270deg, #FF001C 0%, #ED259E 100%)`,
                            }}
                          ></div>
                        </div>
                        <div className="w-[40px] h-[20px]  rounded-full text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">沙尘</div>
                        </div>
                      </div>
                      <div className="w-[100px] h-[20px] mr-[6px] rounded-full  flex">
                        <div className="flex justify-center items-center">
                          <div
                            className="w-[45px] h-[10px]"
                            style={{
                              background: `linear-gradient( 270deg, #220416 0%, #09065B 17%, #57152C 38%, #7E2B0C 56%, #8F7133 71%, #8B8BC6 86%, #784725 100%)`,
                            }}
                          ></div>
                        </div>
                        <div className="w-[40px] h-[20px] rounded-full text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">云</div>
                        </div>
                      </div>
                      <div className="w-[100px] h-[20px] mr-[6px] rounded-full  flex">
                        <div className="flex justify-center items-center">
                          <div
                            className="w-[45px] h-[10px]"
                            style={{
                              background: `linear-gradient( 270deg, #9CDE7E 0%, #2AA1DC 30%, #3FDADE 51%, #B6D341 79%, #70E32D 100%)`,
                            }}
                          ></div>
                        </div>
                        <div className="w-[45px] h-[20px]  rounded-full text-[#333] text-center text-xs leading-[20px]">
                          <div className="d-mt-6">晴空</div>
                        </div>
                      </div>
                    </div>
                    {/* 比例尺 */}
                    <div className="flex-1 flex justify-end items-center">
                      {/* 比例尺 */}
                      <div className="flex h-[20px] items-end justify-end">
                        <div className="mr-[3px] flex flex-col w-[210px]">
                          <div className="flex  h-[30px] text-xs justify-end flex-row-reverse">
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2 leading-[17px] text-[#333]">
                                <div className="d-mt-8">{(((viewport?.metersPerPixel ?? 0) * 180) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="mt-[-6px] ml-[5px] w-full h-[17px] text-xs leading-[17px] text-left">
                                <div className="d-mt-8 text-[#333]">{unitInfo.unit}</div>
                              </div>
                            </div>
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 135) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] bg-[#000000] border-[1px] border-solild border-[#000000]" />
                            </div>
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 90) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] bg-[#ffffff] border-[1px] border-solild border-[#000000]" />
                            </div>
                            <div className="w-[45px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 45) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] bg-[#000000] border-[1px] border-solild border-[#000000]" />
                            </div>
                            <div className="w-[23px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{(((viewport?.metersPerPixel ?? 0) * 22.5) / unitInfo.value).toFixed(0)}</div>
                              </div>
                              <div className="w-full h-[7px] flex border-[1px] border-solild border-[#000000]">
                                <div className="flex-1 bg-[#000000] h-full"></div>
                                <div className="flex-1 bg-[#ffffff] h-full"></div>
                              </div>
                            </div>
                            <div className="w-[22px] h-full flex flex-col">
                              <div className="text-center h-[17px] -translate-x-1/2">
                                <div className="d-mt-8 text-[#333]">{0}</div>
                              </div>
                              <div className="w-full h-[7px] flex border-[1px] border-solild border-[#000000]">
                                <div className="flex-1 bg-[#000000] h-full"></div>
                                <div className="flex-1 bg-[#ffffff] h-full"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* 按钮 */}
                  <div className="w-full bg-[#ECF1F3] flex justify-center items-center absolute left-0 right-0 bottom-0 h-[96px] leading-[96px]">
                    {!timeLineOptions.play ? (
                      <Button
                        className="w-[92px] h-[38px] mr-[10px] px-[9px] flex items-center gap-[4px] text-sm"
                        type="primary"
                        onClick={() =>
                          setTimeLineOptions((prev) => ({
                            ...prev,
                            play: !prev.play,
                          }))
                        }
                      >
                        {!isLoading ? (
                          <img className="float-left" src="/assets/images/download/start.png" alt="" />
                        ) : (
                          <div className="w-[10px] h-[10px] flex items-center">
                            <LoadingOutlined style={{ fontSize: '12px' }} />
                          </div>
                        )}
                        <span className="float-left text-sm">播放动画</span>
                      </Button>
                    ) : (
                      <Button
                        className="w-[92px] h-[38px] mr-[10px] px-[9px] flex items-center gap-[4px] text-sm"
                        type="primary"
                        onClick={() =>
                          setTimeLineOptions((prev) => ({
                            ...prev,
                            play: !prev.play,
                          }))
                        }
                      >
                        <img className="float-left" src="/assets/images/download/stop.png" alt="" />
                        <span className="float-left text-sm">暂停动画</span>
                      </Button>
                    )}
                    <Button
                      className="w-[92px] h-[38px] mr-[10px] px-[9px] flex items-center gap-[4px] text-sm"
                      type="primary"
                      onClick={() => exportGif()}
                    >
                      <img className="float-left" src="/assets/images/download/download.png" alt="" />
                      <span className="float-left text-sm">下载动画</span>
                    </Button>
                    <Button
                      className="w-[92px] h-[38px] mr-[10px] px-[9px] flex items-center gap-[4px] text-sm"
                      type="primary"
                      onClick={() => exportVideo()}
                    >
                      <img className="float-left" src="/assets/images/download/download.png" alt="" />
                      <span className="float-left text-sm">下载视频</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};
export default DownloadAnimation;
