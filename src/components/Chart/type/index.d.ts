declare type IEchartsEvent = undefined | { fn: (arg: any) => void; eventName: string }[];

declare type IEchartsOptionsConfig = {
  data: (null | number)[][];
  x?: (string | number)[];
  colors?: string[];
  legend_names?: string[];
  unit?: string;
  leftUnit?: string;
  rightUnit?: string;
};

declare type IEchartsPieOptionsConfig = {
  data: { value: number | null; name: string; ratio?: number }[][];
  colors?: string[];
  legend_names?: string[];
  unit?: string;
};

declare type IEchartsOnePieOptionsConfig = {
  data: { value: number | null; name: string; ratio?: number }[];
  colors?: string[];
  legend_names?: string[];
  unit?: string;
};

declare type IEchartsCustomChartOptionsConfig = {
  data: (number | null)[][];
  colors?: string[];
  x?: (string | number)[];
  unit?: string;
  leftUnit?: string;
  rightUnit?: string;
};
