import type { EChartsResponsiveOption } from 'echarts';
import { memo } from 'react';
import useChartEvent from './hooks/useChartEvent';
import { useInit } from './hooks/useInit';

const chart = memo<{
  mixConfig: EChartsResponsiveOption;
  events?: IEchartsEvent;
}>(({ mixConfig, events }) => {
  const { containerRef, chartRef } = useInit(mixConfig);
  useChartEvent(chartRef, events);
  return <div style={{ height: '100%', width: '100%' }} ref={containerRef} />;
});

export default chart;
