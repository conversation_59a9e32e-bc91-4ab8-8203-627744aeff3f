import type { EChartsType } from 'echarts';
import type { MutableRefObject } from 'react';
import { useEffect } from 'react';
// 获取规范化数组
export function array_<T>(data: T[] | undefined): T[] {
  return data instanceof Array ? data.slice() : ([] as T[]);
}
const useChartEvent = (
  chart: MutableRefObject<EChartsType | null>,
  event: IEchartsEvent, // 注入操作事件
) => {
  useEffect(() => {
    array_(event).forEach(({ fn, eventName }) => {
      chart?.current?.on(eventName, (e: any) => {
        if (typeof fn === 'function') fn(e);
      });
    });
    return () => {
      array_(event).forEach(({ eventName }) => {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        chart?.current?.off(eventName);
      });
    };
  }, [chart, event]);
};
export default useChartEvent