import * as echarts from 'echarts';
import { useCallback, useEffect, useRef } from 'react';

// charts的容器 和 option 已经是否需要刷新图
export const useInit = (option: any, needRefresh: boolean = true) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const chartRef = useRef<echarts.EChartsType | null>(null); // chart
  const init = useCallback(() => {
    const container = containerRef.current as HTMLDivElement; // 获取容器
    if (chartRef.current === null) {
      chartRef.current = echarts.init(container); // 装载
    }
    chartRef.current.setOption(option, needRefresh); // 设定属性
  }, [containerRef, needRefresh, option]);

  const resizeCb = () => {
    (chartRef.current as echarts.EChartsType).resize();
  };

  useEffect(() => {
    if (containerRef.current === null) return;
    init();
    const resizeObserver = new ResizeObserver(resizeCb); // 容器resize 的时候图形变化
    resizeObserver.observe(containerRef.current);
    return () => {
      resizeObserver.disconnect(); // 组件销毁的时候 清空监听
    };
  }, [containerRef, init]);

  return { containerRef, chartRef };
};
