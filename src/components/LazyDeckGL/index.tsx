import { useThrottleFn } from 'ahooks';
import { Deck, DeckProps, Viewport } from 'deck.gl/typed';
import React, {
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { twMerge } from 'tailwind-merge';

export type ViewState = Record<string, any>;
export type DeckGLProps = {
  /**
   * DeckGL 配置项
   * @link https://deck.gl/docs/api-reference/core/deck#properties
   */
  deckCfg: Omit<DeckProps, 'viewState' | 'parent'>;
  /**
   * DeckGL 容器属性，主要用来设置样式
   */
  containerCfg?: React.HTMLAttributes<HTMLDivElement>;
  /**
   * 根据 Viewport 和 viewState 实时渲染组件
   *
   * @param params 实时 Viewport 和 viewState
   * @returns
   */
  renderOnViewportChange?: (params: { viewport: Viewport }) => React.ReactNode;
};

export type LazyDeckGLRef = {
  /**
   * DeckGL 实例
   */
  readonly deckGLInstance: Deck | null;
  /**
   * 视图状态
   */
  readonly viewState: ViewState | undefined;
  /**
   * 设置视图状态
   */
  setViewState: React.Dispatch<React.SetStateAction<Record<string, any>>>;
};

function LazyDeckGL(
  {
    deckCfg,
    containerCfg,
    renderOnViewportChange: onUpdate = () => null,
  }: DeckGLProps,
  ref: React.Ref<LazyDeckGLRef | undefined>,
) {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const canvasRef = React.useRef<HTMLCanvasElement>(null);
  const deckRef = React.useRef<Deck | null>(null);
  const viewStateRef = React.useRef<ViewState | null>(null);

  const [firstViewport, setFirstViewport] = useState<Viewport | undefined>(
    undefined,
  );

  const containerClassName = twMerge(
    'w-[500px] h-[500px] relative',
    containerCfg?.className,
  );

  const getViewport = useCallback(() => {
    return deckRef.current?.isInitialized
      ? deckRef.current?.getViewports()?.[0]
      : undefined;
  }, []);

  const { run: setFirstViewportThrottle } = useThrottleFn(
    () => {
      const firstViewport = getViewport();
      if (firstViewport) {
        setFirstViewport(firstViewport);
      }
    },
    { wait: 33.34, leading: false },
  );

  const onViewStateChange = useCallback(function onViewStateChange(
    viewState: ViewState,
  ) {
    viewStateRef.current = { ...viewState };
    deckRef.current?.setProps({ viewState: viewState });
    setFirstViewportThrottle();
     //迁移
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 初始化
  useEffect(function initialDeckInstance() {
    const container = containerRef.current;
    const canvas = canvasRef.current;
    if (!container || !canvas) {
      throw new Error('deckGL container was not ready');
    }
    viewStateRef.current = deckCfg.initialViewState;

    const deckInstance = (deckRef.current = new Deck({
      ...deckCfg,
      parent: container,
      canvas: canvas,
      initialViewState: undefined,
      // initialViewState: deckCfg.initialViewState,
      viewState: deckCfg.initialViewState,
      onViewStateChange(params) {
        onViewStateChange(params.viewState);
        deckCfg.onViewStateChange?.(params);
      },
    }));
    return () => deckInstance.finalize();
     //迁移
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 更新配置项
  useEffect(() => {
    deckRef.current?.setProps({
      ...deckCfg,
      onViewStateChange(params) {
        onViewStateChange(params.viewState);
        deckCfg.onViewStateChange?.(params);
      },
    });
     //迁移
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deckCfg]);

  const getViewState = useCallback(() => {
    const viewport = getViewport();
    if (!viewport) return deckRef.current?.props.initialViewState;
    // @ts-expect-error any
    const { longitude, latitude, zoom, width, altitude, bearing, pitch } =
      viewport;
    return {
      longitude,
      latitude,
      zoom,
      width,
      altitude,
      bearing,
      pitch,
    };
  }, [getViewport]);

  const setViewState = useCallback(
    (params: ViewState | ((viewState: ViewState) => ViewState)) => {
      // 迁移
      if (deckRef?.current?.isInitialized&&deckRef?.current) {
        const currentViewState = getViewState();
        const newState =
          typeof params === 'function' ? params(currentViewState) : params;

        deckRef?.current?.setProps({
          viewState: { ...newState },
        });
      }
    },
    [getViewState],
  );

  useImperativeHandle(
    ref,
    function getRefHandles() {
      return {
        get deckGLInstance() {
          return deckRef.current;
        },
        get viewState() {
          return getViewState();
        },
        setViewState,
      };
    },
    [getViewState, setViewState],
  );

  return (
    <div ref={containerRef} {...containerCfg} className={containerClassName}>
      <canvas className="w-full h-full" ref={canvasRef}></canvas>
      {firstViewport && onUpdate({ viewport: firstViewport })}
    </div>
  );
}

export default memo(React.forwardRef(LazyDeckGL));
