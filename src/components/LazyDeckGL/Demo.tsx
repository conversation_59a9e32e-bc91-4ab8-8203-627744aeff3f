import React, { useRef } from "react";
import Lazy<PERSON>eckG<PERSON>, { LazyDeckGLRef } from ".";
import { BitmapLayer, MapView, TileLayer } from "deck.gl/typed";
import { DEFAULT_VIEW_STATE } from "../../MultipleView/configs";
import { But<PERSON> } from "antd";
import { FlyToInterpolator } from "deck.gl/typed";

const MAPBOX_TOKEN =
  "pk.eyJ1Ijoic2F1cnkxMDI5IiwiYSI6ImNrb3RvajY4cjBiaHkydnBmbHUwMWRsZHMifQ.byJtXxjso2A1kCiKWbeD1w";
const SURFACE_IMAGE = `https://api.mapbox.com/v4/mapbox.satellite/{z}/{x}/{y}@2x.png?access_token=${MAPBOX_TOKEN}`;

const tileLayer = new TileLayer({
  id: "tile-layer",
  data: SURFACE_IMAGE,
  renderSubLayers: (props) => {
    const {
      bbox: { west, south, east, north },
    } = props.tile;
    return new BitmapLayer(props, {
      data: undefined,
      image: props.data,
      bounds: [west, south, east, north],
    });
  },
});

function LazyDeckDemo() {
  const lazyDeckGLRef = useRef<LazyDeckGLRef | undefined>();
  const handleRandomPosition = () => {
    lazyDeckGLRef.current?.setViewState({
      longitude: Math.random() + 110,
      latitude: Math.random() + 30,
      zoom: 4 + Math.random() * 6,
      transitionDuration: 500,
      interpolator: new FlyToInterpolator(),
    });
  };
  const handleResetPosition = () => {
    lazyDeckGLRef.current?.setViewState(DEFAULT_VIEW_STATE);
  };

  return (
    <>
      <div className="flex items-center gap-2 mb-2">
        <Button type="primary" onClick={handleRandomPosition}>
          随机设置位置
        </Button>
        <Button type="primary" onClick={handleResetPosition}>
          复位
        </Button>
      </div>
      <LazyDeckGL
        ref={lazyDeckGLRef}
        containerCfg={
          {
            // className: "w-full h-[700px]",
          }
        }
        deckCfg={{
          controller: true,
          views: [new MapView({ repeat: true })],
          layers: [tileLayer],
          initialViewState: DEFAULT_VIEW_STATE,
        }}
      />
    </>
  );
}

export default LazyDeckDemo;
