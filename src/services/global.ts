import type { FetchPollutionListParams, TextureMap } from '@/types';
import { request } from '@/utils';
import { secondLevelRegex } from '@/utils/formRules';
import omitBy from 'lodash/omitBy';
import { stringify } from 'qs';

export interface BaseParams {
  startDate: string;
  endDate: string;
  regionCode: string | number;
}

interface FetchRegionListParams {
  code?: number;
  login?: number;
}

interface FetchGeojsonParams {
  code: number;
  level: number;
}

export const fetchUserInfo = () => request('/api/user/info');
export const fetchRegionList = (params: FetchRegionListParams) => request(`/api/region/list?${stringify(params)}`);

export const fetchGeojsonByRegionCode = (regionCode: number) => request(`/api/region/geojson?code=${regionCode}`);

export const uploadFile = (formData: FormData) => {
  return request(`/api/file`, {
    method: 'POST',
    body: formData,
  });
};
// 获取指定行政区下属区域
export const fetchGeojsonIncludeChild = (params: FetchGeojsonParams) => request(`/api/region/list/geojson?${stringify(params)}`);

// 根据用户regionCode获取全省市区
export const getRegionAll = (regionCode: number | string) => request(`/api/region/all?userRegionCode=${regionCode}`);

export const fetchUserTextureMap = (): Promise<TextureMap> =>
  new Promise((resolve) => {
    resolve({
      maxLat: 56.3754545083708,
      maxLng: 135.415325243092,
      minLat: 31.0186153819943,
      minLng: 86.1010555779645,
      url: '/150000.png',
    });
  });

export const fetchSopDicList = () => request('/api/sop/dic/list');

export const fetchPollutionDetail = (id: string) => request(`/api/sop?id=${id}`);

export const fetchPollutionList = (params: FetchPollutionListParams) => {
  const newParams = omitBy(params, (item) => {
    if (Array.isArray(item)) {
      return item.length === 0;
    }

    return !item;
  });

  return request(
    `/api/sop/list?${stringify({
      ...newParams,
      industry: newParams.industry?.filter((item) => secondLevelRegex.test(`${item}`)).join(','),
      level: newParams.level?.join(','),
      status: newParams.status?.join(','),
      type: newParams.type?.join(','),
    })}`,
  );
};

export const fetchIndustryAlertStat = (
  params: BaseParams & {
    type: 1 | 2;
  },
) => request(`/api/evaluation/industry/alert/stat?${stringify(params)}`);

export interface StatisticsFetchParams {
  type: 1 | 2 | 3 | 4;
  regionCode: number;
  startDate: string; // yyyy/MM/dd HH:mm:ss
  endDate: string; // yyyy/MM/dd HH:mm:ss
}

export const getAlertSopStat = (params: StatisticsFetchParams) => request(`/api/alert/sop/stat?${stringify(params)}`);

export interface FetchSusPoParams {
  id: string | number;
  name?: string;
}
export const fetchSuspectedPollutions = (params: FetchSusPoParams) => request(`/api/sop/suspected/pollution/source?${stringify(params)}`);

export interface FetchWarningListParams {
  alertNumber?: string;
  endDate: string;
  startDate: string;
  regionCode?: number;
  types: string;
  size?: number;
  page?: number;
}
export const fetchWarningList = (params: FetchWarningListParams) => request(`/api/alert/list?${stringify(omitBy(params, (item) => !item))}`, {});

interface FetchTextureTimePointsParams {
  agg: string;
  endDate: string;
  type: string;
  startDate: string;
}
export const fetchTextureTimePoints = (params: FetchTextureTimePointsParams) => request(`/api/texture/time/point?${stringify(params)}`);

export const fetchTextureToken = (domains = 'all', type = '') =>
  request(
    // eslint-disable-next-line max-len
    `/api/texture/token?appKey=696314bd-58f9-4e5b-b415-728a4aa0329c&domains=${domains}&type=${type}`,
  );
export interface UpdatePasswordParams {
  /** 新登录密码,示例值(12x3456) */
  newPassword: string;
  /** 原登录密码,示例值(12x3456) */
  originalPassword: string;
}

export const updatePassword = (params: UpdatePasswordParams) =>
  request(
    '/api/user/password/update',
    {
      method: 'POST',
      body: JSON.stringify(params),
    },
    true,
  );
