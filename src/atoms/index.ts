import type { User } from '@/types';
import { atom } from 'jotai';

export const userInfoAtom = atom<User | undefined>(undefined);

export const userGeojsonAtom = atom<any>(null);

export const fullScreenAtom = atom(false);
export const regionAtom = atom<number | undefined>(undefined);

// 从沙尘home 中迁入的
export const isSubscribeModalOpenAtom = atom(false);
export const changeRoutesAtom = atom<any>([]);
