import '@/assets/icomoon/style.css';
import { changeRoutesAtom, fullScreenAtom, regionAtom, userGeojson<PERSON>tom, userInfoAtom } from '@/atoms';
import SideMenu from '@/components/global/SideMenu';
import GlobalProvider, { GlobalConsumer } from '@/components/GlobalProvider';
import { Flex, GlobalStyle, Main } from '@/components/ui';
import theme from '@/components/ui/theme';
import { useCalcLayoutMainWidth, useRouter } from '@/hooks';
import { fetchGeojsonByRegionCode, fetchUserInfo } from '@/services/global';
import type { User } from '@/types';
import { ConfigProvider, message } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import isoWeek from 'dayjs/plugin/isoWeek';
import localeData from 'dayjs/plugin/localeData';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import updateLocale from 'dayjs/plugin/updateLocale';
import weekday from 'dayjs/plugin/weekday';
import { useAtom, useAtomValue, useSetAtom, useSetAtom as useUpdateAtom } from 'jotai';
import 'moment/locale/zh-cn';
import { useEffect, useMemo } from 'react';
import { QueryClient, QueryClientProvider, useQuery } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { ThemeProvider } from 'styled-components';
import { Helmet, Navigate, Outlet } from 'umi';

import 'dayjs/locale/zh-cn';

dayjs.extend(updateLocale);
dayjs.extend(localeData);
dayjs.extend(weekday);
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek);
dayjs.locale('zh-cn');
dayjs.updateLocale('zh-cn', {
  weekStart: 1,
});

const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 30,
    },
  },
});

// 禁用右键
// document.oncontextmenu = () => {
//   return false;
// };

const paths: Record<string, string[]> = {
  '1004': ['/thematic-map', '/thematic-map-list', 'thematic-map-tpl-list', '/thematic-map-tpl'],
  1006: ['/comprehensive-assessment'],
  1005: ['/user-management', '/receive-monitoring', '/receiving-log', '/data-download', '/system-log'],
  1007: ['/dust-event', '/dust-event-detail'],
  1008: ['/report', '/report-detail'],
};
const pathArr = Object.keys(paths).reduce((acc, key) => {
  return [...acc, ...paths[key]];
}, [] as string[]);




const Layout: React.FC = ({ children }) => {
  const { pathname } = useRouter();
  const token = localStorage.getItem('__NMENG_TOKEN__');
  const setRegionCode = useUpdateAtom(regionAtom);
  const [userInfo, setUserInfo] = useAtom(userInfoAtom);
  const setUserGeojson = useUpdateAtom(userGeojsonAtom);
  const isFullScreen = useAtomValue(fullScreenAtom);

  const setChangeRoutes = useSetAtom(changeRoutesAtom);

  useEffect(() => {
    if (!token) {
      setChangeRoutes([]);
    } else {
      setChangeRoutes((prev: string[]) => [...prev, pathname]);
    }
  }, [pathname, setChangeRoutes, token]);

  useEffect(() => {
    if (!token && pathname !== '/login') {
      const { href } = window.location;
      window.location.href = `/#/login?from=${encodeURIComponent(href)}`;
    }
  }, [token, pathname]);

  const { data } = useQuery<User>(['user-info'], fetchUserInfo, {
    enabled: !!token && pathname !== '/login',
  });

  useEffect(() => {
    if (data) {
      fetchGeojsonByRegionCode(data.regionCode).then((respData) => {
        setUserGeojson(respData);
      });

      setRegionCode(data.regionCode);
      setUserInfo(data);
    }
  }, [data, setRegionCode, setUserGeojson, setUserInfo]);

  const mainWidth = useCalcLayoutMainWidth();

  const canReachPaths = useMemo(() => {
    return userInfo?.permissions.reduce((acc, cur) => {
      return paths[cur] ? [...acc, ...paths[cur]] : acc;
    }, [] as string[]);
  }, [userInfo?.permissions]);

  const shouldRedirect = useMemo(() => {
    if (!pathArr.includes(pathname)) return false;

    return !canReachPaths?.includes(pathname);
  }, [canReachPaths, pathname]);

  if (shouldRedirect && userInfo?.permissions && pathname !== '/login') {
    message.error('无访问当前页面权限，请联系管理员');

    return <Navigate replace to="/" />;
  }

  return (
    <>
      <ReactQueryDevtools position="bottom-right" />
      <Helmet>
        <script src="/assets/js/gif.js" />
      </Helmet>
      <div id="dark-container" style={{ position: 'relative' }} />
      <ConfigProvider locale={zhCN}>
        <ThemeProvider theme={theme}>
          <GlobalStyle />
          {pathname === '/login' || pathname === '/404' ? (
            <Outlet />
          ) : (
            <>
              <Flex>
                {isFullScreen && pathname === '/' ? null : <SideMenu />}
                <Main style={{ flex: isFullScreen ? '1' : `0 0 ${mainWidth}px` }}>
                  {/* 全局注入 */}
                  <GlobalProvider>
                    <GlobalConsumer>{() => <Outlet />}</GlobalConsumer>
                  </GlobalProvider>
                  {/* 全局注入 */}
                </Main>
              </Flex>
            </>
          )}
        </ThemeProvider>
      </ConfigProvider>
    </>
  );
};

const LayoutWithQueryClient: React.FC = ({ children }) => {
  return (
    <QueryClientProvider client={client} contextSharing={true}>
      <Layout />
    </QueryClientProvider>
  );
};

export default LayoutWithQueryClient;
