
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { getTextureToken } from '@/pages/DustAnalyze/services';

export default function useTextureToken(type?: DUST.DataTypes) {
  const params = useMemo(() => (type ? { type } : { type: '' }), [type]);

  const { data } = useQuery<{ token: string }>(
    ['texture-token', type],
    () => getTextureToken(params),
    {
      enabled: true,
    },
  );

  return data?.token;
}
