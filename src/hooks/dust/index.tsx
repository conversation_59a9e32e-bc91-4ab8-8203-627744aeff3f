import { useEffect, useRef } from "react";

export const useClickOutside = (handler: (e: MouseEvent) => void) => {
  const domNode = useRef<any>();

  useEffect(() => {
    const listener = (event: MouseEvent) => {
      if (!domNode.current || domNode.current.contains(event.target)) {
        return;
      }
      handler(event);
    };
    document.addEventListener('click', listener);
    return () => {
      document.removeEventListener('click', listener);
    };
  }, [handler]);

  return domNode;
};