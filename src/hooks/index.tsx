import { changeRoutes<PERSON><PERSON>, userInfo<PERSON><PERSON> } from '@/atoms';
import { menuCollapsed<PERSON>tom } from '@/atoms/menu';
// eslint-disable-next-line max-len
import TextureBitmapLayer from '@/layers/createTextureLayer/bitmap-layer/bitmap-layer';
import type { MetaData } from '@/pages/DataDownload/types';
import useGetLegend from '@/pages/Overview/hooks/useGetLegend';
import { getRegionAll } from '@/services/global';
import { getLevelByRegionCode } from '@/utils';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { getTranslateImageData } from '@/utils/image';
// @ts-ignore
import { MaskExtension } from '@deck.gl/extensions';
import { BitmapLayer, GeoJsonLayer, TileLayer } from 'deck.gl';
import type { SetStateAction } from 'jotai';
import { useAtomValue, useSet<PERSON>tom as useUpdate<PERSON><PERSON> } from 'jotai';
import qs from 'qs';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import { useLocation, useParams } from 'react-router-dom';
import { history } from 'umi';

export const usePanelHeight: () => number = () => {
  const [height, setHeight] = useState(0);

  const setH = () => {
    let winH = window.innerHeight;
    winH = winH < 600 ? 600 : winH;

    setHeight(winH - 114 - 94 - 40 - 14);
  };

  useEffect(() => {
    setH();
    window.addEventListener('resize', setH);

    return () => {
      window.removeEventListener('resize', setH);
    };
  }, []);

  return height;
};

export const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) {
      return;
    }

    const id = setInterval(() => savedCallback.current(), delay);

    // eslint-disable-next-line consistent-return
    return () => {
      clearInterval(id);
    };
  }, [delay]);
};

export const useOnClickOutside = (ref: React.RefObject<HTMLElement | null>, handler: any) => {
  useEffect(() => {
    const listener = (event: any) => {
      if (!ref.current || ref.current.contains(event.target)) {
        return;
      }
      handler(event);
    };
    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);
    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler]);
};

export const useWindowSize = () => {
  const isSSR = typeof window === 'undefined';
  const [windowSize, setWindowSize] = useState({
    width: isSSR ? 1200 : window.innerWidth,
    height: isSSR ? 800 : window.innerHeight,
  });

  function changeWindowSize() {
    setWindowSize({ width: window.innerWidth, height: window.innerHeight });
  }

  useEffect(() => {
    window.addEventListener('resize', changeWindowSize);

    return () => {
      window.removeEventListener('resize', changeWindowSize);
    };
  }, []);

  return windowSize;
};

export const useRouter = () => {
  const params = useParams();
  const location = useLocation();

  return useMemo(() => {
    const query: Record<string, any> = {
      ...qs.parse(location.search.replace('?', '')),
      ...params,
    };
    return {
      push: history.push,
      replace: history.replace,
      pathname: location.pathname,
      query,
      location,
      history,
    };
  }, [location, params]);
};

export const useCalcLayoutMainWidth = () => {
  const { width } = useWindowSize();
  const collapsed = useAtomValue(menuCollapsedAtom);

  return width - (collapsed === 1 ? 80 : 296);
};

export const useCascaderOptionsAndMatchValues = (regionCode: number | undefined, isDisabledFistLevel?: boolean) => {
  const userInfo = useAtomValue(userInfoAtom);
  // 使用Map保存code-name对应关系
  const [regionMap, setRegionMap] = useState(new Map());
  const provinceCode = useMemo(() => (userInfo?.regionCode ? `${String(userInfo.regionCode).substr(0, 2)}0000` : ''), [userInfo?.regionCode]);
  const { data } = useQuery<{
    code: number;
    name: string;
    children: {
      code: number;
      name: string;
      children: { code: number; name: string }[];
    }[];
  }>(['region-data-by-user', userInfo?.regionCode], () => getRegionAll(Number(userInfo?.regionCode)), {
    enabled: Boolean(userInfo?.regionCode),
  });

  useEffect(() => {
    if (data) {
      const map = new Map();
      map.set(data.code, data.name);
      data.children.forEach((province) => {
        map.set(province.code, province.name);
        province.children.forEach((city) => {
          map.set(city.code, city.name);
        });
      });
      setRegionMap(map);
    }
  }, [data]);

  const level = getLevelByRegionCode(Number(userInfo?.regionCode));
  const matchCodeArr = useMemo(
    () =>
      data?.children.reduce(
        (prev, cur) => {
          if (!userInfo?.regionCode) return prev;

          if (cur.code === userInfo.regionCode) {
            return [...prev, cur.code];
          }

          const find = cur.children.find((child) => child.code === userInfo.regionCode);
          if (find) {
            return [...prev, cur.code, find.code];
          }

          return prev;
        },
        [+provinceCode] as number[],
      ),
    [data?.children, provinceCode, userInfo?.regionCode],
  );

  const [, city, county] = matchCodeArr || [];

  const options = useMemo(() => {
    return data
      ? [data].map((region) => {
        return {
          ...region,
          disabled: isDisabledFistLevel ? false : level !== 1,
          children: region.children.map((lv2) => {
            return {
              ...lv2,
              disabled: (level !== 2 || lv2.code !== city || !city) && level !== 1,
              children: lv2.children.map((lv3) => {
                return {
                  ...lv3,
                  disabled: level === 3 && lv3.code !== county,
                };
              }),
            };
          }),
        };
      })
      : [];
  }, [city, county, data, isDisabledFistLevel, level]);

  const twoStageOptions = useMemo(
    () =>
      options.map((item) => ({
        ...item,
        children: item.children.map((child) => {
          const { children, ...rest } = child;

          return rest;
        }),
      })),
    [options],
  );

  const cascaderValue = useMemo(
    () =>
      data
        ? data.children.reduce(
          (prev, cur) => {
            if (cur.code === regionCode) {
              return [...prev, cur.code];
            }

            const find = cur.children.find((child) => child.code === regionCode);
            if (find) {
              return [...prev, cur.code, find.code];
            }

            return prev;
          },
          [+provinceCode] as number[],
        )
        : [],
    [data, provinceCode, regionCode],
  );

  return { options, cascaderValue, twoStageOptions, regionMap, level };
};

export const useMatchCascaderValue = (regionCode: number | undefined) => {
  const userInfo = useAtomValue(userInfoAtom);
  const provinceCode = useMemo(() => (userInfo?.regionCode ? `${String(userInfo.regionCode).substr(0, 2)}0000` : ''), [userInfo?.regionCode]);

  const { data } = useQuery<{
    code: number;
    name: string;
    children: {
      code: number;
      name: string;
      children: { code: number; name: string }[];
    }[];
  }>(['region-data-by-user', userInfo?.regionCode], () => getRegionAll(Number(userInfo?.regionCode)), {
    enabled: Boolean(userInfo?.regionCode),
  });

  const result = useMemo(
    () =>
      data
        ? data.children.reduce(
          (prev, cur) => {
            if (cur.code === regionCode) {
              return [...prev, cur.code];
            }

            const find = cur.children.find((child) => child.code === regionCode);
            if (find) {
              return [...prev, cur.code, find.code];
            }

            return prev;
          },
          [+provinceCode] as number[],
        )
        : [],
    [data, provinceCode, regionCode],
  );

  return result;
};

export const useUserRegionCode = () => {
  const userInfo = useAtomValue(userInfoAtom);

  return userInfo?.regionCode;
};

export const useSelectedTableRowKeys = (selectedRowKeys: React.Key[], setSelectedRowKeys: (update: SetStateAction<React.Key[]>) => void) => {
  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onSelect(record: MetaData, selected: any) {
        if (selected) {
          setSelectedRowKeys((prev) => [...prev, record.timePoints]);
        } else {
          setSelectedRowKeys((prev) => prev.filter((item) => item !== record.timePoints));
        }
      },
      onSelectAll(selected: any, selectedRows: any, changeRows: any) {
        if (selected) {
          setSelectedRowKeys((prev) => [...prev, ...changeRows.map((item: any) => item.timePoints)]);
        } else {
          setSelectedRowKeys((prev) => prev.filter((item) => !changeRows.map((rowItem: any) => rowItem.timePoints).includes(item)));
        }
      },
    }),
    [selectedRowKeys, setSelectedRowKeys],
  );

  return {
    rowSelection,
  };
};

export const useLogOut = () => {
  const updateUserInfo = useUpdateAtom(userInfoAtom);
  const queryClient = useQueryClient();
  const logout = useCallback(() => {
    localStorage.removeItem('__NMENG_TOKEN__');
    queryClient.removeQueries(['user-info']);
    updateUserInfo(undefined);

    const { href } = window.location;
    window.location.href = `/#/login?from=${encodeURIComponent(href)}`;
  }, [queryClient, updateUserInfo]);

  return logout;
};

export const useMaskLayer = (geojson: GeoJSON.GeoJSON | undefined) => {
  const layer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'geojson-mask',
      data: geojson || '',
      operation: 'mask',
    });
  }, [geojson]);

  return layer;
};

export interface TextureLayerGenerateProps {
  id: string;
  dataUrl: string;
  colorRamp: Record<string, string>;
  decoder?: string;
  visible?: boolean;
  filters?: number[];
  filtersChannel?: number;
  needMask?: boolean;
  extent?: [number, number, number, number];
  isBitmapLayer?: boolean;
}

export const useCreateTextureLayer = () => {
  const createTextureLayer = useCallback(
    ({
      id,
      dataUrl,
      colorRamp,
      decoder,
      visible,
      filters = [],
      filtersChannel,
      needMask = true,
      extent,
      isBitmapLayer,
    }: TextureLayerGenerateProps) => {
      return new TileLayer({
        id,
        ...tileLayerBaseConfig,
        // @ts-ignore
        maskId: needMask ? 'geojson-mask' : undefined,
        // @ts-ignore
        extensions: needMask ? [new MaskExtension()] : [],
        data: dataUrl,
        extent,
        maxZoom: 7,
        minZoom: 0,
        // 色带更改后，重新渲染图层
        shouldUpdate: (prevProps: any, nextProps: any) => {
          return prevProps.colorRamp !== nextProps.colorRamp || prevProps.visible !== nextProps.visible;
        },
        visible,
        renderSubLayers: (props: any) => {
          const {
            bbox: { west, south, east, north },
          } = props.tile;

          return isBitmapLayer
            ? new BitmapLayer(props, {
              pickable: true,
              data: null,
              image: props.data,
              bounds: [west, south, east, north],
            })
            : new TextureBitmapLayer(props, {
              data: null,
              image: props.data,
              bounds: [west, south, east, north],
              colorRamp,
              decoder,
              filters,
              filtersChannel,
              opacity: 1,
              // textureParameters: {
              //   [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
              //   [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
              // },
              smooth: true,
            });
        },
        getTileData: (tile: any) => {
          return getTranslateImageData(tile.url)
        }
      });
    },
    [],
  );

  return { createTextureLayer };
};

export const useQyGeojson = () => {
  const { data } = useQuery('qy-geojson', () => {
    return fetch('/assets/geojson/qy.json').then((resp) => resp.json());
  });

  return {
    geojson: data || [],
  };
};

export const useClearFilter = (page: string, callback: () => void) => {
  const changeRoutes = useAtomValue(changeRoutesAtom);
  useEffect(() => {
    if (changeRoutes.length > 1) {
      let secondLast = changeRoutes.slice(-2, -1)[0];
      if (!secondLast.includes(page)) {
        callback();
      }
    }
  }, [callback, changeRoutes, page]);
};

// 自定义图例色带
export const useCustomLegend = (pollutionType: string) => {
  const { legendInfo: target } = useGetLegend(pollutionType, 'remote');
  return { pollutionType: target };
};
