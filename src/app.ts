/* eslint-disable global-require */
import { history } from 'umi';
import moment from 'moment';
import 'moment/locale/zh-cn';

moment().locale('zh-cn');
// {
//   path:'150000/data-download',
//   regionCode: '',
//   component: '@/pages/150000/dataDownload',
//   title: '数据管理',
// },

interface Route {
  component?: string;
  hiddenInMenu?: boolean;
  icon?: string;
  key: string;
  path?: string;
  redirect?: string;
  regionCode: number | string;
  title: string;
  children?: Route[];
}

let extraRoutes: Route[];

const getRoutes: () => Promise<Route[]> | undefined = () => {
  const token = localStorage.getItem('__NMENG_TOKEN__');
  if (!token) {
    history.push('/login');
    return;
  }
  // eslint-disable-next-line consistent-return
  return new Promise((resolve) => {
    resolve([
      {
        path: '',
        regionCode: '',
        component: 'Overview',
        title: '首页监控一张图',
        key: '2',
        icon: 'icon-menu-1',
      },
      {
        path: 'thematic-map',
        regionCode: '',
        component: 'ThematicMap',
        title: '专题图',
        key: '3',
        icon: 'icon-menu-2',
        hiddenInMenu: true,
      },
      {
        path: 'thematic-map-tpl',
        regionCode: '',
        component: 'ThematicMapTpl',
        title: '专题图模板',
        key: '3',
        icon: 'icon-menu-2',
        hiddenInMenu: true,
      },
      {
        path: 'thematic-map-list',
        regionCode: '',
        component: 'ThematicMapList',
        title: '专题图列表',
        key: '3',
      },
      {
        path: 'thematic-map-tpl-list',
        regionCode: '',
        component: 'ThematicMapTplList',
        title: '模板列表',
        key: '3',
        hiddenInMenu: true,
      },
      {
        path: 'comprehensive-assessment',
        regionCode: '',
        component: 'ComprehensiveAssessment',
        title: '综合评估',
        key: '6',
        icon: 'icon-menu-5',
      },
      {
        title: '系统管理',
        key: '8',
        regionCode: '',
        icon: 'icon-menu-6',
        children: [
          {
            path: 'user-management',
            regionCode: '',
            component: 'UserManagement',
            title: '用户管理',
            key: '8-1',
          },
          {
            path: 'data-receive-monitoring',
            regionCode: '',
            component: 'ReceiveMonitoring',
            title: '数据接收监控',
            key: '8-2',
          },
          {
            regionCode: '',
            path: 'data-download',
            component: 'DataDownload',
            title: '数据下载',
            key: '8-3',
          },
          {
            regionCode: '',
            path: 'receiving-log',
            component: 'ReceivingLog',
            title: '数据接收日志',
            key: '8-4',
          },
          {
            regionCode: '',
            path: 'system-log',
            component: 'SystemLog',
            title: '系统日志',
            key: '8-5',
          },
        ],
      },
    ]);
  });
};

export function patchRoutes({ routes }: any) {
  if (extraRoutes) {
    extraRoutes.forEach((route) => {
      try {
        routes[0].routes.unshift({
          regionCode: '',
          path: `${route.regionCode ? `/${route.regionCode}` : ''}/${
            route.path
          }`,
          exact: true,
          // eslint-disable-next-line max-len
          component: require(`@/pages${
            route.regionCode ? `/${route.regionCode}` : ''
          }/${route.component}`).default,
        });
      } catch (error) {
        routes[0].routes.push({
          regionCode: '',
          path: '/404',
          exact: true,
          component: require('@/pages/404').default,
        });
      }
    });
  }
}

export function render(oldRender: any) {
  const promise = getRoutes();
  if (promise) {
    promise.then((newRoutes) => {
      const formattedRoutes = newRoutes.reduce((prev: Route[], cur: Route) => {
        if (cur.children) {
          return [...prev, ...cur.children];
        }

        return [...prev, cur];
      }, []);
      extraRoutes = formattedRoutes;
      oldRender();
    });
  }
  oldRender();
}
