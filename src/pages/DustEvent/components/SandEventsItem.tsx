import CommonButton from './CommonButton';
import { ensureArray } from '@/utils/dust';

interface Props {
  label: string;
  bg: string;
  data: { key: number; value: string }[];
}

const SandEventsItem: React.FC<Props> = ({ label, bg, data }) => {
  return (
    <div
      style={{ backgroundImage: `url(/assets/images/sand-event/${bg}.png)`,width: '100%'}}
      className="bg-[length:auto_100%] bg-right bg-no-repeat rounded-[7px] border-[1px] border-solid border-[#DBEDEC] px-3.5 py-[9px]"
    >
      <div className="text-sm text-[#25265E] font-semibold mb-[5px]">
        {label}
      </div>
      <div className="flex flex-wrap gap-x-2 gap-y-1">
        {ensureArray(data).map((item) => (
          <CommonButton key={item.key} label={item.value} />
        ))}
      </div>
    </div>
  );
};

export default SandEventsItem;
