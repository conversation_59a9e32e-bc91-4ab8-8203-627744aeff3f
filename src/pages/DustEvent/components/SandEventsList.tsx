import { Divider, Skeleton } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useMount } from 'react-use';
import { Link } from 'umi';
import { scrollTopAtom, totalElementsAtom } from '../atoms';
import useSandEventList from '../hooks/useSandEventList';
import EventTop from './EventTop';
import SandEventsItem from './SandEventsItem';

const SandEventsList = ({ scrollRef }:{scrollRef:React.RefObject<HTMLDivElement>}) => {
  const [scrollTop, setScrollTop] = useAtom(scrollTopAtom);
  const totalElements = useAtomValue(totalElementsAtom);
  const { isloading, fetchListData, list, currentPage } = useSandEventList();
  
  useMount(() => {
    scrollRef.current?.scrollTo({
      top: scrollTop,
      behavior: 'smooth',
    });
  });

  return (
    <div
      id="scrollableDiv"
      className="w-full h-[calc(100vh-220px)] overflow-y-scroll scrollbar"
      ref={scrollRef}
    >
      <InfiniteScroll
        dataLength={list.length}
        next={() => !isloading && fetchListData(currentPage + 1)}
        hasMore={list.length < totalElements}
        loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
        endMessage={
          isloading ? (
            <Skeleton avatar paragraph={{ rows: 1 }} active />
          ) : (
            <Divider plain>全部加载完成</Divider>
          )
        }
        onScroll={(e) => {
          // @ts-ignore
          setScrollTop(e?.target?.scrollTop);
        }}
        scrollableTarget="scrollableDiv"
      >
        {list.map((item) => (
          <Link
            to={`/dust-event-detail?id=${item.id}`}
            key={item.code}
            className="w-full h-fit bg-white rounded-2xl flex items-center pt-2.5 pl-3.5 pr-4 mb-1.5 pb-3 cursor-pointer"
            style={{borderBottom: '1px solid #e6e6e6'}}
          >
            <img
              src={item.image}
              className="w-[170px] h-[150px] rounded-lg"
              alt=""
            />
            <div className="w-[calc(100%-184px)] h-full ml-3.5 flex flex-col justify-between">
              <EventTop {...item} />
              <div className="flex justify-between min-h-[100px] relative gap-x-[10px]">
                <SandEventsItem
                  label="沙源地"
                  bg="sand-source"
                  data={item.sources}
                />
                <SandEventsItem
                  label="起沙地区"
                  bg="sand-area"
                  data={item.sendSites}
                />
                <SandEventsItem
                  label="影响省份"
                  bg="effect-prov"
                  data={item.regions}
                />
              </div>
            </div>
          </Link>
        ))}
      </InfiniteScroll>
    </div>
  );
};

export default SandEventsList;
