import SorterComponent from '@/components/Sorter';
import { useAtom } from 'jotai';
import { sorterAtom } from '../atoms';
import { sorterOptions as options } from '../utils';

export type SelectTypes = (typeof options)[number]['value'];

export default function Sorter() {
  const [sortInfo, setSortInfo] = useAtom(sorterAtom);

  return (
    <>
      <SorterComponent<SelectTypes>
        options={options}
        sortInfo={sortInfo}
        setSortInfo={setSortInfo}
      />
    </>
  );
}
