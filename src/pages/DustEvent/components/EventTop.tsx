import { formatAreaNumber, formatAreaUnit } from '@/utils/dust/formatArea';

const EventTop = (props: APIDUST.TDustEventModel) => {
  const { code, duration, startTime, endTime, area } = props;
  return (
    <div className="mb-3.5 h-7 flex items-center justify-between">
      <div className="flex items-center">
        <div
          style={{
            background: 'linear-gradient( 90deg, #152A43 0%, #FFAA17 100%)',
          }}
          className="px-[16px] text-xl font-semibold text-white text-center rounded-full rounded-bl-none"
        >
          {code}
        </div>
        <div className="text-sm text-[#1E315E] font-medium ml-3.5">
          {startTime}-{endTime}（持续{Number(duration) / 3600}小时）
        </div>
      </div>
      <div className="flex items-end">
        <div className="text-sm text-[#666666] mr-2.5">影响面积</div>
        <div className="text-[28px] text-[#F25B20] font-semibold leading-[28px]">
          {formatAreaNumber(area)}
        </div>
        <div className="text-sm text-[#F25B20]">{formatAreaUnit(area)}k㎡</div>
      </div>
    </div>
  );
};

export default EventTop;
