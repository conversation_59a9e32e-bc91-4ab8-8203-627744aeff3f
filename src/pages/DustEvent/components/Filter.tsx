import { useRouter } from '@/hooks';
import { Button, DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import { useAtom, useSetAtom } from 'jotai';
import { useResetAtom } from 'jotai/utils';
import { useEffect, useMemo } from 'react';
import { filterAtom, filterParamsAtom } from '../atoms';
import useFilter from '../hooks/useFilter';
import { useFilterDomWidth } from '../hooks';

const { RangePicker } = DatePicker;
const Filter = () => {
  const { query } = useRouter();
  const { regionOptions, startDustOptions, dustSourceOptions } = useFilter();
  const [filterData, setFilterData] = useAtom(filterAtom);
  const setFilterParams = useSetAtom(filterParamsAtom);
  const resetFilterData = useResetAtom(filterAtom);
  const resetFilterParams = useResetAtom(filterParamsAtom);

  useEffect(() => {
    if (query.sourceCode) {
      setFilterData((prev) => ({
        ...prev,
        sourceCode: Number(query.sourceCode),
      }));
      setFilterParams((prev) => ({
        ...prev,
        sourceCode: Number(query.sourceCode),
      }));
    }
  }, [
    query.sourceCode,
    setFilterData,
    setFilterParams,
  ]);
  const rangePickerValue: [dayjs.Dayjs, dayjs.Dayjs] | null = useMemo(() => {
    if (filterData.startTime && filterData.endTime) {
      return [dayjs(filterData.startTime), dayjs(filterData.endTime)];
    }
    return null;
  }, [filterData.endTime, filterData.startTime]);

  const { curInnerWidth } = useFilterDomWidth()

  const w = useMemo(() => {
    return curInnerWidth > 1440 ? 264 : 200
  }, [curInnerWidth])

  return (
    <div id='event-filter' onWheel={(e) => e.stopPropagation()}
      className="w-full  flex items-center gap-x-[10px] px-[15px]">
      <div className="flex items-center">
        <div className="text-sm text-[#5E677C] mr-2.5 w-[58px]">数据范围</div>
        <RangePicker
          className="w-[264px]"
          style={{ width: w + 60 }}
          showTime={{ format: 'HH' }}
          value={rangePickerValue}
          format="YYYY/MM/DD HH"
          onChange={(dates, dateStrings) => {
            setFilterData((previousValue) => {
              return {
                ...previousValue,
                startTime: dateStrings[0],
                endTime: dateStrings[1],
              };
            });
          }}
          disabledDate={(current) => {
            return current && current.isAfter(dayjs().endOf('day'));
          }}
        />
      </div>
      <div className="flex items-center">
        <div className="text-sm text-[#5E677C] mr-2.5 w-[58px]">关注区域</div>
        <Select
          value={filterData.regionCode}
          style={{ width: w - 40 }}
          onChange={(val) => {
            setFilterData((previousValue) => {
              return {
                ...previousValue,
                regionCode: val,
              };
            });
          }}
          disabled
          options={regionOptions}
        />
      </div>
      <div className="flex items-center">
        <div className="text-sm text-[#5E677C] mr-2.5 w-[58px]">起沙地区</div>
        <Select
          value={filterData.sendSiteRegionCode}
          style={{ width: w - 40 }}
          onChange={(val) => {
            setFilterData((previousValue) => {
              return {
                ...previousValue,
                sendSiteRegionCode: val,
              };
            });
          }}
          options={startDustOptions}
        />
      </div>
      <div className="flex items-center">
        <div className="text-sm text-[#5E677C] mr-2.5 w-[45px]" >沙源地</div>
        <Select
          style={{ width: w - 40 }}
          value={filterData.sourceCode}
          onChange={(val) => {
            setFilterData((previousValue) => {
              return {
                ...previousValue,
                sourceCode: val,
              };
            });
          }}
          options={dustSourceOptions}
        />
      </div>
      <div className='flex-1'></div>
      <div className="flex items-center ml-[42px] text-sm ">
        <Button
          type="primary"
          className="w-[77px] h-[38px] mr-2.5 bg-[#286CFF] text-white rounded-[4px] shadow-none"
          onClick={() => {
            setFilterParams(filterData);
          }}
        >
          查询
        </Button>
        <Button
          className="w-[77px] h-[38px] rounded-[4px] bg-none border-[1px] border-[#D5DDE1]"
          onClick={() => {
            resetFilterData();
            resetFilterParams();
          }}
        >
          重置
        </Button>
      </div>
    </div>
  );
};

export default Filter;
