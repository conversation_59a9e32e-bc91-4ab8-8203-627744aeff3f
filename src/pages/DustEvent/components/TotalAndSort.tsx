import { useAtomValue } from 'jotai';
import { totalElementsAtom } from '../atoms';
import Sorter from './Sorter';

const TotalAndSort = () => {
  const totalElements = useAtomValue(totalElementsAtom);
  return (
    <div className="w-full flex justify-between text-sm text-[#5E677C] px-3 pt-[14px] pb-[10px]">
      <div>· 共筛选出 <span className='text-[#286CFF]'>{totalElements}</span> 个沙尘事件</div>
      <Sorter />
    </div>
  );
};

export default TotalAndSort;
