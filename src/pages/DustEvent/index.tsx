import HelmetTitle from "@/components/global/HealmetTitle";
import PageHead from "@/components/global/PageHead";
import { PageMain } from "@/components/ui";
import SubTitle from "../DustAnalyze/components/SubTitle";
import Filter from "./components/Filter";
import TotalAndSort from "./components/TotalAndSort";
import SandEventsList from "./components/SandEventsList";
import { useRef } from "react";


const DustEvent = () => {
  const scrollRef = useRef<HTMLDivElement>(null);
  return (
    <PageMain>
      <HelmetTitle title="沙尘事件" />
      <PageHead title="沙尘事件">
        <></>
      </PageHead>
      <div className="ml-[-24px] mt-[18px]">
        <SubTitle title="沙尘事件筛选" />
      </div>
      <div
        className="mt-[12px]"
        onWheel={(e) => {
          if (e.deltaY > 0 && scrollRef.current)
            scrollRef.current.scrollTop += 30;
          else if (e.deltaY < 0 && scrollRef.current)
            scrollRef.current.scrollTop -= 30;
        }}
      >
        <Filter />
        <TotalAndSort />
        <SandEventsList scrollRef={scrollRef} />
      </div>
    </PageMain>
  );
};
export default DustEvent;