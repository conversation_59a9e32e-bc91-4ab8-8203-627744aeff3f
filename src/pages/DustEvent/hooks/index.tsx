import { useEffect, useMemo, useState } from "react";

export const useFilterDomWidth = () => {
  const [curInnerWidth, setCurInnerWidth] = useState(window.innerWidth);
  const filterDomWidth = useMemo(() => {
    return {
      width: 264
    }
  }, [])

  function changeWindowSize() {
    setCurInnerWidth(window.innerWidth)
  }

  useEffect(() => {
    window.addEventListener("resize", changeWindowSize);
    return () => {
      window.removeEventListener("resize", changeWindowSize);
    };
  }, []);

  return { filterDomWidth, curInnerWidth };
}