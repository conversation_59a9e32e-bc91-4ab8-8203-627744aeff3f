import { getDustEventPage } from '../services';
import { ensureArray, ensureNumber } from '@/utils/dust';
import dayjs from 'dayjs';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useMemo, useState, useRef } from 'react';
import { useMount, useUpdateEffect } from 'react-use';
import {
  currentPageAtom,
  filterParamsAtom,
  listAtom,
  scrollTopAtom,
  sorterValueAtom,
  totalElementsAtom,
} from '../atoms';

export default function useSandEventList() {
  const [isloading, setIsloading] = useState(false);
  const [list, setList] = useAtom(listAtom);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useAtom(currentPageAtom);
  const filterParams = useAtomValue(filterParamsAtom);
  const sorterValue = useAtomValue(sorterValueAtom);
  const setTotalElements = useSetAtom(totalElementsAtom);
  const setScrollTop = useSetAtom(scrollTopAtom);
  const requestIdRef = useRef(0);

  const params = useMemo(() => {
    return {
      ...filterParams,
      startTime: !!filterParams.startTime
        ? dayjs(filterParams.startTime).format('YYYY/MM/DD HH:mm:ss')
        : undefined,
      endTime: !!filterParams.endTime
        ? dayjs(filterParams.endTime).format('YYYY/MM/DD HH:mm:ss')
        : undefined,
      sort: sorterValue,
      sendSiteRegionCode:
        filterParams.sendSiteRegionCode === 0
          ? undefined
          : filterParams.sendSiteRegionCode,
      sourceCode:
        filterParams.sourceCode === 0 ? undefined : filterParams.sourceCode,
      pageSize: 10,
    };
  }, [filterParams, sorterValue]);

  const fetchListData = useCallback(
    (pageNum: number) => {
      const currentRequestId = ++requestIdRef.current;
      setIsloading(true);
      getDustEventPage({ ...params, pageNum })
        .then((value) => {
          if (currentRequestId !== requestIdRef.current) {
            return;
          }
          const resultList = ensureArray(value?.content);
          setList((previousData) => [...previousData, ...resultList]);
          setTotal(ensureNumber(value?.totalElements));
          setCurrentPage(ensureNumber(value?.pageable?.pageNumber));
          setTotalElements(ensureNumber(value?.totalElements));
          setIsloading(false);
        })
        .catch((reason) => {
          if (currentRequestId === requestIdRef.current) {
            console.error(reason);
          }
          setIsloading(false);
        });
    },
    [params, setList, setTotalElements, setCurrentPage],
  );

  const resetDustEventParamsAndData = useCallback(() => {
    setTotalElements(0);
    setList([]);
    setCurrentPage(0);
    fetchListData(0);
    setScrollTop(0);
  }, [fetchListData, setCurrentPage, setList, setScrollTop, setTotalElements]);

  useMount(() => {
    if (list?.length === 0) {
      fetchListData(0);
    }
  });

  useUpdateEffect(() => {
    resetDustEventParamsAndData();
  }, [params]);

  return {
    isloading,
    list,
    fetchListData,
    total,
    currentPage,
    setCurrentPage,
    resetDustEventParamsAndData,
  };
}
