import { getDustEventSendSiteRegionList, getDustSourceList } from '../services';
import { ensureArray } from '@/utils/dust';
import { regionList } from '@/utils/dust/regionList';
import { useMemo } from 'react';
import { useQuery } from 'react-query';

export default function useFilter() {
  const regionOptions = useMemo(() => {
    return regionList.map(({ code, name }) => {
      return { value: code, label: name === '全国' ? '中国' : name };
    });
  }, []);

  const { data: startDustData } = useQuery(
    '/api/dust/event/send/site/region/list',
    () => getDustEventSendSiteRegionList(),
  );

  const startDustOptions = useMemo(() => {
    const formattedData: { value: number | undefined; label: string }[] =
      ensureArray(startDustData)?.map((item) => {
        return { value: item.key, label: item.value };
      });
    formattedData.unshift({ value: 0, label: '全部' });
    return formattedData;
  }, [startDustData]);

  const { data: dustSourceData } = useQuery(['/api/dust/source/list'], () =>
    getDustSourceList(),
  );

  const dustSourceOptions = useMemo(() => {
    const formattedData = dustSourceData?.map(({ code, name }) => {
      return { value: code, label: name };
    });
    formattedData?.unshift({ value: 0, label: '全部' });
    return formattedData;
  }, [dustSourceData]);

  return { regionOptions, startDustOptions, dustSourceOptions };
}
