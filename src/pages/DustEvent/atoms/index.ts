import { SortInfo } from '@/components/Sorter';
import { atom } from 'jotai';
import { atomWithReset } from 'jotai/utils';
import { SelectTypes } from '../components/Sorter';
import { defaultFilterParams } from '../utils';

type FilterParams = {
  startTime?: string;
  endTime?: string;
  regionCode?: number;
  sendSiteRegionCode?: number;
  sourceCode?: number;
};

// 用于暂存筛选条件
export const filterAtom = atomWithReset<FilterParams>(defaultFilterParams);

// 用于网络请求
export const filterParamsAtom =
  atomWithReset<FilterParams>(defaultFilterParams);

export const sorterAtom = atomWithReset<SortInfo<SelectTypes>>({
  type: 'startTime',
  direction: 'down',
});

export const sorterValueAtom = atom((get) => {
  const { type, direction } = get(sorterAtom);
  const key = `${type}-${direction}`;

  switch (key) {
    case 'startTime-up':
      return 5;
    case 'startTime-down':
      return 6;
    case 'area-up':
      return 1;
    case 'area-down':
      return 2;
    case 'duration-up':
      return 3;
    case 'duration-down':
      return 4;
    default:
      return undefined;
  }
});

export const totalElementsAtom = atomWithReset(0);

export const listAtom = atomWithReset<APIDUST.TDustEventModel[]>([]);

export const currentPageAtom = atomWithReset(0);

export const scrollTopAtom = atomWithReset(0);
