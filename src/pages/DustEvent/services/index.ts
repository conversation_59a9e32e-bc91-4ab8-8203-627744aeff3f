import { request } from '@/utils';
import { stringify } from 'qs';


 /** 获取沙尘事件次数统计-沙源地 */ 
 export const getDustEventStatsCountSource = (params: APIDUST.TGetDustEventStatsCountSourceParams ) => {
	return request(`/api/dust/event/stats/count/source?${stringify(params)}`) as Promise<APIDUST.TDustEventStatsCountModel>
}

/** 获取起沙地行政区信息 */ 
export const getDustEventSendSiteRegionList = () => {
	return request(`/api/dust/event/send/site/region/list`) as Promise<APIDUST.TKeyValuePairModelIntegerString[]>
}

/** 获取沙尘事件影响行政区信息 */ 
export const getDustEventRegionList = (params: APIDUST.TGetDustEventRegionListParams ) => {
	return request(`/api/dust/event/region/list?${stringify(params)}`) as Promise<APIDUST.TDustEventRegionModel[]>
}

/** 获取沙尘事件列表 */ 
export const getDustEventPage = (params: APIDUST.TGetDustEventPageParams ) => {
	return request(`/api/dust/event/page?${stringify(params)}`) as Promise<APIDUST.TPageDustEventModel>
}

/** 获取沙尘事件详情 */ 
export const getDustEventInfo = (params: APIDUST.TGetDustEventInfoParams ) => {
	return request(`/api/dust/event/info?${stringify(params)}`) as Promise<APIDUST.TDustEventInfoModel>
}

/** 获取沙源地列表 */ 
export const getDustSourceList = () => {
	return request(`/api/dust/source/list`) as Promise<APIDUST.TDustSourceModel[]>
}
