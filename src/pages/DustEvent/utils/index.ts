export const eventOptions: {
  label: string;
  value: string;
  bg: string;
}[] = [
  {
    label: '沙源地',
    value: '1',
    bg: 'sand-source',
  },
  {
    label: '起沙地区',
    value: '2',
    bg: 'sand-area',
  },
  {
    label: '影响省份',
    value: '3',
    bg: 'effect-prov',
  },
];

export const sorterOptions = [
  { label: '沙尘影响面积', value: 'area' },
  { label: '持续时间', value: 'duration' },
  { label: '开始时间', value: 'startTime' },
];

export const defaultFilterParams = {
  regionCode: 150000, //内蒙古自治区代码，默认为内蒙古自治区
  startTime: undefined,
  endTime: undefined,
  sendSiteRegionCode: 0,
  sourceCode: 0,
};

export function ensureObject<T>(data: T | undefined): T {
  return data instanceof Object ? data : ({} as T);
}