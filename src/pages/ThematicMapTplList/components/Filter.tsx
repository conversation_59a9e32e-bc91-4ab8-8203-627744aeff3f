import React, { useMemo, useCallback, useEffect } from 'react';
import { userInfoAtom } from '@/atoms';
import { SearchOutlined } from '@ant-design/icons';
import { Cascader, Col, DatePicker, Input, Select } from 'antd';
import { useAtomValue } from 'jotai';
import { FilterRow } from './ui';
import { dateFormatter } from '@/utils';
import { OutlinedButton, Spacer } from '@/components/ui';
import {
  useCascaderOptionsAndMatchValues,
  useRouter,
} from '@/hooks';
import { useAtom } from 'jotai';
import { filtersAtom } from '../atoms';
import { getTmUserList } from '@/pages/ThematicMap/services';
import { useQuery } from 'react-query';
import type { ApiListData } from '@/utils/types';
import type { User } from '@/types';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

const typeOpts = [
  {
    label: '日均值',
    value: 'daily',
  },
  {
    label: '周均值',
    value: 'weekly',
  },
  {
    label: '月均值',
    value: 'monthly',
  },
  {
    label: '季均值',
    value: 'quarterly',
  },
  {
    label: '年均值',
    value: 'yearly',
  },
];

const Filter = () => {
  const userInfo = useAtomValue(userInfoAtom);
  const [filters, setFilters] = useAtom(filtersAtom);
  const { name, cycleType, regionCode, status, uid, startDate, endDate } =
    filters;
  const { history } = useRouter();
  const { options, cascaderValue } = useCascaderOptionsAndMatchValues(
    Number(regionCode), true
  );

  const updateFilters = useCallback(
    (newVal: Record<string, string | number | undefined>) => {
      setFilters((prev) => ({
        ...prev,
        ...newVal,
      }));
    },
    [setFilters],
  );
  const { data: users } = useQuery<ApiListData<User>>(
    ['user-list-in-select', userInfo?.regionCode],
    () => getTmUserList(Number(userInfo?.regionCode)),
    {
      enabled: Boolean(userInfo?.regionCode),
    },
  );
  const userOptions = useMemo(() => {
    if (users) {
      return users?.map((item: any) => ({
        label: item.userName,
        value: item.userId,
      }));
    }
    return [];
  }, [users]);

  const cascaderOptions = useMemo(() => {
    return [
      ...options,
      // {
      //   code: 100000,
      //   name: '全域',
      //   disabled: userInfo?.regionCode === 150000 ? false : true
      // },
    ];
  }, [options]);

  const showCascaderValue = useMemo(() => {
    return regionCode ? regionCode === 100000 ? regionCode : cascaderValue : undefined;
  }, [cascaderValue, regionCode])

  useEffect(() => {
    if (userInfo?.regionCode && !regionCode) {
      updateFilters({
        regionCode: userInfo.regionCode,
      });
    }
  }, [updateFilters, userInfo?.regionCode, regionCode]);

  return (
    <FilterRow gutter={12}>
      <Col>
        <Input
          prefix={<SearchOutlined />}
          placeholder="请输入模板名称"
          value={name}
          onChange={(e) =>
            updateFilters({
              name: e.target.value,
            })
          }
        />
      </Col>
      <Col>
        <Cascader
          style={{ width: 240 }}
          changeOnSelect
          options={cascaderOptions}
          value={showCascaderValue}
          fieldNames={{
            label: 'name',
            value: 'code',
          }}
          onChange={(val) => {
            updateFilters({
              regionCode:
                val?.length > 0 ? +val[val?.length - 1] : userInfo!.regionCode,
              page: 0,
            });
          }}
        />
      </Col>
      <Col>
        <Select
          style={{ width: 140 }}
          options={typeOpts}
          placeholder="模板类型"
          value={cycleType}
          allowClear
          onChange={(val) => {
            updateFilters({
              cycleType: val,
            });
          }}
        />
      </Col>
      <Col>
        <Select
          style={{ width: 140 }}
          allowClear
          options={[
            {
              label: '正常',
              value: 1,
            },
            {
              label: '暂停',
              value: 0,
            },
          ]}
          placeholder="状态"
          value={status}
          onChange={(val) =>
            updateFilters({
              status: val,
            })
          }
        />
      </Col>

      <Col>
        <Select
          style={{ width: 140 }}
          options={userOptions}
          placeholder="创建者"
          value={uid}
          allowClear
          onChange={(val: any) =>
            updateFilters({
              uid: val,
            })
          }
        />
      </Col>
      <Col>
        <RangePicker
          allowClear
          style={{ width: 400 }}
          value={
            startDate && endDate
              ? [dayjs(startDate), dayjs(endDate)]
              : undefined
          }
          onChange={(dates) => {
            if (dates && dates.length === 2) {
              updateFilters({
                startDate: dates[0]?.format(dateFormatter),
                endDate: dates[1]?.format(dateFormatter),
              });
            } else {
              updateFilters({
                startDate: undefined,
                endDate: undefined,
              });
            }
          }}
          disabledDate={(current) => {
            return current && current.isAfter(dayjs().endOf('day'));
          }}
        />
      </Col>
      <Spacer />
      <OutlinedButton
        onClick={() => {
          history.push('/thematic-map-tpl');
        }}
      >
        创建模板
      </OutlinedButton>
    </FilterRow>
  );
};

export default Filter;
