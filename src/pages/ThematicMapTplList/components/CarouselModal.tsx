import DarkModal from '@/components/global/DarkModal';
import { Block, Center, Flex, HorCenter } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import type { TmItem } from '@/pages/ThematicMap/types';
import type { RotationListParams } from '@/pages/ThematicMapTpl/services';
import { getTmTplRotationList } from '@/pages/ThematicMapTpl/services';
import { dateFormatter } from '@/utils';
import type { ApiListData } from '@/utils/types';
import { ConfigProvider, DatePicker, Empty, Pagination, Select, Spin } from 'antd';
import { useAtom } from 'jotai';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { currentTplAtom } from '../atoms';
import {
  AutoPlayBtn,
  CarouselContainer,
  CarouselFilter,
  ImgWrapper,
  ListItem,
  ModalCloseBtn,
  PagiContainer,
  Right,
  SortArea,
  SortBtn,
  Title,
  ImgContainer
} from './ui';
import dayjs from 'dayjs';
import { twMerge } from 'tailwind-merge';

const { RangePicker } = DatePicker;

interface Props {
  visible?: boolean;
  onCancel: () => void;
}

const sortOptions = ['asc', 'desc'] as const;

const CarouselModal: React.FC<Props> = ({ visible, onCancel }) => {
  const [currentTpl] = useAtom(currentTplAtom);
  const [currentTm, setCurrentTm] = useState<TmItem | undefined>();
  const [isStarting, setIsStarting] = useState(false);
  const [index, setIndex] = useState(0);
  const token = localStorage.getItem('__NMENG_TOKEN__')
  const [params, setParams] = useState<Omit<RotationListParams, 'id'>>({
    createType: undefined,
    startDate: undefined,
    endDate: undefined,
    page: 0,
    sortType: 'desc',
    size: 6,
  });

  const { data, isLoading } = useQuery<ApiListData<TmItem>>(
    ['rotation-list', params, currentTpl],
    () =>
      getTmTplRotationList({
        id: Number(currentTpl?.id),
        ...params,
      }),
    {
      enabled: Boolean(currentTpl),
    },
  );

  useEffect(() => {
    if (data?.content && data.content.length > 0) {
      setCurrentTm(data.content[0]);
    } else {
      setCurrentTm(undefined);
    }
  }, [data?.content]);

  const toggle = useCallback(() => {
    setIsStarting((prev) => !prev);
  }, []);

  useEffect(() => {
    if (!data || data.content.length === 0 || !isStarting) {
      return;
    }
    setIndex(0);
    const timer = setInterval(() => {
      setIndex((prev) => {
        if (prev < (data?.content || []).length - 1) {
          return prev + 1;
        }

        setIsStarting(false);
        clearInterval(timer);
        return 0;
      });
    }, 5000);

    return () => {
      clearInterval(timer);
    };
  }, [data, data?.content.length, isStarting]);

  useEffect(() => {
    if (data && data.content.length > 0) {
      setCurrentTm(data.content[index]);
    }
  }, [data, index]);

  useEffect(() => {
    return () => {
      setCurrentTm(undefined);
      setParams({
        createType: undefined,
        startDate: undefined,
        endDate: undefined,
        page: 0,
        sortType: 'desc',
        size: 6,
      })
    };
  }, [visible]);

  const imgSize = useMemo(() => {
    const currentTmContent = currentTm?.content || '';
    if (currentTmContent) {
      const canvasSize = JSON.parse(currentTmContent);
      return {
        width: canvasSize.width,
        height: canvasSize.height,
      };
    }

    return {
      width: 0,
      height: 0,
    };
  }, [currentTm]);

  return (
    <ConfigProvider
      theme={{
        token: {
          colorText: '#c1c1c4', // 设置主字体颜色
          colorBgBase: '#1c1d24',
          colorPrimary: '#286cff',
          colorTextPlaceholder: 'rgba(255, 255, 255, 0.25)',
        },
      }}
    >
      <DarkModal
        width={1540}
        open={visible}
        onCancel={onCancel}
        destroyOnHidden={true}

      >
        <CarouselContainer className='h-[720px]'
          style={{
            overflow: 'auto'
          }}
        >
          <ModalCloseBtn onClick={onCancel}>
            <i className="icomoon icon-close" />
          </ModalCloseBtn>
          <ImgWrapper className={twMerge(``, imgSize.width <= 1050 || imgSize.height <= 700 ? 'flex items-center justify-center' : '')}
            style={{
              overflow: 'auto'
            }}
          >
            {currentTm?.imageUrl &&
              <div>
                <ImgContainer
                  style={{
                    width: imgSize.width >= 1050 ? '100%' : imgSize.width,
                    height: imgSize.height >= 700 ? '700px' : imgSize.height,
                  }}>
                  <img
                    alt={currentTm?.name} src={currentTm?.imageUrl.trim() + `&token=${token}`}
                    className="block"
                    style={{
                      width: imgSize.width >= 1050 ? '100%' : imgSize.width,
                      height: imgSize.height >= 700 ? '700px' : imgSize.height,
                    }}
                  />
                </ImgContainer>
              </div>
            }
            {data?.content.length === 0 && !isLoading && (
              <Block padding="40px">
                <Center>暂无可轮播的图片</Center>
              </Block>
            )}
          </ImgWrapper>
          <Right>
            <Title>{currentTpl?.name}</Title>
            <CarouselFilter>
              <Select
                allowClear
                className="dark-form-item select"
                getPopupContainer={getDarkContainer}
                placeholder="生成方式"
                value={params.createType}
                onChange={(val) => {
                  setParams((prev) => ({
                    ...prev,
                    page: 0,
                    createType: val || '',
                  }));
                }}
                options={[
                  {
                    label: '自动生成',
                    value: 1,
                  },
                  {
                    label: '手动生成',
                    value: 2,
                  },
                ]}
              />
              <RangePicker
                className="w-full dark-form-item"
                getPopupContainer={getDarkContainer}
                allowClear
                disabledDate={(date) => {
                  return date.isAfter(dayjs().endOf('d'));
                }}
                value={params.startDate && params.endDate ? [dayjs(params.startDate), dayjs(params.endDate)] : undefined}
                onChange={(dates) => {
                  if (dates && dates[0] && dates[1]) {
                    setParams((prev) => ({
                      ...prev,
                      page: 0,
                      startDate: dates[0]?.format(dateFormatter),
                      endDate: dates[1]?.format(dateFormatter),
                    }));
                  } else {
                    setParams((prev) => ({
                      ...prev,
                      startDate: undefined,
                      endDate: undefined,
                    }));
                  }
                }}
              />
            </CarouselFilter>
            <SortArea>
              <span>共{data?.totalElements}个</span>
              <HorCenter>
                <span>排序</span>
                <Flex style={{ marginLeft: 8 }}>
                  {sortOptions.map((opt) => (
                    <SortBtn
                      key={opt}
                      active={params.sortType === opt}
                      onClick={() =>
                        setParams((prev) => ({
                          ...prev,
                          sortType: opt,
                          page: 0,
                        }))
                      }
                    >
                      <img src={`/assets/images/sort-${opt}.png`} alt="" />
                    </SortBtn>
                  ))}
                </Flex>
              </HorCenter>
            </SortArea>
            {(data?.content || []).length > 0 && <AutoPlayBtn onClick={toggle}>{isStarting ? '停止播放' : '开始播放'}</AutoPlayBtn>}
            {isLoading && (
              <Center>
                <Block padding="40px">
                  <Spin />
                </Block>
              </Center>
            )}
            {(data?.content || []).length === 0 && !isLoading && (
              <Center>
                <Block padding="40px">
                  <Empty className="dark-empty" />
                </Block>
              </Center>
            )}

            {(data?.content || []).map((item, idx) => (
              <ListItem
                key={item.id}
                active={item.id === currentTm?.id}
                onClick={() => {
                  setCurrentTm(item);
                }}
              >
                <Flex className="title">
                  <div className="index">{idx + 1}</div>
                  <div className="name">{item.name}</div>
                </Flex>
                <div className="date-range">
                  数据范围：
                  {item.fromDate && item.toDate ? `${item.fromDate}-${item.toDate}` : '-'}
                </div>
              </ListItem>
            ))}
            {(data?.totalElements || 0) > 0 && (
              <PagiContainer>
                <Pagination
                  size="small"
                  current={(data?.pageable.pageNumber || 0) + 1}
                  total={data?.totalElements}
                  showSizeChanger={false}
                  pageSize={6}
                  onChange={(val) => {
                    setParams((prev) => ({
                      ...prev,
                      page: val - 1,
                    }));
                  }}
                />
              </PagiContainer>
            )}
          </Right>
        </CarouselContainer>
      </DarkModal>
    </ConfigProvider>
  );
};

export default CarouselModal;
