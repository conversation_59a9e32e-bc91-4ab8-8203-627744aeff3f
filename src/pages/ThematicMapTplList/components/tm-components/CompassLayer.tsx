import type { CompassLayerProps } from '@/pages/ThematicMap/types';
import React from 'react';
import { useMemo } from 'react';

const CompassLayer: React.FC<{
  compassLayer: CompassLayerProps;
  mapType: string;
}> = ({ compassLayer, mapType }) => {
  const compassImg = useMemo(() => {
    if (mapType === 'vec') {
      return `/assets/images/compass-in-tm.png`;
    }
    return `/assets/images/compass-white-big.png`;
  }, [mapType]);

  return compassLayer.type ? (
    <>
      <div
        id={compassLayer.id}
        style={{
          position: 'absolute',
          top: compassLayer.props.top,
          left: compassLayer.props.left,
          zIndex: 9999,
          width: compassLayer.props.width,
          height: compassLayer.props.height,
          display: compassLayer.visible ? 'block' : 'none',
        }}
      >
        <img
          src={compassImg}
          alt=""
          style={{ width: '100%', userSelect: 'none', pointerEvents: 'none' }}
        />
      </div>
    </>
  ) : null;
};

export default CompassLayer;
