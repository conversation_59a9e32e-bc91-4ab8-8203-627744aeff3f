import { Flex, Spacer } from '@/components/ui';
import { remoteSensingValuesAndColors, weatherValuesAndColors } from '@/utils';
import React from 'react';
import { useMemo } from 'react';
import { LegendItem, LegendType, LegendWrapper } from './ui';

interface Props {
  type: string;
  id: string;
  top: number;
  left: number;
  visible: boolean;
  translate: number[];
  scale: number[];
}
const HeatmapLegend: React.FC<Props> = ({
  type,
  id,
  left,
  top,
  translate,
  scale,
  visible,
}) => {
  const lowerType = type.toLowerCase();
  const target =
    remoteSensingValuesAndColors[lowerType] ||
    weatherValuesAndColors[lowerType];
  const result = useMemo(() => {
    return [...(target?.values || [])].reverse();
  }, [target.values]);

  return (
    <LegendWrapper
      id={id}
      style={{
        display: visible ? 'flex' : 'none',
        position: 'absolute',
        zIndex: 1001,
        top,
        left,
        cursor: 'move',
        wordBreak: 'keep-all',
        userSelect: 'none',
        transform:
          `translate(${translate[0]}px, ` +
          ` ${translate[1]}px) scale(${scale[0]}, ${scale[1]})`,
      }}
    >
      <LegendType>
        <p>{target.cn || target.formula}</p>
        {target.unit && <p>({target.unit})</p>}
      </LegendType>
      <Flex>
        {result.map(
          (
            item: { min?: number; max?: number; color: string },
            index: number,
          ) => (
            <LegendItem key={item.color}>
              <div
                className="lump"
                style={{
                  width: 30,
                  height: 8,
                  marginTop: 8,
                  background: item.color,
                }}
              />
              <div
                className="min"
                style={{
                  transform: index === 0 ? 'tranlateX(-50%)' : 'none',
                  height: '1px',
                }}
              >
                {index === 0 && typeof item.min !== 'undefined' ? item.min : ''}
              </div>
              <Spacer />
              <div className="max">{item.max}</div>
            </LegendItem>
          ),
        )}
      </Flex>
    </LegendWrapper>
  );
};

export default HeatmapLegend;
