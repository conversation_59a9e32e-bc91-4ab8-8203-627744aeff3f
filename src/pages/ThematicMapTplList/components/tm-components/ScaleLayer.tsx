import { useMemo } from 'react';
import type { Viewport } from 'deck.gl';
import { transformRgbColorToString } from '@/pages/ThematicMapTpl/utils';
import { ScaleContainer } from './ui';

const ScaleLayer: React.FC<{
  layers: any;
  viewport: Viewport;
}> = ({ layers, viewport }) => {
  const layer = useMemo(() => {
    return layers.find((ly: any) => ly.type === 'scale');
  }, [layers]);
  const val = Math.round(viewport.metersPerPixel * 100);
  const formatVal = (value: number) =>
    value > 5000 ? `${Math.round(value / 1000)}千米` : `${value}米`;
  return layer ? (
    <>
      <ScaleContainer
        id={layer.id}
        fontSize={layer.props.fontSize}
        color={transformRgbColorToString(layer.props.color)}
        borderStyle={layer.props.borderStyle}
        borderWidth={layer.props.borderWidth}
        style={{
          top: layer.props.top,
          left: layer.props.left,
          display: layer.visible ? 'flex' : 'none',
          transform:
            `translate(${layer.props.translate[0]}px, ` +
            ` ${layer.props.translate[1]}px)`,
          zIndex: 9999,
        }}
      >
        <div className="bar">
          <span>{formatVal(val)}</span>
        </div>
      </ScaleContainer>
    </>
  ) : null;
};

export default ScaleLayer;
