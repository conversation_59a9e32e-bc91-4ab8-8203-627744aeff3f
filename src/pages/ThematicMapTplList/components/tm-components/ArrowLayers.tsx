import type { ArrowLayerProps } from '@/pages/ThematicMapTpl/types';
import { transformRgbColorToString } from '@/pages/ThematicMapTpl/utils';
import React from 'react';

const ArrowLayers: React.FC<{
  layers: ArrowLayerProps[];
  index: number;
}> = ({ layers, index }) => {
  const strokeDashArray = {
    dashed: '10, 20',
    dotted: '5, 5',
    solid: '',
  };
  return (
    <>
      {layers.map((layer) => {
        const { id, props } = layer;

        return (
          <React.Fragment key={id}>
            <div
              id={id}
              className="target"
              style={{
                position: 'absolute',
                top: props.top,
                left: props.left,
                display: layer.visible ? 'block' : 'none',
                width: props.width,
                height: props.height,
                zIndex: 1000 - index,
                opacity: props.opacity / 100,
                transform:
                  `translate(${props.translate[0]}px,` +
                  ` ${props.translate[1]}px) ` +
                  `scale(${props.scale[0]}, ${props.scale[1]})` +
                  ` rotate(${props.rotate}deg)`,
              }}
            >
              <svg
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                width={48}
                height={48}
              >
                <path
                  // eslint-disable-next-line max-len
                  d="M263.232 576L640 931.84 554.24 1024 0 515.392 549.952 0 640 98.88 270.4 448H1024v128H263.232z"
                  fill={transformRgbColorToString(layer.props.backgroundColor)}
                  strokeWidth={layer.props.borderWidth}
                  stroke={transformRgbColorToString(layer.props.borderColor)}
                  strokeDasharray={strokeDashArray[layer.props.borderStyle]}
                />
              </svg>
            </div>
          </React.Fragment>
        );
      })}
    </>
  );
};

export default ArrowLayers;
