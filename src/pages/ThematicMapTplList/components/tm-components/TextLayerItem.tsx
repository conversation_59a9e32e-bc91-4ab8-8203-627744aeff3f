import type { TextLayerProps } from '@/pages/ThematicMap/types';
import React from 'react';

const TextLayerItem: React.FC<{
  layer: TextLayerProps;
  index: number;
}> = ({ layer, index }) => {
  const { visible, id } = layer;
  const { top, left, color, width, height, fontSize, fontWeight, fontFamily } =
    layer.props;
  const { r, g, b, a } = color;

  return (
    <>
      <div
        id={id}
        style={{
          position: 'absolute',
          top,
          left,
          width,
          height,
          color: `rgba(${r}, ${g}, ${b}, ${a})`,
          fontSize,
          fontWeight,
          fontFamily,
          lineHeight: 1.5,
          zIndex: 1000 - index,
          userSelect: 'none',
          wordBreak: 'break-all',
          display: visible ? 'block' : 'none',
        }}
      >
        {layer.name}
      </div>
    </>
  );
};

export default TextLayerItem;
