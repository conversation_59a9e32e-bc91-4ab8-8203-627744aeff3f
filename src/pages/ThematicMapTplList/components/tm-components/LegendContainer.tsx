import React, { useMemo } from 'react';
import HeatmapLegend from './HeatmapLegend';
import type { Legend } from '@/pages/ThematicMap/types';
import { DcolorLegendItem } from '@/pages/ThematicMap/components/ui';

const LegendContainer: React.FC<{
  layerLegends: Legend[];
}> = ({ layerLegends }) => {
  const heatmapLayerLegends = useMemo(
    () => layerLegends.filter((legend) => legend.layerType === 'heatmap'),
    [layerLegends],
  );
  const dcolorLayerLegends = useMemo(
    () => layerLegends.filter((legend) => legend.dataType === 'DCOLOR'),
    [layerLegends],
  );

  return (
    <>
      {heatmapLayerLegends.map((legend) => {
        return (
          <React.Fragment key={legend.id}>
            <HeatmapLegend
              key={legend.id}
              top={legend.props.top}
              left={legend.props.left}
              type={legend.dataType}
              id={legend.id}
              visible={legend.visible}
              scale={legend.props.scale}
              translate={legend.props.translate}
            />
          </React.Fragment>
        );
      })}
      {dcolorLayerLegends.map((legend) => {
        const { scale, translate } = legend.props;

        return (
          <React.Fragment key={legend.id}>
            <DcolorLegendItem
              id={legend.id}
              style={{
                position: 'absolute',
                top: legend.props.top,
                left: legend.props.left,
                zIndex: 1001,
                display: legend.visible ? 'flex' : 'none',
                // eslint-disable-next-line max-len
                transform: `translate(${translate[0]}px, ${translate[1]}px) scale(${scale[0]}, ${scale[1]})`,
                userSelect: 'none',
              }}
            >
              <div
                className="block"
                style={{
                  background: legend.props.color,
                  backgroundImage:
                    `linear-gradient` +
                    `(135deg, ${legend.props.from}, ${legend.props.to})`,
                }}
              />
              <span className="label">{legend.props.label}</span>
            </DcolorLegendItem>
          </React.Fragment>
        );
      })}
    </>
  );
};

export default LegendContainer;
