import type { Viewport } from 'deck.gl';
import React, { useMemo } from 'react';
import type { CircleLayerProps } from '@/pages/ThematicMap/types';
import { transformRgbColorToString } from '@/pages/ThematicMap/utils';
import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import { useAtomValue } from 'jotai';
import { viewportAtom } from '../../atoms';

const CircleLayerItem: React.FC<{
  id: string;
  eleTop: number;
  eleLeft: number;
  halfWidth: number;
  visible: boolean;
  viewport: Viewport;
  props: CircleLayerProps['props'];
  index: number;
}> = ({ id, eleTop, eleLeft, halfWidth, visible, props, index }) => {
  return (
    <React.Fragment key={id}>
      <div
        id={id}
        style={{
          position: 'absolute',
          top: eleTop,
          left: eleLeft,
          width: halfWidth * 2,
          height: halfWidth * 2,
          zIndex: 1000 - index,
          background: transformRgbColorToString(props.backgroundColor),
          borderWidth: props.borderWidth,
          borderStyle: props.borderStyle,
          borderColor: transformRgbColorToString(props.borderColor),
          opacity: props.opacity / 100,
          borderRadius: props.as === 'rectangle' ? 0 : 9999,
          display: visible ? 'block' : 'none',
        }}
      />
    </React.Fragment>
  );
};

const CircleLayers: React.FC<{
  layers: CircleLayerProps[];
  viewState: ViewStateProps;
}> = ({ layers }) => {
  const viewport = useAtomValue(viewportAtom);
  const circleLayers = useMemo(
    () =>
      layers.filter((layer) => layer.type === 'circle') as CircleLayerProps[],
    [layers],
  );

  return (
    <>
      {circleLayers.map((layer) => {
        try {
          const { id, props, visible } = layer;
          const index = layers.findIndex((item) => item.id === layer.id);
          const { unit, radius } = props;
          const halfWidth =
            (radius * (unit === 'km' ? 1000 : 1)) / viewport.metersPerPixel;
          const [x, y] = viewport
            ? viewport.project([props.lon, props.lat])
            : [0, 0];
          const eleLeft = x - halfWidth;
          const eleTop = y - halfWidth;

          return (
            <CircleLayerItem
              id={id}
              key={id}
              halfWidth={halfWidth}
              index={index}
              props={props}
              viewport={viewport}
              eleLeft={eleLeft}
              eleTop={eleTop}
              visible={visible}
            />
          );
        } catch (error) {
          return null;
        }
      })}
    </>
  );
};

export default CircleLayers;
