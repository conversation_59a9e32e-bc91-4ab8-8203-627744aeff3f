import type { ImageLayerProps, LayerProps } from '@/pages/ThematicMap/types';
import React, { useMemo } from 'react';

const ImageLayerItem: React.FC<{
  layer: ImageLayerProps;
  index: number;
}> = ({ layer, index }) => {
  const { props, id, visible } = layer;

  return (
    <React.Fragment key={id}>
      <div
        id={id}
        style={{
          position: 'absolute',
          top: props.top,
          left: props.left,
          width: props.width,
          zIndex: 1000 - index,
          display: visible ? 'block' : 'none',
        }}
      >
        <img src={props.url} alt="" width="100%" />
      </div>
    </React.Fragment>
  );
};

const ImageLayers: React.FC<{
  layers: LayerProps[];
}> = ({ layers }) => {
  const imageLayers = useMemo(() => {
    return layers.filter((item) => item.type === 'image') as ImageLayerProps[];
  }, [layers]);

  return (
    <>
      {imageLayers.map((layer: ImageLayerProps) => {
        const { id } = layer;
        const index = layers.findIndex((item) => item.id === id);

        return <ImageLayerItem key={id} layer={layer} index={index} />;
      })}
    </>
  );
};

export default ImageLayers;
