import { useMemo } from 'react';
import type { TextLayerProps } from '@/pages/ThematicMapTpl/types';
import TextLayerItem from './TextLayerItem';

const TextLayerContainer: React.FC<{
  layers: TextLayerProps[];
}> = ({ layers }) => {
  const textLayers = useMemo(() => {
    return layers.filter((ly) => ly.type === 'text');
  }, [layers]);
  return (
    <>
      {textLayers.map((layer) => {
        const index = layers.findIndex((ly) => ly.id === layer.id);
        return <TextLayerItem key={layer.id} layer={layer} index={index} />;
      })}
    </>
  );
};

export default TextLayerContainer;
