import React from 'react';
import { transformRgbColorToString } from '@/pages/ThematicMap/utils';
import type { RectangleLayerProps } from '@/pages/ThematicMap/types';

const RectangleLayerItem: React.FC<{
  id: string;
  visible: boolean;
  props: RectangleLayerProps['props'];
  layerIndex: number;
}> = ({ id, props, visible, layerIndex }) => {
  return (
    <React.Fragment key={id}>
      <div
        id={id}
        className="target"
        style={{
          position: 'absolute',
          top: props.top,
          left: props.left,
          width: props.width,
          height: props.height,
          zIndex: 1000 - layerIndex,
          background: transformRgbColorToString(props.backgroundColor),
          borderWidth: props.borderWidth,
          borderStyle: props.borderStyle,
          borderColor: transformRgbColorToString(props.borderColor),
          opacity: props.opacity / 100,
          display: visible ? 'block' : 'none',
        }}
      />
    </React.Fragment>
  );
};

const RectangleLayers: React.FC<{
  layers: RectangleLayerProps[];
}> = ({ layers }) => {
  const rectLayers = layers.filter(
    (layer) => layer.type === 'rectangle',
  ) as RectangleLayerProps[];

  return (
    <>
      {rectLayers.map((layer: RectangleLayerProps) => {
        const index = layers.findIndex((ly) => ly.id === layer.id);
        const { id, props } = layer;
        return (
          <RectangleLayerItem
            key={id}
            id={id}
            props={props}
            layerIndex={index}
            visible={layer.visible}
          />
        );
      })}
    </>
  );
};

export default RectangleLayers;
