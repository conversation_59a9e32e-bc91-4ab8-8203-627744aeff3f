import type { Viewport } from 'deck.gl';
import { GeoJsonLayer, MapView } from 'deck.gl';
import {
  fetchGeojsonByRegionCode,
  fetchGeojsonIncludeChild,
} from '@/services/global';
import { baseGeojsonLayerConfig, getLevelByRegionCode } from '@/utils';
import DeckGL from '@deck.gl/react';
import React, { useMemo } from 'react';
import { useQuery } from 'react-query';
import { useTextureLayer, useTileLayer, useTileLayerWithLabel } from '../hooks';
import { MapContainer } from './ui';
import RectangleLayers from './tm-components/RectangleLayers';
import TextLayerContainer from './tm-components/TextLayerContainer';
import ScaleLayer from './tm-components/ScaleLayer';
import LegendContainer from './tm-components/LegendContainer';
import CompassLayer from './tm-components/CompassLayer';
import ImageLayers from './tm-components/ImageLayers';
import CircleLayers from './tm-components/CircleLayers';
// eslint-disable-next-line max-len
import FpsThrottledDeck from '@/pages/ThematicMapTpl/components/FpsThrottledDeck';
import { useSetAtom as useUpdateAtom } from 'jotai';
import { viewportAtom } from '../atoms';

const initViewState = {
  latitude: 39.915599,
  longitude: 116.401969,
  zoom: 6,
  maxZoom: 18,
  minZoom: 3,
  pitch: 0,
  bearing: 0,
};

const Map: React.FC<{
  thematicMapDetails: any;
}> = ({ thematicMapDetails }) => {
  const setViewport = useUpdateAtom(viewportAtom);
  const content = useMemo(() => {
    return thematicMapDetails ? thematicMapDetails.content : '';
  }, [thematicMapDetails]);
  const tmDetails = useMemo(() => {
    return content ? JSON.parse(content) : {};
  }, [content]);
  const tileLayer = useTileLayer(
    tmDetails.opacity || 100 / 100,
    tmDetails.mapType || 'img',
  );
  const labeledTilelayer = useTileLayerWithLabel(
    tmDetails.mapType || 'img',
    tmDetails.tileLabelVisible,
  );
  const viewState = useMemo(() => {
    return {
      ...initViewState,
      ...(tmDetails.viewState || {}),
    };
  }, [tmDetails.viewState]);
  const level = useMemo(() => {
    if (tmDetails) {
      return tmDetails.shouldShowAllRegion
        ? 1
        : getLevelByRegionCode(String(tmDetails.regionCode));
    }
    return 1;
  }, [tmDetails]);
  const queryKeys = useMemo(
    () => [
      'user-region-geojson',
      tmDetails.regionCode,
      tmDetails.shouldShowAllRegion,
    ],
    [tmDetails.regionCode, tmDetails.shouldShowAllRegion],
  );

  const { data } = useQuery(
    ['map-geojson', tmDetails.regionCode],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(
          tmDetails.shouldShowAllRegion ? 150000 : tmDetails.regionCode,
        ),
        level,
      }),
    {
      enabled: Boolean(tmDetails.regionCode && level !== 3 && level !== 2),
      staleTime: 0,
    },
  );

  const { data: levelGeojson } = useQuery(
    queryKeys,
    () => fetchGeojsonByRegionCode(Number(tmDetails.regionCode === 100000 ? 150000 : tmDetails.regionCode)),
    {
      enabled: !!tmDetails.regionCode && level === 3,
      staleTime: 0,
    },
  );

  const geojsonLayer = useMemo(
    () =>
      new GeoJsonLayer({
        ...baseGeojsonLayerConfig,
        getLineColor:
          tmDetails.mapType === 'img' ? [255, 255, 255] : [99, 99, 99],
        filled: false,
        getFillColor: () => [40, 108, 255, 120],
        data: data || levelGeojson,
        visible: tmDetails.geojsonVisible,
      }),
    [data, levelGeojson, tmDetails.geojsonVisible, tmDetails.mapType],
  );

  const textureLayers = useTextureLayer(
    (tmDetails.layers || []).filter(
      (ly: any) => ly.type === 'heatmap' || ly.type === 'colormap',
    ),
    tmDetails?.regionCode,
    tmDetails?.shouldShowAllRegion,
    thematicMapDetails?.createType,
    thematicMapDetails?.fromDate,
    thematicMapDetails?.toDate,
  );

  const canvasStyle = useMemo(() => {
    const { width, height } = tmDetails;
    const container = document.getElementById('map-container');

    if (width && height && container) {
      return {
        width,
        height,
        top: 0,
        left: 0,
      };
    }
    return {};
  }, [tmDetails]);

  return (
    <MapContainer id="map-container">
      {thematicMapDetails && (
        <>
          <RectangleLayers layers={tmDetails.layers} />
          <TextLayerContainer layers={tmDetails.layers} />
          <LegendContainer layerLegends={tmDetails.legends} />
          <CompassLayer
            compassLayer={tmDetails.layers.find(
              (ly: any) => ly.type === 'compass',
            )}
            mapType={tmDetails.mapType}
          />
          <ImageLayers layers={tmDetails.layers} />
          <CircleLayers
            layers={tmDetails.layers}
            viewState={{
              width: document.getElementById('map-container')?.offsetWidth,
              height: document.getElementById('map-container')?.offsetHeight,
              ...initViewState,
              ...tmDetails.viewState,
            }}
          />
        </>
      )}
      <div
        style={{
          position: 'absolute',
          ...canvasStyle,
        }}
      >
        <DeckGL
          viewState={viewState}
          views={[new MapView({ repeat: true, orthographic: true })]}
          // @ts-ignore
          Deck={FpsThrottledDeck}
          glOptions={{ preserveDrawingBuffer: true }}
          layers={[tileLayer, labeledTilelayer, geojsonLayer, textureLayers]}
        >
          {({ viewport }: { viewport: Viewport }) => {
            setViewport(viewport);
            return (
              thematicMapDetails && (
                <ScaleLayer layers={tmDetails.layers} viewport={viewport} />
              )
            );
          }}
        </DeckGL>
      </div>
    </MapContainer>
  );
};

export default Map;
