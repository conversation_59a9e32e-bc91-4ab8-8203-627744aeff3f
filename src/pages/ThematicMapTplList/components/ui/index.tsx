import { Center, Flex, HorCenter, OutlinedButton, scrollBarStyles } from '@/components/ui';
import { Row } from 'antd';
import styled from 'styled-components';

export const BtnRow = styled(Flex)`
  padding: 24px 0;
  justify-content: flex-end;
`;

export const FilterRow = styled(Row)`
  margin: 32px 0 20px;
`;

export const TableLink = styled(Center)<{
  isDanger?: boolean;
  disabled?: boolean;
}>`
  display: inline-flex;
  font-size: 14px;
  color: ${({ isDanger, disabled }) => {
    if (disabled) {
      return '#999';
    }

    return isDanger ? '#f44336' : '';
  }};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};

  &:hover {
    color: ${({ theme, isDanger, disabled }) => {
      if (disabled) {
        return '#999';
      }
      return isDanger ? '#f4436' : theme.colors.primary;
    }};
  }

  .icomoon {
    margin-right: 8px;
  }

  & + & {
    margin-left: 30px;
  }
`;

export const CarouselContainer = styled(Flex)`
  position: relative;
  padding: 10px;
`;
export const MapContainer = styled.div`
  position: relative;
  flex: 1;
  margin-right: 40px;
  height: 640px;
  background: white;
  overflow: auto;
  display: flex;
  align-items: center;
`;
export const Right = styled.div`
  width: 400px;
`;
export const CarouselFilter = styled(Flex)`
  padding-bottom: 12px;
  border-bottom: 1px solid #1c1d24;

  .select {
    width: 200px;
    margin-right: 12px;
  }
`;
export const ListItem = styled.div<{
  active?: boolean;
}>`
  padding: 12px 10px;
  border-radius: 4px;
  color: white;
  border-bottom: 1px solid #1c1d24;
  cursor: pointer;

  background: ${({ active, theme }) => {
    return active ? theme.colors.primary : 'transparent';
  }};

  .index {
    width: 24px;
  }
  .name {
    flex: 1;
  }

  .date-range {
    padding-left: 24px;
    color: ${({ active }) => {
      return active ? 'white' : '#c1c1c4';
    }};
  }
`;

export const ModalCloseBtn = styled(Center)`
  position: absolute;
  top: 10px;
  right: 0px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  font-size: 20px;
`;
export const Title = styled.h1`
  font-size: 24px;
  color: white;
  font-weight: normal;
  margin: 0 0 20px;
`;
export const AutoPlayBtn = styled(OutlinedButton)`
  margin: 20px 0;
  background: #286cff;
  color: white;
`;
export const PagiContainer = styled(Center)`
  margin-top: 20px;

  .ant-pagination {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-pagination-item {
    background-color: transparent;
    border: 1px solid #434343;
  }
  .ant-pagination-item a {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-pagination-item:focus-visible,
  .ant-pagination-item:hover {
    border-color: #286cff;
  }
  .ant-pagination-item:focus-visible a,
  .ant-pagination-item:hover a {
    color: #286cff;
  }
  .ant-pagination-item-active {
    border-color: #286cff;
  }
  .ant-pagination-item-active a {
    color: #286cff;
  }
  .ant-pagination-item-active:focus-visible,
  .ant-pagination-item-active:hover {
    border-color: #165996;
  }
  .ant-pagination-item-active:focus-visible a,
  .ant-pagination-item-active:hover a {
    color: #165996;
  }
  .ant-pagination-prev {
    margin-right: 8px;
  }
  .ant-pagination-next,
  .ant-pagination-prev {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-pagination-next button,
  .ant-pagination-prev button {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-pagination-next:hover button,
  .ant-pagination-prev:hover button {
    border-color: #165996;
  }
  .ant-pagination-next .ant-pagination-item-link,
  .ant-pagination-prev .ant-pagination-item-link {
    background-color: transparent;
    border: 1px solid #434343;
  }
  .ant-pagination-next:focus-visible .ant-pagination-item-link,
  .ant-pagination-next:hover .ant-pagination-item-link,
  .ant-pagination-prev:focus-visible .ant-pagination-item-link,
  .ant-pagination-prev:hover .ant-pagination-item-link {
    color: #286cff;
    border-color: #286cff;
  }
  .ant-pagination-disabled,
  .ant-pagination-disabled:focus-visible,
  .ant-pagination-disabled:hover {
    cursor: not-allowed;
  }
  .ant-pagination-disabled .ant-pagination-item-link,
  .ant-pagination-disabled:focus-visible .ant-pagination-item-link,
  .ant-pagination-disabled:hover .ant-pagination-item-link {
    color: rgba(255, 255, 255, 0.3);
    border-color: #434343;
  }
  .ant-pagination.ant-pagination-disabled .ant-pagination-item {
    background: rgba(255, 255, 255, 0.08);
    border-color: #434343;
  }
  .ant-pagination.ant-pagination-disabled .ant-pagination-item a {
    color: rgba(255, 255, 255, 0.3);
    background: 0 0;
    border: none;
  }
  .ant-pagination.ant-pagination-disabled .ant-pagination-item-active {
    background: rgba(255, 255, 255, 0.25);
  }
  .ant-pagination.ant-pagination-disabled .ant-pagination-item-active a {
    color: #000;
  }
  .ant-pagination.ant-pagination-disabled .ant-pagination-item-link {
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: #434343;
  }
  .ant-pagination.ant-pagination-disabled .ant-pagination-item-link-icon {
    opacity: 0;
  }
`;
export const ImgContainer = styled.div`
  flex: 1;
  overflow: auto;
  img {
    display: block;
    width: 100%;
  }
`;
export const ImgWrapper = styled.div`
  flex: 1;
  margin-right: 20px;
  overflow-y: auto;

  ${scrollBarStyles};

  img {
    width: 100%;
  }
`;

export const SortBtn = styled(Center)<{
  active?: boolean;
}>`
  width: 20px;
  height: 20px;
  border-radius: 2px;
  background: ${(p) => (p.active ? p.theme.colors.primary : '#1c1d24')};
  cursor: pointer;

  img {
    width: 14px;
    opacity: ${(p) => (p.active ? 1 : 0.5)};
  }
`;

export const SortArea = styled(HorCenter)`
  margin-top: 12px;
  font-size: 12px;
  color: #c1c1c4;

  span {
    flex: 1;
  }

  ${SortBtn}:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  ${SortBtn}:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
`;
