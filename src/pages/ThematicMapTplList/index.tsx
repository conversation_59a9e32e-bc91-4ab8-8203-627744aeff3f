import { userInfoAtom } from '@/atoms';
import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { PageMain } from '@/components/ui';
import { useRouter } from '@/hooks';
import type { ApiListData } from '@/utils/types';
import { message, Modal, Table } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import NameTmModal from '../ThematicMap/components/NameTmModal';
import { renameTm } from '../ThematicMap/services';
import { PageTabItem, PageTabs } from '../ThematicMapList/components/ui';
import { deleteTpl, getTplList, toggleTplStauts } from '../ThematicMapTpl/services';
import { currentTplAtom, filtersAtom } from './atoms';
import CarouselModal from './components/CarouselModal';
import Filter from './components/Filter';
import { thematicMapColumns } from './tableCols';
import type { TmTplListItem } from './types';

const ThematicMapList = () => {
  const { push } = useRouter();
  const userInfo = useAtomValue(userInfoAtom);
  const queryClient = useQueryClient();
  const [current, setCurrent] = useState<TmTplListItem | null>(null);
  const [visible, setVisible] = useState(false);
  const [carouselVisible, setCarouselVisible] = useState(false);
  const [params, setParams] = useAtom(filtersAtom);
  const [, setCurrentTpl] = useAtom(currentTplAtom);
  const deleteMutation = useMutation((id: number) => deleteTpl(id));
  const renameMutation = useMutation((p: { id: number; name: string }) => renameTm(p));
  const upateStatusMutation = useMutation((p: { id: number; status: 0 | 1 }) => toggleTplStauts(p.id, p.status));
  const { data, isLoading } = useQuery<ApiListData<TmTplListItem>>(['tm-tpl-list', params], () => getTplList(params), {
    enabled: Boolean(params.regionCode),
    staleTime: 0,
    cacheTime: 0,
  });
  const tableColumns = thematicMapColumns(
    {
      deleteAction: (id) => {
        Modal.confirm({
          title: '确定要删除此模板吗？',
          onOk() {
            deleteMutation.mutate(id, {
              onSuccess() {
                message.success('操作成功');
                queryClient.invalidateQueries(['tm-tpl-list', params]);
              },
            });
          },
        });
      },
      editAction: (id) => {
        push(`/thematic-map-tpl?id=${id}&type=edit`);
      },
      carouselAction: (tpl) => {
        setCurrentTpl(tpl);
        setCarouselVisible(true);
      },
      createTmAction: (id) => {
        push(`/thematic-map?tplId=${id}`);
      },
      copyAction: (id) => {
        push(`/thematic-map-tpl?id=${id}&type=copy`);
      },
      renameAction: (item) => {
        setCurrent(item);
        setVisible(true);
      },
      toggleStatus: (id, status) => {
        upateStatusMutation.mutate(
          { id, status },
          {
            onSuccess() {
              message.success('操作成功');
              queryClient.invalidateQueries(['tm-tpl-list', params]);
            },
          },
        );
      },
    },
    userInfo?.id,
  );

  const rename = useCallback(
    (name: string) => {
      if (current) {
        renameMutation.mutate(
          {
            name,
            id: current.id,
          },
          {
            onSuccess() {
              message.success('操作成功');
              queryClient.invalidateQueries(['tm-list', params]);
              setVisible(false);
            },
          },
        );
      }
    },
    [current, params, queryClient, renameMutation],
  );

  return (
    <PageMain>
      <HelmetTitle title="专题图列表" />
      <PageHead title="模板">
        <span>模板</span>
        <i className="icomoon icon-next" />
        <span className="text-primary">模板列表</span>
      </PageHead>
      <PageTabs>
        <PageTabItem to="/thematic-map-list">专题图</PageTabItem>
        <PageTabItem to="/thematic-map-tpl-list" active={1}>
          模板
        </PageTabItem>
      </PageTabs>
      <Filter />
      <Table
        columns={tableColumns}
        loading={isLoading}
        rowKey="id"
        dataSource={data?.content || []}
        pagination={{
          size: 'small',
          position: ['bottomCenter'],
          current: (data?.pageable.pageNumber || 0) + 1,
          pageSize: data?.pageable.pageSize || 10,
          total: data?.totalElements || 0,
        }}
        onChange={({ current: currentPage, pageSize }) => {
          setParams((prev) => ({
            ...prev,
            page: (currentPage || 1) - 1,
            pageSize: pageSize || 10,
          }));
        }}
      />
      <CarouselModal
        visible={carouselVisible}
        onCancel={() => {
          setCarouselVisible(false);
          setCurrentTpl(undefined);
        }}
      />
      <NameTmModal visible={visible} name={current?.name} onOk={rename} onCancel={() => setVisible(false)} />
    </PageMain>
  );
};

export default ThematicMapList;
