import type { HeatmapLayerProps } from '@/pages/ThematicMapTpl/types';
import { stringify } from 'qs';
import { colorRamps, decoder, request, wmts } from '@/utils';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { TileLayer } from 'deck.gl';
import { useMemo } from 'react';
import { useQueries, useQuery } from 'react-query';
import DataTextureLayer from '@/layers/texture-layer/data-texture-layer';
import { fetchUserTextureMap } from '@/services/global';
import { fetchTextureList } from '@/pages/ThematicMap/services';
import moment from 'moment';
import { coders, isSpecialArea } from '@/pages/ThematicMap/utils';

export const useTileLayer = (opacity: number, mapType: any) => {
  const tileLayer = useMemo(
    () =>
      new TileLayer({
        ...tileLayerBaseConfig,
        data: mapType ? wmts(mapType) : mapType,
      }),
    [mapType],
  );

  return tileLayer;
};

export const useTileLayerWithLabel = (
  mapType: 'img' | 'vec' | 'ter' | '',
  visible: boolean,
) => {
  const labelType = useMemo(() => {
    if (mapType === 'img') return 'cia';
    if (mapType === 'ter') return 'cta';
    return 'cva';
  }, [mapType]);

  const labeledTileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'tianditu-with-label',
      data: mapType ? wmts(labelType) : wmts('cva'),
      pickable: false,
      visible,
    });
  }, [labelType, mapType, visible]);

  return labeledTileLayer;
};

export const useTextureLayer = (
  layers: any,
  regionCode?: number,
  shouldShowAllRegion?: boolean,
  createType?: number,
  fromDate?: string | null,
  toDate?: string | null,
) => {
  const dataLayers = useMemo(() => {
    return createType === 1
      ? layers.map((item: any) => ({
          ...item,
          props: {
            ...item.props,
            startDate: fromDate,
            endDate: toDate,
          },
        }))
      : layers;
  }, [createType, fromDate, layers, toDate]);
  const queriesResult = useQueries(
    dataLayers.map((layer: HeatmapLayerProps) => ({
      queryKey: [
        layer.id,
        layer.props.region,
        layer.props.dataType,
        layer.props.dateType,
        layer.props.startDate,
        layer.props.endDate,
      ],
      queryFn: () => {
        const {
          type,
          props: { dateType },
        } = layer;
        let agg = dateType;

        if (type === 'colormap') {
          agg = 'none';
        }
        if (type !== 'colormap' && dateType === 'none') {
          agg = 'daily';
        }

        return request(
          `${layer.props.fetchUrl}?${stringify({
            agg,
            startDate: layer.props.startDate,
            endDate: layer.props.endDate,
            regionCode: layer.props.region,
            type: layer.props.dataType,
          })}`,
        );
      },
      enabled: Boolean(layer.props.startDate && layer.props.endDate),
    })),
  );

  const { data: map } = useQuery(
    ['user-texture-map', regionCode, shouldShowAllRegion],
    () => fetchUserTextureMap(),
    {
      enabled: Boolean(regionCode),
    },
  );

  const mapLayers = useMemo(() => {
    return dataLayers.map((layer: any, index: number) => {
      const currentData = (queriesResult[index].data || []) as any;
      if (layer.type === 'heatmap') {
        if (!map) return null;
        const dataType = layer.props.dataType.toLowerCase() as any;
        const layerClone = { ...layer } as HeatmapLayerProps;
        const textureLayer = new DataTextureLayer({
          id: layerClone.id + map.url,
          url: null,
          map,
          decoder: decoder[layer.props.dataType.toLowerCase()],
          colorRamp: colorRamps[layer.props.dataType.toLowerCase()],
          regionCode: shouldShowAllRegion
            ? 0
            : isSpecialArea(`${regionCode}`)
            ? 150000
            : regionCode,
          visible: layerClone.visible,
        });

        if (currentData && currentData.length === 1) {
          textureLayer.setTileUrl(currentData[0].url as string);
        }

        if (currentData && currentData.length > 1) {
          textureLayer.setAggregateUrls(
            currentData.map((item: any) => item.url),
            // @ts-ignore
            coders[dataType].encoder,
            // @ts-ignore
            coders[dataType].decoder,
          );
        }

        return textureLayer;
        // eslint-disable-next-line no-else-return
      }
      if (layer.type === 'colormap') {
        if (!map) return null;
        const textureLayer = new DataTextureLayer({
          id: layer.id + map.url,
          url: null,
          map,
          regionCode: shouldShowAllRegion
            ? 0
            : isSpecialArea(`${regionCode}`)
            ? 150000
            : regionCode,
          renderType: 'image',
          visible: layer.visible,
        });

        fetchTextureList({
          agg: 'none',
          startDate: layer.props.startDate,
          endDate: layer.props.endDate,
          type: layer.props.dataType,
        }).then((data) => {
          if (data.length > 0) {
            const find = data.find(
              (item: any) =>
                moment(item.dateTime).format('HH:mm') === layer.props.time,
            );
            if (find) {
              textureLayer.setTileUrl(find.url);
            }
          }
        });

        return textureLayer;
        // eslint-disable-next-line no-else-return
      }
    });
  }, [dataLayers, map, queriesResult, regionCode, shouldShowAllRegion]);

  return mapLayers;
};
