import { Badge } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { Link } from 'react-router-dom';
import { TableLink } from '../components/ui';
import type { TmTplListItem } from '../types';

interface TableActions {
  deleteAction: (id: number) => void;
  editAction: (id: number) => void;
  copyAction: (id: number) => void;
  createTmAction: (id: number) => void;
  carouselAction: (tpl: TmTplListItem) => void;
  renameAction: (item: TmTplListItem) => void;
  toggleStatus: (id: number, status: 0 | 1) => void;
}

export const thematicMapColumns: (
  actions: TableActions,
  userId: number | undefined,
) => ColumnsType<TmTplListItem> = (
  {
    deleteAction,
    copyAction,
    createTmAction,
    editAction,
    toggleStatus,
    carouselAction,
  },
  userId,
) => [
  {
    title: '序号',
    render(_text, _record, index) {
      return `${index + 1}`;
    },
  },

  {
    title: '模板名称',
    dataIndex: 'name',
    render(text, record) {
      return (
        <Link
          className="text-primary"
          to={`/thematic-map-tpl?id=${record.id}&type=view`}
        >
          {text}
        </Link>
      );
    },
  },
  {
    title: '行政区域',
    dataIndex: 'region',
  },
  {
    title: '模板类型',
    dataIndex: 'cycleType',
  },
  {
    title: '生成次数',
    dataIndex: 'count',
  },
  {
    title: '创建者',
    dataIndex: 'userName',
  },
  {
    title: '更新时间',
    dataIndex: 'modifyTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
    render(text: 0 | 1) {
      const status = ['default', 'success'];
      const label = ['已暂停', '正常'];
      return (
        <Badge
          status={status[text] as 'default' | 'success'}
          text={label[text]}
        />
      );
    },
  },
  {
    title: '操作',
    width: 540,
    render(_t, record) {
      const isMine = userId === record.userId;
      return (
        <>
          <TableLink color="primary" onClick={() => carouselAction(record)}>
            <i className="icomoon icon-copy" />
            轮播
          </TableLink>
          <TableLink color="primary" onClick={() => copyAction(record.id)}>
            <i className="icomoon icon-copy" />
            复用
          </TableLink>
          <TableLink color="primary" onClick={() => createTmAction(record.id)}>
            <i className="icomoon icon-copy" />
            生成专题图
          </TableLink>
          <TableLink
            color="primary"
            disabled={!isMine}
            onClick={() => {
              if (isMine) {
                toggleStatus(record.id, record.status === 1 ? 0 : 1);
              }
            }}
          >
            <i
              className={`icomoon ${
                record.status === 0 ? 'icon-play' : 'icon-pause-thin'
              }`}
            />
            {record.status === 0 ? '启用' : '暂停'}
          </TableLink>
          <TableLink
            color="primary"
            disabled={!isMine}
            onClick={() => {
              if (isMine) {
                editAction(record.id);
              }
            }}
          >
            <i className="icomoon icon-edit" />
            编辑
          </TableLink>
          <TableLink
            disabled={!isMine}
            isDanger
            onClick={() => {
              if (isMine) {
                deleteAction(record.id);
              }
            }}
          >
            <i className="icomoon icon-trash" /> 删除
          </TableLink>
        </>
      );
    },
  },
];
