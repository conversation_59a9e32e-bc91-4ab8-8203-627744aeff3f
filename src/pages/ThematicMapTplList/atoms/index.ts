import { atom } from 'jotai';
import type { TmTplListItem } from '../types';

export interface FetchTplParams {
  cycleType?: string;
  endDate?: string;
  name?: string;
  page?: number;
  regionCode?: number;
  size?: number;
  startDate?: string;
  status?: number;
  uid?: number;
}

export const filtersAtom = atom({
  cycleType: undefined,
  endDate: undefined,
  name: '',
  page: 0,
  regionCode: undefined,
  size: 10,
  startDate: undefined,
  status: undefined,
  uid: undefined,
});

export const currentTplAtom = atom<undefined | TmTplListItem>(undefined);
export const viewportAtom = atom<any>({})
