import React from 'react';
import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { Flex, PageMain } from '@/components/ui';
import { Left, PageTabItem, PageTabs } from './components/ui';
import Map from './components/Map';
import DataTable from './components/DataTable';
import Filter from './components/Filter';

const DataDownload = () => {
  return (
    <PageMain>
      <HelmetTitle title="数据接收管理" />
      <PageHead title="数据管理">
        <span>系统管理</span>
        <i className="icomoon icon-next" />
        <span className="text-primary">数据下载</span>
      </PageHead>
      <PageTabs>
        <PageTabItem to="/receive-monitoring">接收监控</PageTabItem>
        <PageTabItem to="/receiving-log">接收日志</PageTabItem>
        <PageTabItem to="/data-download" active={1}>
          数据下载
        </PageTabItem>
      </PageTabs>
      <Flex>
        <Left>
          <Filter />
          <DataTable />
        </Left>
        <Map />
      </Flex>
    </PageMain>
  );
};

export default DataDownload;
