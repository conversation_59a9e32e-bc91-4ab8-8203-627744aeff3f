import type { TextureMap } from '@/types';
import { request } from '@/utils';
import { stringify } from 'qs';

interface FetchMetaParams {
  startDate: string;
  endDate: string;
  type: string;
  agg: string;
}

export const fetchMap: () => Promise<TextureMap> = () =>
  new Promise((resolve) => {
    resolve({
      minLng: 60,
      maxLng: 144,
      minLat: 16,
      maxLat: 64,
      url: `/texture-map/100000.png`,
    });
  });

export const fetchMeta = (params: FetchMetaParams) =>
  request(`/api/texture/meta?${stringify(params)}`);
