import { baseGeojsonLayerConfig, getLevelByRegionCode } from '@/utils';
import { GeoJsonLayer } from 'deck.gl';
import { useCallback, useMemo } from 'react';
import { useQuery } from 'react-query';
import { convert } from '../geotiff/exporter';
import type { SampleFormat } from '../geotiff/geotiff';
import { fetchGeojsonIncludeChild } from '@/services/global';
import { useUserRegionCode } from '@/hooks';
import bbox from '@turf/bbox';
import { coders, stringCoders } from '../utils/newIndex';
import { useAtomValue } from 'jotai';
import { filtersAtom } from '../atoms';
import { options, typeOptions } from '../components/Filter';
import moment from 'moment';

export const useGeojsonLayer = (geojson: GeoJSON.GeoJSON) => {
  const layer = useMemo(() => {
    return new GeoJsonLayer({
      ...baseGeojsonLayerConfig,
      data: geojson || [],
      getLineColor: () => [50, 50, 50],
      getFillColor: () => [255, 0, 0, Math.random() * 100 + 50],
      filled: false,
    });
  }, [geojson]);

  return layer;
};

export const map = {
  minLng: 60,
  maxLng: 144,
  minLat: 16,
  maxLat: 64,
  url: `/texture-map/100000.png`,
};
export const useDownloadGeoTiff = () => {
  const regionCode = useUserRegionCode();
  const { data } = useQuery(
    [`map-geojson-${regionCode}`],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(regionCode),
        level: getLevelByRegionCode(regionCode),
      }),
    {
      enabled: Boolean(regionCode),
    },
  );

  const download = useCallback(
    (urls: string[], type: keyof typeof coders, callback: (blol: any) => void) => {
      if (map && data && regionCode) {
        const range = bbox(data);
        convert(urls, {
          // @ts-ignore
          encoder: stringCoders[type].encoder,
          // @ts-ignore
          decoder: stringCoders[type].decoder,
          decoderFunction: coders[type].decoder,
          nodata: coders[type].nodata,
          bands: coders[type].bands as 1 | 4,
          range,
          format: coders[type].format as SampleFormat,
          map,
          region: regionCode,
        }).then((blob) => {
          callback(blob);
        });
      }

    },
    [data, regionCode],
  );
  return download;
};


export const useDownloadName = () => {
  const filters = useAtomValue(filtersAtom);
  const downloadName = useCallback((timePoints: string) => {
    const { agg, type } = filters;
    const getType = typeOptions.find((item) => item.value === type)?.label || '';
    const getAgg = options.find((item) => item.value === agg)?.label || '';

    const time = moment(timePoints).format('YYYY年MM月DD日 HH时mm分')
    return ['DCOLOR', 'TCOLOR'].includes(type) ? `${getType}_${time}` : `${getType}_${time}_${getAgg}`
  }, [filters]);
  return { downloadName };
}