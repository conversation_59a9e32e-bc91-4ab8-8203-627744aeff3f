export class GeotiffView {
  private view: DataView;
  private pos = 0;

  constructor(size: number) {
    this.view = new DataView(new ArrayBuffer(size));
  }

  public getDataBuffer() {
    return this.view.buffer;
  }

  public setAscii(data: string) {
    for (let i = 0; i < data.length; i += 1) {
      this.setU8(data.charCodeAt(i));
    }
    this.setU8(0);
  }

  public setFloat(data: number | number[]) {
    if (Array.isArray(data)) {
      data.forEach((v) => this.setFloat(v));
    } else {
      this.view.setFloat32(this.pos, data);
      this.pos += 4;
    }
  }

  public setDouble(data: number | number[]) {
    if (Array.isArray(data)) {
      data.forEach((v) => this.setDouble(v));
    } else {
      this.view.setFloat64(this.pos, data);
      this.pos += 8;
    }
  }

  public setU8(data: number | number[]) {
    if (Array.isArray(data)) {
      data.forEach((v) => this.setU8(v));
    } else {
      this.view.setUint8(this.pos, data);
      this.pos += 1;
    }
  }

  public setU16(data: number | number[]) {
    if (Array.isArray(data)) {
      data.forEach((v) => this.setU16(v));
    } else {
      this.view.setUint16(this.pos, data);
      this.pos += 2;
    }
  }

  public setU32(data: number | number[]) {
    if (Array.isArray(data)) {
      data.forEach((v) => this.setU32(v));
    } else {
      this.view.setUint32(this.pos, data);
      this.pos += 4;
    }
  }
}
