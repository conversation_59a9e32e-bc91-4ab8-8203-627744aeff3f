export type DataTypeValue = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
export type DataType = {
  value: DataTypeValue;
  size: number;
};

export type Tag = {
  id: number;
  type: DataType;
  count: number;
  inline: boolean;
  value: any;
};

export const DATA_TYPE_BYTE: DataType = { value: 1, size: 1 };
export const DATA_TYPE_ASCII: DataType = { value: 2, size: 0 };
export const DATA_TYPE_SHORT: DataType = { value: 3, size: 2 };
export const DATA_TYPE_LONG: DataType = { value: 4, size: 4 };
export const DATA_TYPE_FLOAT: DataType = { value: 11, size: 4 };
export const DATA_TYPE_DOUBLE: DataType = { value: 12, size: 8 };
