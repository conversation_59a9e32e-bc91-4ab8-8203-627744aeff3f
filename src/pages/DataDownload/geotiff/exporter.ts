import { aggregateTileWithAlpha, PixelDecoder, TILE_PIXELS } from '@/utils/dataDown/tiles';
import { WebMercatorViewport } from 'deck.gl';
import proj4 from 'proj4';
import { calculateBoundsFromWebMercatorTileRowCol, getTileRangeByLngLatRange, getTilesByTileRange } from '../utils/newIndex';
import type { SampleFormat } from './geotiff';
import Geotiff from './geotiff';

export type TextureMap = {
  minLng: number;
  maxLng: number;
  minLat: number;
  maxLat: number;
  url: string;
};

export type GeotiffConvertOptions = {
  /**
   * 当需要聚合时必须传入
   */
  encoder: string;
  /**
   * 栅格数据像素的解码器，传入rgba数值解码为实际的数值
   */
  decoder: string;
  decoderFunction: PixelDecoder;
  bands: 1 | 2 | 3 | 4;
  /**
   * 转换为tiff文件后每个样点的数据格式
   */
  format: SampleFormat;
  region: number;
  map: TextureMap;
  range: [number, number, number, number];
  nodata: number;
};

function loadTileImages(urls: string[], tiles: number[][], encoder: string, decoder: string, bands: 1 | 2 | 3 | 4): Promise<ImageData[]> {
  const tilePromises = tiles.map((tile) => {
    const tileUrls = urls.map((url) => url.replace('{x}', String(tile[0])).replace('{y}', String(tile[1])).replace('{z}', String(7)));
    if (!encoder) {
      throw new Error('PixelEncoder在聚合转换时必须提供！');
    }
    return aggregateTileWithAlpha(tileUrls, encoder, decoder, bands);
  });
  return new Promise((resolve) => {
    Promise.all(tilePromises).then((d) => {
      resolve(d);
    });
  });
}
/**
 * 将一个图片转为Geotiff格式
 * @param url 图片地址, 模板URL, 需要替换{x},{y}两个变量, 传递数组时则代表用多组数据聚合
 * @param options 转换参数
 * @returns 转换后的Geotiff数据
 */
export function convert(urls: string[], options: GeotiffConvertOptions): Promise<Blob> {
  const { minLng, maxLng, minLat, maxLat } = options.map;
  const [l, t, r, b] = options.range;
  const range = getTileRangeByLngLatRange([l, t, r, b], 7);
  const arr = getTilesByTileRange(range);
  const tiles = arr.reduce((prev, curr) => {
    return [...prev, ...curr];
  }, []);
  return new Promise((resolve) => {
    loadTileImages(urls, tiles, options.encoder, options.decoder, options.bands).then((images) => {
      const [west, north] = calculateBoundsFromWebMercatorTileRowCol(tiles[0][0], tiles[0][1], 7);
      const [, , east, south] = calculateBoundsFromWebMercatorTileRowCol(tiles[tiles.length - 1][0], tiles[tiles.length - 1][1], 7);
      const width = (tiles[tiles.length - 1][0] - tiles[0][0] + 1) * TILE_PIXELS;
      const height = (tiles[tiles.length - 1][1] - tiles[0][1] + 1) * TILE_PIXELS;
      const view = new DataView(new ArrayBuffer(width * height * 4));
      for (let i = 0; i < images.length; i++) {
        const imageData = images[i];
        const offsetX = (tiles[i][0] - tiles[0][0]) * TILE_PIXELS;
        const offsetY = (tiles[i][1] - tiles[0][1]) * TILE_PIXELS;
        for (let y = 0; y < TILE_PIXELS; y++) {
          for (let x = 0; x < TILE_PIXELS; x++) {
            const mapOffset = offsetX + x + (offsetY + y) * width;
            const imageDataIndex = x + y * TILE_PIXELS;
            const tileR = imageData.data[imageDataIndex * 4];
            const tileG = imageData.data[imageDataIndex * 4 + 1];
            const tileB = imageData.data[imageDataIndex * 4 + 2];
            const tileA = imageData.data[imageDataIndex * 4 + 3];
            view.setUint8(mapOffset * 4, tileR);
            view.setUint8(mapOffset * 4 + 1, tileG);
            view.setUint8(mapOffset * 4 + 2, tileB);
            view.setUint8(mapOffset * 4 + 3, tileA);
          }
        }
      }
      const arr = new Uint8ClampedArray(view.buffer);
      const image = new ImageData(arr, width, height);
      const img = new Image();
      img.src = options.map.url;
      img.setAttribute('crossOrigin', 'anonymous');
      img.onload = () => {
        const mecrcator = new WebMercatorViewport({ width, height });
        const viewport = mecrcator.fitBounds([
          [west, north],
          [east, south],
        ]);
        const pos = viewport.project([minLng, maxLat]);
        const endPos = viewport.project([maxLng, minLat]);
        // const pos = [
        //   ((minLng - west) / (east - west)) * width,
        //   ((maxLat - north) / (south - north)) * height,
        // ];
        // const endPos = [
        //   ((maxLng - west) / (east - west)) * width,
        //   ((minLat - north) / (south - north)) * height,
        // ];
        const maskTexture = document.createElement('canvas');
        maskTexture.width = width;
        maskTexture.height = height;
        const maskCtx = maskTexture.getContext('2d');
        maskCtx?.drawImage(img, pos[0], pos[1], endPos[0] - pos[0], endPos[1] - pos[1]);
        const maskMap = maskCtx?.getImageData(0, 0, width, height);
        const mask = maskMap?.data || [];
        const region = options.region;
        if (maskMap) {
          for (let i = 0; i < mask.length; i += 4) {
            if (
              !(
                (mask[i] === Math.floor(region / 10000) &&
                  (mask[i + 1] === Math.floor((region % 10000) / 100) || Math.floor((region % 10000) / 100) === 0) &&
                  (mask[i + 2] === Math.floor(region % 100) || Math.floor(region % 100) === 0)) ||
                region === 100000
              )
            ) {
              maskMap.data[i] = 0;
              maskMap.data[i + 1] = 0;
              maskMap.data[i + 2] = 0;
              maskMap.data[i + 3] = 0;
            }
          }
        }
        const [xmin, ymax] = proj4('EPSG:4326', 'EPSG:3857', [west, north]);
        const [xmax] = proj4('EPSG:4326', 'EPSG:3857', [east, south]);
        const geotiff = new Geotiff(image, options.bands);
        geotiff.setPixelSize((xmax - xmin) / width);
        geotiff.setLeftTopTiePoint(xmin, ymax);
        const blob = geotiff.toBlob(options.decoderFunction, options.format, maskMap as ImageData, options.nodata);
        resolve(blob);
      };
    });
  });
}
