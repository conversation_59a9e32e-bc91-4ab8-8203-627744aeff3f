/**
 * 提供在客户端将一张图片转为 GeoTiff 格式的方法
 *
 * 前言：GeoTiff 是对Tiff规范的扩展，并且已经编制在Tiff6.0中，因此需要先了解Tiff
 *
 * Tiff格式说明
 *
 * Tiff图片主要由文件头(Image File Header)和目录信息(Image File Directory)构成
 *
 * - IFH，由8个字节组成，一个文件只有一个，用于对文件进行简单的描述
 * -- 0~1: II/MM 字节序，我们这里只使用MM，即小头字节序
 * -- 2~3: 42 固定的版本号
 * -- 4~7: 第一个IFD的偏移量，永远是8
 *
 * - IFD，用于描述图片的各种属性。Tiff文件比较灵活，可以包含多个IFD，也即一个Tiff文件
 * 可以包含多个图片，每个图片都有自己的属性，例如宽、高、色彩深度等。
 *
 * IFD具有固定的数据结构，由于是用来描述图片属性，因此IFD由多个描述图片属性的Tag组成。
 * 结构如下：
 * -- 2个字节: 属性(即Tag)的数量,即最多可以支持65535个Tag
 * -- Tag1
 * -- ...
 * -- TagN
 * -- 4个字节: 存储下一个IFD的偏移量，如果已经到最后一个IFD，则写入 0 值
 * 由此可以看出，IFD实现上是一个单向链式结构，多个IFD收尾相接恰好可以保存多幅图像。
 *
 * IFD中的Tag是一个12字节的数据结构，如下
 * -- 0~1: tag ID，每一个Tag都具有一个唯一的ID，由Tiff规范中预先分配，并且预留了一些
 * 空间作为保留和私有使用
 * -- 2~3: 数据类型，描述一个属性的数据类型，是一个1~12的数字，具体说明见代码中定义的常量
 * -- 4~7: 数量，说明这个Tag中包含的数据的数量
 * -- 8~11:用于保存数据或者数据的存储位置（字节偏移量），当Tag内的数据数量是1并且可以用4
 * 个字节足以保存的时候，这个字段就直接存储Tag的数值
 *
 * 小结
 * Tiff规范较为简单灵活，但要真正理解和用好并不容易。文件的存储结构大致如下
 *
 * IFH + N个IFD(Tag数量 + N个Tag + 下一个IFD的偏移量 + Tag的数据存储区 + 图像的数据存储区)
 *
 * GeoTiff格式说明
 *
 * GeoTiff实际是在Tiff的基础上增加了地理投影参数的描述，但为了确保不占用过多Tiff TagID资源
 * 采用了特殊的设计, 首先在Tiff的基础上
 *
 * 1. 增加 GeoKeyDirectoryTag(tag ID: 34735)【必选】
 * 2. 增加 GeoDoubleParamsTag(tag ID: 34736)【可选】
 * 3. 增加 GeoAsciiParamsTag(tag ID: 34737) 【可选】
 * 4. 增加 ModelPixelScaleTag(tag ID: 33550)【可选】
 * 5. 增加 ModelTiepointTag(tag ID: 33922)  【看条件】
 * 6. 增加 ModelTransformationTag(tag ID: 34264) 【看条件】
 *
 * GeoTiff就是这6个新增的 Tiff Tag实现了对地理投影参数的描述，只占用了6个TagID。其次，GeoTiff
 * 定义了GeoKeys，作用等同于Tiff的Tag，用来描述投影参数，不同的Key分配了不同的SHORT类型的整数作为
 * Key ID。
 *
 * 重点：投影参数采用GeoKeys来描述，使用GeoKeyDirectoryTag来存储，并且也有自己的数据结构，
 * 即在Tiff的Tag的基础上增加了一个特殊的Tag用来存储投影参数
 *
 * 下面依次对这6个Tag进行简要说明，如需了解详细信息可以参考标准
 *
 * - GeoKeyDirectoryTag
 *   用于存储具体的投影参数，主要由描述信息（Header）和若干GeoKeys Entry组成。作为Tiff Tag，其结构需如下
 *   (ID: 34735, type: SHORT, count: 数量, value: 偏移量)
 * -- Header
 *   由4个SHORT类型数字组成，基本都是预定义好的数值
 * --- 1. KeyDirectorVersion: 1
 * --- 2. KeyRevision: 1
 * --- 3. MinorRevision: 0/1, 分别对应GeoTiff 1.0和1.1两个版本
 * --- 4. NumberOfKeys: 存储的GeoKeys的数量
 *
 * -- GeoKey Entry
 *   具体的投影参数，也是由4个SHORT数字组成
 * --- 1. KeyID
 * --- 2. Tiff Tag Location: 合法的Tiff Tag ID或 0
 * --- 3. Count,
 * --- 4. Key 值或Tiff Tag的索引下标，当Tiff Tag Location为0时，这里存Key值
 *
 * - GeoDoubleParamsTag
 *   如果GeoKey的数据类型是double，则采用这个Tag来存储数值，对应的GeoKey的value存储的是该Tag在所有
 *   Tag数组中的下标
 *
 * - GeoAsciiParamsTag
 *   如果GeoKey的数据类型是ASCII，则采用这个Tag来存储数值，对应的GeoKey的value存储的是该Tag在所有
 *   Tag数组中的下标
 *
 * - ModelPixelScaleTag
 *   用户完成栅格空间(raster space)与模型空间(model space)之间的坐标转换，可看做是图片上的像素坐标
 *   与地球上的坐标的对应关系。作为Tiff Tag，其结构需如下
 *   (ID: 33550, type: DOUBLE, count: 3, value: 偏移量)
 *   该标签包含3个数值，分别对应 scaleX, scaleY, scaleZ, 对我们的场景来说，即pixel size, 例如
 *   1个像素对应0.01度（经纬度相同时）
 *
 * - ModelTiepointTag
 *   与 ModelPixelScaleTag 同为必需像，二者共同来实现从栅格空间到模型空间的坐标映射。Tiepoint就是
 *   图片左上角点对应地球经纬度三维坐标，所以共6个double数值，其中高度值对我们来说传 0 即可。
 *
 * - ModelTransformationTag
 *   当raster与model之前有旋转、缩放等放射变换时，可以通过该标签提供一个转换矩阵来实现关联，暂时我们
 *   不需要
 *
 * 参考文献
 * [1] GeoTiff标准 (http://docs.opengeospatial.org/is/19-008r4/19-008r4.html)
 * [2] TIFF File Format Summary（https://www.fileformat.info/format/tiff/egff.htm）
 *
 * <AUTHOR>
 */

import type { PixelDecoder } from '@/utils/tiles';
import { CompressionTag, CopyrightTag, CrsTag, DescriptionTag, GeoKeyDirectoryTag, PlanarConfigurationTag, SoftwareTag } from './tags';
import type { DataType, Tag } from './types';
import { DATA_TYPE_ASCII, DATA_TYPE_BYTE, DATA_TYPE_DOUBLE, DATA_TYPE_FLOAT, DATA_TYPE_LONG, DATA_TYPE_SHORT } from './types';
import { GeotiffView } from './view';

export type SampleFormat = 'byte' | 'ushort' | 'uint' | 'float' | 'double';

const SAMPLE_BITS = {
  byte: 8,
  ushort: 16,
  uint: 32,
  float: 32,
  double: 64,
};

const SAMPLE_FORMATS = {
  byte: 1,
  ushort: 1,
  uint: 1,
  float: 3,
  double: 3,
};

export default class Geotiff {
  private readonly IFH_SIZE = 8;
  private readonly TAG_SIZE = 12;
  private image: ImageData;
  private tags: Tag[] = [];
  private offset = 0;
  private bands: 1 | 2 | 3 | 4 = 1;
  private view!: GeotiffView;
  private sampleWriter!: any;

  constructor(image: ImageData, bands: 1 | 2 | 3 | 4 = 1) {
    this.image = image;
    this.bands = bands;
    const { width, height } = image;
    this.addTag(256, DATA_TYPE_SHORT, 1, width, true);
    this.addTag(257, DATA_TYPE_SHORT, 1, height, true);
    // samples per pixel
    this.addTag(277, DATA_TYPE_SHORT, 1, bands, true);
    // rows per strip
    this.addTag(278, DATA_TYPE_SHORT, 1, height, true);
    // strip byte counts
    this.addTag(279, DATA_TYPE_LONG, 1, width * height * bands, true);

    this.initDefaultTags();
  }

  public setPixelSize(pixelSize: number) {
    this.addTag(33550, DATA_TYPE_DOUBLE, 3, [pixelSize, pixelSize, 0], false);
  }

  public setLeftTopTiePoint(left: number, top: number) {
    // model tie point
    this.addTag(33922, DATA_TYPE_DOUBLE, 6, [0, 0, 0, left, top, 0], false);
  }

  /**
   * @param mask 与原图中的像素对应，当mask中的像素值大于0时保留像素，否则丢弃
   */
  public toBlob(decoder: PixelDecoder, sampleFormat: SampleFormat, mask: ImageData | false, nodata: number | undefined): Blob {
    const formatValue = SAMPLE_FORMATS[sampleFormat];
    // SAMPLE FORMAT
    this.addTag(339, DATA_TYPE_SHORT, 1, formatValue, true);

    // bits per sample
    this.addTag(258, DATA_TYPE_SHORT, 1, SAMPLE_BITS[sampleFormat], true);

    if (nodata !== undefined) {
      this.tags.push({
        id: 42113,
        type: DATA_TYPE_ASCII,
        count: Math.max(6, String(nodata).length),
        value: String(nodata),
        inline: false,
      });
    }

    // STRIP OFFSET, be sure this is the last tag to add
    const stripOffset = this.offsetToStrip() + this.TAG_SIZE;
    this.addTag(273, DATA_TYPE_LONG, 1, stripOffset, true);
    const size = this.sizeOfTiff(sampleFormat);
    this.view = new GeotiffView(size);

    this.selectSampleWriter(sampleFormat);
    this.writeIFH();
    this.writeIFD();
    this.writeImageData(decoder, mask, nodata);
    return new Blob([this.view.getDataBuffer()], { type: 'image/tiff' });
  }

  private writeIFH() {
    this.view.setU16(0x4d4d);
    this.view.setU16(42);
    this.view.setU32(8);
  }

  private writeIFD() {
    // 按ID升序排序
    const tags = this.tags.sort((a, b) => a.id - b.id);
    this.view.setU16(tags.length);
    this.offset = this.IFH_SIZE + 2 + tags.length * this.TAG_SIZE + 4;
    tags.forEach((tag) => this.writeTag(tag));
    // offset to next IFD
    this.view.setU32(0);
    tags
      .filter((tag) => !tag.inline)
      .forEach((tag) => {
        if (tag.type.value === DATA_TYPE_LONG.value) {
          this.view.setU32(tag.value);
        } else if (tag.type.value === DATA_TYPE_SHORT.value) {
          this.view.setU16(tag.value);
        } else if (tag.type.value === DATA_TYPE_BYTE.value) {
          this.view.setU8(tag.value);
        } else if (tag.type.value === DATA_TYPE_ASCII.value) {
          console.log(tag.value);
          this.view.setAscii(tag.value);
        } else if (tag.type.value === DATA_TYPE_FLOAT.value) {
          this.view.setFloat(tag.value);
        } else if (tag.type.value === DATA_TYPE_DOUBLE.value) {
          this.view.setDouble(tag.value);
        } else {
          throw new Error(`unsupported tag data type: ${tag.type.value}`);
        }
      });
  }

  private writeImageData(decoder: PixelDecoder, mask: ImageData | false, noData: number) {
    const { data } = this.image;
    const dummy = [];
    for (let i = 0; i < this.bands; i += 1) {
      dummy.push(noData || 0);
    }
    for (let i = 0; i < data.length; i += 4) {
      if (mask === false || mask.data[i] > 0) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];
        const value = decoder([r, g, b, a], noData || 0);
        this.sampleWriter(value);
      } else {
        this.sampleWriter(dummy);
      }
    }
  }

  private writeTag(tag: Tag) {
    this.view.setU16(tag.id);
    this.view.setU16(tag.type.value);
    this.view.setU32(tag.count);
    if (tag.inline) {
      if (tag.type.value === DATA_TYPE_LONG.value) {
        this.view.setU32(tag.value);
      } else if (tag.type.value === DATA_TYPE_SHORT.value) {
        this.view.setU16([tag.value, 0]);
      } else if (tag.type.value === DATA_TYPE_BYTE.value) {
        this.view.setU8([tag.value, 0, 0, 0]);
      } else {
        throw new Error(`unsupported inline data type: ${tag.type.value}`);
      }
    } else {
      this.view.setU32(this.offset);
      this.offset += this.sizeOfTagValue(tag);
    }
  }

  private addTag(id: number, type: DataType, count: number, value: any, inline: boolean) {
    this.tags.push({
      id,
      type,
      count,
      value,
      inline,
    });
  }

  private initDefaultTags() {
    this.tags.push(CompressionTag);
    this.tags.push(CopyrightTag);
    this.tags.push(CrsTag);
    this.tags.push(DescriptionTag);
    this.tags.push(GeoKeyDirectoryTag);
    // this.tags.push(EllipsoidTag);
    this.tags.push(PlanarConfigurationTag);
    this.tags.push(SoftwareTag);
    // samples per pixel, we use 1 for now.
    // this.addTag(277, DATA_TYPE_SHORT, 1, 1, true);
  }

  private sizeOfTiff(sampleFormat: SampleFormat): number {
    return this.IFH_SIZE + this.sizeOfIFD() + this.sizeOfImage(sampleFormat);
  }

  /**
   * 计算IFD占用空间大小
   */
  private sizeOfIFD() {
    let size = 2; // IFD Header: Tag count
    this.tags.forEach((tag) => {
      size += this.sizeOfTag(tag);
    });
    size += 4; // offset to next IDF
    return size;
  }

  /**
   * 计算一个IFD Tag占用空间大小
   */
  private sizeOfTag(tag: Tag): number {
    if (tag.inline) return this.TAG_SIZE;
    return this.TAG_SIZE + this.sizeOfTagValue(tag);
  }

  private sizeOfTagValue(tag: Tag): number {
    if (tag.type.value === DATA_TYPE_ASCII.value) {
      return tag.value.length + 1;
    }
    return tag.count * tag.type.size;
  }

  private sizeOfImage(sampleFormat: SampleFormat) {
    const bytesPerPixel = (this.bands * SAMPLE_BITS[sampleFormat]) / 8;
    return this.image.width * this.image.height * bytesPerPixel;
  }

  private offsetToStrip() {
    return this.IFH_SIZE + this.sizeOfIFD();
  }

  private selectSampleWriter(sampleFormat: SampleFormat): any {
    if (sampleFormat === 'byte') {
      this.sampleWriter = this.view.setU8.bind(this.view);
    } else if (sampleFormat === 'ushort') {
      this.sampleWriter = this.view.setU16.bind(this.view);
    } else if (sampleFormat === 'uint') {
      this.sampleWriter = this.view.setU32.bind(this.view);
    } else if (sampleFormat === 'float') {
      this.sampleWriter = this.view.setFloat.bind(this.view);
    } else if (sampleFormat === 'double') {
      this.sampleWriter = this.view.setDouble.bind(this.view);
    } else {
      throw new Error(`unknown sample format: ${sampleFormat}`);
    }
  }
}
