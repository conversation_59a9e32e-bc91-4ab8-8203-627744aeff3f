/* eslint-disable max-len */
/**
 * 预定义一些Tiff Tags
 *
 * 对于Tag的含义，可以使用下边的搜索工具来查询
 * https://www.awaresystems.be/imaging/tiff/tifftags/search.html
 */
import type { Tag } from './types';
import { DATA_TYPE_ASCII, DATA_TYPE_DOUBLE, DATA_TYPE_SHORT } from './types';

const COPYRIGHT = 'SATIMAGE All Rights Reserved.';
export const CopyrightTag: Tag = {
  id: 33432,
  type: DATA_TYPE_ASCII,
  count: COPYRIGHT.length,
  inline: false,
  value: COPYRIGHT,
};

const CRS_NAME =
  'PCS Name = WGS_1984_Web_Mercator_Auxiliary_Sphere|GCS_WGS_1984|ESRI PE String = PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.*********]],PRIM<PERSON>["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0]]|';

export const CrsTag: Tag = {
  id: 34737,
  type: DATA_TYPE_ASCII,
  count: CRS_NAME.length + 1,
  inline: false,
  value: CRS_NAME,
};

const DESC = `
This image is generated through products of SATIMAGE, which is a company targets at
Remote Sensing technologies and applications. For more information, please refer to
our website http://www.ruijingrs.com, we're looking forward to your visit!
`;
export const DescriptionTag: Tag = {
  id: 270,
  type: DATA_TYPE_ASCII,
  count: DESC.length,
  inline: false,
  value: DESC,
};

export const EllipsoidTag: Tag = {
  id: 34736,
  type: DATA_TYPE_DOUBLE,
  count: 2,
  inline: false,
  value: [298.*********, 6378137],
};

const SOFTWARE = 'SATIMAGE Javascript GeoTiff Exporter v0.2';
export const SoftwareTag: Tag = {
  id: 305,
  type: DATA_TYPE_ASCII,
  count: SOFTWARE.length,
  inline: false,
  value: SOFTWARE,
};

export const CompressionTag: Tag = {
  id: 259,
  type: DATA_TYPE_SHORT,
  count: 1,
  inline: true,
  // 1: Uncompressed
  // 2: CCITT 1D
  // 3: CCITT Group 3
  // 4: CCITT Group 4
  // 5: LZW
  // 6: JPEG
  value: 1,
};

// https://www.awaresystems.be/imaging/tiff/tifftags/planarconfiguration.html
export const PlanarConfigurationTag: Tag = {
  id: 284,
  type: DATA_TYPE_SHORT,
  count: 1,
  inline: true,
  // 1: Chunky
  // 2: Plannar
  value: 1,
};

// http://www.opengis.net/spec/GeoTIFF/1.1/req/GTModelTypeGeoKey
// 1: projected 2d CRS
// 2: *geographic 2D CRS
const GEO_KEY_MODEL_TYPE = 1024;
// http://www.opengis.net/spec/GeoTIFF/1.1/req/GTRasterTypeGeoKey
// 1: *PixelIsArea
// 2: PixelIsPoint
const GEO_KEY_RASTER_TYPE = 1025;

export const GeoKeyDirectoryTag: Tag = {
  id: 34735,
  type: DATA_TYPE_SHORT,
  count: 36, // 8个键值对 * 4 = 32
  value: [
    1,
    1,
    0,
    8, // Header: numberOfKeys=5
    GEO_KEY_MODEL_TYPE, // 1024
    0,
    1,
    32767, // GTModelTypeGeoKey = 1 (投影坐标系)
    GEO_KEY_RASTER_TYPE, // 1025
    0,
    1,
    1, // GTRasterTypeGeoKey = 1 (RasterPixelIsArea)
    1026, // GTCitationGeoKey
    34737, // 指向 CrsTag
    50,
    0, // 引用 GeoAsciiParams
    2048, // GeographicTypeGeoKey
    0,
    1,
    4326, // WGS84
    2049,
    34737,
    13,
    50,
    2054,
    0,
    1,
    9102,
    3073,
    34737,
    443,
    63,
    3076,
    0,
    1,
    9001,
  ],
  inline: false,
};
// export const getGdalNodata: (sampleFormat: SampleFormat, nodata: number) => Tag = (sampleFormat, nodata) => {
//   if (sampleFormat === 'byte') {
//     return { id: 42113, type: DATA_TYPE_ASCII, count: Math.max(6, String(nodata).length), value: String(nodata), inline: false };
//   } else if (sampleFormat === 'ushort') {
//     return { id: 42113, type: DATA_TYPE_ASCII, count: Math.max(6, String(nodata).length), value: String(nodata), inline: false };
//   } else if (sampleFormat === 'uint') {
//     return { id: 42113, type: DATA_TYPE_ASCII, count: Math.max(6, String(nodata).length), value: String(nodata), inline: false };
//   } else if (sampleFormat === 'float') {
//     return {
//       tag: {
//         id: 42113,
//         type: DATA_TYPE_ASCII,
//         count: 6,
//         value: String(-99999),
//         inline: false,
//       },
//       noData: -99999,
//     };
//   } else if (sampleFormat === 'double') {
//     return {
//       tag: { id: 42113, type: DATA_TYPE_ASCII, count: 6, value: String(-99999), inline: false },
//       noData: -99999,
//     };
//   } else {
//     throw new Error(`unknown sample format: ${sampleFormat}`);
//   }
// };
