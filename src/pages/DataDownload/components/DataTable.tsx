import type { MouseEvent } from 'react';
import { useEffect, useCallback, useMemo, useState } from 'react';
import { Flex, HorCenter } from '@/components/ui';
import { useSelectedTableRowKeys } from '@/hooks';
import type { TableColumnsType } from 'antd';
import { Spin } from 'antd';
import { Table } from 'antd';
import { useAtom } from 'jotai';
import {
  filtersAtom,
  metaUrlsAtom,
  metaUrlsInMapAtom,
  pageLoadingVisibleAtom,
  selectedRowKeysAtom,
} from '../atoms';
import { columns } from '../tableCols';
import { downloadUseLink, getTileUrl } from '@/utils';
import { useAtomValue, useSet<PERSON>tom as useUpdateAtom } from 'jotai';
import { useQuery } from 'react-query';
import type { MetaData } from '../types';
import { PageLoading } from './ui';
import { useDownloadGeoTiff, useDownloadName } from '../hooks';
import { fetchTextureTimePoints } from '@/services/global';
import { useTextureToken } from '@/pages/Overview/hooks';
import { parse } from 'qs';
import { options, typeOptions } from './Filter';
import moment from 'moment';



const DataTable = () => {
  const filters = useAtomValue(filtersAtom);
  const setMetaUrls = useUpdateAtom(metaUrlsAtom);
  const [visible, setVisible] = useAtom(pageLoadingVisibleAtom);
  const [selectedRowKeys, setSelectedRowKeys] = useAtom(selectedRowKeysAtom);
  const dowloadTiff = useDownloadGeoTiff();
  const [metaUrlsInMap, setMetaUrlsInMap] = useAtom(metaUrlsInMapAtom);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10)
  const token = useTextureToken();

  const { startDate, endDate, type, agg } = filters;
  const queryKeys = useMemo(() => {
    return ['meta-list', startDate, endDate, type, agg];
  }, [agg, endDate, startDate, type]);

  const { data, isLoading } = useQuery<MetaData[]>(
    queryKeys,
    () =>
      fetchTextureTimePoints({
        startDate,
        endDate,
        agg: type === 'DCOLOR' || type === 'TCOLOR' ? 'none' : agg,
        type: type.toUpperCase(),
      }),
    {
      staleTime: 0,
      cacheTime: 0,
    },
  );

  useEffect(() => {
    setCurrent(1);
  }, [filters]);

  const download = useCallback(
    (urls: string[], name: string) => {
      setVisible(true);
      dowloadTiff(urls, type, (blob) => {
        downloadUseLink(URL.createObjectURL(blob), `${name}.tiff`);
        setVisible(false);
      });
    },
    [type, dowloadTiff, setVisible],
  );

  const { rowSelection } = useSelectedTableRowKeys(
    selectedRowKeys,
    setSelectedRowKeys,
  );

  const { downloadName } = useDownloadName()
  const tableColumns: TableColumnsType<MetaData> = [
    ...columns,
    {
      title: '操作',
      width: 170,
      render(text, record) {
        const url = getTileUrl(
          {
            agg: type === 'DCOLOR' || type === 'TCOLOR' ? 'none' : agg,
            type: type.toUpperCase(),
            time: record.timePoints,
            token: token || '',
          },
          true,
        );
        const isInMap = metaUrlsInMap.find((item) => {
          const o1 = parse(item.split('?')[1]);
          const o2 = parse(url.split('?')[1]);

          return (
            o1.time === o2.time &&
            // @ts-ignore
            o1.type.toLowerCase() === o2.type.toLowerCase()
          );
        });
        const lens = metaUrlsInMap.length;

        return (
          <Flex>
            <HorCenter
              as="a"
              style={{ marginRight: 20 }}
              onClick={(e: MouseEvent<HTMLAnchorElement>) => {
                e.preventDefault();

                if (lens === 1 && isInMap) return;
                if (type === 'DCOLOR' || type === 'TCOLOR') {
                  setMetaUrlsInMap([url]);
                } else {
                  setMetaUrlsInMap((prev) =>
                    isInMap
                      ? prev.filter(
                        (u) =>
                          !u.includes(encodeURIComponent(record.timePoints)),
                      )
                      : [...prev, url],
                  );
                }
              }}
            >
              <i
                className={`icomoon icon-${isInMap ? 'visible' : 'hidden'}`}
                style={{ marginRight: 4 }}
              />
              <span>{isInMap ? '预览中' : '去预览'}</span>
            </HorCenter>
            <HorCenter
              as="a"
              onClick={(e: MouseEvent<HTMLAnchorElement>) => {
                e.preventDefault();
                download([url], downloadName(record.timePoints));
              }}
            >
              <i className="icomoon icon-download" style={{ marginRight: 4 }} />
              <span>下载</span>
            </HorCenter>
          </Flex>
        );
      },
    },
  ];

  const numberedList = useMemo(() => {
    return (data ?? []).map((item) => ({
      ...item,
      id: item.timePoints,
      time: item.timePoints,
    }));
  }, [data]);

  useEffect(() => {
    setMetaUrls(numberedList);
  }, [numberedList, setMetaUrls, setMetaUrlsInMap]);

  useEffect(() => {
    if (data && data.length > 0) {
      const isDCOLOROrTCOLOR = type === 'DCOLOR' || type === 'TCOLOR';
      setMetaUrlsInMap([
        getTileUrl(
          {
            agg: isDCOLOROrTCOLOR ? 'none' : agg,
            type: type.toUpperCase(),
            token: token || '',
            time: data[0].timePoints,
          },
          true,
        ),
      ]);
    }
  }, [agg, data, setMetaUrlsInMap, token, type]);

  useEffect(() => {
    setSelectedRowKeys([]);
  }, [setSelectedRowKeys, type]);

  return (
    <>
      {visible && (
        <PageLoading>
          <Spin />
          <span style={{ marginLeft: 12 }}>正在下载</span>
        </PageLoading>
      )}
      <Table
        rowSelection={rowSelection}
        loading={isLoading}
        rowKey="id"
        columns={tableColumns}
        dataSource={numberedList}
        pagination={{
          position: ['bottomCenter'],
          current,
          size: 'small',
          total: numberedList.length,
          pageSize: pageSize,
          style: {},
        }}
        onChange={({ current: cur, pageSize }) => {
          setCurrent(cur || 1);
          setPageSize(pageSize || 10);
        }}
      />
    </>
  );
};

export default DataTable;
