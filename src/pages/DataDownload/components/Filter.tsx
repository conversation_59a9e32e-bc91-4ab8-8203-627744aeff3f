import { OutlinedButton } from '@/components/ui';
import { useTextureToken } from '@/pages/Overview/hooks';
import { dateFormatter, downloadUseLink, getTileUrl } from '@/utils';
import { Col, DatePicker, message, Row, Select } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue, useSet<PERSON>tom as useUpdateAtom } from 'jotai';
import React, { useCallback, useEffect, useMemo } from 'react';
import {
  filtersAtom,
  metaUrlsAtom,
  metaUrlsInMapAtom,
  pageLoadingVisibleAtom,
  selectedRowKeysAtom,
} from '../atoms';
import { useDownloadGeoTiff, useDownloadName } from '../hooks';
import dayjs from 'dayjs';

export const options = [
  {
    label: '日均',
    value: 'daily',
  },
  {
    label: '周均',
    value: 'weekly',
  },
  {
    label: '月均',
    value: 'monthly',
  },
  {
    label: '季均',
    value: 'quarterly',
  },
  {
    label: '年均',
    value: 'yearly',
  },
];

export const typeOptions = [
  {
    label: '沙尘影像',
    value: 'DCOLOR',
  },
  {
    label: '真彩影像',
    value: 'TCOLOR',
  },
  {
    label: 'PM₂.₅',
    value: 'pm25',
  },
  {
    label: 'PM₁₀',
    value: 'pm10',
  },
  {
    label: 'NO₂_S5P',
    value: 'no2',
  },
  {
    label: 'NO₂_GK2B',
    value: 'gk2b_no2',
  },
  {
    label: 'O₃_S5P',
    value: 'o3',
  },
  {
    label: 'O₃_GK2B',
    value: 'gk2b_o3',
  },
  {
    label: 'SO₂',
    value: 'so2',
  },
  {
    label: 'CO',
    value: 'co',
  },
  {
    label: 'HCHO_S5P',
    value: 'hcho',
  },
  {
    label: 'HCHO_GK2B',
    value: 'gk2b_hcho',
  },

];

const Filter = () => {
  const [filters, setFilters] = useAtom(filtersAtom);
  const rowKeys = useAtomValue(selectedRowKeysAtom);
  const metaUrls = useAtomValue(metaUrlsAtom);
  const downloadTiff = useDownloadGeoTiff();
  const setVisible = useUpdateAtom(pageLoadingVisibleAtom);
  const setMetaUrlsInMap = useUpdateAtom(metaUrlsInMapAtom);
  const token = useTextureToken();

  const updateFilters = useCallback(
    (newVal: Record<string, number | string | undefined>) => {
      setFilters((prev) => ({
        ...prev,
        ...newVal,
      }));
    },
    [setFilters],
  );
  const metaArr = useMemo(
    () =>
      metaUrls
        .filter((item) => rowKeys.includes(item.id))
        .map((item) => {
          const isDCOLOROrTCOLOR =
            filters.type === 'DCOLOR' || filters.type === 'TCOLOR';
          return {
            ...item,
            url: getTileUrl(
              {
                type: filters.type.toUpperCase(),
                agg: isDCOLOROrTCOLOR ? 'none' : filters.agg,
                token: token || '',
                time: item.time,
              },
              true,
            ),
          };
        }),
    [filters.agg, filters.type, metaUrls, rowKeys, token],
  );
  const { downloadName } = useDownloadName()
  const batchDownload = useCallback(() => {
    metaArr.forEach((item) => {
      setVisible(true);
      downloadTiff([item.url], filters.type, (blob) => {
        downloadUseLink(URL.createObjectURL(blob), `${downloadName(item.time)}.tiff`);
        setVisible(false);
      });
    });
  }, [metaArr, setVisible, downloadTiff, filters.type, downloadName]);

  const handleBatchDownload = useCallback(() => {
    if (rowKeys.length === 0) {
      message.error('请先勾选数据项');
    } else {
      batchDownload();
    }
  }, [batchDownload, rowKeys.length]);

  const handleAggDownload = useCallback(() => {
    if (rowKeys.length === 0) {
      message.error('请先勾选数据项');
    } else {
      if (metaArr.length > 15) {
        return message.error('聚合下载最多支持下载15天数据');
      }
      setVisible(true);
      downloadTiff(
        metaArr.map((meta) => meta.url),
        filters.type,
        (blob) => {
          const find = typeOptions.find((item) => item.value === filters.type);
          downloadUseLink(
            URL.createObjectURL(blob),
            `${find?.label ? `${find.label}-` : ''}数据聚合下载.tiff`,
          );
          setVisible(false);
        },
      );
    }
  }, [downloadTiff, filters.type, metaArr, rowKeys.length, setVisible]);

  const clearMetaUrls = useCallback(() => {
    setMetaUrlsInMap([]);
  }, [setMetaUrlsInMap]);

  useEffect(() => {
    setFilters({
      agg: 'daily',
      endDate: dayjs().format(dateFormatter),
      startDate: dayjs().subtract(7, 'days').format(dateFormatter),
      type: 'DCOLOR',
    });
  }, [setFilters]);

  return (
    <Row gutter={12} style={{ marginBottom: 20 }}>
      <Col>
        <Select
          value={filters.type}
          options={typeOptions}
          style={{
            width: 150,
          }}
          onChange={(val) => {
            clearMetaUrls();
            updateFilters({ type: val, page: 0 });
          }}
        />
      </Col>
      {filters.type !== 'DCOLOR' && filters.type !== 'TCOLOR' && (
        <Col>
          <Select
            value={filters.agg}
            options={options}
            style={{
              width: 80,
            }}
            onChange={(val) =>
              updateFilters({
                agg: val,
              })
            }
          />
        </Col>
      )}
      <Col flex="1">
        <DatePicker.RangePicker
          className="w-full"
          disabledDate={(current) => {
            return current && current.isAfter(dayjs().endOf('day'));
          }}
          value={[dayjs(filters.startDate), dayjs(filters.endDate)]}
          onChange={(dates) => {
            if (dates && dates[0] && dates[1]) {
              updateFilters({
                startDate: dates[0].format(dateFormatter),
                endDate: dates[1].format(dateFormatter),
              });
            }
          }}
        />
      </Col>
      <Col>
        <OutlinedButton onClick={handleBatchDownload}>批量下载</OutlinedButton>
        {!filters.type.includes('COLOR') && (
          <OutlinedButton
            style={{ marginLeft: 12 }}
            onClick={handleAggDownload}
          >
            聚合下载
          </OutlinedButton>
        )}
      </Col>
    </Row>
  );
};

export default Filter;
