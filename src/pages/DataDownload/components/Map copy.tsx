import { FlyToInterpolator, TileLayer, MapView, BitmapLayer } from 'deck.gl';
import { fetchGeojsonIncludeChild } from '@/services/global';
import {
  colorRamps,
  decoder,
  getLevelByRegionCode,
  getNewViewState,
  globalPoMapping,
  stationPollutionValuesAndColors,
  wmts,
} from '@/utils';
import DeckGL from '@deck.gl/react';
import { useAtom } from 'jotai';
import React, { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { filtersAtom, metaUrlsInMapAtom, viewStateAtom } from '../atoms';
import { useGeojsonLayer } from '../hooks';
import { LegendItem, LegendType, LegendWrapper, MapContainer } from './ui';
import center from '@turf/center';
import DataTextureLayer from '@/layers/texture-layer/data-texture-layer';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import FpsThrottledDeck from './FpsThrottledDeck';
import { Flex, Spacer } from '@/components/ui';
import {
  useMaskLayer,
  useQyGeojson,
  useUserRegionCode,
  useWindowSize,
} from '@/hooks';
import { useAtomValue } from 'jotai';
import { fetchMap } from '../services';
import { stringCoders } from '../utils';
import { useFrontierTileLayerWithLabel } from '@/pages/Overview/hooks';
import { getTranslateImageData } from '@/utils/image';
import useTDColorMaskLayer from './useTDColorMaskLayer';
// @ts-ignore
import { MaskExtension } from '@deck.gl/extensions';
const Map = () => {
  const regionCode = useUserRegionCode();
  const filters = useAtomValue(filtersAtom);
  const [viewState, setViewState] = useAtom(viewStateAtom);
  const metaUrlsInMap = useAtomValue(metaUrlsInMapAtom);
  const iboLayer = useFrontierTileLayerWithLabel(true, 0.6);
  const { height } = useWindowSize();
  const mapHeight = useMemo(() => {
    const pagePaddingTop = 30;
    const headerHeight = 46;
    const tabHeight = 118;

    const result = height - pagePaddingTop - headerHeight - tabHeight - 16;

    return result < 300 ? 300 : result;
  }, [height]);
  const { data } = useQuery(
    [`map-geojson-${regionCode}`],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(regionCode),
        level: getLevelByRegionCode(regionCode),
      }),
    {
      enabled: Boolean(regionCode),
    },
  );

  const { data: map } = useQuery<{
    maxLat: number;
    minLat: number;
    maxLng: number;
    minLng: number;
    url: string;
  }>(['data-download-map'], () => fetchMap());

  const textureLayer = useMemo(() => {
    if (filters.type === 'DCOLOR' || filters.type === 'TCOLOR') {
      return new TileLayer({
        ...tileLayerBaseConfig,
        id: `data-texture-layer-${filters.type}`,
        visible: true,
        extensions: [new MaskExtension()],
        data: metaUrlsInMap[0],
        smooth: false,
        maxZoom: 7,
        minZoom: 3,
        colorFormat: 'RGBA',
        extent: [91.250096, 37.045865, 127.086488, 54.262446],
        pickable: false,
        maskId: 'TDCOLOR-Mask',

        // 色带更改后，重新渲染图层
        shouldUpdate: (prevProps: any, nextProps: any) => {
          return (
            prevProps.colorRamp !== nextProps.colorRamp ||
            prevProps.dataUrl !== nextProps.dataUrl ||
            prevProps.visible !== nextProps.visible ||
            prevProps.min !== nextProps.min ||
            prevProps.max !== nextProps.max ||
            prevProps.filters !== nextProps.filters ||
            prevProps.filtersChannel !== nextProps.filtersChannel
          );
        },
        renderSubLayers: (props) => {
          const {
            bbox: { west, south, east, north },
          } = props.tile;

          return new BitmapLayer(props, {
            pickable: true,
            data: null,
            image: props.data,
            bounds: [west, south, east, north],
          });
        },
        getTileData: (tile) => {
          return getTranslateImageData(tile.url);
        },
      });
    }
    return new DataTextureLayer({
      id: `data-texture-layer-${filters.type}`,
      map,
      regionCode: 0,
      visible: metaUrlsInMap.length > 0,
      decoder: decoder[filters.type.toLowerCase()],
      colorRamp: colorRamps[filters.type.toLowerCase()],
    });
  }, [filters.type, map, metaUrlsInMap]);

  useEffect(() => {
    const lens = metaUrlsInMap.length;

    if (lens > 0 && textureLayer) {
      if (filters.type === 'DCOLOR' || filters.type === 'TCOLOR') {
        return;
      }
      if (lens === 1) {
        textureLayer.setTileUrl(metaUrlsInMap[0]);
      } else {
        textureLayer.setAggregateUrls(
          metaUrlsInMap,
          // @ts-ignore
          stringCoders[filters.type].encoder,
          // @ts-ignore
          stringCoders[filters.type].decoder,
          stringCoders[filters.type].bands,
        );
      }
    }
  }, [filters.type, metaUrlsInMap, textureLayer]);

  useEffect(() => {
    const container = document.getElementById('map-container');
    if (data && container) {
      const { offsetHeight, offsetWidth } = container;
      const newViewState = getNewViewState(
        data,
        viewState,
        offsetWidth,
        offsetHeight,
        80,
      );
      const {
        geometry: { coordinates },
      } = center(data);
      setViewState((prev) => ({
        ...prev,
        ...newViewState,
        longitude: coordinates[0],
        latitude: coordinates[1],
        transitionDuration: 1000,
        transitionInterpolator: new FlyToInterpolator(),
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, setViewState]);

  const tileLayer = new TileLayer({
    ...tileLayerBaseConfig,
    id: 'tianditu-tile-layer',
    data: wmts('vec'),
  });

  const { geojson: TDGeoJson } = useQyGeojson();

  const maskLayer = useMaskLayer(data);

  const geojsonLayer = useGeojsonLayer(data);

  const TDColorMaskLayer = useTDColorMaskLayer(TDGeoJson);

  return (
    <MapContainer
      id="map-container"
      style={{
        minHeight: mapHeight,
      }}
    >
      {stationPollutionValuesAndColors[filters.type] && (
        <LegendWrapper>
          <LegendType>
            <p>{globalPoMapping[filters.type]}</p>
          </LegendType>
          <Flex>
            {stationPollutionValuesAndColors[filters.type]?.values
              .slice()
              .reverse()
              .map(
                (
                  item: { min?: number; max?: number; color: string },
                  index: number,
                ) => (
                  <LegendItem key={item.color}>
                    <div
                      className="lump"
                      style={{
                        width: 30,
                        height: 8,
                        marginTop: 8,
                        background: item.color,
                      }}
                    />
                    <div
                      className="min"
                      style={{
                        transform: index === 0 ? 'tranlateX(-50%)' : 'none',
                        height: '1px',
                      }}
                    >
                      {index === 0 && typeof item.min !== 'undefined'
                        ? item.min
                        : ''}
                    </div>
                    <Spacer />
                    <div className="max">{item.max}</div>
                  </LegendItem>
                ),
              )}
          </Flex>
        </LegendWrapper>
      )}
      <DeckGL
        viewState={viewState}
        // @ts-ignore
        Deck={FpsThrottledDeck}
        glOptions={{ antialias: false }}
        views={[new MapView({ repeat: true, orthographic: true })]}
        controller
        layers={[
          maskLayer,
          TDColorMaskLayer,
          tileLayer,
          textureLayer,
          iboLayer,
          geojsonLayer,
        ]}
        onViewStateChange={({ viewState: newViewState }) => {
          setViewState((prev) => ({
            ...prev,
            ...newViewState,
          }));
        }}
      />
    </MapContainer>
  );
};

export default Map;
