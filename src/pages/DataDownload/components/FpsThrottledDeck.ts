/* eslint-disable no-underscore-dangle */
import { Deck } from '@deck.gl/core';

const defaultProps = {
  fps: 35,
};

/**
 * 播放动画时，60 FPS导致CPU利用率较高，低配机器会出现卡顿，40FPS其实已够用
 */
export default class FpsThrottledDeck extends Deck {
  private readonly frameInterval: number;
  private lastRenderTime = new Date().getTime();

  constructor(props: any) {
    super({ ...defaultProps, ...props });
    this.frameInterval = 1000 / this.props.fps;
  }

  _drawLayers(reason: string) {
    const now = new Date().getTime();
    if (now - this.lastRenderTime > this.frameInterval) {
      super._drawLayers(reason, null);
      this.lastRenderTime = now;
    }
  }
}
