import { FlyToInterpolator, TileLayer, MapView, BitmapLayer } from 'deck.gl';
import { fetchGeojsonIncludeChild } from '@/services/global';
import {
  getLevelByRegionCode,
  getNewViewState,
  wmts,
} from '@/utils';
import { remoteSensingValuesAndColors, colorRamps, decoder, dataDownLoadglobalPoMapping } from '@/utils/dataDown';
import DeckGL from '@deck.gl/react';
import { useAtom } from 'jotai';
import React, { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { filtersAtom, metaUrlsInMapAtom, viewStateAtom } from '../atoms';
import { useGeojsonLayer } from '../hooks';
import { LegendItem, LegendType, LegendWrapper, MapContainer } from './ui';
import center from '@turf/center';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import FpsThrottledDeck from './FpsThrottledDeck';
import { Flex, Spacer } from '@/components/ui';
import {
  useMaskLayer,
  useQyGeojson,
  useUserRegionCode,
  useWindowSize,
} from '@/hooks';
import { useAtomValue } from 'jotai';
import { useFrontierTileLayerWithLabel } from '@/pages/Overview/hooks';
import { getTranslateImageData } from '@/utils/image';
import useTDColorMaskLayer from './useTDColorMaskLayer';
// @ts-ignore
import { MaskExtension } from '@deck.gl/extensions';
import bbox from '@turf/bbox';
import TextureBitmapLayer from '@/layers/dataDown/geoJson-texture-layer/bitmap-layer/bitmap-layer';
import { coders, generateTilesAvg, TILE_PIXEL } from '../utils/newIndex';
const Map = () => {
  const regionCode = useUserRegionCode();
  const filters = useAtomValue(filtersAtom);
  const [viewState, setViewState] = useAtom(viewStateAtom);
  const metaUrlsInMap = useAtomValue(metaUrlsInMapAtom);
  const iboLayer = useFrontierTileLayerWithLabel(true, 0.6);
  const { height } = useWindowSize();
  const mapHeight = useMemo(() => {
    const pagePaddingTop = 30;
    const headerHeight = 46;
    const tabHeight = 118;

    const result = height - pagePaddingTop - headerHeight - tabHeight - 16;

    return result < 300 ? 300 : result;
  }, [height]);
  const { data } = useQuery(
    [`map-geojson-${regionCode}`],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(regionCode),
        level: getLevelByRegionCode(regionCode),
      }),
    {
      enabled: Boolean(regionCode),
    },
  );
  
  const decoderType = useMemo(() => {
   
    return filters.type;
  }, [filters.type]);

  const gl = useMemo(() => {
    const canvas = document.createElement('canvas');
    canvas.width = TILE_PIXEL;
    canvas.height = TILE_PIXEL;
    const gl = canvas.getContext('webgl2');
    return gl;
  }, []);


  const textureLayer = useMemo(() => {
    const isColor = ['DCOLOR', 'TCOLOR'].includes(filters.type)
    if (data) {
      const extent = bbox(data);
      return new TileLayer({
        ...tileLayerBaseConfig,
        id: `custom-data-texture-layer-${filters.type}`,
        // @ts-ignore
        maskId: 'geojson-mask',
        extensions: [new MaskExtension()],
        data: metaUrlsInMap.join(','),
        maxZoom: 7,
        minZoom: 0,
        tileSize: 256,
        colorFormat: 'RGBA',
        pickable: true,
        // @ts-ignore
        extent,
        // 色带更改后，重新渲染图层
        shouldUpdate: (prevProps: any, nextProps: any) => {
          return (
            prevProps.colorRamp !== nextProps.colorRamp ||
            prevProps.dataUrl !== nextProps.dataUrl ||
            prevProps.visible !== nextProps.visible ||
            prevProps.min !== nextProps.min ||
            prevProps.max !== nextProps.max ||
            prevProps.filters !== nextProps.filters ||
            prevProps.filtersChannel !== nextProps.filtersChannel
          );
        },
        visible: metaUrlsInMap.length > 0,
        renderSubLayers: (props: any) => {
          const {
            bbox: { west, south, east, north },
          } = props.tile;
          if (!gl) return null;
          return isColor ?
            new BitmapLayer(props, {
              pickable: true,
              data: null,
              image: props.data,
              bounds: [west, south, east, north],
            }) :
            new TextureBitmapLayer(props, {
              data: null,
              image: generateTilesAvg(gl, props.data, coders[decoderType]?.decode, coders[decoderType]?.encode),
              bounds: [west, south, east, north],
              decoder: decoder[decoderType],
              colorRamp: colorRamps[decoderType],
              smooth: false,
            });
        },
        getTileData: (tile) => {
          return isColor ?
            getTranslateImageData(tile.url)
            : Promise.all(
              tile.url?.split(',').map(
                (url) =>
                  new Promise((resolve) => {
                    getTranslateImageData(url)
                      .then((d) => resolve(d))
                      .catch(() => resolve(null));
                  }),
              ) || [],
            );
        },
      });
    }
    return null;
  }, [data, decoderType, filters.type, gl, metaUrlsInMap]);

  useEffect(() => {
    const container = document.getElementById('map-container');
    if (data && container) {
      const { offsetHeight, offsetWidth } = container;
      const newViewState = getNewViewState(
        data,
        viewState,
        offsetWidth,
        offsetHeight,
        80,
      );
      const {
        geometry: { coordinates },
      } = center(data);
      setViewState((prev) => ({
        ...prev,
        ...newViewState,
        longitude: coordinates[0],
        latitude: coordinates[1],
        transitionDuration: 1000,
        transitionInterpolator: new FlyToInterpolator(),
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, setViewState]);

  const tileLayer = new TileLayer({
    ...tileLayerBaseConfig,
    id: 'tianditu-tile-layer',
    data: wmts('vec'),
  });

  const { geojson: TDGeoJson } = useQyGeojson();

  const maskLayer = useMaskLayer(data);

  const geojsonLayer = useGeojsonLayer(data);

  const TDColorMaskLayer = useTDColorMaskLayer(TDGeoJson);


  return (
    <MapContainer
      id="map-container"
      style={{
        minHeight: mapHeight,
      }}
    >
      {remoteSensingValuesAndColors[filters.type] && (
        <LegendWrapper>
          <LegendType>
            <p>{dataDownLoadglobalPoMapping[filters.type]}</p>
            <p>{remoteSensingValuesAndColors[filters.type]?.unit}</p>
          </LegendType>
          <Flex>
            {remoteSensingValuesAndColors[filters.type]?.values
              .slice()
              .reverse()
              .map(
                (
                  item: { min?: number; max?: number; color: string },
                  index: number,
                ) => (
                  <LegendItem key={item.color}>
                    <div
                      className="lump"
                      style={{
                        width: 30,
                        height: 8,
                        marginTop: 8,
                        background: item.color,
                      }}
                    />
                    <div
                      className="min"
                      style={{
                        transform: index === 0 ? 'tranlateX(-50%)' : 'none',
                        height: '1px',
                      }}
                    >
                      {index === 0 && typeof item.min !== 'undefined'
                        ? item.min
                        : ''}
                    </div>
                    <Spacer />
                    <div className="max">{item.max}</div>
                  </LegendItem>
                ),
              )}
          </Flex>
        </LegendWrapper>
      )}
      <DeckGL
        viewState={viewState}
        // @ts-ignore
        Deck={FpsThrottledDeck}
        glOptions={{ antialias: false }}
        views={[new MapView({ repeat: true, orthographic: true })]}
        controller
        layers={[
          maskLayer,
          TDColorMaskLayer,
          tileLayer,
          textureLayer,
          iboLayer,
          geojsonLayer,
        ]}
        onViewStateChange={({ viewState: newViewState }) => {
          setViewState((prev) => ({
            ...prev,
            ...newViewState,
          }));
        }}
      />
    </MapContainer>
  );
};

export default Map;
