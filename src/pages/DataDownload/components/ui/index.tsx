import { Flex, HorCenter } from '@/components/ui';
import { getColorFromTheme } from '@/components/ui/utils';
import { Link } from 'react-router-dom';
import styled, { css } from 'styled-components';

export const PageTabs = styled(Flex)`
  margin-top: 40px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e6e6e6;
`;

export const PageTabItem = styled(Link)<{
  active?: number;
}>`
  position: relative;
  display: block;
  margin-right: 30px;
  padding: 0 20px 6px;
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  border-bottom: 3px solid transparent;

  ${({ active }) =>
    active &&
    css`
      pointer-events: none;
    `}

  ${(props) => {
    return props.active
      ? css`
          color: ${getColorFromTheme('primary')};
          border-color: ${getColorFromTheme('primary')};
        `
      : null;
  }}
`;

export const MapContainer = styled.div`
  position: relative;
  width: 760px;
  margin-bottom: 24px;
  margin-left: 40px;
  border: 1px solid #e0e0e0;
`;

export const Left = styled.div`
  flex: 1;

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background: transparent;
  }
`;

export const Legend = styled(Flex)`
  position: absolute;
  left: 40px;
  bottom: 40px;
  z-index: 10;

  .type {
    margin-right: 8px;
  }
  .container {
    display: flex;
    flex: 1;

    .item {
      flex-direction: column;

      .rect {
        width: 60px;
        height: 10px;
      }

      .text {
        font-size: 12px;
        text-align: center;
      }
    }
  }
`;

export const LegendWrapper = styled(HorCenter)`
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 20;
`;
export const LegendType = styled.div`
  padding-right: 8px;
  line-height: 24px;
  font-size: 14px;
  text-align: right;

  p {
    margin: 0;
    line-height: 1;

    & + p {
      margin-top: 4px;
      font-size: 12px;
    }
  }
`;

export const LegendItem = styled(Flex)`
  width: 30px;
  flex-wrap: wrap;

  .lump {
    width: 100%;
  }
  .min {
    min-width: 10px;
    font-size: 12px;
  }
  .max {
    font-size: 12px;
    transform: translateX(50%);
  }
`;

export const PageLoading = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
`;
