import { dateFormatter } from '@/utils';
import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import { atom } from 'jotai';
import type { coders } from '../utils';
import dayjs from 'dayjs';

export const viewStateAtom = atom<ViewStateProps>({
  longitude: 111.62056100000001,
  latitude: 45.3719785,
  zoom: 7,
  maxZoom: 18,
  minZoom: 4,
});

export const selectedRowKeysAtom = atom<React.Key[]>([]);

export const filtersAtom = atom<{
  agg: string;
  startDate: string;
  endDate: string;
  type: keyof typeof coders;
}>({
  agg: 'daily',
  endDate: dayjs().format(dateFormatter),
  startDate: dayjs().subtract(7, 'days').format(dateFormatter),
  type: 'DCOLOR',
});

export const metaUrlsAtom = atom<
  {
    id: string;
    time: string;
    name: string;
  }[]
>([]);

export const pageLoadingVisibleAtom = atom(false);

// 正在地图预览的纹理图url数组
export const metaUrlsInMapAtom = atom<string[]>([]);
export const mapAtom = atom<
  | undefined
  | {
      minLng: number;
      maxLng: number;
      minLat: number;
      maxLat: number;
      url: string;
    }
>(undefined);
