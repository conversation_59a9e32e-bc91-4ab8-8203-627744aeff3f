import GL from '@luma.gl/constants';
import { Transform } from '@luma.gl/engine';
import { Buffer, Texture2D } from '@luma.gl/webgl';
import { project32 } from 'deck.gl/typed';

export const coders: Record<string, any> = {
  pm25: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  pm10: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  no2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  gk2b_no2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  no2tcd: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  o3: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  gk2b_o3: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && r * 255 + g) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  so2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 1000) / 255), Math.floor((v * 1000) % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 1000) || nodata,
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 1000.0;`,
    encode: `vec4(floatFloor(v * 1000.0 / 255.0), floatFloor(floatMod(v * 1000.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  so2tcd: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 1000) / 255), Math.floor((v * 1000) % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 1000) || nodata,
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 1000.0;`,
    encode: `vec4(floatFloor(v * 1000.0 / 255.0), floatFloor(floatMod(v * 1000.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  aod: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 1000) / 255), Math.floor((v * 1000) % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 1000) || nodata,
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 1000.0;`,
    encode: `vec4(floatFloor(v * 1000.0 / 255.0), floatFloor(floatMod(v * 1000.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  hcho: {
    format: 'float',
    bands: 1,
    decoder: ([r, g, b, a]: number[], nodata: number) =>
      ((r !== 0 || g !== 0 || b !== 0 || a !== 0) && (g * 255.0 * 255.0 * 255.0 + b * 255.0 * 255.0 + a * 255.0 + r) / 100000000.0) || nodata,
    encoder: (v: number) => [
      Math.round((v * 100000000) % 255),
      Math.floor(Math.round(v * 100000000) / 255 / 255 / 255),
      Math.floor(Math.round(v * 100000000) / 255 / 255) % 255,
      Math.floor(Math.round(v * 100000000) / 255) % 255,
    ],
    decode: `((((d.g * 255.0 + d.b) * 255.0 + d.a) * 255.0 + d.r) * 255.0) / 100000000.0;`,
    // eslint-disable-next-line max-len
    encode: `vec4(floatMod(v * 100000000.0, 255.0), floatFloor(v * 100000000.0 / 255.0 / 255.0 / 255.0), floatMod(floatFloor(v * 100000000.0 / 255.0 / 255.0), 255.0), floatMod(floatFloor(v * 100000000.0 / 255.0), 255.0));`,
    nodata: -99999,
  },
  gk2b_hcho: {
    format: 'float',
    bands: 1,
    decoder: ([r, g, b, a]: number[], nodata: number) =>
      ((r !== 0 || g !== 0 || b !== 0 || a !== 0) && (g * 255.0 * 255.0 * 255.0 + b * 255.0 * 255.0 + a * 255.0 + r) / 100000000.0) || nodata,
    encoder: (v: number) => [
      Math.round((v * 100000000) % 255),
      Math.floor(Math.round(v * 100000000) / 255 / 255 / 255),
      Math.floor(Math.round(v * 100000000) / 255 / 255) % 255,
      Math.floor(Math.round(v * 100000000) / 255) % 255,
    ],
    decode: `((((d.g * 255.0 + d.b) * 255.0 + d.a) * 255.0 + d.r) * 255.0) / 100000000.0;`,
    // eslint-disable-next-line max-len
    encode: `vec4(floatMod(v * 100000000.0, 255.0), floatFloor(v * 100000000.0 / 255.0 / 255.0 / 255.0), floatMod(floatFloor(v * 100000000.0 / 255.0 / 255.0), 255.0), floatMod(floatFloor(v * 100000000.0 / 255.0), 255.0));`,
    nodata: -99999,
  },
  co: {
    format: 'double',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 10000) / 255), Math.floor((v * 10000) % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 10000) || nodata,
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 10000.0;`,
    encode: `vec4(floatFloor(v * 10000.0 / 255.0), floatFloor(floatMod(v * 10000.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  cotcd: {
    format: 'double',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 10000) / 255), Math.floor((v * 10000) % 255), 0, 0],
    decoder: ([r, g]: [number, number], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 10000) || nodata,
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 10000.0;`,
    encode: `vec4(floatFloor(v * 10000.0 / 255.0), floatFloor(floatMod(v * 10000.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  DCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
    decoder: ([r, g, b, a]: [number, number, number, number], nodata: number) =>
      ((r !== 0 || g !== 0 || b !== 0 || a !== 0) && [r, g, b, a]) || [nodata, nodata, nodata, nodata],
  },
  TCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
    decoder: ([r, g, b, a]: [number, number, number, number], nodata: number) =>
      ((r !== 0 || g !== 0 || b !== 0 || a !== 0) && [r, g, b, a]) || [nodata, nodata, nodata, nodata],
  },
  ch4: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 10000 * 10) / 255), Math.floor((v * 10000 * 10) % 255), 0, 0],
    decoder: ([r, g]: number[], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 10000 / 10) || nodata,
    decode: `d.r * 255.0 * 255.0 + d.g * 255.0;`,
    encode: `vec4(floatFloor(v / 255.0), floatFloor(floatMod(v, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  co2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 10) / 255), Math.floor((v * 10) % 255), 0, 0],
    decoder: ([r, g]: number[], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) / 10) || nodata,
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 10.0;`,
    encode: `vec4(floatFloor(v * 10.0 / 255.0), floatFloor(floatMod(v * 10.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  uva: {
    format: 'float',
    bands: 1,
    encoder: (value: number) => [value, 0, 0, 0],
    decoder: ([r]: number[], nodata: number) => (r !== 0 && r) || nodata,
    decode: `d.r * 255.0;`,
    encode: `vec4(floatFloor(v), 0.0, 0.0, 255.0);`,
    nodata: -99999,
  },
  uvb: {
    format: 'float',
    bands: 1,
    encoder: (value: number) => [value * 10, 0, 0, 0],
    decoder: ([r]: number[], nodata: number) => (r !== 0 && r / 10) || nodata,
    decode: `d.r * 255.0 * 10.0;`,
    encode: `vec4(floatFloor(v / 10.0), 0.0, 0.0, 255.0);`,
    nodata: -99999,
  },
  prs: {
    format: 'float',
    bands: 1,
    decoder: ([r, g]: number[], nodata: number) => ((r !== 0 || g !== 0) && (r * 255 + g) * 100) || nodata,
    encoder: (value: number) => {
      const v = Math.round(value / 100);
      return [Math.floor(v / 255), v % 255, 0, 0];
    },
    decode: `(d.r * 255.0 * 255.0 + d.g * 255.0) / 100.0;`,
    encode: `vec4(floatFloor(v * 100.0 / 255.0), floatFloor(floatMod(v * 100.0, 255.0)), 0.0, 255.0);`,
    nodata: -99999,
  },
  pre: {
    format: 'float',
    bands: 1,
    decoder: ([r]: number[], nodata: number) => (r !== 0 && r) || nodata,
    encoder: (value: number) => [value, 0, 0, 0],
    decode: `d.r * 255.0;`,
    encode: `vec4(floatFloor(v), 0.0, 0.0, 255.0);`,
    nodata: -99999,
  },
  rh: {
    format: 'float',
    bands: 1,
    decoder: ([r]: number[], nodata: number) => (r !== 0 && (r / 1000) * 10) || nodata,
    encoder: (value: number) => [Math.floor((value * 1000) / 10), 0, 0, 0],
    decode: `d.r * 255.0 / 1000.0 * 10.0;`,
    encode: `vec4(floatFloor(v * 1000.0 / 10.0), 0.0, 0.0, 255.0);`,
    nodata: -99999,
  },
  tmp: {
    format: 'float',
    bands: 1,
    decoder: ([r]: number[], nodata: number) => (r !== 0 && r - 128 + 273) || nodata,
    encoder: (value: number) => [value - 273 + 128, 0, 0, 0],
    decode: `d.r * 255.0 - 128.0 + 273.0;`,
    encode: `vec4(floatFloor(v + 128.0 - 273.0), 0.0, 0.0, 255.0);`,
    nodata: -99999,
  },
  vis: {
    format: 'float',
    bands: 1,
    decoder: ([r]: number[], nodata: number) => (r !== 0 && r * 1000 * 0.2) || nodata,
    encoder: (value: number) => [Math.floor(value / 1000 / 0.2), 0, 0, 0],
    decode: `d.r * 255.0 * 1000.0 * 0.2;`,
    encode: `vec4(floatFloor(v / 0.2 / 1000.0), 0.0, 0.0, 255.0);`,
    nodata: -99999,
  },
  'wiu-wiv': {
    format: 'float',
    bands: 2,
    decoder: ([r, g, b]: number[], nodata: number) => {
      if (r === 0 && g === 0 && b === 0) {
        return [nodata, nodata];
      }
      const v1 = r - 100 + b / 10 / 10;
      const v2 = g - 100 + (b - (b / 10) * 10) / 10;

      return [v1, v2];
    },
    encoder: (value: number[]) => {
      const [v1, v2] = value;

      const uMps = v1 + 100;
      const vMps = v2 + 100;
      const r = uMps;
      const g = vMps;
      const uFraction = uMps - r;
      const vFraction = vMps - g;
      const b = uFraction * 10 + vFraction * 10;
      const a = 0;

      return [r, g, b, a];
    },
    decode: `vec2 decode(vec4 data) {
      return vec2(data.r * 255.0 + data.b * 255.0 / 10.0 / 10.0 - 100.0, data.g * 255.0 + mod(data.b * 255.0, 10.0)/10.0 - 100.0);
    }`,
    encode: `vec4 encode(vec2 value) {
      float uMps = value.x + 100.0;
      float vMps = value.y + 100.0;
      float r = floor(uMps);
      float g = floor(vMps);
      float uFraction = uMps - r;
      float vFraction = vMps - g;
      float b = uFraction * 100.0 + vFraction * 10.0;
      float a = 0.0;
      return vec4(r, g, b, a);
    }`,
    nodata: -99999,
  },
  fnr: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor((v * 10) % 255), 0, 0, 0],
    decoder: ([r]: [number], nodata: number) => (r !== 0 && r / 10) || nodata,
    decode: `d.r * 255.0 / 10.0;`,
    encode: `vec4(mod(v * 10.0, 255.0), 0.0, 0.0, 0.0);`,
    nodata: -99999,
  },
};

export const stringCoders: Record<string, { format: string; bands: 1 | 2 | 3 | 4; encoder: string; decoder: string }> = {
  pm25: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  pm10: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  no2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  gk2b_no2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  o3: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  gk2b_o3: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  so2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  aod: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  co: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  DCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: `vec4 encode(vec4 value) {
      return value;
    }`,
    decoder: `vec4 decode(vec4 data) {
      return data * 255.0;
    }`,
  },
  F721: {
    format: 'byte',
    bands: 4,
    encoder: `vec4 encode(vec4 value) {
      return value;
    }`,
    decoder: `vec4 decode(vec4 data) {
      return data * 255.0;
    }`,
  },
  BLOOM: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  TCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: `vec4 encode(vec4 value) {
      return value;
    }`,
    decoder: `vec4 decode(vec4 data) {
      return data * 255.0;
    }`,
  },
  ch4: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  co2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  uva: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  uvb: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  prs: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  pre: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  rh: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  tmp: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  vis: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  'wiu-wiv': {
    format: 'float',
    bands: 2,
    decoder: `vec2 decode(vec4 data) {
      return vec2(data.r * 255.0 + data.b * 255.0 / 10.0 / 10.0 - 100.0, data.g * 255.0 + mod(data.b * 255.0, 10.0)/10.0 - 100.0);
    }`,
    encoder: `vec4 encode(vec2 value) {
      float uMps = value.x + 100.0;
      float vMps = value.y + 100.0;
      float r = floor(uMps);
      float g = floor(vMps);
      float uFraction = uMps - r;
      float vFraction = vMps - g;
      float b = uFraction * 100.0 + vFraction * 10.0;
      float a = 0.0;

      return vec4(r, g, b, a);
    }`,
  },
  hcho: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(mod(value, 255.0), floor(value / 255.0 / 255.0 / 255.0), mod(floor(value / 255.0 / 255.0), 255.0), mod(floor(value / 255.0), 255.0));
    }`,
    decoder: `float decode(vec4 data) {
      return (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0;
    }`,
  },
  gk2b_hcho: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(mod(value, 255.0), floor(value / 255.0 / 255.0 / 255.0), mod(floor(value / 255.0 / 255.0), 255.0), mod(floor(value / 255.0), 255.0));
    }`,
    decoder: `float decode(vec4 data) {
      return (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0;
    }`,
  },
  no2tcd: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(mod(value, 255.0), floor(value / 255.0 / 255.0 / 255.0), mod(floor(value / 255.0 / 255.0), 255.0), mod(floor(value / 255.0), 255.0));
    }`,
    decoder: `float decode(vec4 data) {
      return (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0;
    }`,
  },
  o3tcd: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  fnr: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(mod(value * 10.0, 255.0), 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0 / 10.0;
    }`,
  },
};
export function lng2tile(lng: number, zoom: number) {
  return Math.floor(((lng + 180) / 360) * Math.pow(2, zoom));
}
export function lat2tile(lat: number, zoom: number) {
  return Math.floor(((1 - Math.log(Math.tan((lat * Math.PI) / 180) + 1 / Math.cos((lat * Math.PI) / 180)) / Math.PI) / 2) * Math.pow(2, zoom));
}
export function lngLatToPixel(lng: number, lat: number, zoom: number): [number, number] {
  const tileX = lng2tile(lng, zoom);
  const tileY = lat2tile(lat, zoom);
  const pixelX = Math.floor(((lng + 180) / 360) * 256 * Math.pow(2, zoom) - tileX * 256);
  const pixelY = Math.floor(
    ((1 - Math.log(Math.tan((lat * Math.PI) / 180) + 1 / Math.cos((lat * Math.PI) / 180)) / Math.PI) / 2) * 256 * Math.pow(2, zoom) - tileY * 256,
  );
  return [pixelX, pixelY];
}

export const TILE_PIXEL = 256;
export const generateTilesAvg = (gl: WebGLRenderingContext, images: HTMLImageElement[], decode: string, encode: string) => {
  if (!images || images.length === 0 || !gl) {
    return null;
  }
  if (images.length === 1) {

    return images[0];
  }
  const textures = images.map(
    (img) =>
      new Texture2D(gl, {
        data: img,
        format: GL.RGBA,
        dataFormat: GL.RGBA,
        type: GL.UNSIGNED_BYTE,
        width: TILE_PIXEL,
        height: TILE_PIXEL,
        parameters: {
          [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
          [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
        },
        mipmaps: false,
      }),
  );

  const index = new Buffer(gl, {
    data: new Float32Array(TILE_PIXEL * TILE_PIXEL).fill(0).map((_, i) => i),
  });
  const rgbaData = new Buffer(gl, {
    data: new Float32Array(TILE_PIXEL * TILE_PIXEL * 4),
  });
  const float2rgbaTransformProgram = new Transform(gl, {
    vs: `#version 300 es
      in float index;
      out vec4 rgbaData;
      out vec2 pos;
      float floatMod(float dividend, float divisor){
        float num = mod(dividend, divisor);
        if(num == divisor) {
          num = 0.0;
        }
        return num;
      }
      float floatFloor(float value){
        float num = floor(value + 0.00000000001);
        return num;
      }
      ${textures.map((_, i) => 'uniform sampler2D texture_' + i + ';').join('\n')}
      
      {decoder}
      {encoder}
      void main() {
       ${textures
        .map(
          (_, i) =>
            `vec4 color_${i} = texture(texture_${i}, vec2(floatMod(index, ${TILE_PIXEL.toFixed(1)}) / ${TILE_PIXEL.toFixed(
              1,
            )}, floatFloor(index / ${TILE_PIXEL.toFixed(1)}) / ${TILE_PIXEL.toFixed(1)}));`,
        )
        .join('\n')}
       float data_num = 0.0;
       ${textures
        .map(
          (_, i) =>
            `if(color_${i}.r != 0.0 || color_${i}.g != 0.0 || color_${i}.b != 0.0) {
           data_num += 1.0;
          }`,
        )
        .join('\n')}
        if(data_num > 0.0){
          rgbaData = encode((${textures.map((_, i) => `decode(color_${i})`).join(' + ')}) / data_num);
        }
       }`
      .replace(
        '{decoder}',
        decode.includes('return ')
          ? decode
          : `
       float decode(vec4 d) {
         return ${decode}
       }`,
      )
      .replace(
        '{encoder}',
        encode.includes('return ')
          ? encode
          : `
        vec4 encode(float v) {
          return ${encode}
        }`,
      ),
    varyings: ['rgbaData'],
    elementCount: TILE_PIXEL * TILE_PIXEL,
    sourceBuffers: { index },
    feedbackBuffers: {
      rgbaData,
    },
    modules: [project32],
  });
  const uniforms = textures.reduce((prev, curr, i) => {
    return {
      ...prev,
      ['texture_' + i]: curr,
    };
  }, {});

  float2rgbaTransformProgram.run({
    uniforms,
  });
  return {
    width: 256,
    height: 256,
    data: new Uint8ClampedArray(rgbaData.getData()),
  };
};
export function lngLatToTile(lng: number, lat: number, zoom: number) {
  const tileX = lng2tile(lng, zoom);
  const tileY = lat2tile(lat, zoom);
  return [tileX, tileY];
}
export function getTileRangeByLngLatRange(lngLatRange: number[], zoom: number = 7): [number, number, number, number] {
  const [minLng, minLat, maxLng, maxLat] = lngLatRange;
  const minTile = lngLatToTile(minLng, minLat, zoom);
  const maxTile = lngLatToTile(maxLng, maxLat, zoom);
  return [minTile[0], maxTile[1], maxTile[0], minTile[1]];
}
export function getTilesByTileRange(tileRange: [number, number, number, number]) {
  const [minTileX, minTileY, maxTileX, maxTileY] = tileRange;
  const tiles: [number, number][][] = [];
  for (let i = minTileY; i <= maxTileY; i++) {
    const row: [number, number][] = [];
    for (let j = minTileX; j <= maxTileX; j++) {
      row.push([j, i]);
    }
    tiles.push(row);
  }
  return tiles;
}

export const calculateBoundsFromWebMercatorTileRowCol = (x: number, y: number, z: number) => {
  const n = 2 ** z;
  const lon1 = (x / n) * 360 - 180;
  const lon2 = ((x + 1) / n) * 360 - 180;
  const lat1 = Math.atan(Math.sinh(Math.PI * (1 - (2 * y) / n))) * (180 / Math.PI);
  const lat2 = Math.atan(Math.sinh(Math.PI * (1 - (2 * (y + 1)) / n))) * (180 / Math.PI);
  return [lon1, lat1, lon2, lat2];
};
