import { PixelDecoder } from '@/utils/tiles';
import Geotiff from '../geotiff/geotiff';

export const coders = {
  pm25: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  pm10: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  no2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  hcho: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 1000) / 255),
      Math.floor((v * 1000) % 255),
      0,
      0,
    ],
    decoder: ([r, g, b, a]: number[]) =>
      (g * 255.0 * 255.0 * 255.0 + b * 255.0 * 255.0 + a * 255.0 + r) *
      100000000,
  },
  o3: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  so2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 1000) / 255),
      Math.floor((v * 1000) % 255),
      0,
      0,
    ],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 1000,
  },
  aod: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 1000) / 255),
      Math.floor((v * 1000) % 255),
      0,
      0,
    ],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 1000,
  },
  co: {
    format: 'double',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 10000) / 255),
      Math.floor((v * 10000) % 255),
      0,
      0,
    ],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 10000,
  },
  DCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
    decoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
  },
  TCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
    decoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
  },
};

export const stringCoders: Record<
  string,
  { format: string; bands: 1 | 2 | 3 | 4; encoder: string; decoder: string }
> = {
  pm25: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  pm10: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  no2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  o3: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  so2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  aod: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  co: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  DCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: `vec4 encode(vec4 value) {
      return value;
    }`,
    decoder: `vec4 decode(vec4 data) {
      return data * 255.0;
    }`,
  },
  F721: {
    format: 'byte',
    bands: 4,
    encoder: `vec4 encode(vec4 value) {
      return value;
    }`,
    decoder: `vec4 decode(vec4 data) {
      return data * 255.0;
    }`,
  },
  BLOOM: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  TCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: `vec4 encode(vec4 value) {
      return value;
    }`,
    decoder: `vec4 decode(vec4 data) {
      return data * 255.0;
    }`,
  },
  ch4: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  co2: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  uva: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  uvb: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  fnr: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  prs: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
  pre: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  rh: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  tmp: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  vis: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(value, 0.0, 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return data.r * 255.0;
    }`,
  },
  'wiu-wiv': {
    format: 'ushort',
    bands: 2,
    decoder: `vec2 decode(vec4 data) {
      return vec2(data.r * 255.0 + data.b * 255.0 / 10.0 / 10.0 - 100.0,
      data.g * 255.0 + mod(data.b * 255.0, 10.0)/10.0 - 100.0);
    }`,
    encoder: `vec4 encode(vec2 value) {
      float uMps = value.x + 100.0;
      float vMps = value.y + 100.0;
      float r = floor(uMps);
      float g = floor(vMps);
      float uFraction = uMps - r;
      float vFraction = vMps - g;
      float b = uFraction * 100.0 + vFraction * 10.0;
      float a = 0.0;

      return vec4(r, g, b, a);
    }`,
  },
  hcho: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(mod(value, 255.0), floor(value / 255.0 / 255.0 / 255.0), mod(floor(value / 255.0 / 255.0), 255.0),
       mod(floor(value / 255.0), 255.0));
    }`,
    decoder: `float decode(vec4 data) {
      return (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0;
    }`,
  },
  no2tcd: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(mod(value, 255.0), floor(value / 255.0 / 255.0 / 255.0), mod(floor(value / 255.0 / 255.0), 255.0),
       mod(floor(value / 255.0), 255.0));
    }`,
    decoder: `float decode(vec4 data) {
      return (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0;
    }`,
  },
  o3tcd: {
    format: 'float',
    bands: 1,
    encoder: `vec4 encode(float value) {
      return vec4(floor(value / 255.0), mod(value, 255.0), 0.0, 0.0);
    }`,
    decoder: `float decode(vec4 data) {
      return (data.r * 255.0 + data.g) * 255.0;
    }`,
  },
};
export function lng2tile(lng: number, zoom: number) {
  return Math.floor(((lng + 180) / 360) * Math.pow(2, zoom));
}
export function lat2tile(lat: number, zoom: number) {
  return Math.floor(
    ((1 -
      Math.log(
        Math.tan((lat * Math.PI) / 180) + 1 / Math.cos((lat * Math.PI) / 180),
      ) /
        Math.PI) /
      2) *
      Math.pow(2, zoom),
  );
}
export function lngLatToTile(lng: number, lat: number, zoom: number) {
  const tileX = lng2tile(lng, zoom);
  const tileY = lat2tile(lat, zoom);
  return [tileX, tileY];
}
// 已知经纬度范围，获取瓦片行列号
export function getTileRangeByLngLatRange(
  lngLatRange: number[],
  zoom: number = 7,
): [number, number, number, number] {
  const [minLng, minLat, maxLng, maxLat] = lngLatRange;
  const minTile = lngLatToTile(minLng, minLat, zoom);
  const maxTile = lngLatToTile(maxLng, maxLat, zoom);
  return [minTile[0], maxTile[1], maxTile[0], minTile[1]];
}
export function getTilesByTileRange(
  tileRange: [number, number, number, number],
) {
  const [minTileX, minTileY, maxTileX, maxTileY] = tileRange;
  const tiles: [number, number][][] = [];
  for (let i = minTileY; i <= maxTileY; i++) {
    const row: [number, number][] = [];
    for (let j = minTileX; j <= maxTileX; j++) {
      row.push([j, i]);
    }
    tiles.push(row);
  }
  return tiles;
}
export function tileToQuery(tile: [number, number], template: string) {
  const [x, y] = tile;
  const z = 7;
  return template
    .replace('{x}', x.toString())
    .replace('{y}', y.toString())
    .replace('{z}', z.toString());
}
export function mergeTilesToCanvas(
  tiles: HTMLImageElement[][],
  tileRange: [number, number, number, number],
  callback: (node: HTMLCanvasElement) => void,
  tileSize = 256,
) {
  // 扁平化二维数组，并获取长度
  const promises: Promise<any>[] = [];
  const [minTileX, minTileY, maxTileX, maxTileY] = tileRange;
  const width = (maxTileX - minTileX + 1) * tileSize;
  const height = (maxTileY - minTileY + 1) * tileSize;
  const canvasNode = document.createElement('canvas');
  canvasNode.width = width;
  canvasNode.height = height;
  const ctx = canvasNode.getContext('2d')!;
  const onTileLoaded = (tile: HTMLImageElement) => {
    return new Promise((resolve) => {
      // eslint-disable-next-line no-param-reassign
      tile.onload = () => {
        resolve(tile);
      };
    });
  };
  tiles.forEach((row, i) => {
    row.forEach((tile, j) => {
      // setReset((p) => (p > 0 ? p - 1 : 0));
      // ctx.drawImage(tile, j * tileSize, i * tileSize);
      promises.push(
        onTileLoaded(tile).then(() => {
          ctx.drawImage(tile, j * tileSize, i * tileSize);
        }),
      );
    });
  });

  Promise.all(promises).then(() => {
    callback(canvasNode);
  });
}
const FILTER_PIXELS = 'FILTER_PIXELS';
interface Params {
  bounds: number[];
  decoder?: PixelDecoder;
  tileUrlTemplate: string;
}
export const generateTileCanvas = (params: Params) => {
  const { bounds, tileUrlTemplate } = params;

  const range = getTileRangeByLngLatRange(bounds, 7);
  const arr = getTilesByTileRange(range);
  const getImages = () => {
    return arr.map((item) => {
      return item.map((v) => {
        const image = new Image();
        image.width = 256;
        image.height = 256;
        image.crossOrigin = 'anonymous';
        image.src = tileToQuery(v, tileUrlTemplate);
        return image;
      });
    });
  };
  return new Promise((resolve) => {
    mergeTilesToCanvas(getImages(), range, (canvasNode) => {
      resolve(canvasNode);
    });
  });
};
export function lngLatToPixel(
  lng: number,
  lat: number,
  zoom: number,
): [number, number] {
  const tileX = lng2tile(lng, zoom);
  const tileY = lat2tile(lat, zoom);
  const pixelX = Math.floor(
    ((lng + 180) / 360) * 256 * Math.pow(2, zoom) - tileX * 256,
  );
  const pixelY = Math.floor(
    ((1 -
      Math.log(
        Math.tan((lat * Math.PI) / 180) + 1 / Math.cos((lat * Math.PI) / 180),
      ) /
        Math.PI) /
      2) *
      256 *
      Math.pow(2, zoom) -
      tileY * 256,
  );
  return [pixelX, pixelY];
}
export const downloadTileToTiff = (
  bounds: number[],
  canvasNode: HTMLCanvasElement,
  decoder: any,
  type: string,
  map?: {
    maxLat: number;
    minLat: number;
    maxLng: number;
    minLng: number;
    url: string;
  },
) => {
  const ctx = canvasNode.getContext('2d');
  const image = ctx?.getImageData(0, 0, canvasNode.width, canvasNode.height);
  const worker = new Worker('/worker/index.js');
  return new Promise((resolve) => {
    worker.postMessage({ image, type: FILTER_PIXELS });
    worker.onmessage = (e: MessageEvent<any>) => {
      if (e.data.type === FILTER_PIXELS) {
        ctx?.putImageData(e.data.image, 0, 0);
        console.timeEnd();
        const geoTiff = new Geotiff(e.data.image, coders[type]?.bands);
        geoTiff.setPixelSize(0.01);
        geoTiff.setLeftTopTiePoint(bounds[0], bounds[3]);
        const img = new Image();
        img.src = map?.url ?? '';
        img.setAttribute('crossOrigin', 'anonymous');
        img.onload = () => {
          const pos = lngLatToPixel(bounds[0], bounds[3], 7);
          const endPos = lngLatToPixel(bounds[2], bounds[1], 7);
          const maskTexture = document.createElement('canvas');
          maskTexture.width = canvasNode.width;
          maskTexture.height = canvasNode.height;
          const maskCtx = maskTexture.getContext('2d');
          maskCtx?.drawImage(
            img,
            pos[0],
            pos[1],
            canvasNode.width - 256 + endPos[0] - pos[0],
            canvasNode.height - 256 + endPos[1] - pos[1],
          );
          const blob = geoTiff.toBlob(
            decoder,
            coders[type]?.format,
            maskCtx?.getImageData(
              0,
              0,
              maskTexture.width,
              maskTexture.height,
            ) as ImageData,
          );
          resolve(blob);
        };
      }
    };
  });
};
