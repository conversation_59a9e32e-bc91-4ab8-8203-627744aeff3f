import type { TableColumnsType } from 'antd';
import type { MetaData } from '../types';
import moment from 'moment';

export const columns: TableColumnsType<MetaData> = [
  {
    title: '数据时间',
    dataIndex: 'timePoints',
    render(text) {
      return moment(text).format('YYYY年MM月DD日 HH时mm分');
    },
  },
  {
    title: '数据大小',
    dataIndex: 'size',
    render(text) {
      return `${Math.round(text / 1000 / 1000)}M`;
    },
  },
];
