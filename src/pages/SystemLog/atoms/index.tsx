import { atom } from 'jotai';
import type { FetchOperationLog } from '../services';
import type { User } from '../types';

// 获取用户列表参数
export const fetchOperationLogParamsAtom = atom<FetchOperationLog>({
  operator: '',
  page: 0,
  size: 10,
  moduleId: undefined
});

// 待编辑用户
export const currentUserAtom = atom<User | null>(null);

// 创建、编辑用户，批量编辑弹窗显示状态
export const visibleAtom = atom({
  userForm: false,
  batchEdit: false,
});

export const selectedRowKeysAtom = atom<React.Key[]>([])