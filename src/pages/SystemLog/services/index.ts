import { stringify } from 'qs';
import { request } from '@/utils';
import { omitBy } from 'lodash';

export interface FetchOperationLog {
  operator: string;
  page: number;
  size: number;
  moduleId?: number;
  typeId?: number;
  startDate?: string;
  endDate?: string;
}
export interface IOperationLog {
  id: number;
  operator: string;
  module: string;
  type: string;
  target: string;
  content: string;
  dateTime: string;
}
// 操作日志列表
export const fetchOperationLog = (params: FetchOperationLog) => {
  return request(
    `/api/operation/log/list?${stringify(omitBy(params, (item) => !item))}`
  );
};

// 操作模型列表
export const fetchOperationModuleList = () => {
  return request(`/api/operation/log/module/list`);
};

// 操作类型列表
export const fetchOperationTypeList = () => {
  return request(`/api/operation/log/type/list`);
};

// 操作日志导出
export const exportOperationLog = (ids: React.Key[]) => {
  return request(`/api/operation/log/export`, {
    method: 'POST',
    body: JSON.stringify({ ids }),
  });
};
