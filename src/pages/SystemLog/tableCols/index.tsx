
import type { ColumnType } from 'antd/lib/table';
import type { IOperationLog } from '../services';
export const userTableColumns: () => ColumnType<IOperationLog>[] =
  () => [
    {
      title: '序号',
      render(_text, _record, index) {
        return `${index + 1}`;
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
    },
    {
      title: '操作模块',
      dataIndex: 'module',
    },
    {
      title: '操作类型',
      dataIndex: 'type',
    },
    {
      title: '操作对象',
      dataIndex: 'target',
    },
    {
      title: '操作详情',
      dataIndex: 'content',
    },
    {
      title: '操作时间',
      dataIndex: 'dateTime',
    },
  ];
