import { useMutation, useQuery } from 'react-query';
import usePublic from './usePublic';
import {
  exportOperationLog,
  fetchOperationModuleList,
  fetchOperationTypeList,
} from '../services';
import { useAtomValue } from 'jotai';
import { selectedRowKeysAtom } from '../atoms';
import { exportFile } from '@/utils';

const useFilter = () => {
  const {
    fetchOperationLogParams: fetchParams,
    handleSetFetchOperationLogParams: handleSetFetchParams,
  } = usePublic();
  const selectedRowKeys = useAtomValue(selectedRowKeysAtom);
  const { data: moduleOptions } = useQuery(['get-operation-module-list'], () =>
    fetchOperationModuleList()
  );
  const { data: typeOptions } = useQuery(['get-operation-type-list'], () =>
    fetchOperationTypeList()
  );
  const { mutate } = useMutation(() => exportOperationLog(selectedRowKeys), {
    onSuccess: (d) => {
      exportFile(d, `操作日志`);
    },
  });
  return {
    moduleOptions,
    typeOptions,
    fetchParams,
    handleSetFetchParams,
    mutate,
  };
};

export default useFilter;
