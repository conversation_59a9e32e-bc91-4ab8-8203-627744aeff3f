import { useQuery } from 'react-query';
import type { ApiListData } from '@/utils/types';
import type { TablePaginationConfig } from 'antd';

import { useMemo } from 'react';
import usePublic from './usePublic';
import type { IOperationLog } from '../services';
import { fetchOperationLog } from '../services';
import { userTableColumns } from '../tableCols';
import { useAtom } from 'jotai';
import { selectedRowKeysAtom } from '../atoms';

const Table = () => {
  const {
    fetchOperationLogParams,
    handleSetFetchOperationLogParams,
  } = usePublic();
  const [selectedRowKeys, setSelectedRowKeys] = useAtom(selectedRowKeysAtom);
  // 根据 fetchUsersParams 调取数据
  const { data, isLoading } = useQuery<ApiListData<IOperationLog>>(
    ['fetch-operation-log-list', fetchOperationLogParams],
    () => fetchOperationLog(fetchOperationLogParams)
  );
  // 根据 data 计算出 pagination
  const pagination: TablePaginationConfig = useMemo(
    () => ({
      current: (data?.pageable.pageNumber || 0) + 1,
      total: data?.totalElements,
      pageSize: fetchOperationLogParams?.size,
      size: 'small',
      position: ['bottomCenter'],
      style: {
        marginTop: 40,
      },
    }),
    [data?.pageable.pageNumber, data?.totalElements, fetchOperationLogParams?.size]
  );
  const columns = userTableColumns();

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onSelect(record: IOperationLog, selected: any) {
        if (selected) {
          setSelectedRowKeys((prev) => [...prev, record.id]);
        } else {
          setSelectedRowKeys((prev) =>
            prev.filter((item) => item !== record.id)
          );
        }
      },
      onSelectAll(selected: any, selectedRows: any, changeRows: any) {
        if (selected) {
          setSelectedRowKeys((prev) => [
            ...prev,
            ...changeRows.map((item: any) => item.id),
          ]);
        } else {
          setSelectedRowKeys((prev) =>
            prev.filter(
              (item) =>
                !changeRows.map((rowItem: any) => rowItem.id).includes(item)
            )
          );
        }
      },
    }),
    [selectedRowKeys, setSelectedRowKeys]
  );

  return {
    data,
    isLoading,
    pagination,
    columns,
    rowSelection,
    handleSetFetchOperationLogParams,
  };
};

export default Table;
