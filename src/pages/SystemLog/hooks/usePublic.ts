import { useAtom } from 'jotai';
import { fetchOperationLogParamsAtom } from '../atoms';
import { useCallback } from 'react';
import { useQueryClient } from 'react-query';
import * as services from '../services';

interface IMergeFetchUsersParams {
  operator?: string;
  page?: number;
  size?: number;
  moduleId?: number;
  typeId?: number;
  startDate?: string;
  endDate?: string;
}

const usePublic = () => {
  const queryClient = useQueryClient();
  const [fetchOperationLogParams, setFetchOperationLogParams] = useAtom(
    fetchOperationLogParamsAtom
  );

  const handleSetFetchOperationLogParams = useCallback(
    (obj: IMergeFetchUsersParams) => {
      setFetchOperationLogParams((prev) => ({ ...prev, ...obj }));
    },
    [setFetchOperationLogParams]
  );

  return {
    services,
    queryClient,
    fetchOperationLogParams,
    handleSetFetchOperationLogParams,
  };
};

export default usePublic;
