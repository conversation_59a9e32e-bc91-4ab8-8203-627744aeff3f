import { OutlinedButton } from '@/components/ui';
import { SearchOutlined } from '@ant-design/icons';
import { Input, Col, Select, DatePicker, message, DatePickerProps } from 'antd';
import { FilterBtnsContainer, FilterContainer } from './ui';
import useFilter from '../hooks/useFilter';
import moment from 'moment';
import { selectedRowKeysAtom } from '../atoms';
import { useAtomValue } from 'jotai';
import dayjs from 'dayjs';


export const disableNewsDates: DatePickerProps['disabledDate'] = (current) => {
  // 禁用未来日期
  if (current.isAfter(dayjs().endOf('day'))) return true;
  return false;
}

const { RangePicker } = DatePicker;
const Filter = () => {
  // 界面所需数据
  const {
    moduleOptions,
    typeOptions,
    fetchParams,
    handleSetFetchParams,
    mutate
  } = useFilter();
  // const userInfo = useAtomValue(userInfoAtom);
  // const createBtnVisible = useMemo(() => {
  //   return userInfo?.permissions.includes('1005');
  // }, [userInfo?.permissions]);
  const selectedRowKeys = useAtomValue(selectedRowKeysAtom);
  return (
    <FilterContainer gutter={12}>
      <Col>
        <Input
          allowClear
          prefix={<SearchOutlined />}
          placeholder="请输入操作人"
          value={fetchParams.operator}
          onChange={(e) => {
            handleSetFetchParams({
              operator: e.target.value,
              page: 0,
            });
          }}
        />
      </Col>
      <Col>
        <Select
          allowClear
          placeholder="请选择操作模块"
          value={fetchParams.moduleId}
          style={{ width: 160 }}
          fieldNames={{
            label: 'name',
            value: 'id'
          }}
          onChange={(val) => {
            handleSetFetchParams(({
              moduleId: val,
              page: 0,
            }));
          }}
          options={moduleOptions}
        />
      </Col>
      <Col>
        <Select
          allowClear
          placeholder="请选择操作类型"
          value={fetchParams.typeId}
          style={{ width: 160 }}
          fieldNames={{
            label: 'name',
            value: 'id'
          }}
          onChange={(val) => {
            handleSetFetchParams(({
              typeId: val,
              page: 0,
            }));
          }}
          options={typeOptions}
        />
      </Col>
      <Col>
        <RangePicker
          className="w-[280px]"
          value={[
            // @ts-ignore
            fetchParams.startDate && dayjs(fetchParams.startDate),
            // @ts-ignore
            fetchParams.endDate && dayjs(fetchParams.endDate)
          ]}
          format="YYYY/MM/DD"
          onChange={(_dates, dateStrings) => {
            handleSetFetchParams({
              startDate: dateStrings[0],
              endDate: dateStrings[1],
              page: 0,
            });
          }}
          disabledDate={disableNewsDates}

        /></Col>
      <FilterBtnsContainer flex="1">
        <OutlinedButton onClick={() => {
          if (selectedRowKeys.length > 0) {
            mutate()
            return
          } else {
            message.error('至少选择一条系统日志')
            return;
          }
        }}>
          导出
        </OutlinedButton>
      </FilterBtnsContainer>
    </FilterContainer >
  );
};
export default Filter;
