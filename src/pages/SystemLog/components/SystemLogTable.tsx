import { Table } from 'antd';
import useTable from '../hooks/useTable';

const SystemLogTable = () => {
  const { data, isLoading, pagination, columns, handleSetFetchOperationLogParams, rowSelection } =
    useTable();

  return (
    <div>
      <Table
        rowKey="id"
        loading={isLoading}
        rowSelection={rowSelection}
        columns={columns}
        dataSource={data?.content}
        pagination={pagination}
        onChange={({ current ,pageSize}) => {
          handleSetFetchOperationLogParams({ page: (current || 1) - 1,size: pageSize ?? 10});
        }}
      />
    </div>
  );
};

export default SystemLogTable;
