import styled from 'styled-components';
import { Col, Row, Form, Input } from 'antd';
import { OutlinedButton, StyledButton } from '@/components/ui';

export const FilterContainer = styled(Row)`
  margin: 40px 0 20px;
`;

export const FilterBtnsContainer = styled(Col)`
  text-align: right;

  ${OutlinedButton} {
    margin-left: 12px;
  }
`;

export const ModalContent = styled.div`
  padding: 20px 30px;
`;

export const ModalTitle = styled.h3`
  font-size: 16px;
  color: white;
`;

export const ModalBody = styled.div`
  margin: 30px 0 0;
`;
export const ModalFooter = styled.div`
  text-align: right;

  ${StyledButton} {
    margin-left: 20px;
  }
`;

export const GrayTextFormItem = styled(Form.Item)`
  .ant-form-item-label > label {
    color: #c1c1c4;
  }
`;

export const DarkInput = styled(Input)`
  font-size: 20px;
  background: #1c1d24 !important;
  border-color: #1c1d24 !important;
  color: white;
`;
