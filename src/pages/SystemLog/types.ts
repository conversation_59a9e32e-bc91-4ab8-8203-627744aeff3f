export interface User {
  enabled: boolean;
  id: number;
  lastLoginTime: string;
  name: string;
  permissions: string[];
  phoneNumber: string;
  region: string;
  regionCode: number;
  role: number;
  token: null | string;
  totalLoginTimes: number;
}

export interface UserFormData {
  name: string;
  permissions: string[];
  phoneNumber: string;
  regionCode: number;
}

export interface EditUserData extends UserFormData {
  id: number;
}
