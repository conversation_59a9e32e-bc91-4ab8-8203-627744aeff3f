
import { useRouter } from '@/hooks';
import { getDustEventInfo } from '@/pages/DustEvent/services';
import { formatAreaNumber, formatAreaUnit } from '@/utils/dust/formatArea';
import moment from 'moment';
import { useQuery } from 'react-query';

const DetailInfo = () => {
  const { query } = useRouter();
  const { data } = useQuery(
    ['getDustEventInfo', query.id],
    () => getDustEventInfo({ dustEventId: query.id }),
    { enabled: Boolean(query.id) },
  );

  return (
    <>
      <div
        className="w-[185px] h-[45px] mt-[20px] ml-[20px]"
        style={{
          background: "url('/assets/images/dust-detail/detailIdBg.png')",
        }}
      >
        <span className="mt-[4px] ml-[20px] text-[#ffffff] h-[25px] w-max font-semibold text-lg leading-[25px] block">
          {data?.code}
        </span>
      </div>
      <div className="w-[400px] h-max rounded-[16px] bg-[#ffffff] ml-[20px] mt-[-15px] flex flex-col">
        <div className="flex ml-[17px] mr-[16px] mt-[19px] h-[20px] justify-between">
          <div className="text-sm font-medium text-[#1E315E] leading-[20px]">
            {moment(data?.startTime).format('YYYY/MM/DD HH:mm')}-
            {moment(data?.endTime).format('YYYY/MM/DD HH:mm')}
          </div>
          <div className="text-sm font-medium text-[#1E315E] leading-[20px]">
            持续
            {moment(data?.endTime).diff(moment(data?.startTime), 'minute') / 60}
            小时
          </div>
        </div>
        <div className="flex ml-[17px] mt-[10px] h-[20px]">
          <div className="text-sm font-medium text-[#821C1C] leading-[20px]">
            沙尘影响面积
          </div>
          <div className="text-sm font-normal text-[#F44436] leading-[20px] ml-[22px]">
            {formatAreaNumber(data?.area) + formatAreaUnit(data?.area)}k㎡
          </div>
        </div>
        <div className="ml-[17px] w-[365px] border-t-[1px] border-dashed border-[#D1DEE7] mt-[10px]"></div>

        {(data?.sources?.length || -1) > 0 && (
          <>
            <div className="ml-[17px] mt-[10px] text-[#25265E] text-sm font-medium h-[20px] leading-[20px]">
              沙源地
            </div>
            <div className="px-[18px] flex gap-[8px] my-[8px] flex-wrap">
              {data?.sources?.map((sources) => (
                <div
                  key={sources.key}
                  className="h-[24px] px-[6px] bg-[#ECF2F6] rounded-[4px] text-sm leading-[24px] font-normal"
                >
                  {sources.value}
                </div>
              ))}
            </div>
          </>
        )}
        <div className="ml-[17px] mt-[4px] text-[#25265E] text-sm font-medium h-[20px] leading-[20px]">
          起沙地区
        </div>
        <div className="px-[18px] flex gap-[8px] mt-[8px] mb-[16px] flex-wrap">
          {((data?.sendSites?.length || -1) > 0 &&
            data?.sendSites.map((sendSite) => (
              <div
                key={sendSite.key}
                className="h-[24px] px-[6px] bg-[#ECF2F6] rounded-[4px] text-sm leading-[24px] font-normal"
              >
                {sendSite.value}
              </div>
            ))) || (
              <div className="text-[#25265E] text-xs font-medium h-[24px] leading-[24px] font-normal">
                未检测到起沙地区
              </div>
            )}
        </div>
      </div>
    </>
  );
};
export default DetailInfo;
