
import Chart from '@/components/Chart';
import { useRouter } from '@/hooks';
import { ConfigProvider, Switch } from 'antd';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import useProviceChartOptions from '../hooks/useProviceChartOptions';
import { formatAreaNumber, formatAreaUnit } from '@/utils/dust/formatArea';
import { buildColorRamp } from '@/pages/DustAnalyze/utile';
import { getDustRecordDateRegions } from '@/components/DustDetail/services';
import { getDustEventRegionList } from '@/pages/DustEvent/services';
import { EChartsResponsiveOption } from 'echarts';

const colorRamp = {
  '0.0': '#7E0023',
  '0.14': '#B5004A',
  '0.28': '#F44336',
  '0.43': '#FF7E00',
  '0.57': '#FFC719',
  '0.71': '#61DDAA',
  '0.86': '#4951EE',
  '1.00': '#2D1E6E',
};
const ProviceChart = () => {
  const colorData = buildColorRamp(colorRamp);
  const [legends, setLegends] = useState<
    {
      regionCode: number;
      label: string;
      color: string;
      check: boolean;
      area: number;
    }[]
  >([]);
  const { query } = useRouter();

  const { data } = useQuery(
    ['getDustRecordDateRegions', query.id],
    () => getDustRecordDateRegions({ dustEventId: query.id }),
    {
      enabled: Boolean(query.id),
      cacheTime: 0,
      staleTime: 0,
      onSuccess: (d) => {
        const regionList = d.reduce((prev, curr) => {
          const res = prev;
          curr.dataList.forEach((info) => {
            if (prev.map((p) => p.regionCode).includes(info.regionCode)) {
              const index = res.findIndex(
                (region) => region.regionCode === info.regionCode,
              );
              res[index] = {
                ...res[index],
                area: res[index].area + info.area,
              };
            } else {
              res.push(info);
            }
          });
          return res;
        }, [] as any[]);
        setLegends(
          regionList.map((region, index) => ({
            ...region,
            label: region.region,
            check: true,
            color: [
              colorData[Math.floor((index / regionList.length) * 256) * 4],
              colorData[Math.floor((index / regionList.length) * 256) * 4 + 1],
              colorData[Math.floor((index / regionList.length) * 256) * 4 + 2],
              colorData[Math.floor((index / regionList.length) * 256) * 4 + 3],
            ].reduce((prev, curr) => {
              const val = curr.toString(16);
              return prev + (val.length < 2 ? '0' + val : val);
            }, '#'),
          })),
        );
      },
    },
  );
  const option = useProviceChartOptions(legends, data);
  const { data: legendList } = useQuery(['getDustEventRegionList', query.id], () => getDustEventRegionList({ dustEventId: query.id }))
  const legendsDom = useMemo(() => {
    return legendList?.map((l) => {
      const legend = legends.find((legend) => legend.regionCode === l.regionCode)
      return (
        <div
          className="w-[130px] h-[80px] rounded-[10px] bg-[#ffffff] flex flex-col"
          key={legend?.label}
        >
          <ConfigProvider
            theme={{
              components: {
                Switch: {
                  handleBg: legend?.color,
                },
              },
            }}
          >
            <Switch
              className="mt-[6px] ml-[10px] ant-switch-xs"
              checked={legend?.check}
              value={legend?.check}
              style={{
                background:
                  (legend?.check && legend?.color.slice(0, 7) + '2C') ||
                  'rgba(0, 0, 0, 0.25)',
              }}
              onChange={(e) => {
                setLegends((prev) => {
                  const res = prev;
                  const index = legends.findIndex((legend) => legend.regionCode === l.regionCode)
                  res[index].check = e;
                  return res.concat();
                });
              }}
            />
          </ConfigProvider>
          <div className="mx-[10px] h-[28px] mt-[3px] font-normal text-xs text-[#1E315E] flex items-center">
            {legend?.label}
          </div>
          <div className="flex h-[17px] px-[11px] items-center gap-[4px] mt-[4px]">
            <div className="text-[#F44436] font-normal">
              {formatAreaNumber(l.area)}
            </div>
            <div className="text-[#F44436] font-semibold text-xs">
              {formatAreaUnit(l.area)}k㎡
            </div>
          </div>
        </div>
      )
    });
  }, [legendList, legends]);
  return (
    <>
      <div className="w-full flex flex-col">
        <div className="flex w-full mt-[10px] pl-[24px] pr-[22px] items-center">
          <div className="w-[3px] h-[16px] bg-[#286CFF]" />
          <div className="font-semibold text-[#091625] h-[22px] leading-[22px] flex-1 ml-[8px]">
            影响省份
          </div>
          <div className="font-normal text-sm text-[#091625] h-[20px] leading-[20px]">
            共影响{legends.length}省份
          </div>
        </div>
        <div className="mt-[12px] max-h-[166px] w-[410px] ml-[20px] overflow-auto scrollbar flex flex-wrap gap-[4px]">
          {legendsDom}
        </div>
      </div>
      <div className="w-[400px] h-[270px] bg-[#ffffff] rounded-[16px] mx-[20px] mt-[6px] relative mb-[10px]">
        <div className="absolute left-[13px] top-[16px] text-xs text-[#666666]">
          单位：km²
        </div>
        <Chart mixConfig={option as EChartsResponsiveOption} />
      </div>
    </>
  );
};
export default ProviceChart;
