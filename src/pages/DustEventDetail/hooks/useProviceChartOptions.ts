import { formatAreaNumber, formatAreaUnit } from '@/utils/dust/formatArea';
import moment from 'moment';
import { useMemo } from 'react';

const useProviceChartOptions = (
  legend: {
    label: string;
    color: string;
    check: boolean;
    area: number;
  }[],
  data: APIDUST.TDateLabelsModelDustRecordRegionInfoModel[]
) => {
  const captions = data?.map((d) => moment(d.date).format('HH:mm'));
  const valueList = legend
    .filter((l) => l.check)
    .map((l) => ({
      value: data?.map(
        (d) =>
          Number(
            d.dataList.find((v) => v.region === l.label)?.area.toFixed(1)
          ) || null
      ),
      color: l.color,
      label: l.label,
    }));
  const axisLabel = useMemo(() => {
    return {
      fontSize: 12,
      color: 'rgba(51, 51, 51, 1)',
    };
  }, [])

  const option = useMemo(
    () => ({
      tooltip: {
        confine: true,// 限制在画布内显示
        trigger: 'axis',
        axisPointer: {
          snap: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(209, 222, 231, 1)', // 线的颜色
            width: 1, // 线宽
            type: 'solid', // 线型
          },
        },
        borderWidth: 0,
        backgroundColor: 'rgba(25, 34, 60, 0.7)',

        formatter: (d) => {
          return `<div style="display: flex;flex-direction: column;">
            <div style="color: #FFFFFF;">${data[d[0].dataIndex].date}</div>
            ${d
              .map(
                (
                  value
                ) => `<div style="height: 17px;display: flex;padding: 0, 4px;margin-top: 4px;color: #FFFFFF;display: flex;align-items: center;justify-content:space-between;">
                        <div style="width: 75px;display: flex;align-items: center;">
                          <div style="width: 6px;height: 6px;background: ${value.color
                  };border-radius: 3px;"></div>
                          <div style="margin-left: 4px;width: 64px">${value.seriesName
                  }:</div>
                        </div>
                        <div style="text-align:right; margin-left:${value.seriesName.length>6?'70px':value.seriesName.length>5?'40px':'20px'}">${((value.value && formatAreaNumber(value.value)) ||
                    '-') +
                  ((value.value && formatAreaUnit(value.value)) || '')
                  }km²</div>
                      </div>`
              )
              .join('')}
          </div>`;
        },
      },
      dataZoom: [
        {
          type: 'slider', // 在底部
          show: captions?.length > 9 ? true : false, // 是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
          start: 0, // 数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
          end: 100, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
          /**
            不指定时，当 dataZoom-slider.orient 为 'horizontal'时，
            默认控制和 dataZoom 平行的第一个 xAxis。但是不建议使用默认值，建议显式指定。
            如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。
            */
          xAxisIndex: [0, 1],
          handleIcon:
            'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAJRJREFUKFNjZEAC6o5z1f8y/F/BwMCoz8DIwAiVus3AyBB/Z2/ycZhSmASYr+I4dw0Dw/+jd/anTGBgYPgPFnOeF/L///+yu/uSzbBrcpp7gomBJe/WvvhTMAUKDvMlWJj+nb+zL1kSr6b/jH9Lb+9NClVxmrv6zz+m3FFNpAQEiUE+bzXD/3/HSIpcspIRcjrExwYAA9abDhve7g8AAAAASUVORK5CYII=',
          handleSize: '100%',
          moveHandleSize: 12,
          brushSelect: false,
          height: 12,
          bottom: 10,
          brushStyle: {
            borderColor: '#1E3C6A',
          },
          handleStyle: {
            color: '#3AAAF0',
            borderColor: '#007acc',
          },
          labelFormatter: '',
          fillerColor: 'rgba(40, 108, 255, 0.10)',
          backgroundColor: 'rgba(247, 247, 249, 1)',
          showDetail: false,
          selectedDataBackground: {
            areaStyle: {
              opacity: 0,
            },
            lineStyle: {
              opacity: 0,
            },
          },
          left: 20,
          right: 20,
          borderColor: '#ffffff',
          borderRadius: 2,
          dataBackground: {
            areaStyle: {
              opacity: 0,
            },
            lineStyle: {
              opacity: 0,
            },
          },
        },
      ],
      grid: {
        left: 15,
        bottom: 28,
        containLabel: true,
        right: 21,
        top: 42,
      },
      xAxis: {
        type: 'category',
        data: captions,
        axisTick: {
          show: false, //隐藏X轴刻度
        },
        axisLabel,
      },
      yAxis: {
        // type: 'value',
        // name: '单位：k㎡',
        // nameTextStyle: {
        //   fontSize: 12,
        //   color: 'rgba(102, 102, 102, 1)',
        //   align: 'right',
        //   containLabel: true,
        //   padding: [0, valueList.length > 0 ? -16 : 0, 0, 0],
        // },
        axisLabel,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(209, 222, 231, 1)',
            type: 'dashed',
          },
        },
      },
      series: valueList.map((values) => ({
        type: 'line',
        data: values.value.map((v) => (v === null ? 0 : v)),
        color: values.color,
        lineStyle: {
          color: values.color,
        },
        name: values.label,
        showSymbol: values.value.length <= 1,
      })),
    }),
    [axisLabel, captions, data, valueList]
  );
  return option;
};
export default useProviceChartOptions;
