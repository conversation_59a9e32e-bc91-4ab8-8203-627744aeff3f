import {
  isAnnotationVisible<PERSON>tom,
  isCloudLayerVisibleAtom,
  isDustRangeLayerVisibleAtom,
  isSandSourceLayerVisibleAtom,
  isSphereOfInfluenceAtom,
  isWindLayerVisibleAtom,
  stationTypeAtom,
  textureTypeAtom,
} from '@/components/DustDetail/atoms';
import Bottombar from '@/components/DustDetail/components/Bottombar';
import DataTypeGroups from '@/components/DustDetail/components/DataTypeGroups';
import DownloadModal from '@/components/DustDetail/components/DownloadModal';
import Map from '@/components/DustDetail/components/Map';
// import Right from '@/components/DustDetail/components/Right';
import SubscribeModal from '@/components/DustDetail/components/SubscribeModal';
import TimelineWithRegion from '@/components/DustDetail/components/TimelineWithRegion';
import { useRouter } from '@/hooks';
import { useSetAtom } from 'jotai';
import { useEffect } from 'react';
import DetailInfo from './components/DetailInfo';
import ProviceChart from './components/ProviceChart';

const DustEventDetail = () => {
  const { replace } = useRouter();
  const setIsAnnotationVisible = useSetAtom(isAnnotationVisibleAtom);
  const setIsSphereOfInfluence = useSetAtom(isSphereOfInfluenceAtom);
  const setIsWindLayerVisible = useSetAtom(isWindLayerVisibleAtom);
  const setIsCloudLayerVisible = useSetAtom(isCloudLayerVisibleAtom);
  const setIsDustRangeLayerVisible = useSetAtom(isDustRangeLayerVisibleAtom);
  const setIsSandSourceLayerVisible = useSetAtom(isSandSourceLayerVisibleAtom);
  const setTextureType = useSetAtom(textureTypeAtom);
  const setStationType = useSetAtom(stationTypeAtom);
  useEffect(() => {
    return () => {
      setIsAnnotationVisible(false);
      setIsSphereOfInfluence(true);
      setIsWindLayerVisible(false);
      setIsCloudLayerVisible(false);
      setIsDustRangeLayerVisible(true);
      setIsSandSourceLayerVisible(false);
      setTextureType('DCOLOR');
      setStationType(undefined);
    };
  }, [
    setIsAnnotationVisible,
    setIsCloudLayerVisible,
    setIsDustRangeLayerVisible,
    setIsSandSourceLayerVisible,
    setIsSphereOfInfluence,
    setIsWindLayerVisible,
    setStationType,
    setTextureType,
  ]);
  return (
    <div className="w-full h-full select-none cursor-default min-w-[1350px]">
      <div className="flex w-full h-full">
        <div className="w-[440px] h-full bg-[#F0F2F6] overflow-y-auto overflow-x-hidden scrollbar">
          <div className="w-full min-h-full flex flex-col">
            <div className="w-full flex px-[27px] mt-[20px] items-center gap-[8px]">
              <img
                className="cursor-pointer"
                src="/assets/images/dust-detail/back.png"
                alt=""
                onClick={() => replace('/dust-event')}
              />
              <div className="flex h-[22px] font-semibold text-[#091625]">
                返回沙尘事件列表
              </div>
            </div>
            <DetailInfo />
            <ProviceChart />
          </div>
        </div>
        <div className="flex-1 h-full relative">
          <DataTypeGroups />
          <TimelineWithRegion />
          <Bottombar />
          {/* <Right /> */}
          <Map />
          <SubscribeModal />
          <DownloadModal />
        </div>
      </div>
    </div>
  );
};
export default DustEventDetail;
