import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { PageMain} from '@/components/ui';
import { PageContainer } from '../ComprehensiveAssessment/components/ui';
import FilterDate from './components/FilterDate';
import Left from './components/Left';
import Right from './components/Right';

const DustAnalyze = () => {
  return (
    <PageMain className='bg-[#F7F7F9]'>
      <HelmetTitle title="沙尘评估" />
      <PageHead title="沙尘评估" type="between" style={{ marginLeft: '20px' }}>
        <FilterDate />
      </PageHead>
      <PageContainer
      >
       <div className="flex w-full min-w-[1350px] min-h-[800px]  h-full gap-[10px]">
            <Left />
            <Right />
          </div>
      </PageContainer>
    </PageMain>
  );
};
export default DustAnalyze;