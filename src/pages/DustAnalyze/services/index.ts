import { request } from '@/utils';
import { stringify } from 'qs';

export const APIBASEDUST = ''
/** 获取起沙地统计 右边饼图 */ 
export const getDustStatsSendSite = (params: APIDUST.TGetDustStatsSendSiteParams ) => {
  return request(`/api/dust/stats/send/site?${stringify(params)}`) as Promise<APIDUST.TDustStatsSendSiteModel>
}

/** 获取行政区沙尘天数统计 行政区域沙尘天排名 */ 
export const getDustStatsRegionCount = (params: APIDUST.TGetDustStatsRegionCountParams ) => {
  return request(`/api/dust/stats/region/count?${stringify(params)}`) as Promise<APIDUST.TDustStatsRegionCountModel[]>
}

/** 获取沙尘事件总数统计 */ 
export const getDustStatsEventTotal = (params: APIDUST.TGetDustStatsEventTotalParams ) => {
  return request(`/api/dust/stats/event/total?${stringify(params)}`) as Promise<APIDUST.TDustStatsEventTotalModel>
}

/** 获取沙尘事件行政区面积统计 */ 
export const getDustStatsEventRegionArea = (params: APIDUST.TGetDustStatsEventRegionAreaParams ) => {
  return request(`/api/dust/stats/event/region/area?${stringify(params)}`) as Promise<APIDUST.TDustStatsEventRegionModel[]>
}

/** 获取沙尘事件持续时间统计 */ 
export const getDustStatsEventDuration = (params: APIDUST.TGetDustStatsEventDurationParams ) => {
  return request(`/api/dust/stats/event/duration?${stringify(params)}`) as Promise<APIDUST.TDustStatsEventDurationModel>
}

/** 获取沙尘天数统计 日历那块*/ 
export const getDustStatsDateCount = (params: APIDUST.TGetDustStatsDateCountParams ) => {
  return request(`/api/dust/stats/date/count?${stringify(params)}`) as Promise<APIDUST.TDustStatsDateCountModule[]>
}
// 纹理图管理模块

 /** 纹理瓦片token */ 
 export const getTextureToken = (params: APIDUST.TGetTextureTokenParams ) => {
  return request(`/api/texture/token?${stringify(params)}`) as Promise<APIDUST.TTileTokenModel>
}

/** 纹理瓦片时间节点接口 */ 
// export const getTextureTimePoint = (params: APIDUST.TGetTextureTimePointParams ) => {
//   return request(`/api/texture/time/point?${stringify(params)}`) as Promise<APIDUST.TTextureTimePointModel[]>
// }

// 新纹理瓦片时间节点接口 -新接口* 
export const getTextureTimePoint = (params: APIDUST.TGetTextureTimePointParams ) => {
  return request(`/api/texture/time/point?${stringify(params)}`) as Promise<APIDUST.TTextureTimePointModel[]>
}


/** 纹理瓦片聚合接口 */ 
export const getTextureAgg = (params: APIDUST.TGetTextureAggParams ) => {
  return request(`/api/texture/agg?${stringify(params)}`) as Promise<unknown>
}

  // 沙尘统计导出模块

   /** 获取起沙地统计 */ 
	export const getDustStatsExportSendSite = (params: APIDUST.TGetDustStatsExportSendSiteParams ) => {
		return request(`/api/dust/stats/export/send/site?${stringify(params)}`) as Promise<unknown>
	}

 /** 获取行政区沙尘天数统计 */ 
	export const getDustStatsExportRegionCount = (params: APIDUST.TGetDustStatsExportRegionCountParams ) => {
		return request(`/api/dust/stats/export/region/count?${stringify(params)}`) as Promise<unknown>
	}

 /** 获取沙尘事件行政区面积统计 */ 
	export const getDustStatsExportEventRegionArea = (params: APIDUST.TGetDustStatsExportEventRegionAreaParams ) => {
		return request(`/api/dust/stats/export/event/region/area?${stringify(params)}`) as Promise<unknown>
	}

 /** 获取沙尘事件持续时间统计 */ 
	export const getDustStatsExportEventDuration = (params: APIDUST.TGetDustStatsExportEventDurationParams ) => {
		return request(`/api/dust/stats/export/event/duration?${stringify(params)}`) as Promise<unknown>
	}
