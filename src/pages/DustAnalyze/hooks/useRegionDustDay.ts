import { getDustStatsRegionCount } from "../services";
import { EChartsResponsiveOption } from "echarts";
import { useAtomValue } from "jotai";
import { useEffect, useMemo, useState } from "react";
import { useQuery } from "react-query";
import { timeOptionsAtom } from "../atom";
import { menuCollapsedAtom } from "@/atoms/menu";
import { useWindowSize } from "@/hooks";
const useRegionDustDay = () => {
  const [dataIndex, setDataIndex] = useState(-1);
  const timeOptions = useAtomValue(timeOptionsAtom);
  const menuCollapsed = useAtomValue(menuCollapsedAtom);
  const { data: dataInfo, isLoading } = useQuery(
    ["getDustStatsRegionCount", timeOptions],
    () => getDustStatsRegionCount(timeOptions)
  );
  const data = useMemo(() => {
    return dataInfo?.map((d) => d.count) || []
  }, [dataInfo]) as number[]
  const region = useMemo(
    () => {
      return dataInfo?.map((d) => d.regionName) || []
    },
    [dataInfo]
  );

  const [zoomRange, setZoomRange] = useState({
    start: 0,
    end: 60,
  });
  const { width } = useWindowSize()

  const isShowDataZoom = useMemo(() => {
    return region.length >= 6 ? true : false;
  }, [region]);

  const maxSpan = useMemo(() => {
    return width >= 1920 && menuCollapsed === 1 ? 100 : 60
  }, [menuCollapsed, width]);


  useEffect(() => {
    setZoomRange({
      start: 0,
      end:maxSpan,
    });
  }, [maxSpan]);
  const moveEvent = useMemo<IEchartsEvent>(() => {
    return [
      {
        eventName: "mouseover",
        fn: (e) => {
          setDataIndex(e.dataIndex);
        },
      },
      {
        eventName: "mouseout",
        fn: () => {
          setDataIndex(-1);
        },
      },
      {
        eventName: "dataZoom",
        fn: (e) => {
          setZoomRange({ start: e.start, end: e.end });
        },
      },
    ];
  }, []);
  const max = useMemo(() => {
    const res = data.reduce((prev, curr) => {
      if (curr > prev) {
        return curr;
      } else {
        return prev;
      }
    }, 0);
    if (res < 10) {
      // return res + 1;
      // 对于小于10的值，确保最大值是2的倍数
      return Math.ceil(res / 2) * 2;
    } else {
      const length = res.toFixed(0).length;
      return (
        Math.ceil(res / Math.pow(10, length - 1)) * Math.pow(10, length - 1)
      );
    }
  }, [data]);
  const options = useMemo<EChartsResponsiveOption>(() => {
    return {
      tooltip: {
        show: false,
      },
      grid: {
        top: 40,
        right: 7,
        left: 44,
        bottom: isShowDataZoom ? 63 : 40
      },
      xAxis: [
        {
          type: "category",
          data: region,
          axisLine: {
            lineStyle: {
              color: "#F0F2F6",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#333333",
            textStyle: {
              fontSize: 12,
            },
            align: "center",
            interval: 0,
          },
        },
      ],
      yAxis: [
        {
          name: "(天)",
          axisLabel: {
            show: true,
            formatter: "{value}",
            color: "#333333",
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(255,255,255,1)",
            },
          },
          nameTextStyle: {
            padding: [0, 35, 0, 0], // 修改单位位置
            color: "#7F7F7F",
            fontSize: 14,
            fontWeight: 400,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#E2E8EC",
              type: "dashed",
            },
          },
          max
        },
      ],
      dataZoom: [
        {
          type: "slider", // 在底部
          show: isShowDataZoom ? true : false, // 是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
          start: zoomRange.start, // 数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
          end: zoomRange.end, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
          /**
            不指定时，当 dataZoom-slider.orient 为 'horizontal'时，
            默认控制和 dataZoom 平行的第一个 xAxis。但是不建议使用默认值，建议显式指定。
            如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。
            */
          xAxisIndex: [0, 1],
          maxSpan: maxSpan,
          handleIcon:
            "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAJRJREFUKFNjZEAC6o5z1f8y/F/BwMCoz8DIwAiVus3AyBB/Z2/ycZhSmASYr+I4dw0Dw/+jd/anTGBgYPgPFnOeF/L///+yu/uSzbBrcpp7gomBJe/WvvhTMAUKDvMlWJj+nb+zL1kSr6b/jH9Lb+9NClVxmrv6zz+m3FFNpAQEiUE+bzXD/3/HSIpcspIRcjrExwYAA9abDhve7g8AAAAASUVORK5CYII=",
          handleSize: "100%",
          moveHandleSize: 12,
          brushSelect: false,
          height: 12,
          brushStyle: {
            borderColor: "#1E3C6A",
          },
          handleStyle: {
            color: "#3AAAF0",
            borderColor: "#007acc",
          },
          labelFormatter: "",
          fillerColor: "rgba(40, 108, 255, 0.10)",
          backgroundColor: "rgba(247, 247, 249, 1)",
          showDetail: false,
          selectedDataBackground: {
            areaStyle: {
              opacity: 0,
            },
            lineStyle: {
              opacity: 0,
            },
          },
          borderColor: "#ffffff",
          borderRadius: 2,
          dataBackground: {
            areaStyle: {
              opacity: 0,
            },
            lineStyle: {
              opacity: 0,
            },
          },
        },
      ],
      series: [
        {
          name: "背景",
          type: "bar",
          barWidth: 30,
          stackStrategy: "all",
          data: new Array(data.length)
            .fill(max)
            .map((v, index) => (dataIndex === index ? v : 0)),
          itemStyle: {
            color: "#F0F2F6",
          },
          zlevel: 0,
        },
        {
          type: "bar",
          data,
          barWidth: 12,
          barGap: "-70%",
          stackStrategy: "all",
          itemStyle: {
            normal: {
              color: "#FFAA17",
              barBorderRadius: [4, 4, 0, 0],
            },
          },
          zlevel: 2,
        },
        {
          name: "tooltip",
          type: "bar",
          barWidth: 12,
          barGap: "-70%",
          stackStrategy: "all",
          data: data.map((v, index) => (dataIndex === index ? v : "")),
          itemStyle: {
            color: "rgba(0,0,0,0)",
          },
          zlevel: 1,
          label: {
            show: true,
            fontSize: 12,
            position: [-18, -30],
            formatter: [`{a| ${dataIndex >= 0 && data[dataIndex]}天}`].join(
              "\n"
            ),
            rich: {
              a: {
                width: 37,
                height: 30,
                backgroundColor: {
                  image: "/assets/images/analyze/useRegionDustDayChartLabel.png",
                },
                borderRadius: 4,
                align: "center",
                lineHeight: 25,
                color: "#333333",
                padding: [0, 3, 0, 0],
              },
            },
          },
        },
      ],
    } as EChartsResponsiveOption;
  }, [data, dataIndex, isShowDataZoom, max, maxSpan, region, zoomRange.end, zoomRange.start]);
  return { options, moveEvent, isLoading, dataInfo };
};
export default useRegionDustDay;
