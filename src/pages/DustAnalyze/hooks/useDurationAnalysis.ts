
import { EChartsResponsiveOption } from 'echarts';
import { useAtomValue } from 'jotai';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';
import { getDustStatsEventDuration } from '../services';

const useDurationAnalysis = () => {
  const [dataIndex, setDataIndex] = useState(-1);
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data: eventDurationInfo, isLoading } = useQuery(
    ['getDustStatsEventDuration', timeOptions],
    () => getDustStatsEventDuration(timeOptions),
  );
  const data = useMemo(
    // @ts-ignore
    () =>eventDurationInfo?.models?.sort((a, b) => a.key - b.key).map((m) => m.value) || [],
    [eventDurationInfo?.models],
  );
  const region = useMemo(() => {
    const models =
     // @ts-ignore
      eventDurationInfo?.models.sort((a, b) => a.key - b.key) || [];
    return models.map(
      (m) => m.key,
      // index !== models.length - 1
      //   ? m.key * 5 + '小时-' + models[index + 1].key * 5 + '小时'
      //   : m.key * 5 + '小时以上',
    );
  }, [eventDurationInfo?.models]);
  const moveEvent = useMemo<IEchartsEvent>(() => {
    return [
      {
        eventName: 'mouseover',
        fn: (e) => {
          setDataIndex(e.dataIndex);
        },
      },
      {
        eventName: 'mouseout',
        fn: () => {
          setDataIndex(-1);
        },
      },
    ];
  }, []);
  const max = useMemo(() => {
    const res = data.reduce((prev, curr) => {
      if (curr > prev) {
        return curr;
      } else {
        return prev;
      }
    }, 0);
    if (res < 10) {
      // return res + 1;
      return Math.ceil(res / 2) * 2;
    } else {
      const length = res.toFixed(0).length;
      return (
        Math.ceil(res / Math.pow(10, length - 1)) * Math.pow(10, length - 1)
      );
    }
  }, [data]);
  const options = useMemo<EChartsResponsiveOption>(() => {
    return {
      tooltip: {
        show: false,
      },
      grid: {
        top: 40,
        right: 7,
        left: 44,
        bottom: 20,
      },
      xAxis: [
        {
          type: 'category',
          data: region,
          axisLine: {
            lineStyle: {
              color: '#F0F2F6',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#333333',
            textStyle: {
              fontSize: 12,
            },
            align: 'center',
          },
        },
      ],
      yAxis: [
        {
          name: '(起)',
          axisLabel: {
            show: true,
            formatter: '{value}',
            color: '#333333',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255,255,255,1)',
            },

          },
          nameTextStyle: {
            padding: [0, 35, 0, 0], // 修改单位位置
            color: '#7F7F7F',
            fontSize: 14,
            fontWeight: 400,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E2E8EC',
              type: 'dashed',
            },
          },
          max,
          interval: Math.ceil(max / 4)
        },
      ],
      series: [
        {
          name: '背景',
          type: 'bar',
          barWidth: 30,
          stackStrategy: 'all',
          data: new Array(data.length)
            .fill(max)
            .map((v, index) => (dataIndex === index ? v : 0)),
          itemStyle: {
            color: '#F0F2F6',
          },
          zlevel: 0,
        },
        {
          type: 'bar',
          data,
          barWidth: 12,
          barGap: '-70%',
          stackStrategy: 'all',
          itemStyle: {
            normal: {
              color: '#F44436',
              barBorderRadius: [4, 4, 0, 0],
            },
          },
          zlevel: 2,
        },
        {
          name: 'tooltip',
          type: 'bar',
          barWidth: 12,
          barGap: '-70%',
          stackStrategy: 'all',
          data: data.map((v, index) => (dataIndex === index ? v : '')),
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zlevel: 1,
          label: {
            show: true,
            fontSize: 12,
            position: [-18, -30],
            formatter: [`{a| ${dataIndex >= 0 && data[dataIndex]}起}`].join(
              '\n',
            ),
            rich: {
              a: {
                width: 37,
                height: 30,
                backgroundColor: {
                  image: '/assets/images/analyze/useDurationAnalysisChartLabel.png',
                },
                borderRadius: 4,
                align: 'center',
                lineHeight: 25,
                color: '#333333',
                padding: [0, 3, 0, 0],
              },
            },
          },
        },
      ],
    } as EChartsResponsiveOption;
  }, [data, dataIndex, max, region]);
  return {
    options,
    moveEvent,
    maxDuration: eventDurationInfo?.maxDuration,
    eventDurationInfo,
    isLoading,
  };
};
export default useDurationAnalysis;
