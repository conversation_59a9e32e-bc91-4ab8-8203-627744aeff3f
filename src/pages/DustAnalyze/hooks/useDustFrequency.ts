import { TiandituLayer } from '@/layers/deckgl-tianditu-layer/dist';
import { getLevelByRegionCode, request, wmts } from '@/utils';
import geojson from '@/utils/dust/geojson/10.json';
import { Bit<PERSON><PERSON><PERSON>ay<PERSON>, Geo<PERSON><PERSON><PERSON>ayer, TileLayer } from 'deck.gl';
import { useAtomValue } from 'jotai';
import { stringify } from 'qs';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';
import { EXTENT } from '../config';
import useTextureToken from '../hooks/useTextureToken';
import { getTextureTimePoint } from '../services';
import { data2ImageData, getTileUrl } from '../utils';
import { tileLayerBaseConfig } from '../utils/layers/baseTileLayerConfig';
import TextureBitmapLayer from '../utils/layers/TextureBitmapLayer';
import { fetchGeojsonIncludeChild } from '@/services/global';
import bbox from '@turf/bbox';
import { useMaskLayer } from '@/hooks';
// @ts-ignore
import { MaskExtension } from '@deck.gl/extensions';

const useDustFrequency = (isExport = false) => {
  const tileToken = useTextureToken('DMASK_COUNT');
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data, isLoading } = useQuery(['getTextureTimePoint', 'DMASK_COUNT', timeOptions], () =>
    getTextureTimePoint({
      type: 'DMASK_COUNT',
      agg: 'daily',
      startDate: timeOptions.startTime,
      endDate: timeOptions.endTime,
    }),
  );
  const baseLayer = useMemo(() => {
    return new TiandituLayer({
      id: 'tianditu-tile-layer-vec' + ((isExport && '-export') || ''),
      data: wmts('vec'),
    });
  }, [isExport]);

  const provinceGeojsonLayer = useMemo(
    () =>
      new GeoJsonLayer({
        id: 'city-geojson-layer' + ((isExport && '-export') || ''),
        pickable: true,
        stroked: true,
        lineWidthUnits: 'pixels',
        getLineWidth: 2,
        lineWidthMinPixels: 2,
        // @ts-ignore
        data: geojson || [],
        getLineColor: () => {
          return [255, 255, 255, 255];
        },
        filled: false,
        getFillColor: () => [0, 0, 0, 0],
      }),
    [isExport],
  );
  const labelTileLayer = useMemo<any>(() => {
    return new TileLayer({
      id: `tianditu-tile-label-layer${(isExport && '-export') || ''}`,
      data: wmts('cia'),
      pickable: false,
      zoomOffset: 1,
      renderSubLayers: (props: any) => {
        const {
          bbox: { west, south, east, north },
        } = props.tile;
        return new BitmapLayer(props, {
          data: null,
          image: props.data,
          bounds: [west, south, east, north],
        });
      },
    });
  }, [isExport]);

  const regionCode = '150000'
  const { data: geojsonMapData } = useQuery(
    [`map-geojson-${regionCode}`],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(regionCode),
        level: getLevelByRegionCode(regionCode),
      }),
    {
      enabled: Boolean(regionCode),
    },
  );

  const dmaskCountTileLayer = useMemo(() => {
    if (!tileToken || isLoading) return;
    if (!geojsonMapData) return;
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: `data-texture-layer-dmask-count-${timeOptions.startTime}-${timeOptions.endTime}-${(isExport && '-export') || ''}`,
      data:
        data?.length !== 1
          ? `/api/texture/agg?${stringify({
              type: 'DMASK_COUNT',
              startDate: timeOptions.startTime,
              endDate: timeOptions.endTime,
              tileToken,
            })}&x={x}&y={y}&z={z}`
          : getTileUrl(
              {
                time: data[0].timePoints,
                type: 'DMASK_COUNT',
                agg: 'daily',
                token: tileToken,
              },
              true,
            ),
      extent: EXTENT,
      maskId: 'geojson-mask',
      extensions: [new MaskExtension()],
      maxZoom: 7,
      minZoom: 0,
      // colorFormat: 'RGBA',
      renderSubLayers: (props) => {
        const {
          bbox: { west, south, east, north },
        } = props.tile;
        return new TextureBitmapLayer(props, {
          pickable: false,
          data: null,
          image: props.data,
          bounds: [west, south, east, north],
          decoder: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
          max: Math.max(48, data?.length || 1),
          min: 0,
          colorRamp: {
            '0.0': 'rgb(135, 255, 255)',
            '0.333': 'rgb(255, 254, 0)',
            '0.667': 'rgb(250, 147, 2)',
            '1.0': 'rgb(247, 5, 2)',
          },
          visible: props.data,
        });
      },
      updateTriggers: {
        renderSubLayers: [timeOptions],
      },
      // @ts-ignore
      getTileData: async (tile) => {
        if (tile.url.includes('http://tile.openrj.cn/svc/tile/texture/tile.webp')) {
          return tile.url;
        }
        const imgBlob = await request(tile.url);
        const url = await data2ImageData(imgBlob);
        return url;
      },
    });
  }, [data, geojsonMapData, isExport, isLoading, tileToken, timeOptions]);
  const maskLayer = useMaskLayer(geojsonMapData);
  return {
    baseLayer,
    provinceGeojsonLayer,
    labelTileLayer,
    dmaskCountTileLayer,
    maskLayer
  };
};
export default useDustFrequency;
