import { getTextureToken } from '../services';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { DataTypes } from '../types';

export default function useTextureToken(type?: DataTypes) {
  const params = useMemo(() => (type ? { type } : {type:''}), [type]);

  const { data } = useQuery<{ token: string }>(
    ['texture-token', type],
    () => getTextureToken(params),
    {
      enabled: true,
    },
  );

  return data?.token;
}
