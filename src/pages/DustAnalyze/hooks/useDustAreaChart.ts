import { getDustStatsEventRegionArea } from '../services';
import { useAtomValue } from 'jotai';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';
import { buildColorRamp } from '../utile';

const colorRamp = {
  '0.0': '#2D1E6E',
  '0.14': '#4951EE',
  '0.28': '#289FFF',
  '0.43': '#61DDAA',
  '0.57': '#FEDB00',
  '0.71': '#FF7E00',
  '0.86': '#B5004A',
  '1.00': '#7E0023',
};
const useDustAreaChart = (proviceNames: string[]) => {
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data, isLoading } = useQuery(
    ['getDustStatsEventRegionArea', timeOptions],
    () => getDustStatsEventRegionArea(timeOptions),
  );
  const [checkIndex, setCheckIndex] = useState(-1);
  // 选中覆盖面积总和
  const baseArea = useMemo(() => {
    if (!Array.isArray(data)) return 0;
    return (
      data
        ?.filter((d) => proviceNames.includes(d.regionName))
        .reduce((prev, curr) => prev + curr.area, 0) || 0
    );
  }, [data, proviceNames]);
  // 数据格式化
  const data_1 = useMemo(() => {
    if (!data?.length || !baseArea) return [];
    const colorData = buildColorRamp(colorRamp);
    const res =
      data.map((d, index) => ({
        name: d.regionName,
        value: Number(((d.area / baseArea) * 100).toFixed(2)),
        area: d.area,
        color: [
          colorData[Math.floor((index / data.length) * 256) * 4],
          colorData[Math.floor((index / data.length) * 256) * 4 + 1],
          colorData[Math.floor((index / data.length) * 256) * 4 + 2],
          colorData[Math.floor((index / data.length) * 256) * 4 + 3],
        ].reduce((prev, curr) => {
          const val = curr.toString(16);
          return prev + (val.length < 2 ? '0' + val : val);
        }, '#'),
      })) || [];
    res[res?.length - 1].value = res
      ?.filter((d) => proviceNames.includes(d.name))
      ?.reduce((prev, curr, index) => {
        if (
          index !==
          res?.filter((d) => proviceNames.includes(d.name)).length - 1
        ) {
          return Number((prev - curr.value).toFixed(2));
        }
        return prev;
      }, 100);
    return res;
  }, [baseArea, data, proviceNames]);
  const moveEvent = useMemo<IEchartsEvent>(() => {
    return [
      {
        eventName: 'mouseover',
        fn: (e) => {
          setCheckIndex(e.dataIndex);
        },
      },
      {
        eventName: 'mouseout',
        fn: () => {
          setCheckIndex(-1);
        },
      },
    ];
  }, []);

  const pieData1 = useMemo(() => {
    const filterData = data_1?.filter((d) => proviceNames.includes(d.name));
    const color = filterData.map((provice) => provice.color);
    return filterData.map((d, i) => ({
      ...d,
      itemStyle: {
        color: color[i],
        borderColor: color[i],
        borderWidth: 0,
      },
    }));
  }, [data_1, proviceNames]);
  const showArea = useMemo(() => {
    return checkIndex >= 0 ? pieData1[checkIndex].area : baseArea;
    // : data?.reduce((prev, curr) => prev + curr.area, 0) || 0;
  }, [baseArea, checkIndex, pieData1]);
  const option = useMemo(
    () => ({
      tooltip: {
        show: true,
        trigger: 'item',
        position: 'right',
        formatter: (params: any) => {
          if (params.name) {
            return `${params.name}: ${params.value}%`;
          } else {
            return false;
          }
        },
      },
      series: [
        {
          name: 'background',
          type: 'pie',
          radius: ['78%', '100%'],
          avoidLabelOverlap: false, // 防止标签重叠
          hoverAnimation: false,
          label: {
            show: false,
            position: 'center', // 标签位置
          },
          emphasis: {
            label: {
              show: false,
              fontSize: 40,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: pieData1,
          z: 0,
        },
      ],
    }),
    [pieData1],
  );
  return { option, showArea, data_1, data, moveEvent, isLoading };
};
export default useDustAreaChart;
