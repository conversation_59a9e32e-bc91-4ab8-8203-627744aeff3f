import { getDustStatsDateCount } from '../services';
import { EChartsResponsiveOption } from 'echarts';
import { useAtomValue } from 'jotai';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';

const useDustNumberChartOptions = () => {
  const [dataIndex, setDataIndex] = useState(-1);
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data: chartData } = useQuery(
    ['getDustStatsDateCount', timeOptions.startTime, timeOptions.endTime],
    () =>
      getDustStatsDateCount({
        startTime: moment(timeOptions.startTime).format('YYYY/MM/DD'),
        endTime: moment(timeOptions.endTime).format('YYYY/MM/DD'),
      }),
  );
  const data = useMemo(() => {
    return chartData?.map((d) => d.count) || []

  }, [chartData]) as number[];
  const time = useMemo(
    () => {
      return chartData?.map((d) => moment(d.date).format('MM月')) || []
    },
    [chartData],
  );
  const moveEvent = useMemo<IEchartsEvent>(() => {
    return [
      {
        eventName: 'mouseover',
        fn: (e) => {
          setDataIndex(e.dataIndex);
        },
      },
      {
        eventName: 'mouseout',
        fn: () => {
          setDataIndex(-1);
        },
      },
    ];
  }, []);
  const max = useMemo(() => {
    const res = data.reduce((prev, curr) => {
      if (curr > prev) {
        return curr;
      } else {
        return prev;
      }
    }, 0);
    if (res < 10) {
      return res + 1;
    } else {
      const length = res.toFixed(0).length;
      return (
        Math.ceil(res / Math.pow(10, length - 1)) * Math.pow(10, length - 1)
      );
    }
  }, [data]);
  const options = useMemo<EChartsResponsiveOption>(() => {
    return {
      tooltip: {
        show: false,
      },
      grid: {
        top: 30,
        right: 0,
        left: 0,
        bottom: 20,
      },
      xAxis: [
        {
          type: 'category',
          data: time,
          axisLine: {
            lineStyle: {
              color: '#F0F2F6',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            color: '#333333',
            textStyle: {
              fontSize: 12,
            },
          },
        },
      ],
      yAxis: [
        {
          axisLabel: {
            formatter: '{value}',
            color: '#e2e9ff',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255,255,255,1)',
            },
          },
          splitLine: {
            show: false,
          },
          max,
        },
      ],
      dataZoom: [
        {
          type: 'slider', // 在底部
          show: data.length <= 12 ? false : true, // 是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
          start: 0, // 数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
          end: (100 / data.length) * 12, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
          bottom: 0, // 控制滚动条距离底部的位置;
          /**
            不指定时，当 dataZoom-slider.orient 为 'horizontal'时，
            默认控制和 dataZoom 平行的第一个 xAxis。但是不建议使用默认值，建议显式指定。
            如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。
            */
          xAxisIndex: [0, 1],
          handleSize: '0px',
          brushSelect: false,
          height: 5,
          brushStyle: {
            borderColor: '#ffffff',
          },
          handleStyle: {
            color: '#ffffff',
            borderColor: '#ffffff',
          },
          labelFormatter: '',
          fillerColor: '#acacac',
          backgroundColor: 'ffffff',
          showDetail: true,
          borderColor: '#ffffff',
          dataBackground: {
            areaStyle: {
              opacity: 0,
            },
            lineStyle: {
              opacity: 0,
            },
          },
        },
      ],
      series: [
        {
          name: '背景',
          type: 'bar',
          barWidth: 24,
          stackStrategy: 'all',
          data: new Array(data.length)
            .fill(max)
            .map((v, index) => (dataIndex === index ? v : 0)),
          itemStyle: {
            color: '#F0F2F6',
          },
          zlevel: 0,
        },
        {
          type: 'bar',
          data,
          barWidth: 10,
          barGap: '-70.833%',
          stackStrategy: 'all',
          itemStyle: {
            normal: {
              color: 'rgba(76, 120, 255, 1)',
              barBorderRadius: [4, 4, 0, 0],
            },
          },
          zlevel: 2,
        },
        {
          name: 'tooltip',
          type: 'bar',
          barWidth: 10,
          barGap: '-70.833%',
          stackStrategy: 'all',
          data: data.map((v, index) => (dataIndex === index ? v : '')),
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zlevel: 1,
          label: {
            show: true,
            fontSize: 12,
            position: [-18, -30],
            formatter: [`{a| ${dataIndex >= 0 && data[dataIndex]}天}`].join(
              '\n',
            ),
            rich: {
              a: {
                width: 37,
                height: 30,
                backgroundColor: {
                  image: '/assets/images/analyze/useDustNumberChartLabel.png',
                },
                borderRadius: 4,
                align: 'center',
                lineHeight: 25,
                color: '#333333',
                padding: [0, 3, 0, 0],
              },
            },
          },
        },
      ],
    } as EChartsResponsiveOption;
  }, [data, dataIndex, max, time]);
  return { options, moveEvent };
};
export default useDustNumberChartOptions;
