import { getDustStatsSendSite } from '../services';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';
import { buildColorRamp } from '../utile';
const colorRamp = {
  '0.000': '#FFBA17',
  '0.333': '#5FE1E0',
  '0.667': '#289FFF',
  '1.000': '#4951EE',
};
const useSourceAnalysis = () => {
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data, isLoading } = useQuery(
    ['getDustStatsSendSite', timeOptions],
    () => getDustStatsSendSite(timeOptions),
  );
  const data_1 = useMemo(() => {
    const colorData = buildColorRamp(colorRamp);
    return (
      data?.regionModels?.map((model, index) => ({
        value: model.count,
        name: model.regionName,
        color: [
          colorData[Math.floor((index / data?.regionModels?.length) * 256) * 4],
          colorData[
            Math.floor((index / data?.regionModels?.length) * 256) * 4 + 1
          ],
          colorData[
            Math.floor((index / data?.regionModels?.length) * 256) * 4 + 2
          ],
          colorData[
            Math.floor((index / data?.regionModels?.length) * 256) * 4 + 3
          ],
        ].reduce((prev, curr) => {
          const val = curr.toString(16);
          return prev + (val.length < 2 ? '0' + val : val);
        }, '#'),
      })) || []
    );
  }, [data]);
  const data_2 = useMemo(
    () => [
      {
        value: data?.jnCount,
        name: '境内',
        color: '#016EFF',
      },
      {
        value: data?.jwCount,
        name: '境外',
        color: '#5FE1E0',
      },
    ],
    [data?.jnCount, data?.jwCount],
  );
  // const { pieData1, pieData2, pieData3 } = useMemo(() => {
  //   const color_1 = data_1.map((provice) => provice.color);
  //   const color_2 = [
  //     color_1[color_1.length - 1],
  //     ...color_1.slice(0, color_1.length - 1),
  //   ];
  //   const data1 = data_1.map((item, index) => {
  //     let res: any = item;
  //     res.itemStyle = {
  //       color: color_1[index],
  //     };
  //     return res;
  //   });
  //   const data2 = data_1.map((item, index) => {
  //     let res: any = item;
  //     res.itemStyle = {
  //       color: color_2[index],
  //       borderColor: color_2[index],
  //     };
  //     return res;
  //   });
  //   const sum = data1.reduce((per, cur) => per + cur.value, 0);
  //   const gap = (1 * sum) / 100;
  //   const pieData1 = [];
  //   const pieData2 = [];
  //   const gapData = {
  //     name: '',
  //     value: gap,
  //     itemStyle: {
  //       color: 'transparent',
  //     },
  //   };
  //   for (let i = 0; i < data_1.length; i++) {
  //     // 第一圈数据
  //     pieData1.push({
  //       ...data1[i],
  //       itemStyle: {
  //         color: color_1[i],
  //         borderColor: color_1[i],
  //         borderWidth: 0,
  //       },
  //     });
  //     pieData1.push({
  //       ...gapData,
  //       res: data1[i].value,
  //       resName: data1[i].name,
  //       itemStyle: {
  //         borderColor: color_1[i],
  //         color: color_1[i],
  //         borderWidth: 0,
  //       },
  //     });
  //     pieData2.push(data2[i]);
  //     pieData2.push(gapData);
  //   }
  //   const pieData3 = useMemo(() => data_2.map((d) => ({
  //     ...d,
  //     itemStyle: { color: d.color },
  //   });
  //   return { pieData1, pieData2, pieData3 };
  // }, [data_1, data_2]);
  const pieData1 = useMemo(() => {
    const color_1 = data_1.map((provice) => provice.color);
    return data_1.map((d, i) => ({
      ...d,
      itemStyle: {
        color: color_1[i],
        borderColor: color_1[i],
        borderWidth: 0,
      },
    }));
  }, [data_1]);
  const pieData2 = useMemo(
    () =>
      data_2.map((d) => ({
        ...d,
        itemStyle: { color: d.color },
      })),
    [data_2],
  );
  const option = useMemo(() => {
    return {
      tooltip: {
        show: true,
        trigger: 'item',
        position: 'right',
        formatter: (params: any) => {
          if (params.name) {
            return `${params.name}起沙 ${params.value} 起`;
          } else {
            return false;
          }
        },
      },
      series: [
        {
          name: 'background',
          type: 'pie',
          radius: ['70.6%', '90.6%'],
          avoidLabelOverlap: false, // 防止标签重叠
          hoverAnimation: false,
          label: {
            show: false,
            position: 'center', // 标签位置
          },
          emphasis: {
            label: {
              show: false,
              fontSize: 40,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: pieData1,
          z: 0,
        },
        // {
        //   name: 'show',
        //   type: 'pie',
        //   radius: ['70.6%', '90.6%'],
        //   avoidLabelOverlap: false,
        //   hoverAnimation: false,
        //   itemStyle: {
        //     borderRadius: [0, '50%', 0, '50%'],
        //   },
        //   label: {
        //     show: false,
        //     position: 'center',
        //   },
        //   emphasis: {
        //     label: {
        //       show: false,
        //       fontSize: 40,
        //       fontWeight: 'bold',
        //     },
        //   },
        //   labelLine: {
        //     show: false,
        //   },
        //   data: pieData2,
        //   z: 1,
        // },
        {
          name: 'background',
          type: 'pie',
          radius: ['0', '40%'],
          avoidLabelOverlap: false, // 防止标签重叠
          hoverAnimation: false,
          label: {
            show: false,
            position: 'center', // 标签位置
          },
          emphasis: {
            label: {
              show: false,
              fontSize: 40,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: pieData2,
          z: 0,
        },
      ],
    };
  }, [pieData1, pieData2]);
  return { option, isLoading, data };
};
export default useSourceAnalysis;
