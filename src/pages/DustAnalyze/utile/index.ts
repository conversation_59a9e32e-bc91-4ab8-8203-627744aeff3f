import { downloadUseLink } from '@/utils';
import { FlyToInterpolator } from 'deck.gl';
import html2canvas from 'html2canvas';

export const DEFAULT_VIEW_STATE = {
  latitude: 46.0445418002959,
  longitude:113.69886201705926,
  zoom: 3.5,
  minZoom: 1,
  maxZoom: 14,
  transitionInterpolator: new FlyToInterpolator(),
  transitionDuration: 500,
};
export const buildColorRamp = (colors: Record<string, string>) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('error creating canvas');
  }

  canvas.width = 256;
  canvas.height = 1;

  const gradient = ctx.createLinearGradient(0, 0, 256, 0);

  Object.keys(colors).forEach((stop: any) => {
    gradient.addColorStop(+stop, colors[stop]);
  });

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, 256, 1);

  return new Uint8Array(ctx.getImageData(0, 0, 256, 1).data);
};
// 导出图表
export function handleExportChart(domId: string | HTMLElement, title_: string, cb?: unknown, needFull?: boolean) {
  const background = '#fff';
  const title = title_.replace(/\./g, '·') + '.jpg';
  const container = typeof domId === 'string' ? document.getElementById(domId) : domId;
  if (container) {
    const defaultBg = (container as HTMLDivElement).style.background;
    (container as HTMLDivElement).style.background = background;
    if (needFull) (container as HTMLDivElement).classList.add('full-height-important');
    window.requestAnimationFrame(() => {
      html2canvas(container).then((canvas:any) => {
        const url = canvas.toDataURL('image/png');
        downloadUseLink(url, title);
        if (typeof cb === 'function') cb();
        (container as HTMLDivElement).style.background = defaultBg;
        if (needFull) setTimeout(() => (container as HTMLDivElement).classList.remove('full-height-important'), 500);
      });
    });
  }
}