import type { Texture2D } from '@luma.gl/webgl';
import TextureDrawer from './texture-drawer';

type ColorRampOptions = Record<number, string>;
export default class ColorRamp {
  public static build(
    gl: WebGL2RenderingContext,
    colors: ColorRampOptions,
  ): Texture2D {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('error creating canvas');
    }

    canvas.width = 256;
    canvas.height = 1;

    const gradient = ctx.createLinearGradient(0, 0, 256, 0);

    Object.keys(colors).forEach((stop: any) => {
      gradient.addColorStop(+stop, colors[stop]);
    });

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 256, 1);

    const data = new Uint8Array(ctx.getImageData(0, 0, 256, 1).data);

    return TextureDrawer.createTexture(gl, {
      width: 256,
      height: 1,
      data,
      filter: gl.LINEAR,
    });
  }
}
