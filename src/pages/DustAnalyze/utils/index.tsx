import { stringify } from 'qs';

export function data2ImageData(data: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(data);
    reader.onload = () => {
      let url = '';
      if (typeof reader.result === 'string') {
        url = reader.result?.replace(/^data:.*;/, 'data:image/png;');
      }
      resolve(url);
    };
    reader.onerror = () => {
      reject('error');
    };
  });
}


interface TileParams {
  agg: string;
  type: string;
  time: string;
  token?: string;
}

export const tilePrefix = 'http://tile.openrj.cn/svc/tile/texture/tile.webp';

export const getTileUrl = (tileParams: TileParams, needZ = false) =>
  tileParams.token
    ? `${tilePrefix}?${stringify(tileParams)}&x={x}&y={y}${
        needZ ? '&z={z}' : ''
      }`
    : '';
