import { getDustStatsEventTotal } from '../services';
import { useAtomValue } from 'jotai';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';

const EventAnalysis = () => {
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data } = useQuery(['getDustStatsEventTotal', timeOptions], () =>
    getDustStatsEventTotal(timeOptions),
  );
  return (
    <div className="w-full h-full flex flex-col overflow-hidden py-[20px]">
      <div className="w-full h-[22px] flex">
        <div className="ml-[24px] mt-[2px]  w-[3px] h-[16px] bg-[#ffffff]" />
        <div className="ml-[10px] h-[22px] leading-[22px] text-[#ffffff] font-semibold text-base">
          沙尘事件统计分析
        </div>
      </div>
      <div className="mt-[24px] flex h-[85px] justify-start">
        <div className="w-[64px] h-[64px] ml-[65px] mt-[8px]">
          <img
            className="pointer-events-none"
            src="/assets/images/analyze/dustEvent.png"
            alt=""
          />
        </div>
        <div className="w-[149px] h-[full] pl-[14px] flex flex-col gap-[4px]">
          <div className="w-full h-[25px] flex items-center">
            <div className="font-semibold text-[#ffffff] text-lg leading-[25px] ml-[4px]">
              沙尘事件
            </div>
            <div className="ml-[2px] h-[20px] text-sm text-[#ffffff] font-semibold">
              (起)
            </div>
          </div>
          <div className="w-full flex-1 flex">
            <div className="h-[56px] leading-[43px] text-[38px] text-[#ffffff] font-normal max-w-[112px]">
              {data?.count}
            </div>
          </div>
        </div>
        <div className="w-[64px] h-[64px] mt-[8px]">
          <img
            className="pointer-events-none"
            src="/assets/images/analyze/dustArea.png"
            alt=""
          />
        </div>
        <div className="w-[158px] h-[full] pl-[14px] flex flex-col gap-[4px]">
          <div className="w-full h-[25px] flex items-center">
            <div className="font-semibold text-[#ffffff] text-lg leading-[25px] ml-[4px]">
              覆盖面积
            </div>
            <div className="ml-[2px] h-[20px] text-sm text-[#ffffff] font-semibold">
              ({(data?.area ?? 0) / 10000 > 10000 ? '亿' : (data?.area ?? 0) > 10000 ? '万' : ''}
              k㎡)
            </div>
          </div>
          <div className="w-full flex-1 flex">
            <div className="h-[56px] leading-[43px] text-[38px] text-[#ffffff] font-normal max-w-[106px]">
              {((data?.area ?? 0) / 10000 > 10000
                ? (data?.area ?? 0) / 10000 / 10000
                : (data?.area ?? 0) > 10000 ? (data?.area ?? 0) / 10000 : (data?.area ?? 0)
              ).toFixed(0)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default EventAnalysis;
