import { getDustStatsDateCount } from '../services';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { useAtom, useAtomValue } from 'jotai';
import moment, { Moment } from 'moment';
import { useCallback, useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { clanderMonthAtom, timeOptionsAtom } from '../atom';

const monthList = ['一', '二', '三', '四', '五', '六', '日'];
const Clander = () => {
  const timeOptions = useAtomValue(timeOptionsAtom);
  const [month, setMonth] = useAtom(clanderMonthAtom);
  const dayTable = useMemo(() => {
    const startDay = moment(month).startOf('month').startOf('week');
    const endDay = moment(month).endOf('month').endOf('week');
    const dayList = new Array(endDay.diff(startDay, 'day') + 1)
      .fill('')
      .map((_, index) => startDay.clone().add(index, 'day'));
    const monthList = dayList.reduce((prev, curr, index) => {
      const res = prev;
      if (index % 7 === 0) {
        res.push([curr]);
      } else {
        res[res.length - 1].push(curr);
      }
      return res;
    }, [] as Moment[][]);
    return monthList;
  }, [month]);
  const isBefor = useMemo(() => {
    const time = month.clone().subtract(1, 'month').endOf('month');
    if (time.isBefore(timeOptions.startTime)) {
      return true;
    } else {
      return false;
    }
  }, [month, timeOptions.startTime]);
  const isAfter = useMemo(() => {
    const time = month.clone().add(1, 'month').startOf('month');
    if (time.isAfter(timeOptions.endTime)) {
      return true;
    } else {
      return false;
    }
  }, [month, timeOptions.endTime]);
  const beforMonth = useCallback(() => {
    setMonth((prev) => {
      return prev.clone().subtract(1, 'month').startOf('month');
    });
  }, [setMonth]);
  const nextMonth = useCallback(() => {
    setMonth((prev) => prev.clone().add(1, 'month').startOf('month'));
  }, [setMonth]);
  const { data } = useQuery(['getDustStatsDateCount', timeOptions], () =>
    getDustStatsDateCount({
      startTime: moment(timeOptions.startTime).format('YYYY/MM/DD'),
      endTime: moment(timeOptions.endTime).format('YYYY/MM/DD'),
    }),
  );
  const dustInfo = useMemo(
    () => {
      return data?.find(
        (d) => moment(d.date).format('YYYY/MM') === month.format('YYYY/MM'),
      ) || undefined;
    },
    [data, month],
  );
  useEffect(() => {
    setMonth(moment(timeOptions.endTime).startOf('month'));
  }, [setMonth, timeOptions.endTime]);
  return (
    <>
      <div className="w-full bg-[#F0F2F6] rounded-[8px] h-[46px] flex flex-row items-center px-[16px]">
        <div
          className="text-xs text-[#FFAA17] ml-[5px] h-max"
          style={{
            cursor: !isBefor ? 'pointer' : 'not-allowed',
          }}
        >
          <LeftOutlined
            onClick={beforMonth}
            style={{
              pointerEvents: !isBefor ? 'auto' : 'none',
              color: !isBefor ? '#FFAA17' : '#aaaaaa',
            }}
          />
        </div>
        <div className="font-semibold w-[63px] text-center h-max text-[#1E315E]">
          {month.get('month') + 1}月
        </div>
        <div
          className="text-xs text-[#FFAA17] h-max"
          style={{
            cursor: !isAfter ? 'pointer' : 'not-allowed',
          }}
        >
          <RightOutlined
            onClick={nextMonth}
            style={{
              pointerEvents: !isAfter ? 'auto' : 'none',
              color: !isAfter ? '#FFAA17' : '#aaaaaa',
            }}
          />
        </div>
        <div className="flex-1 flex justify-end items-center">
          <img
            className="w-[14px] h-[14px] mr-[3px]"
            src="/assets/images/analyze/dustDayNumber.png"
            alt=""
          />
          <div className="flex h-[20px] text-sm font-normal">
            <p className="text-[#333333]">沙尘天数：</p>
            <p className="text-[#FFAA17] font-semibold">{dustInfo?.count}</p>
            <p className="text-[#333333]">天</p>
          </div>
        </div>
      </div>
      <div
        className="flex flex-col h-max"
        style={{ gap: dayTable.length < 6 ? 10 : 2 }}
      >
        <div className="w-full mt-[17px] h-[38px] flex px-[2px]">
          {monthList.map((d) => (
            <div
              key={d}
              className="flex-1 flex h-[full] items-center justify-center text-sm text-[#333333] font-medium"
            >
              {d}
            </div>
          ))}
        </div>
        {dayTable.map((week) => (
          <div className="w-full flex px-[2px]" key={week[0].format('YYYY-WW')}>
            {week.map((d) => (
              <div
                key={d.format('YYYY-MM-DD')}
                className="flex-1 flex h-full items-center justify-center"
              >
                <div
                  className="w-[38px] rounded-full h-[38px] flex items-center justify-center text-sm font-medium"
                  style={{
                    background: dustInfo?.dustDataTimes
                      .map((d) => moment(d).format('YYYY/MM/DD'))
                      .includes(d.format('YYYY/MM/DD'))
                      ? '#FFAA17'
                      : '#FFFFFF',
                    color: dustInfo?.dustDataTimes
                      .map((d) => moment(d).format('YYYY/MM/DD'))
                      .includes(d.format('YYYY/MM/DD'))
                      ? '#FFFFFF'
                      : d.get('month') === month.get('month')
                        ? '#333333'
                        : '#CDCDCD',
                  }}
                >
                  {d.get('date')}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </>
  );
};
export default Clander;
