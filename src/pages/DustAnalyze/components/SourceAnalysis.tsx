import { getDustStatsExportSendSite } from '../services';
import Chart from '@/components/Chart';
import { exportFile } from '@/utils';
import { useAtomValue } from 'jotai';
import { useMutation } from 'react-query';
import { useResizeDetector } from 'react-resize-detector';
import { timeOptionsAtom } from '../atom';
import useSourceAnalysis from '../hooks/useSourceAnalysis';
import { handleExportChart } from '../utile';
import SubTitle from './SubTitle';

const SourceAnalysis = () => {
  const { height, ref } = useResizeDetector();
  const { option, isLoading, data } = useSourceAnalysis();
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { mutate } = useMutation(
    () => getDustStatsExportSendSite(timeOptions),
    {
      onSuccess: (data) => {
        exportFile(
          data,
          `${timeOptions.startTime}-${timeOptions.endTime}起沙源头分析表`,
        );
      },
    },
  );
  return (
    <div className="relative w-full h-full overflow-hidden">
      <div
        className="w-full h-full flex flex-col overflow-hidden pt-[20px] pb-[4px] relative"
        ref={ref}
      >
        <div className="w-full h-[22px] flex">
          <div className='flex flex-1'>
            <SubTitle title="起沙源头分析" />
          </div>
          <div
            className="mr-[24px] mt-[2px] cursor-pointer"
            onClick={() => {
              mutate();
              handleExportChart(
                'dust-send-site',
                `${timeOptions.startTime}-${timeOptions.endTime}起沙源头分析图`,
                () => { },
                true,
              );
            }}
          >
            <iconpark-icon size="18" name="xiazai"></iconpark-icon>
          </div>
        </div>
        {!isLoading && data?.regionModels !== null ? (
          <div className="flex-1 ml-[8px] flex items-center py-[12px]">
            <div className="w-[262px] h-full flex justify-center">
              <div
                className="relative"
                style={{
                  width: (height && height > 46 && height - 46) || 0,
                  height: (height && height > 46 && height - 46) || 0,
                }}
              >
                <div className="absolute w-full h-full">
                  <div className="w-full h-full absolute rounded-full bg-[#F4FAFF] flex justify-center items-center">
                    <div
                      className="w-[49.4%] h-[49.4%] bg-[#ffffff] rounded-full"
                      style={{
                        boxShadow: `0px 0px 34px 0px rgba(0,0,0,0.08)`,
                      }}
                    ></div>
                  </div>
                </div>
                <div className="absolute flex w-full h-full">
                  <Chart mixConfig={option} />
                </div>
              </div>
            </div>
            <div className="w-[177px] h-full flex flex-wrap flex-col gap-[15px] overflow-x-auto horizontal-scroll justify-center pl-[4px]">
              <div className="w-[81px] flex items-center h-[17px] gap-[10px]">
                <div
                  className="w-[10px] h-[10px] rounded-[3px]"
                  style={{
                    background: '#5FE1E0',
                    boxShadow: `0px 3px 4px 0px #5FE1E0`,
                  }}
                />
                <div className="text-[#333333] font-regular text-xs leading-[17px]">
                  境外
                </div>
              </div>
              <div className="w-[81px] flex items-center h-[17px] gap-[10px]">
                <div
                  className="w-[10px] h-[10px] rounded-[3px]"
                  style={{
                    background: '#016EFF',
                    boxShadow: `0px 3px 4px 0px #016EFF`,
                  }}
                />
                <div className="text-[#333333] font-regular text-xs leading-[17px]">
                  境内
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full flex-1 flex flex-col items-center justify-evenly">
            <img
              className="h-[60px]"
              src="/assets/images/analyze/no-data.png"
              alt=""
            />
            <div className="text-[#999999] leading-[22px] h-[22px]">
              {data?.regionModels === null ? '当前时间段无数据' : '加载中'}
            </div>
          </div>
        )}
      </div>
      <div
        id={'dust-send-site'}
        className="w-[500px] h-[240px] absolute z-[-1] flex flex-col top-0 pt-[20px]"
      >
        <div className="w-full h-[22px] flex">
          <div className='flex flex-1'>
            <SubTitle title="起沙源头分析"/>
          </div>
        </div>
        <div className="flex-1 ml-[8px] flex items-center py-[12px]">
          <div className="w-[262px] h-full flex justify-center">
            <div
              className="relative"
              style={{
                width: (height && height > 46 && height - 46) || 0,
                height: (height && height > 46 && height - 46) || 0,
              }}
            >
              <div className="absolute w-full h-full">
                <div className="w-full h-full absolute rounded-full bg-[#F4FAFF] flex justify-center items-center">
                  <div
                    className="w-[49.4%] h-[49.4%] bg-[#ffffff] rounded-full"
                    style={{
                      boxShadow: `0px 0px 34px 0px rgba(0,0,0,0.08)`,
                    }}
                  ></div>
                </div>
              </div>
              <div className="absolute flex w-full h-full">
                <Chart mixConfig={option} />
              </div>
            </div>
          </div>
          <div className="w-[177px] h-full flex flex-wrap flex-col gap-[15px] overflow-x-auto horizontal-scroll justify-center pl-[4px]">
            <div className="w-[81px] flex items-center h-[17px] gap-[10px]">
              <div
                className="w-[10px] h-[10px] rounded-[3px]"
                style={{
                  background: '#5FE1E0',
                  boxShadow: `0px 3px 4px 0px #5FE1E0`,
                }}
              />
              <div className="text-[#333333] font-regular text-xs leading-[17px] mt-[-10px]">
                境外
              </div>
            </div>
            <div className="w-[81px] flex items-center h-[17px] gap-[10px]">
              <div
                className="w-[10px] h-[10px] rounded-[3px]"
                style={{
                  background: '#016EFF',
                  boxShadow: `0px 3px 4px 0px #016EFF`,
                }}
              />
              <div className="text-[#333333] font-regular text-xs leading-[17px] mt-[-10px]">
                境内
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default SourceAnalysis;
