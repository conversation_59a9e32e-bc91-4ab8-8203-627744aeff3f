import { getDustStatsExportEventDuration } from '../services';
import Chart from '@/components/Chart';
import { exportFile } from '@/utils';
import { useAtomValue } from 'jotai';
import { useMutation } from 'react-query';
import { timeOptionsAtom } from '../atom';
import useDurationAnalysis from '../hooks/useDurationAnalysis';
import { handleExportChart } from '../utile';
import SubTitle from './SubTitle';

const DurationAnalysis = () => {
  const { options, moveEvent, maxDuration, isLoading } = useDurationAnalysis();
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { mutate } = useMutation(
    () => getDustStatsExportEventDuration(timeOptions),
    {
      onSuccess: (data) => {
        exportFile(
          data,
          `${timeOptions.startTime}-${timeOptions.endTime}持续时长分析表`,
        );
      },
    },
  );
  return (
    <div className="relative w-full h-full overflow-hidden">
      <div className="w-full h-full flex flex-col overflow-hidden pt-[20px] pb-[18px]">
        <div className="w-full h-[22px] flex">
          <SubTitle title="持续时长分析" />
          <div className="flex-1 flex justify-end h-[22px] leading-[22px] mr-[16px] font-semibold text-[#1E315E] gap-[5px] text-base">
            <p>沙尘事件持续时间最长为</p>
            <p className="text-[#FFAA17]">{(maxDuration ?? 0) / 60 / 60}h</p>
          </div>
          <div
            className="mr-[24px] mt-[2px] cursor-pointer"
            onClick={() => {
              mutate();
              handleExportChart(
                'dust-event-duration',
                `${timeOptions.startTime}-${timeOptions.endTime}持续时长分析图`,
                () => { },
                true,
              );
            }}
          >
            <iconpark-icon size="18" name="xiazai"></iconpark-icon>
          </div>
        </div>
        {!isLoading && maxDuration !== 0 ? (
          <div className="flex-1 mx-[24px]">
            <Chart mixConfig={options} events={moveEvent} />
          </div>
        ) : (
          <div className="w-full flex-1 flex flex-col items-center justify-evenly">
            <img
              className="h-[60px]"
              src="/assets/images/analyze/no-data.png"
              alt=""
            />
            <div className="text-[#999999] leading-[22px] h-[22px]">
              {maxDuration === 0 ? '当前时间段无数据' : '加载中'}
            </div>
          </div>
        )}
        <div
          id={'dust-event-duration'}
          className="w-[500px] h-[282px] absolute z-[-1] flex flex-col top-0 pt-[20px]"
        >
          <div className="w-full h-[22px] flex">
            <SubTitle title="持续时长分析" />
            <div className="flex-1 flex justify-end h-[22px] leading-[22px] mr-[16px] font-semibold text-[#1E315E] gap-[5px] text-base">
              <p>沙尘事件持续时间最长为</p>
              <p className="text-[#FFAA17]">{(maxDuration ?? 0) / 60 / 60}h</p>
            </div>
          </div>
          <div className="flex-1 mx-[24px]">
            <Chart mixConfig={options} events={moveEvent} />
          </div>
        </div>
      </div>
    </div>
  );
};
export default DurationAnalysis;
