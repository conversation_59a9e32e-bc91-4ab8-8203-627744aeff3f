import { getDustStatsExportRegionCount } from '../services';
import Chart from '@/components/Chart';
import { exportFile } from '@/utils';
import { useAtomValue } from 'jotai';
import { useMutation } from 'react-query';
import { useResizeDetector } from 'react-resize-detector';
import { timeOptionsAtom } from '../atom';
import useRegionDustDay from '../hooks/useRegionDustDay';
import { handleExportChart } from '../utile';
import SubTitle from './SubTitle';

const RegionDustDay = () => {
  const { ref } = useResizeDetector();
  const { options, moveEvent, isLoading, dataInfo } = useRegionDustDay();
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { mutate } = useMutation(
    () => getDustStatsExportRegionCount(timeOptions),
    {
      onSuccess: (data) => {
        exportFile(
          data,
          `${timeOptions.startTime}-${timeOptions.endTime}行政区域沙尘天排名表`,
        );
      },
    },
  );
  return (
    <div className="relative w-full h-full overflow-hidden">
      <div className="w-full h-full flex flex-col overflow-hidden pt-[20px]">
        <div className="w-full h-[22px] flex">
          <div className='flex flex-1'>
            <SubTitle title="行政区域沙尘天排名" />
          </div>
          <div className="flex-1 flex justify-end items-center h-[22px] leading-[22px] mr-[24px] font-semibold text-[#1E315E] text-base">
            <iconpark-icon
              size="18"
              style={{ cursor: 'pointer' }}
              name="xiazai"
              onClick={() => {
                mutate();
                handleExportChart(
                  'region-count',
                  `${timeOptions.startTime}-${timeOptions.endTime}行政区域沙尘天排名图`,
                  () => { },
                  true,
                );
              }}
            ></iconpark-icon>
          </div>
        </div>
        <div className="flex-1 mx-[24px]" ref={ref}>
          {!isLoading && dataInfo?.length !== 0 ? (
            <Chart mixConfig={options} events={moveEvent} />
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-evenly">
              <img className="h-1/2" src="/assets/images/analyze/no-data.png" alt="" />
              <div className="text-[#999999] leading-[22px] h-[22px]">
                {dataInfo?.length === 0 ? '当前时间段无数据' : '加载中'}
              </div>
            </div>
          )}
        </div>
        <div
          id={'region-count'}
          className="h-[362px] absolute z-[-1] flex flex-col top-0 pt-[20px]"
          style={{ width: Math.max(1200, 100 + options.xAxis[0].data.length * 95) }}
        >
          <div className="w-full h-[22px] flex">
            <div className='flex flex-1'>
              <SubTitle title="行政区域沙尘天排名"/>
            </div>
          </div>
          <div className="flex-1 mx-[24px]">
            <Chart
              mixConfig={{
                ...options,
                dataZoom: [
                  {
                    type: 'slider',
                    show: dataInfo?.length&&dataInfo?.length>=8?true:false,
                  },
                ],
              }}
              events={moveEvent}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default RegionDustDay;
