import { getDustStatsDateCount } from '../services';
import Chart from '@/components/Chart';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';
import useDustNumberChartOptions from '../hooks/useDustNumberChartOptions';
import Clander from './Clander';
import dayjs from 'dayjs';

const NumberOfDays = () => {
  // const month = useAtomValue(clanderMonthAtom);
  // const clanderMonth = useAtomValue(clanderMonthAtom);
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { options, moveEvent } = useDustNumberChartOptions();
  const { data } = useQuery(['getDustStatsDateCount-chart', timeOptions], () =>
    getDustStatsDateCount({
      startTime: dayjs(timeOptions.startTime).format('YYYY/MM/DD'),
      endTime: dayjs(timeOptions.endTime).format('YYYY/MM/DD'),
    }),
  );
  const dayCount = useMemo(() => {
    if(!Array.isArray(data)) return 0;
    return data?.reduce((prev, curr) => {
      const res = prev;
      return res + curr.count;
    }, 0);
  }, [data]);
  return (
    <div className="w-full h-full flex flex-col overflow-hidden py-[20px]">
      <div className="w-full h-[22px] flex">
        <div className="ml-[24px] mt-[2px]  w-[3px] h-[16px] bg-[#286CFF]" />
        <div className="ml-[10px] h-[22px] leading-[22px] text-[#333] font-semibold text-base">
          沙尘天数统计
        </div>
        <div className="flex-1 flex justify-end h-[22px] leading-[22px] mr-[17px] font-semibold text-[#1E315E] gap-[5px] text-base">
          <p>内蒙古自治区-沙尘天数</p>
          <p className="text-[#FFAA17]">{dayCount}</p>
          <p>天</p>
        </div>
      </div>
      <div className="flex-1 mx-[24px]">
        <div
          className="w-full mt-[-30px]"
          style={{ height: 'calc(100% + 20px)' }}
        >
          <Chart mixConfig={options} events={moveEvent} />
        </div>
      </div>
      <div className="h-max ml-[22px] mr-[24px]">
        <Clander />
      </div>
    </div>
  );
};
export default NumberOfDays;
