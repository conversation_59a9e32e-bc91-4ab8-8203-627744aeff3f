import { CaretDownOutlined } from "@ant-design/icons";
import { DatePicker } from "antd";
import { timeOptionsAtom } from "../atom";
import { useAtom } from "jotai";
import dayjs from "dayjs";
import { useClearFilter } from "@/hooks";
import { useCallback } from "react";
import { useResetAtom } from "jotai/utils";

const { RangePicker } = DatePicker;
const FilterDate = () => {
  const [timeOptions, setTimeOptions] = useAtom(timeOptionsAtom);


  // 重置
  const resetparams = useResetAtom(timeOptionsAtom);
  const fn = useCallback(() => {
    resetparams()
  }, [resetparams])
  useClearFilter('dust-analyze', fn)

  return (
    <div className="mr-[38px] border-[1px] border-[#286CFF] rounded-[8px]" id="filterDate">
      <div className="bg-[#fff] h-[36px] leading-[36px] rounded-[8px] flex items-center pl-[12px] text-[16px]">
        <img
          className="w-[18px] h-[18px] mr-[8px]"
          src={'/assets/images/analyze/filterDate.png'}
          alt=""
        />
        <span className="mr-[5px] text-[16px]">数据时间</span>
        <RangePicker
          className="!bg-transparent !text-[#333] border-0 w-[220px] !shadow-none header-range-picker"
          format="YYYY/MM/DD"
          allowClear={false}
          suffixIcon={<CaretDownOutlined className="text-[#333]" />}
          separator={<div className="w-[10px] h-[2px] bg-[#333]" />}
          value={[dayjs(timeOptions.startTime), dayjs(timeOptions.endTime)]}
          disabledDate={(current) => {
            return (
              current &&
              dayjs(current.format('YYYY/MM/DD')).isAfter(dayjs().endOf('day'))
            );
          }}
          onChange={(dates) => {
            if (dates) {
              const [start, end] = dates;
              if (start && end) {
                setTimeOptions((prev) => ({
                  ...prev,
                  startTime: start?.format('YYYY/MM/DD'),
                  endTime: end?.format('YYYY/MM/DD'),
                }));
              }
            }
          }}
        />
      </div>
    </div>
  );
};
export default FilterDate;