import { getDustStatsExportEventRegionArea } from '../services';
import Chart from '@/components/Chart';
import { exportFile } from '@/utils';
import { useAtomValue } from 'jotai';
import { useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { useResizeDetector } from 'react-resize-detector';
import { timeOptionsAtom } from '../atom';
import useDustAreaChart from '../hooks/useDustAreaChart';
import { handleExportChart } from '../utile';

const DustArea = () => {
  const [checkProvice, setCheckProvice] = useState<string[]>([]);
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { height, ref } = useResizeDetector();
  const { mutate } = useMutation(
    () => getDustStatsExportEventRegionArea(timeOptions),
    {
      onSuccess: (data) => {
        exportFile(
          data,
          `${timeOptions.startTime}-${timeOptions.endTime}覆盖面积分析表`,
        );
      },
    },
  );
  const scale = useMemo(() => {
    if (!height || height < 62) return 0;
    return ((height - 62) * 100) / 154 / 100;
  }, [height]);
  const { option, showArea, data_1, data, moveEvent, isLoading } =
    useDustAreaChart(checkProvice);
  useEffect(() => {
    setCheckProvice(Array.isArray(data) ? data.map((d) => d.regionName) : []);
  }, [data]);
  return (
    <div className="relative w-full h-full overflow-hidden">
      <div
        className="w-full h-full flex flex-col overflow-hidden pt-[20px] pb-[4px]"
        ref={ref}
      >
        <div className="w-full h-[22px] flex">
          <div className="ml-[24px] mt-[2px] rounded-full w-[6px] h-[19px] bg-[#FFAA17]" />
          <div className="ml-[10px] h-[22px] leading-[22px] text-[#823F1D] font-semibold text-base flex-1">
            覆盖面积分析
          </div>
          <div
            className="mr-[24px] mt-[2px] cursor-pointer"
            onClick={() => {
              mutate();
              handleExportChart(
                'dust-area',
                `${timeOptions.startTime}-${timeOptions.endTime}覆盖面积分析图`,
                () => { },
                true,
              );
            }}
          >
            <iconpark-icon size="18" name="xiazai"></iconpark-icon>
          </div>
        </div>
        {!isLoading && data?.length !== 0 ? (
          <div className="flex-1 ml-[8px] flex items-center py-[20px]">
            <div className="w-[262px] h-full flex justify-center">
              <div
                className="relative"
                style={{
                  width: (height && height > 62 && height - 62) || 0,
                  height: (height && height > 62 && height - 62) || 0,
                }}
              >
                <div className="absolute flex w-full h-full justify-center items-center">
                  <div
                    className="w-[100px] h-[100px] rounded-full flex flex-col items-center"
                    style={{
                      boxShadow: `0px 0px 34px 0px rgba(0,0,0,0.08)`,
                      transform: `scale(${scale}, ${scale})`,
                    }}
                  >
                    <div className="w-[100px] mt-[16px] text-[#333333] text-center font-semibold text-[25px] h-[36px] leading-[36px]">
                      {showArea > 10000
                        ? (showArea / 10000).toFixed(0)
                        : showArea.toFixed(0)}
                    </div>
                    <div className="w-[100px] mt-[-6px] text-[#333333] text-center font-regular text-xs h-[17px] leading-[17px]">
                      {showArea > 10000 && '万'}
                      k㎡
                    </div>
                    <div className="w-[100px] mt-[3px] text-[#333333] text-center font-regular text-xs h-[17px] leading-[17px]">
                      覆盖面积
                    </div>
                  </div>
                </div>
                <div className="absolute flex w-full h-full">
                  <Chart mixConfig={option} events={moveEvent} />
                </div>
              </div>
            </div>
            <div
              className="pt-[4px] w-[177px] h-full flex flex-wrap flex-col gap-[15px] overflow-x-auto horizontal-scroll pl-[4px]"
              style={{ height: (height && height > 81 && height - 81) || 0 }}
            >
              {Array.isArray(data) && data?.map((p) => (
                <div
                  className="w-[81px] flex items-center h-[17px] gap-[10px]"
                  key={p.regionName}
                  onClick={() =>
                    setCheckProvice((provice) =>
                      provice.includes(p.regionName)
                        ? provice.filter((provice) => provice !== p.regionName)
                        : provice.concat(p.regionName),
                    )
                  }
                >
                  <div
                    className="w-[10px] h-[10px] rounded-[3px]"
                    style={{
                      background: checkProvice.includes(p.regionName)
                        ? data_1.find((d) => d.name === p.regionName)?.color
                        : '#ACACAC',
                      boxShadow: checkProvice.includes(p.regionName)
                        ? `0px 3px 4px 0px ${data_1.find((d) => d.name === p.regionName)?.color}`
                        : 'none',
                    }}
                  />
                  <div className="text-[#333333] font-regular text-xs leading-[17px] truncate w-[61px]">
                    {p.regionName}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="w-full flex-1 flex flex-col items-center justify-evenly">
            <img
              className="h-[60px]"
              src="/assets/images/analyze/no-data.png"
              alt=""
            />
            <div className="text-[#999999] leading-[22px] h-[22px]">
              {data?.length === 0 ? '当前时间段无数据' : '加载中'}
            </div>
          </div>
        )}
        <div
          id={'dust-area'}
          className="w-max h-[240px] absolute z-[-1] flex flex-col top-0 pt-[20px]"
        >
          <div className="w-full h-[22px] flex">
            <div className="ml-[24px] mt-[2px] rounded-full w-[6px] h-[19px] bg-[#FFAA17]" />
            <div className="ml-[10px] mt-[-10px] h-[22px] leading-[22px] text-[#823F1D] font-semibold text-base flex-1">
              覆盖面积分析
            </div>
          </div>
          <div className="flex-1 ml-[8px] flex items-center py-[20px]">
            <div className="w-[262px] h-full flex justify-center">
              <div
                className="relative"
                style={{
                  width: (height && height > 62 && height - 62) || 0,
                  height: (height && height > 62 && height - 62) || 0,
                }}
              >
                <div className="absolute flex w-full h-full justify-center items-center">
                  <div
                    className="w-[100px] h-[100px] rounded-full flex flex-col items-center"
                    style={{
                      boxShadow: `0px 0px 34px 0px rgba(0,0,0,0.08)`,
                      transform: `scale(${scale}, ${scale})`,
                    }}
                  >
                    <div className="w-[100px] mt-[3px] text-[#333333] text-center font-semibold text-[25px] h-[36px] leading-[36px]">
                      {showArea > 10000
                        ? (showArea / 10000).toFixed(0)
                        : showArea.toFixed(0)}
                    </div>
                    <div className="w-[100px] mt-[-3px] text-[#333333] text-center font-regular text-xs h-[17px] leading-[17px]">
                      {showArea > 10000 && '万'}
                      k㎡
                    </div>
                    <div className="w-[100px] mt-[3px] text-[#333333] text-center font-regular text-xs h-[17px] leading-[17px]">
                      覆盖面积
                    </div>
                  </div>
                </div>
                <div className="absolute flex w-full h-full">
                  <Chart mixConfig={option} events={moveEvent} />
                </div>
              </div>
            </div>
            <div className="pt-[4px] h-[159px] w-max h-full flex flex-wrap flex-col gap-[15px] pl-[4px]">
              {Array.isArray(data) && data?.map((p) => (
                <div
                  className="w-[81px] flex items-center h-[17px] gap-[10px]"
                  key={p.regionName}
                  onClick={() =>
                    setCheckProvice((provice) =>
                      provice.includes(p.regionName)
                        ? provice.filter((provice) => provice !== p.regionName)
                        : provice.concat(p.regionName),
                    )
                  }
                >
                  <div
                    className="w-[10px] h-[10px] rounded-[3px]"
                    style={{
                      background: checkProvice.includes(p.regionName)
                        ? data_1.find((d) => d.name === p.regionName)?.color
                        : '#ACACAC',
                      boxShadow: checkProvice.includes(p.regionName)
                        ? `0px 3px 4px 0px ${data_1.find((d) => d.name === p.regionName)?.color}`
                        : 'none',
                    }}
                  />
                  <div className="text-[#333333] font-regular text-xs leading-[17px] h-[17px] truncate w-[61px] relative">
                    <div className="mt-[-8px]">{p.regionName}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default DustArea;
