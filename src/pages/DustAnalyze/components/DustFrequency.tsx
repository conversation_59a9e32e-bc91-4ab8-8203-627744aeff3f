import { MapView } from 'deck.gl';
import { FlyToInterpolator } from 'deck.gl/typed';
import { useAtomValue } from 'jotai';
import { useRef } from 'react';
import { useQuery } from 'react-query';
import { timeOptionsAtom } from '../atom';
import DeckGL, { DeckGLRef } from '../components/DeckGL';
import useDustFrequency from '../hooks/useDustFrequency';
import { getTextureTimePoint } from '../services';
import { DEFAULT_VIEW_STATE, handleExportChart } from '../utile';
import SubTitle from './SubTitle';

// 根据z和经纬度计算瓦片的xy编号
const getTileXY = (z: number, lon: number, lat: number) => {
  const n = Math.pow(2, z);
  const x = Math.floor(((lon + 180) / 360) * n);
  const y = Math.floor(((1 - Math.log(Math.tan((lat * Math.PI) / 180) + 1 / Math.cos((lat * Math.PI) / 180)) / Math.PI) / 2) * n);
  return { x, y };
};

const DustFrequency = () => {
  const {
    baseLayer,
    provinceGeojsonLayer,
    labelTileLayer,
    dmaskCountTileLayer,
    maskLayer
  } = useDustFrequency();
  const {
    maskLayer: maskLayerExport,
    baseLayer: baseLayerExport,
    provinceGeojsonLayer: provinceGeojsonLayerExport,
    labelTileLayer: labelTileLayerExport,
    dmaskCountTileLayer: dmaskCountTileLayerExport,
  } = useDustFrequency(true);
  const timeOptions = useAtomValue(timeOptionsAtom);
  const { data } = useQuery(['getTextureTimePoint', 'DMASK_COUNT', timeOptions], () =>
    getTextureTimePoint({
      type: 'DMASK_COUNT',
      agg: 'daily',
      startDate: timeOptions.startTime,
      endDate: timeOptions.endTime,
    }),
  );
  const ref = useRef<DeckGLRef>(null);
  const refExport = useRef<DeckGLRef>(null);
  return (
    <div className="relative w-full h-full overflow-hidden">
      <div className="w-full h-full flex flex-col overflow-hidden pt-[20px] px-[22px] pb-[18px] gap-[10px]">
        <div className="w-full h-[22px] flex">
          <div className="flex flex-1 ml-[-24px]">
            <SubTitle title="沙尘频次图" />
          </div>
          <div
            className="flex-1 flex justify-end items-center h-[22px] leading-[22px] mr-[2px] font-semibold text-[#1E315E] text-base cursor-pointer"
            onClick={() => {
              handleExportChart('dust-time-count', `${timeOptions.startTime}-${timeOptions.endTime}沙尘频次图`, () => {}, true);
            }}
          >
            <iconpark-icon size="18" name="xiazai"></iconpark-icon>
          </div>
        </div>
        <div className="flex-1 relative">
          <div
            className="absolute w-[22px] h-[124px] bottom-[12px] left-[11px] bg-[#ffffff] rounded-[2px] z-[1]"
            style={{ boxShadow: `0px 2px 4px 0px rgba(0,0,0,0.14)` }}
          >
            <div className="w-full h-[14px] text-xs text-center text-[#666666] font-regular leading-[14px]">
              {Math.max(48, data?.length as number)}
            </div>
            <div
              className="mx-[4px] w-[14px] h-[95px]"
              style={{
                background: `linear-gradient( 180deg, rgb(247, 5, 2) 0%, rgb(250, 147, 2) 33.3%, rgb(255, 254, 0) 66.7%, rgb(135, 255, 255) 100%)`,
              }}
            />
            <div className="w-full h-[14px] text-xs text-center text-[#666666] font-regular leading-[14px]">0</div>
          </div>
          <div
            className="absolute w-[29px] h-[29px] bottom-[49px] right-[12px] bg-[#ffffff] rounded-[2px] z-[1] cursor-pointer leading-[29px] text-center font-regular text-xl"
            style={{ boxShadow: `0px 2px 4px 0px rgba(0,0,0,0.14)` }}
            onClick={() => {
              ref.current?.setViewState((viewState: any) => ({
                ...viewState,
                zoom: viewState.zoom + 0.2,
                transitionInterpolator: new FlyToInterpolator(),
                transitionDuration: 100,
              }));
            }}
          >
            +
          </div>
          <div
            className="absolute w-[29px] h-[29px] bottom-[16px] right-[12px] bg-[#ffffff] rounded-[2px] z-[1] cursor-pointer leading-[29px] text-center font-regular text-xl"
            style={{ boxShadow: `0px 2px 4px 0px rgba(0,0,0,0.14)` }}
            onClick={() => {
              ref.current?.setViewState((viewState: any) => ({
                ...viewState,
                zoom: viewState.zoom - 0.2,
                transitionInterpolator: new FlyToInterpolator(),
                transitionDuration: 100,
              }));
            }}
          >
            -
          </div>
          <DeckGL
            className="w-full h-full"
            controller
            ref={ref}
            views={[new MapView({ repeat: true })]}
            initialViewState={DEFAULT_VIEW_STATE}
            layers={[
              maskLayer,
              baseLayer,
              dmaskCountTileLayer,
              provinceGeojsonLayer,
              labelTileLayer,
            ]}
          ></DeckGL>
        </div>
        <div id={'dust-time-count'} className="w-[860px] h-[598px] absolute flex flex-col z-[-1] p-[20px]">
          <div className="w-full h-[22px] flex">
            <div className='flex flex-1 ml-[-24px]'>
              <SubTitle title="沙尘频次图" />
            </div>
          </div>
          <div className="flex-1 relative mt-[20px]">
            <div className="absolute w-[22px] h-[124px] bottom-[12px] left-[11px] bg-[#ffffff] rounded-[2px] z-[1]">
              <div className="w-full h-[14px] text-xs text-center text-[#666666] font-regular leading-[14px]">
                <div className="mt-[-6px]">{Math.max(48, data?.length as number)}</div>
              </div>
              <div
                className="mx-[4px] mt-[6px] w-[14px] h-[95px]"
                style={{
                  background: `linear-gradient( 180deg, rgb(247, 5, 2) 0%, rgb(250, 147, 2) 33.3%, rgb(255, 254, 0) 66.7%, rgb(135, 255, 255) 100%)`,
                }}
              />
              <div className="w-full h-[14px] text-xs text-center text-[#666666] font-regular leading-[14px]">
                <div className="mt-[-6px]">0</div>
              </div>
            </div>
            <DeckGL
              className="w-full h-full"
              controller={false}
              ref={refExport}
              views={[new MapView({ repeat: true })]}
              initialViewState={DEFAULT_VIEW_STATE}
              layers={[
                maskLayerExport,
                baseLayerExport,
                dmaskCountTileLayerExport,
                provinceGeojsonLayerExport,
                labelTileLayerExport,
              ]}
              glOptions={{ preserveDrawingBuffer: true }}
            ></DeckGL>
          </div>
        </div>
      </div>
    </div>
  );
};
export default DustFrequency;
