import { useMemo } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import DurationAnalysis from './DurationAnalysis';
import DustArea from './DustArea';
import EventAnalysis from './EventAnalysis';
import SourceAnalysis from './SourceAnalysis';

const Right = () => {
  const { height, ref } = useResizeDetector();
  const { card1, card2, card3 } = useMemo(() => {
    if (!height || height <= 20) {
      return { card1: 0, card2: 0, card3: 0 };
    }
    const cardHeight = height - 20;
    return {
      card1: (240 / 762) * cardHeight,
      card2: (282 / 762) * cardHeight,
      card3: (240 / 762) * cardHeight,
    };
  }, [height]);
  return (
    <div className="flex flex-col w-[500px] gap-[7px]">
      <div
        className="w-full h-[181px]"
        style={{ background: "url('/assets/images/analyze/eventcard-bg.jpg')" }}
      >
        <EventAnalysis />
      </div>
      <div
        className="w-full flex-1 flex flex-col gap-[10px] overflow-hidden"
        ref={ref}
      >
        <div
          className="w-full rounded-[16px] bg-[#ffffff]"
          style={{ height: card1 }}
        >
          <DustArea />
        </div>
        <div
          className="w-full rounded-[16px] bg-[#ffffff]"
          style={{ height: card2 }}
        >
          <DurationAnalysis />
        </div>
        <div
          className="w-full rounded-[16px] bg-[#ffffff]"
          style={{ height: card3 }}
        >
          <SourceAnalysis />
        </div>
      </div>
    </div>
  );
};
export default Right;
