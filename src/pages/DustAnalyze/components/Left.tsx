import { useMemo } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import NumberOfDays from './NumberOfDays';
import RegionDustDay from './RegionDustDay';
import DustFrequency from './DustFrequency';

const Left = () => {
  const { height, width, ref } = useResizeDetector();
  const { topHeight, bottomHeight } = useMemo(() => {
    if (!height || height <= 10) return { topHeight: 0, bottomHeight: 0 };
    const topMin = 449;
    const clanderHeight = 341;
    const cardsHeight = height - 10;
    let topHeight =
      (247 / 609) * (cardsHeight - 10 - clanderHeight) + clanderHeight;
    if (topHeight < topMin) {
      topHeight = topMin;
    }
    return {
      topHeight,
      bottomHeight: cardsHeight - topHeight,
    };
  }, [height]);
  const { leftWidth, rightWidth } = useMemo(() => {
    if (!width || width <= 10) return { leftWidth: 0, rightWidth: 0 };
    const cardsWidth = width - 10;
    const leftMin = 360;
    let leftWidth = (500 / 1370) * cardsWidth;
    if (leftWidth < leftMin) {
      leftWidth = leftMin;
    }
    return {
      leftWidth,
      rightWidth: cardsWidth - leftWidth,
    };
  }, [width]);
  return (
    <div className="flex-1 flex flex-col overflow-hidden gap-[10px]" ref={ref}>
      <div
        className="w-full flex flex-row gap-[10px]"
        style={{ height: topHeight }}
      >
        <div
          className="h-full rounded-[16px] bg-[#ffffff]"
          style={{ width: leftWidth }}
        >
          <NumberOfDays />
        </div>
        <div
          className="h-full rounded-[16px] bg-[#ffffff]"
          style={{ width: rightWidth }}
        >
          <DustFrequency />
        </div>
      </div>
      <div
        className="w-full flex rounded-[16px] bg-[#ffffff]"
        style={{ height: bottomHeight }}
      >
        <RegionDustDay />
      </div>
    </div>
  );
};
export default Left;
