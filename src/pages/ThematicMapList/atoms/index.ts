import { atom } from 'jotai';

export interface FetchParams {
  createType?: number;
  cycleType?: string;
  endDate?: string;
  name?: string;
  page?: number;
  regionCode?: number;
  size?: number;
  startDate?: string;
  status?: number;
  uid?: number;
}

export const filtersAtom = atom({
  createType: undefined,
  cycleType: undefined,
  endDate: undefined,
  name: '',
  page: 0,
  regionCode: undefined,
  size: 10,
  startDate: undefined,
  status: undefined,
  uid: undefined,
});
