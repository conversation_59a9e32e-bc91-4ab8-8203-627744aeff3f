import type { ColumnsType } from 'antd/lib/table';
import { Link } from 'react-router-dom';
import { typeOpts } from '../components/Filter';
import { TableLink } from '../components/ui';
import type { TmListItem } from '../types';

interface TableActions {
  deleteAction: (id: number) => void;
  editAction: (id: number) => void;
  copyAction: (id: number) => void;
  renameAction: (item: TmListItem) => void;
}

export const thematicMapColumns: (
  actions: TableActions,
  userId: number | undefined,
) => ColumnsType<TmListItem> = (
  { deleteAction, copyAction, editAction, renameAction },
  userId,
) => [
  {
    title: '序号',
    render(_text, _record, index) {
      return `${index + 1}`;
    },
  },

  {
    title: '专题图名称',
    dataIndex: 'name',
    render(text, record) {
      return (
        <Link
          className="text-primary"
          to={`/thematic-map?id=${record.id}&type=view`}
        >
          {text}
        </Link>
      );
    },
  },
  {
    title: '行政区域',
    dataIndex: 'region',
  },
  {
    title: '数据类型',
    dataIndex: 'cycleType',
    render(text) {
      const find = typeOpts.find((opt) => opt.value === text);
      return find?.label || '-';
    },
  },
  {
    title: '生成方式',
    dataIndex: 'createType',
    render(text) {
      return text === 1 ? '自动生成' : '手动生成';
    },
  },
  {
    title: '创建者',
    dataIndex: 'userName',
    render(text) {
      return text;
    },
  },
  {
    title: '数据时间',
    render(record) {
      const { fromDate, toDate } = record;
      return fromDate && toDate ? `${fromDate}-${toDate}` : '-';
    },
  },
  {
    title: '更新时间',
    dataIndex: 'modifyTime',
  },
  {
    title: '操作',
    width: 400,
    render(_t, record) {
      const isMine = userId === record.userId;
      return (
        <>
          <TableLink color="primary" onClick={() => copyAction(record.id)}>
            <i className="icomoon icon-copy" />
            复制
          </TableLink>
          <TableLink
            color="primary"
            disabled={!isMine}
            onClick={() => {
              if (isMine) {
                renameAction(record);
              }
            }}
          >
            <i className="icomoon icon-fillin" /> 重命名
          </TableLink>
          <TableLink
            color="primary"
            disabled={record.createType === 1 || !isMine}
            onClick={() => {
              if (record.createType === 2 && isMine) {
                editAction(record.id);
              }
            }}
          >
            <i className="icomoon icon-edit" />
            编辑
          </TableLink>
          <TableLink
            disabled={!isMine}
            isDanger
            onClick={() => {
              if (isMine) {
                deleteAction(record.id);
              }
            }}
          >
            <i className="icomoon icon-trash" /> 删除
          </TableLink>
        </>
      );
    },
  },
];
