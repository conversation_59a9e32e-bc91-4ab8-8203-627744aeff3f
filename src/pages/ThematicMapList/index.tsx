import React, { useCallback, useMemo } from 'react';
import { message, Table } from 'antd';
import { PageMain } from '@/components/ui';
import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { thematicMapColumns } from './tableCols';
import { useRouter } from '@/hooks';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useState } from 'react';
import { deleteTm, getTmList, renameTm } from '../ThematicMap/services';
import type { ApiListData } from '@/utils/types';
import type { TmListItem } from './types';
import { Modal } from 'antd';
import NameTmModal from '../ThematicMap/components/NameTmModal';
import Filter from './components/Filter';
import { PageTabItem, PageTabs } from './components/ui';
import { filtersAtom } from './atoms';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { userInfoAtom } from '@/atoms';

const ThematicMapList = () => {
  const userInfo = useAtomValue(userInfoAtom);
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const [params, setParams] = useAtom(filtersAtom);
  const [current, setCurrent] = useState<TmListItem | null>(null);
  const [visible, setVisible] = useState(false);
  const deleteMutation = useMutation((id: number) => deleteTm(id));
  const renameMutation = useMutation((p: { id: number; name: string }) =>
    renameTm(p),
  );
  const { data, isLoading } = useQuery<ApiListData<TmListItem>>(
    ['tm-list', params],
    () => getTmList(params),
    {
      enabled: Boolean(params.regionCode),
      staleTime: 0,
      cacheTime: 0,
    },
  );
  const tableColumns = useMemo(
    () =>
      thematicMapColumns(
        {
          deleteAction: (id) => {
            Modal.confirm({
              title: '确定要删除此专题图吗？',
              onOk() {
                deleteMutation.mutate(id, {
                  onSuccess() {
                    message.success('操作成功');
                    queryClient.invalidateQueries(['tm-list', params]);
                  },
                });
              },
            });
          },
          editAction: (id) => {
            push(`/thematic-map?id=${id}&type=edit`);
          },
          copyAction: (id) => {
            push(`/thematic-map?id=${id}&type=copy`);
          },
          renameAction: (item) => {
            setCurrent(item);
            setVisible(true);
          },
        },
        userInfo?.id,
      ),
    [deleteMutation, params, push, queryClient, userInfo?.id],
  );

  const rename = useCallback(
    (name: string) => {
      if (current) {
        renameMutation.mutate(
          {
            name,
            id: current.id,
          },
          {
            onSuccess() {
              message.success('操作成功');
              queryClient.invalidateQueries(['tm-list', params]);
              setVisible(false);
            },
          },
        );
      }
    },
    [current, params, queryClient, renameMutation],
  );

  return (
    <PageMain>
      <HelmetTitle title="专题产品" />
      <PageHead title="专题图">
        <span>专题图</span>
        <i className="icomoon icon-next" />
        <span className="text-primary">专题图列表</span>
      </PageHead>
      <PageTabs>
        <PageTabItem to="/thematic-map-list" active={1}>
          专题图
        </PageTabItem>
        <PageTabItem to="/thematic-map-tpl-list">模板</PageTabItem>
      </PageTabs>
      <Filter />
      <Table
        columns={tableColumns}
        loading={isLoading}
        rowKey="id"
        dataSource={data?.content || []}
        pagination={{
          size: 'small',
          position: ['bottomCenter'],
          current: (data?.pageable.pageNumber || 0) + 1,
          pageSize: data?.pageable.pageSize || 10,
          total: data?.totalElements || 0,
        }}
        onChange={({ current: currentPage, pageSize }) => {
          setParams((prev) => ({
            ...prev,
            page: (currentPage || 1) - 1,
            size: pageSize || 10,
          }));
        }}
      />
      <NameTmModal
        visible={visible}
        name={current?.name}
        onOk={rename}
        onCancel={() => setVisible(false)}
      />
    </PageMain>
  );
};

export default ThematicMapList;
