import { userInfoAtom } from '@/atoms';
import { OutlinedButton, Spacer } from '@/components/ui';
import { useCascaderOptionsAndMatchValues, useMatchCascaderValue, useRouter } from '@/hooks';
import { getTmUserList } from '@/pages/ThematicMap/services';
import type { User } from '@/types';
import { dateFormatter } from '@/utils';
import type { ApiListData } from '@/utils/types';
import { SearchOutlined } from '@ant-design/icons';
import { Cascader, Col, DatePicker, Input, Select } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { filtersAtom } from '../atoms';
import { FilterRow } from './ui';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

export const typeOpts = [
  {
    label: '日均值',
    value: 'daily',
  },
  {
    label: '周均值',
    value: 'weekly',
  },
  {
    label: '月均值',
    value: 'monthly',
  },
  {
    label: '季均值',
    value: 'quarterly',
  },
  {
    label: '年均值',
    value: 'yearly',
  },
];
const generateOpts = [
  {
    label: '自动生成',
    value: 1,
  },
  {
    label: '手动生成',
    value: 2,
  },
];

const Filter = () => {
  const userInfo = useAtomValue(userInfoAtom);
  const { history } = useRouter();
  const [filters, setFilters] = useAtom(filtersAtom);
  const { regionCode } = filters;
  // const { options } = useCascaderOptionsAndMatchValues(userInfo?.regionCode);
  // const cascaderValue = useMatchCascaderValue(Number(filters.regionCode));

  const { options, cascaderValue } = useCascaderOptionsAndMatchValues(
    Number(regionCode), true
  );

  const updateFilters = useCallback(
    (newVal: Record<string, string | number | undefined>) => {
      setFilters((prev) => ({
        ...prev,
        ...newVal,
        page: 0,
      }));
    },
    [setFilters],
  );
  const { name, createType, cycleType, uid, startDate, endDate } = filters;
  const { data: users } = useQuery(
    ['user-list-in-select', userInfo?.regionCode],
    () => getTmUserList(Number(userInfo?.regionCode)),
    {
      enabled: Boolean(userInfo?.regionCode),
    },
  );

  const userOptions = useMemo(() => {
    if (users) {
      return users?.map((item: any) => ({
        label: item.userName,
        value: item.userId,
      }));
    }
    return [];
  }, [users]);

  const cascaderOptions = useMemo(() => {
    return [
      ...options,
      // {
      //   code: 100000,
      //   name: '全域',
      //   disabled: userInfo?.regionCode === 150000 ? false : true
      // },
    ];
  }, [options]);

  const showCascaderValue = useMemo(() => {
    return regionCode ? regionCode === 100000 ? regionCode : cascaderValue : undefined;
  }, [cascaderValue, regionCode])

  useEffect(() => {
    if (userInfo?.regionCode&&!regionCode) {
      updateFilters({
        regionCode: userInfo.regionCode,
      });
    }
  }, [regionCode, updateFilters, userInfo?.regionCode]);

  return (
    <FilterRow gutter={12}>
      <Col>
        <Input
          prefix={<SearchOutlined />}
          placeholder="请输入专题图名称"
          value={name}
          onChange={(e) => {
            updateFilters({
              name: e.target.value,
            });
          }}
        />
      </Col>
      <Col>
        <Cascader
          style={{ width: 240 }}
          changeOnSelect
          options={cascaderOptions}
          value={showCascaderValue}
          fieldNames={{
            label: 'name',
            value: 'code',
          }}
          onChange={(val) => {
            updateFilters({
              regionCode:
                val?.length > 0 ? +val[val?.length - 1] : userInfo!.regionCode,
            });
          }}
        />
      </Col>
      <Col>
        <Select
          style={{ width: 140 }}
          options={typeOpts}
          allowClear
          placeholder="周期类型"
          value={cycleType}
          onChange={(val) => {
            updateFilters({
              cycleType: val,
            });
          }}
        />
      </Col>
      <Col>
        <Select
          style={{ width: 140 }}
          allowClear
          options={generateOpts}
          placeholder="生成方式"
          value={createType}
          onChange={(val) => {
            updateFilters({
              createType: val,
            });
          }}
        />
      </Col>
      <Col>
        <Select
          style={{ width: 140 }}
          value={uid}
          allowClear
          options={userOptions}
          placeholder="创建者"
          onChange={(val) => {
            updateFilters({
              uid: val,
            });
          }}
        />
      </Col>
      <Col>
        <RangePicker
          style={{ width: 400 }}
          allowClear
          value={startDate && endDate ? [dayjs(filters.startDate), dayjs(filters.endDate)] : undefined}
          onChange={(val) => {
            if (val && val.length === 2) {
              updateFilters({
                startDate: dayjs(val[0]).format(dateFormatter),
                endDate: dayjs(val[1]).format(dateFormatter),
              });
            } else {
              updateFilters({
                startDate: undefined,
                endDate: undefined,
              });
            }
          }}
          disabledDate={(current) => {
            return current && current.isAfter(dayjs().endOf('day'));
          }}
        />
      </Col>
      <Spacer />
      <OutlinedButton
        onClick={() => {
          history.push('/thematic-map');
        }}
      >
        创建专题图
      </OutlinedButton>
    </FilterRow>
  );
};

export default Filter;
