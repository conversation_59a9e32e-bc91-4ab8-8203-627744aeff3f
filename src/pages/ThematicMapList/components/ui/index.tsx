import { Center, Flex } from '@/components/ui';
import { Row } from 'antd';
import styled, { css } from 'styled-components';
import { Link } from 'react-router-dom';
import { getColorFromTheme } from '@/components/ui/utils';

export const BtnRow = styled(Flex)`
  padding: 24px 0;
  justify-content: flex-end;
`;

export const FilterRow = styled(Row)`
  margin: 32px 0 20px;
`;

export const PageTabs = styled(Flex)`
  margin-top: 40px;
  border-bottom: 1px solid #e6e6e6;
`;

export const PageTabItem = styled(Link)<{
  active?: number;
}>`
  position: relative;
  display: block;
  margin-right: 30px;
  padding: 0 20px 6px;
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  border-bottom: 3px solid transparent;

  ${({ active }) =>
    active &&
    css`
      pointer-events: none;
    `}

  ${(props) => {
    return props.active
      ? css`
          color: ${getColorFromTheme('primary')};
          border-color: ${getColorFromTheme('primary')};
        `
      : null;
  }}
`;

export const TableLink = styled(Center)<{
  isDanger?: boolean;
  disabled?: boolean;
}>`
  display: inline-flex;
  font-size: 14px;
  color: ${({ isDanger, disabled }) => {
    if (disabled) {
      return '#999';
    }

    return isDanger ? '#f44336' : '';
  }};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};

  &:hover {
    color: ${({ theme, isDanger, disabled }) => {
      if (disabled) {
        return '#999';
      }
      return isDanger ? '#f4436' : theme.colors.primary;
    }};
  }

  .icomoon {
    margin-right: 8px;
  }

  & + & {
    margin-left: 30px;
  }
`;
