import { atomWithReset } from "jotai/utils";
import { ReportListParams } from "../types";

export const filtersAtom = atomWithReset<ReportListParams>({
  name:undefined,
  reportType: undefined,
  uid: undefined,
  regionCode: undefined,
  startDate: undefined,
  endDate: undefined,
  createStartDate: undefined,
  createEndDate: undefined,
  page: 0,
  size: 10,
});

export const regionCascaderValueAtom = atomWithReset<number[] | undefined>(undefined);

export const deleteLoadingAtom = atomWithReset<boolean>(false);