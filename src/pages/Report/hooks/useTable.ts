import { useMutation, useQuery, useQueryClient } from 'react-query';
import type { TablePaginationConfig } from 'antd';
import { message, Modal } from 'antd';
import { useCallback, useMemo } from 'react';
import { userTableColumns } from '../tableCols';
import { deleteLoadingAtom, filtersAtom } from '../atom';
import { useAtom, useAtomValue } from 'jotai';
import { userInfoAtom } from '@/atoms';
import { useRouter } from '@/hooks';
import { deleteReport, getReportList, reportDownloadView } from '../services';
import { exportFile } from '@/utils';

export const useTable = () => {
  const { history } = useRouter();
  const [filters, setFilters] = useAtom(filtersAtom);
  const userInfo = useAtomValue(userInfoAtom);
  const queryClient = useQueryClient();
  const [deleteLoading, setdeleteLoading] = useAtom(deleteLoadingAtom)

  const { data, isLoading } = useQuery(
    ['list', filters],
    () => getReportList(filters),
    {
      staleTime: 0,
      cacheTime: 0,
      refetchInterval: 40*1000*1, 
    }
  );

  // 根据 data 计算出 pagination
  const pagination: TablePaginationConfig = useMemo(
    () => ({
      current: (data?.pageable.pageNumber || 0) + 1,
      total: data?.totalElements,
      size: 'small',
      showTotal: (total) => `共 ${total} 条 记录`,

      position: ['bottomCenter'],
      showSizeChanger: false,
      showQuickJumper: true,
      style: {
        marginTop: 40,
      },
    }),
    [data],
  );

  const changePage = ({ current: currentPage, pageSize }: TablePaginationConfig) => {
    setFilters((prev) => ({
      ...prev,
      page: (currentPage || 1) - 1,
      size: pageSize || 10,
    }));

  };

  const deleteCallback = useCallback(() => {
    getReportList({
      ...filters,
    }).then((result) => {
      setFilters((p) => ({
        ...p,
        page: result.totalPages > 0 && filters.page && filters.page > result.totalPages - 1 ? result.totalPages - 1 : filters.page
      }))
      queryClient.invalidateQueries('list');
    })

  }, [filters, queryClient, setFilters]);

  const deleteMutation = useMutation((id: number) => deleteReport(id), {
    onSuccess: () => {
      message.success('删除成功');
      deleteCallback();
      setdeleteLoading(false);
    },
    onError: () => {
      setdeleteLoading(false);
    }
  });

  const downloadReport = useCallback((id: number, name?: string) => {
    reportDownloadView(id).then((res) => {
      exportFile(res, name || '报告', '.docx');
    })
  }, [])

  const columns = userTableColumns({
    handleViewReport: (reportId, id, name,isCurrentUser) => {
      history.push(`/report-detail?reportId=${reportId}&id=${id}&name=${name}&isCurrentUser=${isCurrentUser}`);
    },
    downloadReport: (id, name) => {
      downloadReport(id, name);
    },
    deleteReport: (id, userId) => {
      Modal.confirm({
        title: '确定删除此报告吗？',
        onOk() {
          setdeleteLoading(true);
          deleteMutation.mutate(id)
        },
      });
    },
    userInfoId: userInfo?.id || 0,
    deleteLoading
  });

  return {
    data,
    isLoading,
    pagination,
    columns,
    changePage
  };
};
