import { request } from '@/utils';
import { stringify } from 'qs';
import { ReportListParams } from '../types';

export const getReportList = (params: ReportListParams) => {
	return request(`/api/analysis/report/list?${stringify(params)}`)
}
export const deleteReport = (id: number) =>
	request(`/api/analysis/report/delete?id=${id}`, {
		method: 'POST',
	});

export const getUserList = () => {
	return request(`/api/user/noPageList`)
}
export const reportDownloadView = (id: number) => {
	return request(`/api/file?type=permanent&id=${id}`, {
		method: 'GET',
	})
}

export const reportview = (params: { fileId: number, type: string }) =>
	request(`/api/analysis/report/preview?fileId=${params.fileId}&type=${params.type}`, {
		method: 'POST',
	});



