import { TableActionLink } from '@/components/ui';
import { Tooltip } from 'antd';
import type { ColumnType } from 'antd/lib/table';
import dayjs from 'dayjs';

interface TableActions {
  handleViewReport: (reportId: number, id: number, name: string, isCurrentUser: boolean) => void;
  downloadReport: (id: number, name: string) => void;
  deleteReport: (id: number, userId: number) => void;
  userInfoId: number;
  deleteLoading: boolean;
}

export const userTableColumns: (actions: TableActions) => ColumnType[] =
  ({ handleViewReport, downloadReport, deleteReport, userInfoId, deleteLoading }) => [
    {
      title: '报告名称',
      dataIndex: 'name',
      render(text, record) {
        return <div className=''>
          <span>{text}</span>
          {
            record.status === 1 && <span className='bg-[#DCE7FF] text-[#286CFF] rounded-[15px] text-[12px] ml-[4px] px-[4px] py-[4px]'>AI分析中...</span>
          }
          {
            record.status === 3 && <span className='bg-[#DCE7FF] text-[#f44336] rounded-[15px] text-[12px] ml-[4px] px-[4px] py-[4px]'>AI分析失败</span>
          }
        </div>
      },
    },
    {
      title: '数据时间',
      render(text, record) {
        return <span>{record?.startDate && `${dayjs(record?.startDate).format('YYYY/MM/DD HH')} 时`}-{record?.endDate && `${dayjs(record?.endDate).format('YYYY/MM/DD HH')} 时`}</span>;
      },
    },
    {
      title: '行政区域',
      dataIndex: 'regionName',
    },

    {
      title: '报告类型',
      dataIndex: 'reportType',
      render(reportType) {
        return reportType === 1 ? '沙尘' : '大气颗粒物';
      },
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      render(createTime) {
        return <span>{createTime && dayjs(createTime).format('YYYY/MM/DD HH:mm') || '-'}</span>;
      },
    },
    {
      title: '创建者',
      dataIndex: 'createUser',
      render(createUser) {
        return <span>{createUser}</span>;
      },
    },

    {
      title: '操作',
      width: 200,
      render(_t, record) {
        const { userId, id, regionCode, fileUrl, status, name } = record
        const reportId = fileUrl ? fileUrl.split('id=')[1] : ''
        const isCurrentUser = userId === userInfoId;
        const hasReport = status === 2
        const imgType = isCurrentUser ? '1' : '2';
        return (
          <div className='flex items-center gap-x-[8px]'>
            <TableActionLink
              // color="primary"
              onClick={() => {
                if (!hasReport) return;
                handleViewReport(reportId, id, name, isCurrentUser);
              }}
              className='flex items-center gap-x-[4px]'
              disabled={!hasReport}
            >
              <img src={`/assets/images/report/view-${hasReport ? 1 : 2}.png`} alt="" style={{ width: '14px', height: '14px' }} />
              <span> 查看</span>
            </TableActionLink>
            {/* <TableActionLink
              className='flex items-center gap-x-[4px]'
              onClick={() => {
                if (!hasReport) return;
                downloadReport(reportId, name)
              }}
              disabled={!hasReport}
            >
              <img src={`/assets/images/report/download-${hasReport ? 1 : 2}.png`} alt="" style={{ width: '14px', height: '14px' }} />
              <span> 导出</span>
            </TableActionLink> */}
            <TableActionLink
              color="danger"
              onClick={() => {
                if (!isCurrentUser || deleteLoading) return;
                deleteReport(id, userId);
              }}
              className='flex items-center gap-x-[4px]'
              disabled={!isCurrentUser || deleteLoading}
            >
              <img src={`/assets/images/report/delete-${deleteLoading ? 2 : imgType}.png`} alt="" style={{ width: '14px', height: '14px' }} />
              <span> 删除</span>
            </TableActionLink>

          </div>
        );
      },
    },
  ];
