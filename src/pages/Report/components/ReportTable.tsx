import { Table } from 'antd';
import { useTable } from '../hooks/useTable';
const ReportTable = () => {
  const { data, isLoading, pagination, columns, changePage } =
    useTable();
  return (
    <div className='mt-[10px]'>
      <Table
        rowKey="id"
        loading={isLoading}
        columns={columns}
        dataSource={data?.content}
        pagination={pagination}
        onChange={({ current: currentPage, pageSize }) => {
          changePage({ current: currentPage, pageSize });
        }}
      />
    </div>
  );
};

export default ReportTable;
