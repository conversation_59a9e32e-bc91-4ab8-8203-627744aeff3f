
import { SearchOutlined } from '@ant-design/icons';
import { Row, Cascader, Col, Input, Select, DatePicker } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import { filtersAtom } from '../atom';
import { useQuery } from 'react-query';
import { getUserList } from '../services';
import dayjs from 'dayjs';
import { userInfoAtom } from '@/atoms';

const { RangePicker } = DatePicker;

export const reporttypeOptions = [
  {
    label: '大气颗粒物',
    value: 2,
  },
  {
    label: '沙尘',
    value: 1,
  }
]


const Filter = () => {
  const [filters, setFilters] = useAtom(filtersAtom);
  const { name, uid, reportType, regionCode } = filters;
  const userInfo = useAtomValue(userInfoAtom);
  const updateFilters = useCallback(
    (newVal: Record<string, string | number | undefined>) => {
      setFilters((prev) => ({
        ...prev,
        ...newVal,
        page: 0,
      }));
    },
    [setFilters],
  );

  const filterDomWidth = useMemo(() => {
    return {
      width: '254px'
    }
  }, []) as React.CSSProperties

  const { options, cascaderValue } = useCascaderOptionsAndMatchValues(
    Number(regionCode), true
  );

  const cascaderOptions = useMemo(() => {
    return [
      ...options,
      {
        code: 100000,
        name: '全域',
        disabled: userInfo?.regionCode === 150000 ? false : true
      },
    ];
  }, [options, userInfo?.regionCode]);

  const { data: userList } = useQuery(
    ['userList'],
    () => getUserList(),
  )

  const showCascaderValue = useMemo(() => {
    return regionCode ? regionCode === 100000 ? regionCode : cascaderValue : undefined;
  }, [cascaderValue, regionCode])

  // 数据监听
  useEffect(() => {
    if (userInfo?.regionCode&&!regionCode) {
      updateFilters({ regionCode: userInfo?.regionCode });
    }
  }, [regionCode, updateFilters, userInfo?.regionCode]);

  return (
    <Row gutter={12}>
      <Col className='mb-[10px]'>
        <Input
          style={filterDomWidth}
          allowClear
          // @ts-ignore
          prefix={<SearchOutlined />}
          placeholder="请输入报告名称"
          value={name}
          onChange={(e) => {
            updateFilters({
              name: e.target.value,
            });
          }}
        />
      </Col>
      <Col className='mb-[10px]'>
        <RangePicker
          className=""
          style={filterDomWidth}
          placeholder={['数据开始', '数据结束']}
          format="YYYY/MM/DD"
          onChange={(dates, dateStrings) => {
            updateFilters({
              startDate: dates ? dateStrings[0] : undefined,
              endDate: dates ? dateStrings[1] : undefined,
            })
          }}
          disabledDate={(current) => {
            return current && current.isAfter(dayjs().endOf('day'));
          }}
        />
      </Col>

      <Col className='mb-[10px]'>
        <Cascader
          style={filterDomWidth}
          changeOnSelect
          options={cascaderOptions}
          value={showCascaderValue}
          fieldNames={{
            label: 'name',
            value: 'code',
          }}
          placeholder="行政区域"
          onChange={(val) => {
            updateFilters({
              // regionCode: last,
              regionCode:
                val?.length > 0 ? +val[val?.length - 1] : userInfo!.regionCode,
              page: 0,
            });
          }}
        />
      </Col>
      <Col className='mb-[10px]'>
        <Select
          style={filterDomWidth}
          options={reporttypeOptions}
          allowClear
          placeholder="报告类型"
          optionFilterProp="label"
          fieldNames={{
            label: 'label',
            value: 'value',
          }}
          value={reportType}
          onChange={(val) => {
            updateFilters({
              reportType: val || undefined,
            });
          }}
        />
      </Col>
      <Col className='mb-[10px]'>
        <RangePicker
          className=""
          style={filterDomWidth}
          placeholder={['生成开始', '生成结束']}
          format="YYYY/MM/DD"
          onChange={(dates, dateStrings) => {
            updateFilters({
              createStartDate: dates ? `${dateStrings[0]} 00:00:00` : undefined,
              createEndDate: dates ? `${dateStrings[1]} 23:59:59` : undefined,
            })
          }}
          disabledDate={(current) => {
            return current && current.isAfter(dayjs().endOf('day'));
          }}
        />
      </Col>

      <Col className='mb-[10px]'>
        <Select
          style={filterDomWidth}
          options={userList || []}
          allowClear
          fieldNames={{
            label: 'name',
            value: 'id',
          }}
          placeholder="请选择创建者"
          value={uid}
          onChange={(val) => {
            updateFilters({
              uid: val || undefined,
            });
          }}
        />
      </Col>

    </Row>
  );
};

export default Filter;
