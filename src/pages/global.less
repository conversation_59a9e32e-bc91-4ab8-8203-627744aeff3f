#index {
  color: #c1c1c4;

  .avatar-overlay {
    .ant-dropdown-arrow {
      right: 28px;
    }
  }

  .ant-picker-cell-in-view.ant-picker-cell-in-range::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before,
  .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner::after,
  .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner::after {
    background: #111b26;
  }

  .ant-picker-calendar {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    background: #141414;
    font-feature-settings: 'tnum';
  }
  .ant-picker-calendar-header {
    display: flex;
    justify-content: flex-end;
    padding: 12px 0;
  }
  .ant-picker-calendar-header .ant-picker-calendar-year-select {
    min-width: 80px;
  }
  .ant-picker-calendar-header .ant-picker-calendar-month-select {
    min-width: 70px;
    margin-left: 8px;
  }
  .ant-picker-calendar-header .ant-picker-calendar-mode-switch {
    margin-left: 8px;
  }
  .ant-picker-calendar .ant-picker-panel {
    background: #141414;
    border: 0;
    border-top: 1px solid #303030;
    border-radius: 0;
  }
  .ant-picker-calendar .ant-picker-panel .ant-picker-month-panel,
  .ant-picker-calendar .ant-picker-panel .ant-picker-date-panel {
    width: auto;
  }
  .ant-picker-calendar .ant-picker-panel .ant-picker-body {
    padding: 8px 0;
  }
  .ant-picker-calendar .ant-picker-panel .ant-picker-content {
    width: 100%;
  }
  .ant-picker-calendar-mini {
    border-radius: 2px;
  }
  .ant-picker-calendar-mini .ant-picker-calendar-header {
    padding-right: 8px;
    padding-left: 8px;
  }
  .ant-picker-calendar-mini .ant-picker-panel {
    border-radius: 0 0 2px 2px;
  }
  .ant-picker-calendar-mini .ant-picker-content {
    height: 256px;
  }
  .ant-picker-calendar-mini .ant-picker-content th {
    height: auto;
    padding: 0;
    line-height: 18px;
  }
  .ant-picker-calendar-full .ant-picker-panel {
    display: block;
    width: 100%;
    text-align: right;
    background: #141414;
    border: 0;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-body th,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-body td {
    padding: 0;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-body th {
    height: auto;
    padding: 0 12px 5px 0;
    line-height: 18px;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell::before {
    display: none;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell:hover .ant-picker-calendar-date {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell .ant-picker-calendar-date-today::before {
    display: none;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date-today,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date-today {
    background: #111b26;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date .ant-picker-calendar-date-value,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date .ant-picker-calendar-date-value,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date-today .ant-picker-calendar-date-value,
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date-today .ant-picker-calendar-date-value {
    color: #286cff;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date {
    display: block;
    width: auto;
    height: auto;
    margin: 0 4px;
    padding: 4px 8px 0;
    border: 0;
    border-top: 2px solid #303030;
    border-radius: 0;
    transition: background 0.3s;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-value {
    line-height: 24px;
    transition: color 0.3s;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-content {
    position: static;
    width: auto;
    height: 86px;
    overflow-y: auto;
    color: rgba(255, 255, 255, 0.85);
    line-height: 1.5715;
    text-align: left;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-today {
    border-color: #286cff;
  }
  .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-today .ant-picker-calendar-date-value {
    color: rgba(255, 255, 255, 0.85);
  }
  @media only screen and (max-width: 480px) {
    .ant-picker-calendar-header {
      display: block;
    }
    .ant-picker-calendar-header .ant-picker-calendar-year-select {
      width: 50%;
    }
    .ant-picker-calendar-header .ant-picker-calendar-month-select {
      width: calc(50% - 8px);
    }
    .ant-picker-calendar-header .ant-picker-calendar-mode-switch {
      width: 100%;
      margin-top: 8px;
      margin-left: 0;
    }
    .ant-picker-calendar-header .ant-picker-calendar-mode-switch > label {
      width: 50%;
      text-align: center;
    }
  }
  .ant-picker-calendar-rtl {
    direction: rtl;
  }
  .ant-picker-calendar-rtl .ant-picker-calendar-header .ant-picker-calendar-month-select {
    margin-right: 8px;
    margin-left: 0;
  }
  .ant-picker-calendar-rtl .ant-picker-calendar-header .ant-picker-calendar-mode-switch {
    margin-right: 8px;
    margin-left: 0;
  }
  .ant-picker-calendar-rtl.ant-picker-calendar-full .ant-picker-panel {
    text-align: left;
  }
  .ant-picker-calendar-rtl.ant-picker-calendar-full .ant-picker-panel .ant-picker-body th {
    padding: 0 0 5px 12px;
  }
  .ant-picker-calendar-rtl.ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-content {
    text-align: right;
  }
}

#view-default-view {
  z-index: 20;
}

.dark-cascader-dropdown {
  .ant-select {
    color: rgba(255, 255, 255, 0.85);
  }
  &.ant-select-dropdown {
    color: rgba(255, 255, 255, 0.85);

    background-color: #1f1f1f;
   
    .ant-cascader-menu-item-content{
      color:#fff !important;
    }

    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  .ant-select-dropdown-hidden {
    display: none;
  }
  .ant-select-item {
    position: relative;
    display: block;
    min-height: 32px;
    padding: 5px 12px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    transition: background 0.3s ease;
  }
  .ant-menu {
    box-sizing: border-box;
    margin: 0;
    margin-bottom: 0;
    padding: 0;
    padding-left: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    line-height: 0;
    text-align: left;
    list-style: none;
    background: #141414;
    outline: 0;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
    transition: background 0.3s, width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;
    font-feature-settings: 'tnum';
  }
  .ant-menu::before {
    display: table;
    content: '';
  }
  .ant-menu::after {
    display: table;
    clear: both;
    content: '';
  }
  .ant-menu ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .ant-menu-hidden {
    display: none;
  }
  .ant-menu-item:active {
    background: #111b26;
  }
  .ant-menu-item a {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-menu-item a:hover {
    color: #177ddc;
  }
  .ant-menu-item a::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: transparent;
    content: '';
  }
  .ant-menu-item {
    position: relative;
    display: block;
    margin: 0;
    padding: 0 20px;
    white-space: nowrap;
    cursor: pointer;
    transition: border-color 0.3s, background 0.3s, padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  .ant-menu-item .ant-menu-item-icon,
  .ant-menu-item .anticon {
    min-width: 14px;
    font-size: 14px;
    transition: font-size 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), color 0.3s;
  }
  .ant-menu-item .ant-menu-item-icon + span,
  .ant-menu-item .anticon + span {
    margin-left: 10px;
    opacity: 1;
    transition: opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), margin 0.3s, color 0.3s;
  }
  .ant-menu-item .ant-menu-item-icon.svg {
    vertical-align: -0.125em;
  }
  .ant-menu-item:focus-visible {
    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
  }
  .ant-menu.ant-menu-dark {
    color: rgba(255, 255, 255, 0.65);
    background: #1f1f1f;
  }
  .ant-menu-dark .ant-menu-item,
  .ant-menu-dark .ant-menu-item > a,
  .ant-menu-dark .ant-menu-item > span > a {
    color: rgba(255, 255, 255, 0.65);
  }
  .ant-menu-dark .ant-menu-item-active,
  .ant-menu-dark .ant-menu-item:hover {
    color: #fff;
    background-color: transparent;
  }
  .ant-menu-dark .ant-menu-item-active > a,
  .ant-menu-dark .ant-menu-item-active > span > a,
  .ant-menu-dark .ant-menu-item:hover > a,
  .ant-menu-dark .ant-menu-item:hover > span > a {
    color: #fff;
  }
  .ant-menu-dark .ant-menu-item:hover {
    background-color: transparent;
  }
  .ant-dropdown {
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 1050;
    display: block;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
  }
  .ant-dropdown::before {
    position: absolute;
    top: -4px;
    right: 0;
    bottom: -4px;
    left: -7px;
    z-index: -9999;
    opacity: 0.0001;
    content: ' ';
  }
  .ant-dropdown-hidden,
  .ant-dropdown-menu-hidden {
    display: none;
  }
  .ant-dropdown-menu {
    position: relative;
    margin: 0;
    padding: 4px 0;
    text-align: left;
    list-style-type: none;
    background-color: #1f1f1f;
    background-clip: padding-box;
    border-radius: 2px;
    outline: 0;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  .ant-dropdown-menu-item {
    position: relative;
    display: flex;
    align-items: center;
  }
  .ant-dropdown-menu-item-icon {
    min-width: 12px;
    margin-right: 8px;
    font-size: 12px;
  }
  .ant-dropdown-menu-title-content {
    flex: auto;
  }
  .ant-dropdown-menu-title-content > a {
    color: inherit;
    transition: all 0.3s;
  }
  .ant-dropdown-menu-title-content > a:hover {
    color: inherit;
  }
  .ant-dropdown-menu-title-content > a::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    content: '';
  }
  .ant-dropdown-menu-item {
    clear: both;
    margin: 0;
    padding: 5px 12px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s;
  }
  .ant-dropdown-menu-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
  .ant-dropdown-menu-dark,
  .ant-dropdown-menu-dark .ant-dropdown-menu {
    background: #1f1f1f;
  }
  .ant-dropdown-menu-dark .ant-dropdown-menu-item,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item > a {
    color: rgba(255, 255, 255, 0.65);
  }
  .ant-dropdown-menu-dark .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a:hover,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item > a:hover {
    color: #fff;
    background: 0 0;
  }
  .ant-cascader {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-cascader-menus {
    background: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }

  .ant-cascader-menu {
    border-right: 1px solid #303030;
  }

  .ant-cascader-menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    background-color: #111b26;
  }

  .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon {
    color: rgba(255, 255, 255, 0.45);
  }
}

.ant-cascader-menu:empty {
  display: none !important;
}

// 新增样式

/* 滚动条美化 */
.scrollbar {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}
/*兼容safari*/
.scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02);
}

.scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.ant-switch-xs.ant-switch .ant-switch-handle {
  width: 10px;
  height: 10px;
}
.ant-switch-xs.ant-switch.ant-switch-checked .ant-switch-handle {
  inset-inline-start: calc(100% - 12px);
}
.ant-switch-xs {
  width: 28px !important;
  min-width: 28px;
  height: 14px !important;
}

.scrollbarDustDetail {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
  }

  &::-webkit-scrollbar-thumb {
    background: #69696d;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #69696d;
  }
}
/*兼容safari*/
.scrollbarDust::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbarDust::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02);
}

.scrollbarDust::-webkit-scrollbar-thumb {
  background: #69696d;
  border-radius: 2px;
}

.scrollbarDust::-webkit-scrollbar-thumb:hover {
  background: #69696d;
}

.monitor-spin .ant-spin-dot i {
  background-color: #ffba17;
}
