import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { PageMain } from '@/components/ui';
import FormControl from './components/FormControl';
import {
  Left,
  PageContainer,
  CtContainer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>t<PERSON>ontaine<PERSON>,
  Right,
} from './components/ui';
import { useWindowSize } from '@/hooks';
import AreaPoAvgColumn from './components/AreaPoAvgColumn';
import EvaMonitorLine from './components/EvaMonitorLine';
import MonitorDays from './components/MonitorDays';
import StatList from './components/StatList';

const ComprehensiveAssessment = () => {
  const { width } = useWindowSize();
  const isWidescreen = width >= 1800;

  return (
    <PageMain>
      <HelmetTitle title="大气评估" />
      <PageHead title="大气评估">
        <></>
      </PageHead>
      <PageContainer
        style={{
          gridTemplateColumns: isWidescreen ? `434px 1fr` : `1fr 1fr`,
        }}
      >
        <Left>
          <FormControl />
          <RtContainer>
            <StatList />
          </RtContainer>
          <MonitorDays />
        </Left>
        <Right>
          <CtContainer>
            <AreaPoAvgColumn />
          </CtContainer>
          <CbContainer>
            <EvaMonitorLine />
          </CbContainer>
        </Right>
      </PageContainer>
    </PageMain>
  );
};

export default ComprehensiveAssessment;
