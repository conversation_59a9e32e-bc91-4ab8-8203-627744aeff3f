export interface BaseParams {
  startDate: string;
  endDate: string;
  regionCode: string | number;
}
export interface PoAvgDataItem {
  region: string;
  thisTime: string;
  pm25Avg: number;
  pm10Avg: number;
  o3Avg: number;
  no2Avg: number;
  hchoAvg: number;
}

export type PoTypeKeys = 'pm25Avg' | 'pm10Avg' | 'o3Avg' | 'no2Avg' | 'hchoAvg';
export type PoType = 'pm25' | 'pm10' | 'o3' | 'hcho' | 'no2';
export interface MonitorItem {
  region: string;
  value: number;
  regionCode: number;
}

export interface MonitorData {
  time: string;
  evaluationMonitorTimeModels: MonitorItem[];
}

export interface LevelStatModel {
  count: number;
  level: 1 | 2 | 3 | 4;
  levelText: string;
  percent: number;
}

export interface TypeStatModel {
  count: number;
  type: string;
  percent: number;
}

export interface IndustryAlertItem {
  alerts: number;
  industryName: string;
  industryCode: number;
  levelText: string;
  merge: number;
  percent: number;
}

export interface evaAvgModel {
  ratio: number;
  region: string;
  regionCode: number;
  type: string;
  value: number;
}

export interface AvgStatData {
  evaluationRegionPollutionAvgStatModels: evaAvgModel[];
  region: string;
  regionCode: number;
  type: string;
  value: number;
}

export interface YoyStatData {
  region: string;
  regionCode: number;
  type: string;
  value: number;
  yoyValue: number;
}

export interface IDaskStatData {
  // 沙尘强度
  intensity: number;
  // 地区
  region: string;
  // 地区代码
  regionCode: number;
  // 面积和
  value: number;
}
