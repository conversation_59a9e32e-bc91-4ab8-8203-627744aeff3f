import { request } from '@/utils';
import { stringify } from 'qs';
import type { BaseParams } from '../types';

// 不同污染源种类的报警次数-导出
export const exportIndustryAlertStat = (params: BaseParams) =>
  request(`/api/evaluation/export/industry/alert/stat?${stringify(params)}`);

// 不同污染源种类的报警次数
export const fetchIndustryAlertStat = (
  params: BaseParams & {
    type: 1 | 2;
  },
) => request(`/api/evaluation/industry/alert/stat?${stringify(params)}`);

// 各区域污染物浓度均值及同比统计
export const fetchPoAvgStat = (
  params: BaseParams & {
    agg: string;
    types: string;
  },
) => request(`/api/evaluation/pollution/avg/stat?${stringify(params)}`);
// 沙尘统计
export const fetchDmaskStat = (params: BaseParams) =>
  request(`/api/evaluation/dmask/intensity?${stringify(params)}`);

// 导出各区域污染物浓度均值统计
export const exportPoAvgStat = (
  params: BaseParams & {
    types: string;
    agg: string;
  },
) => request(`/api/evaluation/export/pollution/avg/stat?${stringify(params)}`);

// 导出各区域污染物浓度均值同比统计
export const exportPoYoyStat = (params: BaseParams) =>
  request(`/api/evaluation/export/pollution/yoy/stat?${stringify(params)}`);

// 导出各区域污染物浓度均值同比统计
export const fetchPoYoyStat = (
  params: BaseParams & {
    agg: string;
    types: string;
  },
) => request(`/api/evaluation/pollution/yoy/stat?${stringify(params)}`);

// 监测数据
export const fetchMonitor = (
  params: BaseParams & {
    type: string;
    agg: string;
  },
) => request(`/api/evaluation/monitor?${stringify(params)}`);

// 监测数据
export const exportMonitor = (
  params: BaseParams & {
    type: string;
  },
) => request(`/api/evaluation/export/monitor?${stringify(params)}`);

export const fetchSatelliteList = (
  params: Omit<BaseParams, 'startDate' | 'endDate'> & {
    date: string;
    type: string;
  },
) => request(`/api/evaluation/satellite/list?${stringify(params)}`);

export interface LevelPoParams {
  endDate: string;
  startDate: string;
  industryCode: number;
  regionCode: number;
  type: number;
}

export const fetchIndustryLevelPollution = (params: LevelPoParams) =>
  request(`/api/evaluation/industry/level/pollution?${stringify(params)}`);

interface FetchValidDaysParams {
  startDate: string;
  endDate: string;
  regionCode: number;
}
// 有效天数
export const fetchValidDays = (params: FetchValidDaysParams) =>
  request(`/api/evaluation/valid/days?${stringify(params)}`);

// 导出沙尘统计
export const exportDmaskStat = (params: BaseParams) =>
  request(`/api/evaluation/export/dmask/intensity?${stringify(params)}`);
