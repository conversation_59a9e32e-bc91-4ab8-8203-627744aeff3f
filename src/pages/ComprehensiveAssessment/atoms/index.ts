import { dateFormatter } from '@/utils';
import dayjs from 'dayjs';
import { atom } from 'jotai';
export interface FormValues {
  regionCode: string | number | undefined;
  agg: 'quarter' | 'month' | 'year' | 'custom';
  type: string;
  startDate: string;
  endDate: string;
}

export const formValuesAtom = atom<FormValues>({
  regionCode: undefined,
  agg: 'month',
  type: 'pm25',
  startDate: dayjs().startOf('M').format(dateFormatter),
  endDate: dayjs().endOf('M').format(dateFormatter),
});

export const poAtom = atom('pm2.5');

export const monitorDaysAtom = atom(0);
