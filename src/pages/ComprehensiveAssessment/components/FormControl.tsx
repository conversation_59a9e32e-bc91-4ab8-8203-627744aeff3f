import { userInfo<PERSON>tom } from '@/atoms';
import { Hor<PERSON>enter } from '@/components/ui';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import { FormContainer, ItemContainer } from '@/components/ui';
import { SlectList } from './ui';
import { dateFormatter } from '@/utils';
import { Cascader, DatePicker, DatePickerProps, Select } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { formValuesAtom } from '../atoms';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { twMerge } from 'tailwind-merge';
import dayjs, { Dayjs } from 'dayjs';

export const comAssOptions: {
  label: string;
  value: string;
  unit: string;
  type?: string;
}[] = [
    {
      label: 'PM₂.₅',
      value: 'pm25',
      unit: 'μg/m³',
    },
    {
      label: 'PM₁₀',
      value: 'pm10',
      unit: 'μg/m³',
    },
    {
      label: 'AOD',
      value: 'aod',
      unit: '无量纲',
    },
    {
      label: 'O₃',
      type: 'GK2B',
      value: 'gk2b_o3',
      unit: 'μg/m³',
    },
    {
      label: 'O₃',
      type: 'S5P',
      value: 'o3',
      unit: 'μg/m³',
    },
    {
      label: 'NO₂',
      type: 'GK2B',
      value: 'gk2b_no2',
      unit: 'μg/m³',
    },
    {
      label: 'NO₂',
      type: 'S5P',
      value: 'no2',
      unit: 'μg/m³',
    },
    {
      label: 'HCHO',
      type: 'GK2B',
      value: 'gk2b_hcho',
      unit: '1e16molec./c㎡',
    },
    {
      label: 'HCHO',
      type: 'S5P',
      value: 'hcho',
      unit: '1e16molec./c㎡',
    },
    {
      label: 'SO₂',
      value: 'so2',
      unit: 'DU',
    },
    {
      label: 'CO',
      value: 'co',
      unit: '1e19molec./c㎡',
    },


    // {
    //   label: '沙尘',
    //   value: 'dmask',
    //   unit: 'km²',
    // },
  ];

const { RangePicker } = DatePicker;
const options = [
  {
    label: '月',
    value: 'month',
  },
  {
    label: '季度',
    value: 'quarter',
  },
  {
    label: '年',
    value: 'year',
  },
  {
    label: '自定义',
    value: 'custom',
  },
];

const defaultShowValue = 9;

const FormControl = () => {
  const [formValues, setFormValues] = useAtom(formValuesAtom);
  const [selectedDates, setSelectedDates] = useState<any>([]);
  const [hackValues, setHackValues] = useState<any>();
  const userInfo = useAtomValue(userInfoAtom);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const { twoStageOptions, cascaderValue } = useCascaderOptionsAndMatchValues(
    Number(formValues.regionCode),
  );

  useEffect(() => {
    if (userInfo && userInfo.regionCode) {
      setFormValues((prev) => ({
        ...prev,
        regionCode: userInfo.regionCode,
      }));
    }
  }, [setFormValues, userInfo]);

  const handleNext = useCallback(() => {
    if (!isAnimating && currentIndex < comAssOptions.length - defaultShowValue) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentIndex(prevIndex => prevIndex + 1);
        setIsAnimating(false);
      }, 300);
    }
  }, [isAnimating, currentIndex])

  const handlePrevious = useCallback(() => {
    if (!isAnimating && currentIndex > 0) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentIndex(prevIndex => prevIndex - 1);
        setIsAnimating(false);
      }, 300);
    }
  }, [isAnimating, currentIndex])



  const visibleComAssOptions = useMemo(() => {
    return comAssOptions.slice(currentIndex, currentIndex + defaultShowValue);
  }, [currentIndex])

  const rightCursor = useMemo(() => {
    return {
      cursor: currentIndex < comAssOptions.length - defaultShowValue ? '' : 'no-drop',
    }
  }, [currentIndex])

  const leftCursor = useMemo(() => {
    return {
      cursor: currentIndex > 0 ? '' : 'no-drop',
    }
  }, [currentIndex])


  const disableFutureAndOldDates: DatePickerProps['disabledDate'] = (current, { from, type }) => {
    // 禁用未来日期
    if (current.isAfter(dayjs().endOf('day'))) return true;
    if (["year"].includes(formValues.agg)) {
      // 禁用未来年份的日期
      const isFutureYear = current.year() > dayjs().year();
      // 禁用去年之前的日期
      const isBeforeLastYear = current.year() < dayjs().year() - 1;
      // 如果日期是未来的或去年之前的，则返回 true 禁用它
      return isFutureYear || isBeforeLastYear;
    }
    if (['quarter', 'month'].includes(formValues.agg)) {
      const now = dayjs();
      const thisYear = now.year();
      const currentMonth = now.month() + 1; // 1-12
      // 防止第一个无法选择处理的
      const minMonth = dayjs().year(thisYear - 1).month(currentMonth - 1).startOf('month');
      const maxMonth = dayjs().year(thisYear).month(11).startOf('month');

      // 只允许选择 minMonth ~ maxMonth 之间的月份
      return current.isBefore(minMonth, 'month') || current.isAfter(maxMonth, 'month');
    }

    return false;
  };


  return (
    <FormContainer>
      <ItemContainer>
        <Cascader
          options={twoStageOptions}
          value={cascaderValue}
          fieldNames={{
            label: 'name',
            value: 'code',
          }}
          style={{ width: '100%' }}
          changeOnSelect
          onChange={(val) => {
            setFormValues((prev) => ({
              ...prev,
              regionCode: val?.length > 0 ? +val[val?.length - 1] : userInfo!.regionCode,
            }));
          }}
        />
      </ItemContainer>
      <HorCenter>
        <Select
          style={{ width: 120, marginRight: 12 }}
          value={formValues.agg}
          options={options}
          onChange={(val: 'quarter' | 'month' | 'year' | 'custom') => {
            setFormValues((prev) => ({
              ...prev,
              agg: val,
              startDate:
                val === 'custom'
                  ? dayjs().subtract(31, 'd').format(dateFormatter)
                  : dayjs().startOf(val).format(dateFormatter),
              endDate:
                val === 'custom'
                  ? dayjs().subtract(1, 'd').format(dateFormatter)
                  : dayjs().endOf(val).format(dateFormatter),
            }));
          }}
        />
        {formValues.agg === 'custom' ? (
          <RangePicker
            style={{ flex: 1 }}
            allowClear={false}
            value={
              hackValues || [
                dayjs(formValues.startDate),
                dayjs(formValues.endDate),
              ]
            }
            disabledDate={(current) => {
              const baseCondition =
                current > dayjs().endOf('day') ||
                Math.abs(current.diff(dayjs(), 'year')) > 0;

              return !selectedDates || selectedDates.length === 0
                ? baseCondition
                : baseCondition ||
                (selectedDates[0] &&
                  current.diff(dayjs(selectedDates[0]), 'day') > 180) ||
                (selectedDates[1] &&
                  current.diff(dayjs(selectedDates[1]), 'day') < -180);
            }}
            onOpenChange={(open) => {
              if (open) {
                setHackValues([]);
                setSelectedDates([]);
              } else {
                setHackValues(undefined);
              }
            }}
            onCalendarChange={(dates) => setSelectedDates(dates)}
            onChange={(val) => {
              if (val) {
                const [start, end] = val;
                setFormValues((prev) => ({
                  ...prev,
                  startDate: String(start?.format(dateFormatter)),
                  endDate: String(end?.format(dateFormatter)),
                }));
              }
            }}
          />
        ) : (
          <DatePicker
            style={{ flex: 1 }}
            picker={formValues.agg}
            allowClear={false}
            value={dayjs(formValues.startDate)}
            disabledDate={disableFutureAndOldDates}
            onChange={(val) => {
              if (val && formValues.type !== 'custom') {
                setFormValues((prev) => ({
                  ...prev,
                  startDate: val
                    .startOf(prev.agg as 'month' | 'quarter' | 'year')
                    .format(dateFormatter),
                  endDate: val
                    .endOf(prev.agg as 'month' | 'quarter' | 'year')
                    .format(dateFormatter),
                }));
              }
            }}
          />
        )}
      </HorCenter>
      <div className='flex items-center relative z-10'>
        <LeftOutlined onClick={handlePrevious}
          style={{ fontSize: 10, color: "#333", ...leftCursor }}

        />
        <SlectList as="ul" style={{ width: 420, marginLeft: '8px' }}>
          {visibleComAssOptions.map((item: any) => (
            <li
              key={item.value}
              className={item.value === formValues.type ? `active` : 'text-center'}
              style={{ marginRight: '10px' }}
              onClick={() =>
                setFormValues((prev) => ({
                  ...prev,
                  type: item.value,
                }))
              }
            >
              <div className={twMerge(`text-[14px] text-[#333] text-center`, item.value === formValues.type ? `active` : 'text-center')}>{item.label}</div>
              {
                item.type && <div className={twMerge('leading-[14px] text-[#333] text-[10px] text-center', item.value === formValues.type ? `active` : '')}>({item.type})</div>
              }

            </li>
          ))}
        </SlectList>
        <RightOutlined onClick={handleNext} style={{ fontSize: 10, color: "#333", ...rightCursor }} />
      </div>

    </FormContainer>
  );
};

export default FormControl;
