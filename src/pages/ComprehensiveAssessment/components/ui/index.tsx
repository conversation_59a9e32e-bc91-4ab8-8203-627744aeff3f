import { BaseGrayContainer, Flex, HorCenter } from '@/components/ui';
import { rankColors } from '@/utils';
import styled, { css } from 'styled-components';
import { Block } from '@/components/ui';

export const PageContainer = styled(Flex)`
  padding-top: 16px;
  align-items: flex-start;
`;

export const Left = styled.div`
  padding-bottom: 28px;
  padding-top: 20px;
  width: 434px;
  display: grid;
  height: 64vh;
  min-height: 852px;
  grid-template-rows: 120px 1fr 110px;
  gap: 20px;
`;
export const Center = styled.div`
  flex: 1;
  padding: 0 60px;
`;
export const Right = styled.div`
  flex: 1;
  padding: 0 60px;
  row-gap: 40px;
`;
export const CenterChartContainer = styled.div`
  position: relative;
`;
export const CtContainer = styled.div`
  padding-top: 28px;
  height: 46vh;
  min-height: 430px;
`;
export const CbContainer = styled.div``;

export const PoTabItem = styled.span<{
  active?: boolean;
}>`
  display: block;
  margin-right: 24px;
  color: ${(props) => (props.active ? props.theme.colors.primary : '#333')};
  pointer-events: auto;
  cursor: pointer;
`;

export const PoTabItemsContainer = styled(HorCenter)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
  pointer-events: none;
  background: transparent;
`;
export const RtContainer = styled(BaseGrayContainer)`
  padding: 30px 20px 10px;
  border-radius: 10px;
`;
export const RbContainer = styled(BaseGrayContainer)`
  display: flex;
  padding: 20px 0;
  border-radius: 10px;
`;

export const PdContainer = styled(BaseGrayContainer)`
  display: flex;
  justify-content: space-between;
  border-radius: 10px;
  padding: 0 30px;
  width: 100%;
  .title {
    position: relative;
    left: -20px;
  }
  .val {
    font-weight: 600;
    font-size: 26px;
  }
  .innerAfter {
    display: inline-block;
    margin-left: 10px;
  }
  .after {
    margin-top: 32px;
  }
  .innerVal {
    font-weight: 500;
    font-size: 16px;
  }
`;

export const TileBox = styled.div`
  width: 10px;
  height: 10px;
  margin-right: 6px;
  display: inline-block;
`;

export const RbItem = styled.div`
  flex: 1;
  padding: 0 46px;

  .title {
  }

  .val {
    display: flex;
    align-items: flex-end;
    margin-top: 12px;
    font-size: 40px;
    line-height: 1;

    span {
      padding-bottom: 4px;
      padding-left: 4px;
      font-size: 14px;
    }
  }

  &:last-child {
    position: relative;

    &::after {
      position: absolute;
      bottom: 2px;
      left: 0;
      display: block;
      width: 1px;
      height: 32px;
      background: #e6e6e6;
      content: '';
    }
  }
`;

export const MiniPieContainer = styled(HorCenter)`
  margin-top: 20px;
  padding-top: 20px;

  .type {
  }
  .val {
  }
`;

export const MiniPieChart = styled.div`
  width: 120px;
  height: 120px;
`;

export const MiniChartInfo = styled.div`
  flex: 1;
  padding-left: 24px;

  .type {
  }
  .val {
    font-size: 20px;
  }
  .percent {
    font-size: 14px;
  }
`;

const levelMapping = {
  1: {
    label: '轻度污染',
    color: 'rgb(255, 126, 0)',
  },
  2: {
    label: '中度污染',
    color: 'rgb(255, 0, 0)',
  },
  3: {
    label: '重度污染',
    color: 'rgb(153, 0, 76)',
  },
  4: {
    label: '严重污染',
    color: 'rgb(126, 0, 35)',
  },
};

export const LegendGroup = styled.div`
  display: inline-flex;
  align-items: center;

  .rect {
    width: 10px;
    height: 10px;
    margin: 8px 8px 8px 0;
    background: ${levelMapping[1].color};
    border-radius: 3px;
  }
`;

export const LegendContainer = styled(HorCenter)`
  flex-wrap: wrap;
`;

export const LevelLegendContainer = styled(LegendContainer)`
  ${LegendGroup} {
    ${() =>
      rankColors.slice(2).map((item, index) => {
        return css`
          &:nth-child(${index + 1}) {
            .rect {
              background: ${item};
            }
          }
        `;
      })}
  }
`;

export const SlectList = styled(Flex)`
  margin-top: 16px;
  list-style-type: none;
  justify-content: space-between;

  li {
    margin-right: 14px;
    cursor: pointer;
  }

  .active {
    color: #286cff;
  }
`;

export const StatListHead = styled(HorCenter)`
  margin-bottom: 10px;

  ${Flex} {
    flex: 1;
    padding: 8px 12px;
    background: #ececef;
    border-radius: 4px;

    .region {
      flex: 1.5;
    }

    .value {
      flex: 1;
    }

    .percentage {
      flex: 1;
    }

    .active {
      color: ${({ theme }) => theme.colors.primary};
    }
  }
  .rank {
    flex: 0 0 60px;
    text-align: center;
  }
`;

export const StatListItem = styled(Flex)<{
  active?: boolean;
}>`
  .left {
    flex: 1;
    padding: 8px 12px;
    border-bottom: 1px solid #ececef;
  }

  ${Flex} {
    .region {
      flex: 1.5;
    }

    .value {
      flex: 1;
    }

    .percentage {
      flex: 1;
    }
  }

  .bar {
    height: 4px;
    margin-top: 6px;
    background: #ececef;
  }

  .bar-inner {
    height: 4px;
    background: ${({ theme }) => theme.colors.primary};
  }

  .rank {
    flex: 0 0 60px;
    padding: 8px;
    text-align: center;
  }
`;

export const PreviewLine = styled.div`
  margin-bottom: 20px;
  padding-left: 10px;
  font-size: 14px;

  .value {
    padding-right: 4px;
    color: ${({ theme }) => theme.colors.primary};
    font-weight: 700;
    font-size: 18px;
  }
`;

export const LeftBlock = styled(Block)`
  height: 35vh;
  overflow: auto;
  min-height: 300px;

  &::-webkit-scrollbar {
    width: 4px;
    height: 6px;
  }
  /* 滚动槽 */
  &::-webkit-scrollbar-track {
    width: 2px;
    border-radius: 10px;
  }
  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    width: 2px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb:window-inactive {
    width: 2px;
    background: rgba(0, 0, 0, 0.2);
  }
`;
