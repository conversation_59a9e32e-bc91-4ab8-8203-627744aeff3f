import { Block, MuTitle, Spacer } from '@/components/ui';
import { Empty, Select } from 'antd';
// eslint-disable-next-line max-len
import { TitleWithDownload } from '@/components/ui';
import { Line } from '@ant-design/charts';
import { useAtomValue } from 'jotai';
import React, { useEffect, useRef } from 'react';
import { useState } from 'react';
import { memo, useMemo } from 'react';
import { formValuesAtom } from '../atoms';
import { useExportLine, useLineConfig, useTypeSelectOptions } from '../hooks';
import { CenterChartContainer, PoTabItemsContainer } from './ui';
import { comAssOptions } from './FormControl';
import { usePanelHeight } from '@/hooks';

const EvaMonitorLine = () => {
  const chartRef = useRef<any>();
  const [agg, setAgg] = useState<string>('daily');
  const formValues = useAtomValue(formValuesAtom);
  const { startDate, endDate, regionCode, agg: pageAgg, type } = formValues;

  const { config, shouldShowEmpty } = useLineConfig({
    startDate,
    endDate,
    regionCode: Number(regionCode),
    type,
    agg,
  });
  const handleExport = useExportLine({
    startDate,
    endDate,
    regionCode: Number(regionCode),
    type,
    agg,
  });
  const options = useTypeSelectOptions(pageAgg);
  const unit = useMemo(() => {
    const find = comAssOptions.find((item) => item.value === type);
    return find ? find.unit : '';
  }, [type]);

  const h = usePanelHeight()

  useEffect(() => {
    setAgg('daily')
  }, [pageAgg]);

  return (
    <>
      <TitleWithDownload>
        <MuTitle paddingLeft="10px">监测数据</MuTitle>
        {!shouldShowEmpty && (
          <a href="" onClick={handleExport}>
            <i className="icomoon icon-download" />
          </a>
        )}
      </TitleWithDownload>
      {shouldShowEmpty && (
        <Block padding="40px">
          <Empty />
        </Block>
      )}
      {!shouldShowEmpty && (
        <CenterChartContainer>
          <PoTabItemsContainer>
            <Select
              style={{ width: 100, marginRight: 20, pointerEvents: 'auto' }}
              value={agg}
              options={options}
              bordered={false}
              onChange={(val) => {
                setAgg(val);
              }}
            />
            <Spacer />
            {unit && (
              <span>
                单位：
                {unit}
              </span>
            )}
          </PoTabItemsContainer>
          {/* 38 48 */}
          <div id="line-chart" style={{ height: h < 649 ? '48vh' : '38vh', minHeight: '340px' }}>
            <Line
              {...config}
              onReady={(plot) => {
                chartRef.current = plot;
              }}
            />
          </div>
        </CenterChartContainer>
      )}
    </>
  );
};

export default memo(EvaMonitorLine);
