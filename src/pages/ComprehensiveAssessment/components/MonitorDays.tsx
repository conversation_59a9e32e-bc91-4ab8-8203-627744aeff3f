import { useAtomValue } from 'jotai';
import moment from 'moment';
import React, { useMemo } from 'react';
import { useQuery } from 'react-query';
import { formValuesAtom } from '../atoms';
import { fetchValidDays } from '../services';
import { RbContainer, RbItem } from './ui';

const MonitorDays = () => {
  const { startDate, endDate, regionCode } = useAtomValue(formValuesAtom);

  const { data } = useQuery(
    ['valid-days', startDate, endDate, regionCode],
    () =>
      fetchValidDays({
        startDate,
        endDate,
        regionCode: Number(regionCode),
      }),
    {
      enabled: Boolean(regionCode),
    },
  );
  const computedDays = useMemo(() => {
    return moment(endDate).diff(moment(startDate), 'day');
  }, [startDate, endDate]);

  return (
    <RbContainer style={{marginTop:'-10px'}}>
      <RbItem>
        <div className="title">监测总数</div>
        <div className="val">
          <strong>{computedDays + 1}</strong> <span>天</span>
        </div>
      </RbItem>
      <RbItem>
        <div className="title">有效数据</div>
        <div className="val">
          <strong>{data || 0}</strong> <span>天</span>
        </div>
      </RbItem>
    </RbContainer>
  );
};

export default MonitorDays;
