import { Block, MuTitle, Spacer } from '@/components/ui';
// eslint-disable-next-line max-len
import { TitleWithDownload } from '@/components/ui';
import { Column } from '@ant-design/charts';
import { Empty } from 'antd';
import { useAtomValue } from 'jotai';
import { useMemo, memo } from 'react';
import { formValuesAtom } from '../atoms';
import { useColumnChartConfig, useExportColumn } from '../hooks';
import { comAssOptions } from './FormControl';
import { CenterChartContainer, PoTabItemsContainer } from './ui';

const AreaPoAvgColumn = () => {
  const formValues = useAtomValue(formValuesAtom);
  const { startDate, endDate, regionCode, agg, type } = formValues;
  const { chartConfig, shouldShowEmpty } = useColumnChartConfig({
    type,
    startDate,
    endDate,
    agg,
    regionCode: Number(regionCode),
  });
  const handleExport = useExportColumn({
    type,
    agg,
    startDate,
    endDate,
    regionCode: Number(regionCode),
  });

  const unit = useMemo(() => {
    const find = comAssOptions.find((item) => item.value === type);

    return find ? find.unit : '';
  }, [type]);

  return (
    <>
      <TitleWithDownload>
        <MuTitle paddingLeft="10px">各区域污染物浓度均值同比统计</MuTitle>
        {!shouldShowEmpty && (
          <a href="" onClick={handleExport}>
            <i className="icomoon icon-download" />
          </a>
        )}
      </TitleWithDownload>
      {shouldShowEmpty && (
        <Block padding="40px">
          <Empty />
        </Block>
      )}
      {!shouldShowEmpty && (
        <CenterChartContainer>
          <PoTabItemsContainer style={{ paddingLeft: 16 }}>
            <Spacer />
            {unit && (
              <span>
                单位：
                {unit}
              </span>
            )}
          </PoTabItemsContainer>
          <div id="chart" style={{ height: '35vh', minHeight: '300px' }}>
            <Column {...chartConfig} />
          </div>
        </CenterChartContainer>
      )}
    </>
  );
};

export default memo(AreaPoAvgColumn);
