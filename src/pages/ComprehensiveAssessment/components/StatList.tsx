import { Center, Flex, MuTitle, TitleWithDownload } from '@/components/ui';
import { exportFile, remoteSensingValuesAndColors } from '@/utils';
import { Empty, Spin } from 'antd';
import { useAtomValue } from 'jotai';
import React, { useMemo, useCallback } from 'react';
import { useMutation, useQuery } from 'react-query';
import { formValuesAtom } from '../atoms';
import {
  exportDmaskStat,
  exportPoAvgStat,
  fetchDmaskStat,
  fetchPoAvgStat,
} from '../services';
import type { AvgStatData, BaseParams, IDaskStatData } from '../types';
import { comAssOptions } from './FormControl';
import { PreviewLine, StatListHead, StatListItem, LeftBlock } from './ui';

const StatList = () => {
  const { startDate, endDate, type, agg, regionCode } =
    useAtomValue(formValuesAtom);
  const exportMutation = useMutation(
    (
      p: BaseParams & {
        types: string;
        agg: string;
      },
    ) => exportPoAvgStat(p),
  );
  const exportDmaskMutation = useMutation((p: BaseParams) =>
    exportDmaskStat(p),
  );
  const { data, isLoading } = useQuery<AvgStatData[]>(
    ['pollution-avg-stat', startDate, endDate, type, agg, regionCode],
    () =>
      fetchPoAvgStat({
        startDate,
        endDate,
        types: type,
        agg: agg === 'custom' ? 'daily' : `${agg}ly`,
        regionCode: Number(regionCode),
      }),
    {
      enabled: Boolean(regionCode && type !== 'dmask'),
      cacheTime: 0,
      staleTime: 0,

    },
  );

  const { data: dmaskData } = useQuery<IDaskStatData[]>(
    ['dask-stat', startDate, endDate, regionCode],
    () =>
      fetchDmaskStat({
        startDate,
        endDate,
        regionCode: Number(regionCode),
      }),
    {
      enabled: Boolean(regionCode && type === 'dmask'),
      cacheTime: 0,
      staleTime: 0,
    },
  );

  const dmaskList = useMemo(() => {
    return (dmaskData || []).slice().sort((a, b) => b.intensity - a.intensity);
  }, [dmaskData]);

  const previewData = useMemo(() => {
    if (data && data.length > 0) {
      return {
        type: data[0].type,
        value: data[0].value,
        region: data[0].region,
      };
    }
    return null;
  }, [data]);

  const listData = useMemo(() => {
    if (data && data.length > 0) {
      const list = data[0].evaluationRegionPollutionAvgStatModels;
      const listClone = list.slice();
      listClone.sort((a, b) => b.value - a.value);

      return listClone;
    }
    return [];
  }, [data]);

  const unit = useMemo(() => {
    if (previewData) {
      const find = comAssOptions.find(
        (item) => item.value === previewData.type.toLowerCase(),
      );
      return find ? find.unit : '';
    }
    return '';
  }, [previewData]);

  const shouldShowEmpty = useMemo(() => {
    return data?.length === 0 && !isLoading;
  }, [data?.length, isLoading]);

  const exportList = useCallback(() => {
    if (type === 'dmask') {
      exportDmaskMutation.mutate(
        {
          startDate,
          regionCode: Number(regionCode),
          endDate,
        },
        {
          onSuccess(d) {
            exportFile(d, `${startDate}-${endDate}沙尘均值统计`);
          },
        },
      );
    } else {
      exportMutation.mutate(
        {
          agg: `${agg === 'custom' ? 'dai' : agg}ly`,
          startDate,
          regionCode: Number(regionCode),
          endDate,
          types: type,
        },
        {
          onSuccess(d) {
            exportFile(d, `${startDate}-${endDate}各区域污染物浓度均值统计`);
          },
        },
      );
    }
  }, [
    agg,
    endDate,
    exportDmaskMutation,
    exportMutation,
    regionCode,
    startDate,
    type,
  ]);

  const formatValue = useCallback((valType: string, val: number) => {
    if (['hcho', 'co', 'gk2b_hcho'].includes(valType)) {
      return `${remoteSensingValuesAndColors[valType]?.roundFunc(val)}`;
    }
    if (valType === 'dmask') {
      return val.toFixed(1);
    }

    return remoteSensingValuesAndColors[valType]?.roundFunc(val);
  }, []);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <TitleWithDownload>
        <MuTitle paddingLeft="10px">各区域污染物浓度均值统计</MuTitle>
        {!shouldShowEmpty && (
          <a
            href=""
            onClick={(e) => {
              e.preventDefault();
              exportList();
            }}
          >
            <i className="icomoon icon-download" />
          </a>
        )}
      </TitleWithDownload>
      {isLoading && (
        <Center>
          <LeftBlock padding="40px">
            <Spin />
          </LeftBlock>
        </Center>
      )}
      {shouldShowEmpty && (
        <LeftBlock padding="40px">
          <Empty />
        </LeftBlock>
      )}
      {!isLoading && !shouldShowEmpty && (
        <div style={{ flex: 1 }}>
          {previewData && type !== 'dmask' && (
            <PreviewLine className='flex items-center'>
              {previewData.region}均值浓度
              <span className="value">
                {formatValue(type, previewData.value)}
              </span>
              <span className="unit">{unit}</span>

            </PreviewLine>
          )}
          <StatListHead>
            <Flex>
              <div className="region">行政区域</div>
              <div className="value">
                {type !== 'dmask' ? '浓度均值' : '影响面积'}
              </div>
              <div className="percentage">
                {' '}
                {type !== 'dmask' ? '超出占比' : '沙尘指数'}
              </div>
            </Flex>
            <div className="rank">排行</div>
          </StatListHead>
          <LeftBlock
            style={{
              height: type === 'dmask' ? '42vh' : '36vh',
            }}
          >
            {(type === 'dmask' ? dmaskList || [] : listData).map(
              (item, index) => (
                <StatListItem key={item.regionCode}>
                  <div className="left">
                    <Flex>
                      <div className="region">{item.region || '未知'}</div>
                      <div className="value">
                        {formatValue(type, item.value)}
                      </div>
                      <div
                        className={`percentage ${'ratio' in item && item.ratio > 0 ? 'text-danger' : ''
                          }`}
                      >
                        {type !== 'dmask' && 'ratio' in item && (
                          <span>
                            {item.ratio > 0 ? `+${item.ratio}` : item.ratio} %
                          </span>
                        )}
                        {type === 'dmask' &&
                          'intensity' in item &&
                          item.intensity.toFixed(4) + '%'}
                      </div>
                    </Flex>
                  </div>
                  <div className={`rank ${index < 3 ? 'text-danger' : ''}`}>
                    {index + 1}
                  </div>
                </StatListItem>
              ),
            )}
          </LeftBlock>
        </div>
      )}
    </div>
  );
};

export default StatList;
