import {
  baseChartTooltipDomStyle,
  dashedXAxisGridLine,
  downloadUseLink,
  exportFile,
  globalPoMapping,
  rankColors,
  remoteSensingValuesAndColors,
  warningRankText,
} from '@/utils';
import { toScientificNotation } from '@/utils/number';
import type {
  ColumnConfig,
  Datum,
  LineConfig,
  PieConfig,
} from '@ant-design/charts';
import { message } from 'antd';
import html2canvas from 'html2canvas';
import { useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { formValuesAtom } from '../atoms';
import {
  exportIndustryAlertStat,
  exportMonitor,
  exportPoYoyStat,
  fetchIndustryAlertStat,
  fetchIndustryLevelPollution,
  fetchMonitor,
  fetchPoYoyStat,
} from '../services';
import type {
  BaseParams,
  IndustryAlertItem,
  LevelStatModel,
  MonitorData,
  PoType,
  TypeStatModel,
  YoyStatData,
} from '../types';

// pm10 前端取整数 pm25 取整 aod 保留2位小数 o3取整 No2 取整  so2 保留一位小数
//HCHO(s5p gk2b) 没有格式化 co 没有格式化 

export interface Params {
  type: PoType | string;
  startDate: string;
  endDate: string;
  regionCode: number;
  agg: string;
}
const roundedSquareSymbol = (x: number, y: number, r: number) => {
  const size = r * 2.5;       // 正方形大小（基于默认半径）
  const cornerRadius = 3;   // 圆角半径
  // 计算关键点坐标
  const left = x - size / 2;
  const right = x + size / 2;
  const top = y - size / 2;
  const bottom = y + size / 2;

  // 创建圆角矩形路径
  return [
    ['M', left + cornerRadius, top], // 左上角起点
    ['L', right - cornerRadius, top], // 上边线
    // 右上圆角
    ['A', cornerRadius, cornerRadius, 0, 0, 1, right, top + cornerRadius],
    ['L', right, bottom - cornerRadius], // 右边线
    // 右下圆角
    ['A', cornerRadius, cornerRadius, 0, 0, 1, right - cornerRadius, bottom],
    ['L', left + cornerRadius, bottom], // 下边线
    // 左下圆角
    ['A', cornerRadius, cornerRadius, 0, 0, 1, left, bottom - cornerRadius],
    ['L', left, top + cornerRadius], // 左边线
    // 左上圆角
    ['A', cornerRadius, cornerRadius, 0, 0, 1, left + cornerRadius, top],
    ['Z'] // 闭合路径
  ];
};


// 各区域污染物浓度均值同比统计图表配置
export const useColumnChartConfig = (params: Params) => {
  const { startDate, endDate, regionCode, type, agg } = params;
  const queryKeys = useMemo(() => {
    return ['area-po-avg-data', startDate, endDate, regionCode, agg, type];
  }, [endDate, regionCode, startDate, agg, type]);
  const { data, isLoading } = useQuery<YoyStatData[]>(
    queryKeys,
    () =>
      fetchPoYoyStat({
        startDate,
        endDate,
        regionCode: Number(regionCode),
        agg: agg === 'custom' ? 'daily' : `${agg}ly`,
        types: type,
      }),
    {
      enabled: Boolean(regionCode),
    },
  );
  const chartData = useMemo(() => {
    return (data ?? []).reduce(
      (prev, cur) => {
        let value = cur.value;
        let yoyValue = cur.yoyValue;
        value = Number(remoteSensingValuesAndColors[cur.type.toLowerCase()].roundFunc(cur.value))
        yoyValue = Number(remoteSensingValuesAndColors[cur.type.toLowerCase()].roundFunc(cur.yoyValue))

        return [
          ...prev,
          {
            region: cur.region,
            value,
            time: '本期',
          },
          {
            region: cur.region,
            value: yoyValue,
            time: '去年同期',
          },
        ];
      },
      [] as {
        region: string;
        value: number;
        time: string;
      }[],
    );
  }, [data]);

  const formattedData = useMemo(() => {
    return chartData.filter((item) => {
      if (item.time === '去年同期' && item.value === 0) {
        return false;
      }

      return true;
    });
  }, [chartData]);
  // 之前带单位使用的
  // const values = formattedData.map(item => item.value).filter(v => v > 0);
  // const max = Math.max(...values);
  // const min = Math.min(...values);
  // const paddingLeft = useMemo(() => {
  //   if (['gk2b_hcho', 'hcho', 'co'].includes(type)) {
  //     let maxValueLength = toScientificNotation(max).toString().length
  //     let minValueLength = toScientificNotation(min).toString().length
  //     let maxLength = maxValueLength > minValueLength ? maxValueLength : minValueLength
  //     return maxLength > 12 ? 180 : maxLength > 8 ? 100 : maxLength > 5 ? 80 : 60;
  //   } else {
  //     return 40;
  //   }
  // }, [type, max, min])

  const chartConfig: ColumnConfig = useMemo(() => {
    return {
      renderer: 'svg',
      data: formattedData,
      color: ['#f6bd16', '#5b8ff9'],
      loading: isLoading,
      height: 360,
      padding: [60, 40, 40, 50],
      isGroup: true,
      xField: 'region',
      autoFit: true,
      yField: 'value',
      seriesField: 'time',
      xAxis: {
        label: {
          autoRotate: false,
          formatter(val) {
            const arr = val.split('');
            if (arr.length > 5) {
              return arr.reduce((prev, cur, index) => {
                if (index % 4 === 0 && index !== 0) {
                  return prev + cur + '\n';
                }
                return prev + cur;
              }, '');
            }

            return val;
          },
        },
      },
      yAxis: {
        line: {
          style: {
            stroke: '#ccc',
            lineWidth: 1,
          },
        },
        label: {
          formatter(val) {
            return val || ''
          },

        },
        grid: {
          ...dashedXAxisGridLine,
        },
        type: 'linear'

      },
      legend: {
        position: 'top-left',
        offsetX: 20,
        offsetY: -4,
        items: [
          {
            id: '1',
            name: '本期',
            marker: {
              symbol: roundedSquareSymbol,
              style: {
                fill: '#f6bd16',
                r: 4
              },
            },
            value: 1,
          },
          {
            id: '2',
            name: '去年同期',
            marker: {
              symbol: roundedSquareSymbol,
              style: {
                fill: '#5b8ff9',
                r: 4
              },
            },
            value: 2,
          },
        ],
      },
      tooltip: {
        formatter(datum: Datum) {
          let value = datum.value;
          return {
            name: datum.time,
            value,
          };
        },
        domStyles: baseChartTooltipDomStyle,
      },
      label: false,
    };
  }, [formattedData, isLoading]);

  const shouldShowEmpty = useMemo(
    () => data?.length === 0 && !isLoading,
    [data?.length, isLoading],
  );

  return { chartConfig, shouldShowEmpty };
};

// 导出各区域污染物浓度均值同比统计
export const useExportColumn = (params: Params) => {
  const { startDate, endDate, regionCode, agg, type } = params;
  const exportMutation = useMutation(
    (p: Omit<Params, 'type'> & { types: string }) => exportPoYoyStat(p),
  );
  const handleExport = useCallback(
    (e) => {
      e.preventDefault();

      exportMutation.mutate(
        {
          types: type,
          agg: agg === 'custom' ? 'daily' : `${agg}ly`,
          startDate,
          endDate,
          regionCode,
        },
        {
          onSuccess(d) {
            exportFile(
              d,
              `${startDate}-${endDate}各区域污染物浓度均值同比统计`,
            );
            message.success('操作成功');

            const chartDom = document.getElementById('chart');

            if (chartDom) {
              setTimeout(() => {
                html2canvas(chartDom).then((canvas) => {
                  const src = canvas.toDataURL('image/jpeg');
                  downloadUseLink(
                    src,
                    `${startDate}-${endDate}各区域污染物浓度均值同比统计`,
                  );
                });
              }, 500);
            }
          },
        },
      );
    },
    [agg, endDate, exportMutation, regionCode, startDate, type],
  );

  return handleExport;
};

// #region 监测数据图表配置
export const useLineConfig = (
  params: Omit<Params, 'type'> & {
    type: string;
    agg: string;
  },
) => {
  const { startDate, endDate, regionCode, type, agg } = params;
  const queryKeys = useMemo(
    () => ['eva-monitor-line-data', startDate, agg, endDate, regionCode, type],
    [endDate, regionCode, startDate, type, agg],
  );
  const { data, isLoading } = useQuery<MonitorData[]>(
    queryKeys,
    () =>
      fetchMonitor({
        startDate,
        endDate,
        type,
        regionCode,
        agg,
      }),
    {
      enabled: Boolean(regionCode),
    },
  );

  const chartData = useMemo(() => {
    return (data ?? []).reduce(
      (prev, cur) => {
        return [
          ...prev,
          ...cur.evaluationMonitorTimeModels.map((item) => ({
            region: item.region,
            value:
              Number(remoteSensingValuesAndColors[type].roundFunc(item.value)),
            date: cur.time,
          })),
        ];
      },
      [] as {
        region: string;
        date: string;
        value: number;
      }[],
    );
  }, [data, type]);

  const dataLens = useMemo(() => {
    const regionArr = chartData.reduce((prev, cur) => {
      if (prev.includes(cur.region)) {
        return prev;
      }
      return [...prev, cur.region];
    }, [] as string[]);

    return regionArr.length;
  }, [chartData]);

  const shouldShowTwoLineLegend = useMemo(() => {
    return dataLens >= 8;
  }, [dataLens]);

  const sliderStart = useMemo(() => {
    const result = Math.round((data?.length || 10) / 10);
    return 1 - 10 / ((data?.length || 5) + result);
  }, [data?.length]);

  // const values = chartData.map(item => item.value).filter(v => v > 0);
  // const max = Math.max(...values);
  // const min = Math.min(...values);
  // const paddingLeft = useMemo(() => {
  //   let maxValueLength = toScientificNotation(max).toString().length
  //   let minValueLength = toScientificNotation(min).toString().length
  //   let maxLength = maxValueLength > minValueLength ? maxValueLength : minValueLength
  //   return maxLength > 12 ? 300 : maxLength > 8 ? 220 : maxLength > 5 ? 200 : 160;

  // }, [max, min])



  const config: LineConfig = useMemo(
    () => ({
      data: chartData || [],
      loading: isLoading,
      padding: [
        shouldShowTwoLineLegend ? 110 - 40 : 80 - 40,
        20,
        60,
        160,
      ],
      height: 460,
      xField: 'date',
      yField: 'value',
      seriesField: 'region',
      autoFit: true,
      smooth: true,
      yAxis: {
        line: {
          style: {
            stroke: '#ccc',
            lineWidth: 1,
          },
        },
        label: {
          formatter(val) {
            return val || ''
          },
        },
        grid: {
          ...dashedXAxisGridLine,
        },
      },
      xAxis: {
        nice: true,
      },
      legend: {
        layout: 'vertical',
        position: 'left',
        offsetX: 6,
        offsetY: 30,
        itemsHeight: 14,
        flipPage: false, // 禁用图例的翻页功能
        marker: {
          symbol: roundedSquareSymbol,
          style: (styleProps) => {
            return {
              r: 4,
              fill: styleProps.stroke,
            }
          }
        }
      },
      tooltip: {
        domStyles: baseChartTooltipDomStyle,
        formatter(datum: Datum) {
          let value = datum.value;
          return {
            name: datum.region,
            value,
          };
        },
      },
      slider: {
        // eslint-disable-next-line max-len
        start: (data || []).length > 10 ? sliderStart.toPrecision(3) : 0,
        end: 1,
      },
    }),
    [chartData, data, isLoading, shouldShowTwoLineLegend, sliderStart],
  );

  const shouldShowEmpty = useMemo(
    () => data?.length === 0 && !isLoading,
    [data?.length, isLoading],
  );

  return { config, shouldShowEmpty };
};
//#endregion

export const useTypeSelectOptions = (
  dateType: 'quarter' | 'month' | 'year' | 'custom',
) => {
  const options = useMemo(() => {
    const base = [
      {
        label: '日均值',
        value: 'daily',
      },
    ];
    switch (dateType) {
      case 'month':
        return [
          ...base,
          {
            label: '周均值',
            value: 'weekly',
          },
        ];
      case 'quarter':
        return [
          ...base,
          {
            label: '周均值',
            value: 'weekly',
          },
          {
            label: '月均值',
            value: 'monthly',
          },
        ];
      case 'year':
        return [
          ...base,
          {
            label: '周均值',
            value: 'weekly',
          },
          {
            label: '月均值',
            value: 'monthly',
          },
          {
            label: '季均值',
            value: 'quarterly',
          },
        ];
      default:
        return base;
    }
  }, [dateType]);

  return options;
};
export const useExportLine = (
  params: Omit<Params, 'type'> & {
    type: string;
  },
) => {
  const { regionCode, startDate, endDate, type, agg } = params;
  const exportMutation = useMutation(
    (
      p: BaseParams & {
        type: string;
        agg: string;
      },
    ) => exportMonitor(p),
  );

  const handleExport = useCallback(
    (e) => {
      e.preventDefault();
      exportMutation.mutate(
        {
          startDate,
          endDate,
          type,
          regionCode: Number(regionCode),
          agg,
        },
        {
          onSuccess(d) {
            message.success('操作成功');
            setTimeout(() => {
              const container = document.getElementById('line-chart');
              if (container) {
                html2canvas(container).then((canvas) => {
                  const url = canvas.toDataURL('image/jpeg');
                  downloadUseLink(url, `${startDate}-${endDate}-${type.toUpperCase()}监测数据`);
                });
              }
            }, 1000);
            exportFile(d, `${startDate}-${endDate}-${type.toUpperCase()}监测数据`);
          },
        },
      );
    },
    [agg, endDate, exportMutation, regionCode, startDate, type],
  );

  return handleExport;
};

const chartTypeMapping: Record<PoType, { label: string; color: string }> = {
  pm25: {
    label: 'PM₂.₅',
    color: '#5b8ff9',
  },
  pm10: {
    label: 'PM₁₀',
    color: '#61ddaa',
  },
  o3: {
    label: 'O₃',
    color: '#65789B',
  },
  no2: {
    label: 'NO₂',
    color: '#F6BD16',
  },
  hcho: {
    label: 'HCHO',
    color: '#7262FD',
  },
};

export const useIndustryPieConfig = () => {
  interface ICityInfo {
    type?: string;
    color?: string;
    percent?: string;
    value?: number;
  }
  const chartRef = useRef<any>();
  const { startDate, endDate, regionCode } = useAtomValue(formValuesAtom);
  const [industryType, setIndustryType] = useState<1 | 2>(1);
  const [levelData, setLevelData] = useState<LevelStatModel[]>([]);
  const [typeData, setTypeData] = useState<TypeStatModel[]>([]);
  const [levelTextData, setLevelTextData] = useState<LevelStatModel | null>(
    null,
  );
  const [typeTextData, setTypeTextData] = useState<TypeStatModel | null>(null);
  const [miniPieTitle, setMiniPieTitle] = useState('');
  const [industryCode, setIndustryCode] = useState<number | undefined>(
    undefined,
  );
  const [cityInfo, setCityInfo] = useState<ICityInfo>({});
  const exportMutation = useMutation(
    (
      params: BaseParams & {
        type: number;
      },
    ) => exportIndustryAlertStat(params),
  );

  const queryKeys = useMemo(() => {
    return [
      'industry-alert-pie',
      {
        startDate,
        endDate,
        regionCode,
        type: industryType,
      },
    ];
  }, [endDate, regionCode, startDate, industryType]);

  const { data, isLoading } = useQuery<IndustryAlertItem[]>(
    queryKeys,
    () =>
      fetchIndustryAlertStat({
        startDate,
        endDate,
        regionCode: Number(regionCode),
        type: industryType,
      }),
    {
      enabled: Boolean(regionCode),
    },
  );

  const { data: childData } = useQuery<{
    levelStatModels: LevelStatModel[];
    typeStatModels: TypeStatModel[];
  }>(
    [
      'industry-level-pollution',
      regionCode,
      industryType,
      endDate,
      startDate,
      industryCode,
    ],
    () =>
      fetchIndustryLevelPollution({
        industryCode: Number(industryCode),
        regionCode: Number(regionCode),
        type: industryType,
        startDate,
        endDate,
      }),
    {
      enabled: Boolean(industryCode && regionCode),
    },
  );

  useEffect(() => {
    if (data && data.length > 0) {
      const sortedData = data.slice().sort((a, b) => b.alerts - a.alerts);
      setIndustryCode(sortedData[0].industryCode);

      setMiniPieTitle((prev) =>
        sortedData[0].industryName !== prev ? sortedData[0].industryName : prev,
      );
    }
  }, [data]);

  const chartData = useMemo(() => {
    return (data ?? []).map((item) => ({
      type: item.industryName,
      value: item.alerts,
      code: item.industryCode,
    }));
  }, [data]);

  useEffect(() => {
    if (childData) {
      setLevelData(childData.levelStatModels);
      setTypeData(childData.typeStatModels);
    }
  }, [childData]);

  const chartConfig: PieConfig = useMemo(
    () => ({
      appendPadding: 10,
      loading: isLoading,
      color: [
        '#5B8FF9',
        '#CDDDFD',
        '#61DDAA',
        '#CDF3E4',
        '#65789B',
        '#CED4DE',
        '#F6BD16',
        '#FCEBB9',
        '#7262FD',
        '#D3CEFD',
        '#CCCCCC',
        '#9661BB',
      ],
      data: chartData,
      angleField: 'value',
      colorField: 'type',
      radius: 0.9,
      label: {
        type: 'inner',
        content: function content(_ref) {
          const { percent } = _ref;
          return ''.concat((percent * 100).toFixed(0), '%');
        },
        style: {
          fontSize: 14,
          textAlign: 'center',
        },
      },
      interactions: [{ type: 'element-single-selected' }],
      tooltip: {
        domStyles: {
          ...baseChartTooltipDomStyle,
          'g2-tooltip-list-item': {
            display: 'flex',
            alignItems: 'center',
            minWidth: 140,
            color: 'white',
          },
        },
      },
      legend: {
        position: 'top',
        flipPage: false,
      },
    }),
    [chartData, isLoading],
  );

  const levelChartConfig: PieConfig = useMemo(
    () => ({
      width: 120,
      height: 120,
      data: levelData,
      angleField: 'count',
      colorField: 'level',
      color: ({ level: lv }) => {
        return rankColors.slice(1)[lv];
      },
      legend: false,
      radius: 1,
      innerRadius: 0.6,
      label: false,
      statistic: {
        title: false,
        content: {
          style: {
            fontSize: '12px',
            fontWeight: 'normal',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          },
          content: miniPieTitle,
        },
      },
      tooltip: {
        domStyles: {
          ...baseChartTooltipDomStyle,
          'g2-tooltip': {
            minWidth: 200,
            background: '#1C1D24',
            boxShadow: 'none',
          },
          'g2-tooltip-list-item': {
            minWidth: '120px',
            color: 'white',
          },
        },
        formatter: (datum: Datum) => {
          return {
            name: `${warningRankText.slice(1)[datum.level]}污染`,
            value: datum.count,
          };
        },
      },
    }),
    [levelData, miniPieTitle],
  );

  const typeChartData = useMemo(
    () =>
      typeData.map((item) => ({
        ...item,
        type: item.type.toLowerCase(),
      })),
    [typeData],
  );

  const poTypeChartConfig: PieConfig = useMemo(
    () => ({
      width: 120,
      height: 120,
      data: typeChartData,
      angleField: 'count',
      colorField: 'type',
      color: ({ type }) => {
        return chartTypeMapping[type as PoType].color;
      },
      legend: false,
      radius: 1,
      innerRadius: 0.6,
      label: false,
      statistic: {
        title: false,
        content: {
          style: {
            fontSize: '12px',
            fontWeight: 'normal',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          },
          content: miniPieTitle,
        },
      },
      tooltip: {
        domStyles: baseChartTooltipDomStyle,
        formatter: (datum: Datum) => {
          return {
            name:
              globalPoMapping[datum.type as keyof typeof globalPoMapping] ||
              datum.type,
            value: datum.count,
          };
        },
      },
    }),
    [miniPieTitle, typeChartData],
  );

  useEffect(() => {
    const maxItem = levelData.slice().sort((a, b) => b.count - a.count)[0];
    setLevelTextData(maxItem);
  }, [levelData]);

  useEffect(() => {
    const maxItem = typeData.slice().sort((a, b) => b.count - a.count)[0];
    setTypeTextData(maxItem);
  }, [typeData]);

  const mainPieReady = useCallback((plot) => {
    chartRef.current = plot;
    plot.on('element:click', (e: any) => {
      const total = e.view.filteredData.reduce(
        (a: any, b: any) => a + b.value,
        0,
      );
      const { value } = e.data.data;
      const percent = `${((value * 100) / total).toFixed(2)}%`;
      setCityInfo({
        type: e.data.data.type,
        color: e.data.color,
        value,
        percent,
      });
      setIndustryCode(e.data.data.code);
      setMiniPieTitle(e.data.data.type);
    });
  }, []);

  const levelMinChartReady = useCallback((plot) => {
    plot.on('element:click', (e: any) => {
      setLevelTextData(e.data.data);
    });
  }, []);
  const typeMinChartReady = useCallback((plot) => {
    plot.on('element:click', (e: any) => {
      setTypeTextData(e.data.data);
    });
  }, []);

  return {
    chartConfig,
    levelChartConfig,
    poTypeChartConfig,
    mainPieReady,
    levelMinChartReady,
    typeMinChartReady,
    startDate,
    endDate,
    regionCode,
    industryType,
    setIndustryCode,
    industryCode,
    typeTextData,
    setTypeTextData,
    levelTextData,
    setLevelData,
    setIndustryType,
    miniPieTitle,
    setMiniPieTitle,
    exportMutation,
    cityInfo,
    setCityInfo,
  };
};

export const useExportIndustryPies = (
  params: Omit<Params, 'type'> & {
    type: 1 | 2;
  },
) => {
  const { startDate, endDate, regionCode, type } = params;
  const exportMutation = useMutation(
    (
      p: BaseParams & {
        type: 1 | 2;
      },
    ) => exportIndustryAlertStat(p),
  );

  const handleExport = useCallback(
    (e) => {
      e.preventDefault();

      exportMutation.mutate(
        {
          startDate,
          endDate,
          regionCode: Number(regionCode),
          type,
        },
        {
          onSuccess(d) {
            exportFile(d, '不同污染源种类的报警次数');
            const container = document.getElementById('in-chart');
            if (container) {
              setTimeout(() => {
                html2canvas(container).then((canvas) => {
                  const url = canvas.toDataURL('image/jpeg');
                  downloadUseLink(url, '不同污染源种类的报警次数');
                });
              }, 1000);
            }
          },
        },
      );
    },
    [endDate, exportMutation, regionCode, startDate, type],
  );

  return handleExport;
};
