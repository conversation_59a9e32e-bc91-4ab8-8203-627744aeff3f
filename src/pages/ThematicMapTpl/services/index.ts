import { request } from '@/utils';
import type { SaveTplParams } from '../types';
import { stringify } from 'qs';
import omitBy from 'lodash/omitBy';
import type { FetchTplParams } from '@/pages/ThematicMapTplList/atoms';

export const getTplList = (params: FetchTplParams) => {
  const newParams = omitBy(params, (param) => !param && param !== 0);
  return request(`/api/tm/tem/list?${stringify(newParams)}`);
};
export interface RotationListParams {
  id: number;
  createType?: 1 | 2;
  endDate?: string;
  startDate?: string;
  page: number;
  size: number;
  sortType?: 'asc' | 'desc';
}
export const getTmTplRotationList = (params: RotationListParams) =>
  request(
    `/api/tm/tem/rotation/list?${stringify(omitBy(params, (param) => !param))}`,
  );

export const getTplDetails = (id: number | string) =>
  request(`/api/tm/tem?id=${id}`);

export const createTpl = (params: Omit<SaveTplParams, 'id'>) =>
  request(`/api/tm/tem/create`, {
    method: 'POST',
    body: JSON.stringify(params),
  });

export const updateTpl = (params: SaveTplParams) =>
  request(`/api/tm/tem/update`, {
    method: 'POST',
    body: JSON.stringify(params),
  });

export const deleteTpl = (id: string | number) =>
  request(`/api/tm/tem/delete?id=${id}`, {
    method: 'POST',
  });

export const toggleTplStauts = (id: string | number, status: 0 | 1) =>
  request(`/api/tm/tem/status/update?id=${id}&status=${status}`, {
    method: 'POST',
  });

export const getTplMenu = () => request(`/api/tm/menu`);

export const renameTpl = (params: { id: number; name: string }) =>
  request(`/api/tm/rename`, {
    method: 'POST',
    body: JSON.stringify(params),
  });

export const getTmMenu = () => request(`/api/tm/menu`);

export interface FetchTextureListParams {
  endDate: string;
  startDate: string;
  type: string;
  agg: string;
}

export const fetchTextureList = (params: FetchTextureListParams) =>
  request(`/api/texture/meta?${stringify(params)}`);
