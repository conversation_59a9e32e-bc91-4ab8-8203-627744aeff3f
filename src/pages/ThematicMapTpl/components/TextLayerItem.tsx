import { useRouter } from '@/hooks';
import { useCallback } from 'react';
import { useMemo } from 'react';
import React from 'react';
import {
  currentLayerIdAtom,
  tplLayerAtomFamily,
  tplLayerIdsAtom,
} from '../atoms';
import { useAtom } from 'jotai';
import Moveable from 'react-moveable';
import type { TextLayerProps } from '../types';

const TextLayerItem: React.FC<{
  id: string;
}> = ({ id }) => {
  const { query } = useRouter();
  const [layerIds] = useAtom(tplLayerIdsAtom);
  const [textLayer, setTextLayer] = useAtom(tplLayerAtomFamily({ id }));
  const { props, name, visible } = textLayer;
  const { top, left, color, width, height, fontSize, fontWeight, fontFamily } =
    props;
  const { r, g, b, a } = color;
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const isCurrent = currentLayerId === id;

  const index = useMemo(() => {
    return layerIds.findIndex((layerId) => layerId === id);
  }, [id, layerIds]);

  const setProps = useCallback(
    (newVal: Record<string, string | number>) => {
      setTextLayer((prev: TextLayerProps) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      }));
    },
    [setTextLayer],
  );

  return textLayer.type ? (
    <>
      <Moveable
        key={id}
        target={
          isCurrent && query.type !== 'view' && visible
            ? (document.querySelector(`#${id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        resizable={currentLayerId === id}
        draggable={currentLayerId === id}
        throttleDrag={0}
        throttleResize={0}
        onDrag={({ left: l, top: t }) => {
          setProps({
            top: t,
            left: l,
          });
        }}
        onResize={({ width: w, height: h }) => {
          setProps({
            width: w,
            height: h,
          });
        }}
      />
      <div
        id={id}
        style={{
          position: 'absolute',
          top,
          left,
          width,
          height,
          color: `rgba(${r}, ${g}, ${b}, ${a})`,
          fontSize,
          fontWeight,
          fontFamily,
          lineHeight: 1.5,
          zIndex: 1000 - index,
          userSelect: 'none',
          wordBreak: 'break-all',
          display: visible ? 'block' : 'none',
        }}
        onClick={() => setCurrentLayerId(id)}
      >
        {name}
      </div>
    </>
  ) : null;
};

export default TextLayerItem;
