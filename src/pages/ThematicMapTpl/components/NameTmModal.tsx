import DarkModal from '@/components/global/DarkModal';
import { Block, StyledButton } from '@/components/ui';
import formRules from '@/utils/formRules';
import { Form, Input } from 'antd';
import React, { useCallback, useMemo, useEffect, useState } from 'react';
import { ModalTitle, NameModalFooter, NameModalTag } from './ui';
import { useAtomValue } from 'jotai';
import { dateTypeAtom } from '../atoms';

interface Props {
  visible: boolean;
  name?: string;
  onOk: (name: string) => void;
  onCancel: () => void;
}

const NameTmModal: React.FC<Props> = ({ visible, name, onOk, onCancel }) => {
  const [form] = Form.useForm();
  const dateType = useAtomValue(dateTypeAtom);
  const [hasTag, setHasTag] = useState(true);
  const [paddingLeft, setPaddingLeft] = useState(11);

  const tagText = useMemo(() => {
    switch (dateType) {
      case 'daily':
        return '#yyyy年mm月dd日#';
      case 'weekly':
        return '#yyyy年-ww周#';
      case 'monthly':
        return '#yyyy年-mm月#';
      case 'quarterly':
        return '#yyyy年-q季度#';
      default:
        return '#yyyy年#';
    }
  }, [dateType]);

  useEffect(() => {
    if (name && visible) {
      const regex = /^#.+#/g;
      form.setFieldsValue({
        name: regex.test(name) ? name.replace(regex, '') : name,
      });
    }

    return () => {
      form.resetFields();
    };
  }, [form, name, visible]);

  useEffect(() => {
    return () => {
      setHasTag(true);
    };
  }, [visible]);

  const handleOk = useCallback(() => {
    form.validateFields().then((values) => {
      onOk(hasTag ? tagText + values.name : values.name);
    });
  }, [form, hasTag, onOk, tagText]);

  return (
    <DarkModal zIndex={10000} visible={visible} onCancel={onCancel}>
      <Block padding="30px 30px 0">
        <ModalTitle>设置模板名称</ModalTitle>
        <Form form={form}>
          <div
            style={{
              position: 'relative',
            }}
          >
            {hasTag && (
              <NameModalTag
                ref={(node) => {
                  if (node && hasTag) {
                    setPaddingLeft(node.offsetWidth + 12);
                  } else {
                    setPaddingLeft(11);
                  }
                }}
                id="modal-tag"
              >
                <span>{tagText}</span>
                <img
                  src="/assets/images/clear.png"
                  alt=""
                  onClick={() => setHasTag(false)}
                />
              </NameModalTag>
            )}
            <Form.Item
              name="name"
              style={{
                position: 'relative',
              }}
              rules={[
                ...formRules.required('请输入模板名称'),
                {
                  type: 'string',
                  max: 30,
                  message: '模板名称限制在30字符以内',
                },
              ]}
            >
              <Input
                name="name"
                className="dark-form-item"
                style={{
                  paddingLeft,
                }}
                placeholder="请输入模板名称"
              />
            </Form.Item>
          </div>
        </Form>
      </Block>
      <NameModalFooter>
        <StyledButton onClick={onCancel}>取消</StyledButton>
        <StyledButton variant="primary" onClick={handleOk}>
          确定
        </StyledButton>
      </NameModalFooter>
    </DarkModal>
  );
};

export default NameTmModal;
