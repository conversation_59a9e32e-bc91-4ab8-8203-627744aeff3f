import { useRouter } from '@/hooks';
import React from 'react';
import { useDrag } from 'react-dnd';
import { ToolItem } from './ui';

interface Props {
  label: string;
  iconClass: string;
  type: 'new-text' | 'new-rectangle' | 'new-circle' | 'new-arrow';
}
const ToolBoxDragItem: React.FC<Props> = ({ label, type, iconClass }) => {
  const { query } = useRouter();

  const [{ isDragging }, drag] = useDrag({
    type: 'toolBoxItem',
    item: () => ({ type }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag() {
      return query.type !== 'view';
    },
  });

  return (
    <ToolItem
      ref={drag}
      style={
        isDragging
          ? {
              background: '#286cff',
              color: 'white',
            }
          : {}
      }
    >
      <i className={`icomoon ${iconClass}`} />
      <div className="label">{label}</div>
    </ToolItem>
  );
};

export default ToolBoxDragItem;
