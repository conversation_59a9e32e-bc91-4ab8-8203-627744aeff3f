import { Flex, Spacer } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { Select, DatePicker } from 'antd';
import { useAtom } from 'jotai';
import { dateAtom, dateTypeAtom } from '../atoms';
import { ConfigLabel } from './ui';
import dayjs from 'dayjs';
import { dateFormatter } from '@/utils';
// import { useUpdateFamilyChildProps } from '../hooks';

const DataScope = () => {
  const [dateType, setDateType] = useAtom(dateTypeAtom);
  const [date, setDate] = useAtom(dateAtom);
//  const update = useUpdateFamilyChildProps();
  return (
    <>
      <Flex>
        <Spacer>
          <ConfigLabel>模板类型</ConfigLabel>
          <Select
            style={{ marginRight: 12 }}
            className="w-full dark-form-item"
            getPopupContainer={getDarkContainer}
            value={dateType}
            onChange={(val) => {
              setDateType(val);
              if (val === 'daily') {
                setDate({
                  startDate: dayjs().subtract(1, 'd').format(dateFormatter),
                  endDate: dayjs().subtract(1, 'd').format(dateFormatter),
                });
              } else {
                setDate({
                  startDate:   dayjs()
                  .startOf(val.replace('ly', '') as any)
                  .format(dateFormatter),
                  endDate:  dayjs()
                  .endOf(val.replace('ly', '') as any)
                  .format(dateFormatter),
                });
              }
            }}
            options={[
              {
                label: '日均值',
                value: 'daily',
              },
              {
                label: '周均值',
                value: 'weekly',
              },
              {
                label: '月均值',
                value: 'monthly',
              },
              {
                label: '季度均值',
                value: 'quarterly',
              },
              {
                label: '年均值',
                value: 'yearly',
              },
            ]}
          />
        </Spacer>
        <Spacer style={{ marginLeft: 12 }}>
          <ConfigLabel>示例时间</ConfigLabel>

          <DatePicker
            allowClear={false}
            className="w-full dark-form-item"
            getPopupContainer={getDarkContainer}
            // @ts-ignore
            picker={dateType === 'daily' ? 'date' : dateType.replace('ly', '')}
            value={dayjs(date.startDate)}
            disabledDate={(cur) => cur && cur > dayjs()}
            onChange={(val) => {
              const formattedType =
                dateType === 'daily' ? 'date' : dateType.replace('ly', '');
              let startDate: string;
              let endDate: string;

              if (formattedType === 'date') {
                startDate = dayjs(val).format(dateFormatter);
                endDate = dayjs(val).format(dateFormatter);
              } else {
                startDate = dayjs(val)
                  .startOf(formattedType as any)
                  .format(dateFormatter);
                endDate = dayjs(val)
                  .endOf(formattedType as any)
                  .format(dateFormatter);
              }

              setDate({
                startDate,
                endDate,
              });
            }}
          />
        </Spacer>
      </Flex>
    </>
  );
};

export default DataScope;
