import { Input } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { currentLayerIdAtom, tplLayerAtomFamily } from '../atoms';
import type { ImageLayerProps } from '../types';
import { ConfigLabel, DarkContainer } from './ui';

const ImageLayerControl = () => {
  const currentId = useAtomValue(currentLayerIdAtom) as string;
  const [currentLayer, setCurrent] = useAtom(
    tplLayerAtomFamily({ id: currentId }),
  );

  return (
    <DarkContainer
      style={{
        padding: '12px 0',
      }}
    >
      <ConfigLabel>图层名称</ConfigLabel>
      <Input
        className="dark-form-item w-full mb-12"
        placeholder="请输入图层名称"
        value={currentLayer?.name}
        onChange={(e) => {
          const {
            target: { value },
          } = e;
          setCurrent((prev: ImageLayerProps) => ({
            ...prev,
            name: value,
          }));
        }}
      />
    </DarkContainer>
  );
};

export default ImageLayerControl;
