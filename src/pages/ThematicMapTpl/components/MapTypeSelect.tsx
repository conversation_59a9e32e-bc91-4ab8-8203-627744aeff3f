import { getDarkContainer } from '@/components/ui/utils';
import { Select } from 'antd';
import { useAtom } from 'jotai';
import React from 'react';
import { mapTypeAtom } from '../atoms';
import { Label } from './ui';

const MapTypeSelect = () => {
  const [type, setType] = useAtom(mapTypeAtom);
  return (
    <>
      <Label>地图模式</Label>
      <Select
        className="w-full dark-form-item"
        getPopupContainer={getDarkContainer}
        value={type}
        onChange={(val) => setType(val)}
        options={[
          {
            label: '影像底图',
            value: 'img',
          },
          {
            label: '标准底图',
            value: 'vec',
          },
          {
            label: '地形底图',
            value: 'ter',
          },
          {
            label: '无',
            value: '',
          },
        ]}
      />
    </>
  );
};

export default MapTypeSelect;
