import React, { memo } from 'react';
import TextLayerContainer from './TextLayerContainer';
import CompassLayer from './CompassLayer';
import ScaleLayer from './ScaleLayer';
import RectangleLayers from './RectangleLayers';
import ImageLayers from './ImageLayers';
import ArrowLayers from './ArrowLayers';
import CircleLayers from './CircleLayers';

const LayersInMap = () => {
  return (
    <div
      style={{
        pointerEvents: 'auto',
      }}
    >
      <RectangleLayers />
      <TextLayerContainer />
      <ScaleLayer />
      <CompassLayer />
      <ImageLayers />
      <ArrowLayers />
      <CircleLayers />
      {/* <LegendContainer /> */}
    </div>
  );
};

export default memo(LayersInMap);
