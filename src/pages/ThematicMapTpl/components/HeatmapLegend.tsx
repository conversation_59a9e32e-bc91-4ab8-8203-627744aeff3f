import { Flex } from '@/components/ui';
import { weatherValuesAndColors } from '@/utils';
import React from 'react';
import { useMemo } from 'react';
import { LegendItem, LegendType, LegendWrapper } from './ui';
import { remoteSensingValuesAndColors } from '@/utils/dataDown';

interface Props {
  type: string;
  id: string;
  top: number;
  left: number;
  color: string;
  visible: boolean;
  translate: number[];
  scale: number[];
  onClick: () => void;
}
const HeatmapLegend: React.FC<Props> = ({
  type,
  id,
  left,
  top,
  translate,
  scale,
  color,
  visible,
  onClick,
}) => {
  const lowerType = type.toLowerCase();
  const target =
    remoteSensingValuesAndColors[lowerType] ||
    weatherValuesAndColors[lowerType];
  const result = useMemo(() => {
    return [...(target?.values || [])].reverse();
  }, [target.values]);

  return (
    <LegendWrapper
      id={id}
      onClick={onClick}
      style={{
        display: visible ? 'flex' : 'none',
        position: 'absolute',
        zIndex: 1001,
        top,
        left,
        cursor: 'move',
        wordBreak: 'keep-all',
        userSelect: 'none',
        color,
        transform:
          `translate(${translate[0]}px, ` +
          ` ${translate[1]}px) scale(${scale[0]}, ${scale[1]})`,
      }}
    >
      <LegendType>
        <p>{target.cn || target.formula}</p>
        {target.unit && <p>({target.unit})</p>}
      </LegendType>
      <Flex>
        {result.map(
          (
            item: { min?: number; max?: number; color: string },
            idx: number,
          ) => (
            <LegendItem key={item.color}>
              <div
                className="lump"
                style={{
                  width: 40,
                  height: 8,
                  marginTop: 8,
                  background: item.color,
                }}
              />
              <div
                className="min"
                style={{
                  transform: idx === 0 ? 'tranlateX(-50%)' : 'none',
                  height: '1px',
                  flex: 1,
                }}
              >
                {idx === 0 && typeof item.min !== 'undefined' ? item.min : ''}
              </div>
              <div
                className="max"
                style={{
                  transform: `translateX(${
                    idx === result.length - 1 ? '0' : '50%'
                  })`,
                }}
              >
                {item.max}
              </div>
            </LegendItem>
          ),
        )}
      </Flex>
    </LegendWrapper>
  );
};

export default HeatmapLegend;
