import { Center } from '@/components/ui';
import { useRouter } from '@/hooks';
import React from 'react';
import { LayersActions } from './ui';

const RightPanelActions: React.FC<{
  handleSave: () => void;
  handleExport: () => void;
  handleClear: () => void;
  handleBack: () => void;
}> = ({ handleSave, handleExport, handleClear, handleBack }) => {
  const { query } = useRouter();
  return (
    <LayersActions>
      {query.type !== 'view' && (
        <Center onClick={handleSave}>
          <i className="icomoon icon-save-line" />
          保存
        </Center>
      )}
      <Center onClick={handleExport}>
        <i className="icomoon icon-export-line" />
        导出
      </Center>
      {!query.type && (
        <Center onClick={handleClear}>
          <i className="icomoon icon-trash-line" />
          清空
        </Center>
      )}
      {(query.type === 'edit' || query.type === 'view') && (
        <Center onClick={handleBack}>
          <i className="icomoon icon-back" />
          返回
        </Center>
      )}
    </LayersActions>
  );
};

export default RightPanelActions;
