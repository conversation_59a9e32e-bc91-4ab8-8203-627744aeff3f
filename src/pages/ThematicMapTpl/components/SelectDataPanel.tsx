import { <PERSON><PERSON><PERSON><PERSON>, StyledButton } from '@/components/ui';
import { message, Collapse, Checkbox } from 'antd';
import { useAtomValue } from 'jotai';
import { useAtomCallback } from 'jotai/utils';
import { uniqueId } from 'lodash';
import React, { useMemo, useState } from 'react';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { useQuery } from 'react-query';
import { tplLayerAtomFamily, tplLayerIdsAtom } from '../atoms';
import { getTmMenu } from '../services';
import type { LayerProps, TmMenuItem } from '../types';
import {
  CollapseTitle,
  DataContainer,
  DataItem,
  DataItemsContainer,
  DataSelectPanel,
  SelectDataModalBody,
  SelectDataModalFooter,
  SelectDataModalTitle,
} from './ui';
import { useFormatDisplayName } from '@/utils';

const { Panel } = Collapse;

export interface SelectedItem {
  id: string;
  dataUrl: string;
  layerType: string;
  groupName: string;
  displayName: string;
  internalName: string;
  legendType: string;
}

interface Props {
  visible?: boolean;
  onOk: (vals: SelectedItem[]) => void;
  onCancel: () => void;
}
const SelectDataPanel: React.FC<Props> = ({ visible, onOk, onCancel }) => {
  const [selected, setSelected] = useState<SelectedItem[]>([]);
  const [layers, setLayers] = useState<LayerProps[]>([]);
  const { data } = useQuery<TmMenuItem[]>('tm-menu', getTmMenu);
  const [activeKey, setActiveKey] = useState<string>('');
  const layerIds = useAtomValue(tplLayerIdsAtom);

  const getDataLayers = useAtomCallback(
    useCallback(
      (get) => {
        return layerIds
          .map((id) => {
            return get(tplLayerAtomFamily({ id }));
          })
          .filter((layer) => layer.type === 'heatmap');
      },
      [layerIds],
    ),
  );

  useEffect(() => {
    const lys = getDataLayers();
    setLayers(lys);
  }, [getDataLayers]);

  const layerNames = useMemo(() => {
    return layers.map((item) => item.name);
  }, [layers]);

  const handleOnOk = useCallback(() => {
    if (selected.length === 0) {
      message.error('请选择数据源');
    } else {
      onOk(selected);
    }
  }, [onOk, selected]);

  const formattedData = useMemo(() => {
    return (data || []).map((item) => ({
      ...item,
      id: uniqueId('group-'),
      elements: item.elements.map((child) => ({
        ...child,
        id: uniqueId('child-'),
      })).filter((item)=>!['DMASK','DCOLOR'].includes(item.internalName)),
    }));
  }, [data]);

  const getLayerLengendType = (item: TmMenuItem) => {
    if (item.layerType === 'heatmap') {
      return 'heatmap';
    }

    if (item.layerType === 'nation' || item.layerType === 'province') {
      return 'station';
    }
    return '';
  };

  const createNewItem = useCallback((item: any, parent: any) => {
    const { layerType, groupName } = parent;
    const { id, displayName, internalName, dataUrl } = item;
    return {
      id,
      layerType,
      dataUrl,
      groupName,
      displayName,
      internalName,
      legendType: getLayerLengendType(parent),
    };
  }, []);

  const handleItemChange = useCallback(
    (item, parent) => {
      setSelected((prev) => {
        if (prev.find((child) => child.id === item.id)) {
          return prev.filter((v) => v.id !== item.id);
        }

        return [createNewItem(item, parent), ...prev];
      });
    },
    [createNewItem],
  );

  useEffect(() => {
    return () => {
      setSelected([]);
    };
  }, [visible]);

  useEffect(() => {
    if (formattedData.length > 0) {
      setActiveKey(formattedData[0].id);
    }
  }, [formattedData]);

  const {formatDisplayName} =useFormatDisplayName()

  

  return visible ? (
    <DataSelectPanel>
      <SelectDataModalBody>
        <SelectDataModalTitle>新增数据</SelectDataModalTitle>
        <DataContainer>
          <Collapse activeKey={[String(activeKey)]} accordion>
            {formattedData.map((item) => {
              const selectedIds = selected.map((s) => s.id);
              return (
                <Panel
                  showArrow={false}
                  header={
                    <HorCenter>
                      <CollapseTitle
                        onClick={() =>
                          setActiveKey((prev) =>
                            prev === item.id ? '' : item.id,
                          )
                        }
                      >
                        {item.groupName}
                      </CollapseTitle>
                    </HorCenter>
                  }
                  key={item.id}
                >
                  <DataItemsContainer>
                    {item.elements.map((el) => {
                      const disabled = layerNames.includes(
                        `${item.groupName}-${el.displayName}`,
                      );

                      return (
                        <DataItem key={el.id}>
                          <Checkbox
                            checked={selectedIds.includes(el.id) || disabled}
                            disabled={disabled}
                            onChange={() => {
                              handleItemChange(el, item);
                            }}
                          >
                            <div className="label">
                              {formatDisplayName(el.displayName)}
                            </div>
                          </Checkbox>
                        </DataItem>
                      );
                    })}
                  </DataItemsContainer>
                </Panel>
              );
            })}
          </Collapse>
        </DataContainer>
        <SelectDataModalFooter>
          <StyledButton onClick={onCancel}>取消</StyledButton>
          <StyledButton variant="primary" onClick={handleOnOk}>
            确定
          </StyledButton>
        </SelectDataModalFooter>
      </SelectDataModalBody>
    </DataSelectPanel>
  ) : null;
};

export default SelectDataPanel;
