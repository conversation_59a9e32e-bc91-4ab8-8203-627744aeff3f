import DarkModal from '@/components/global/DarkModal';
import { Block, StyledButton } from '@/components/ui';
import React from 'react';
import { ModalTitle, NameModalFooter } from './ui';

interface Props {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
}

const ConfirmSaveModal: React.FC<Props> = ({ visible, onOk, onCancel }) => {
  return (
    <DarkModal zIndex={10000} visible={visible} onCancel={onCancel}>
      <Block padding="30px 30px 0">
        <ModalTitle>是否确定保存模板？</ModalTitle>
        <div>保存后，系统将使用最新保存的模板定期生成专题图。</div>
      </Block>
      <NameModalFooter>
        <StyledButton onClick={onCancel}>取消</StyledButton>
        <StyledButton variant="primary" onClick={onOk}>
          确定
        </StyledButton>
      </NameModalFooter>
    </DarkModal>
  );
};

export default ConfirmSaveModal;
