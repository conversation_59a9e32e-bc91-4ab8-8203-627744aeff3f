/* eslint-disable max-len */
import { Center, Spacer } from '@/components/ui';
import { useRouter, useWindowSize } from '@/hooks';
import { useAtom } from 'jotai';
import { useSet<PERSON>tom as useUpdateAtom } from 'jotai';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { useMemo } from 'react';
import { geojsonVisibleAtom, tileLabelVisibleAtom } from '../atoms';
import {
  accordionStatusAtom,
  canvasSizeAtom,
  currentTmIdAtom,
  currentTmNameAtom,
  tplLayerIdsAtom,
  layerLegendsAtom,
  mapTypeAtom,
  tileLayerOpacityAtom,
  tplStatusAtom,
} from '../atoms';
import MapLayersInPanel from './MapLayersInPanel';
import MapTypeSelect from './MapTypeSelect';
import RegionCascader from './RegionCascader';
import DataScope from './DataScope';
import SelectImageDataPanel from './SelectImageDataPanel';
import {
  CheckboxContainer,
  DarkContainer,
  DetailsViewLine,
  LayersActions,
  PanelTitle,
  PropPanel,
  Right,
  RightInner,
  ScrollContainer,
  SubTitle,
} from './ui';
import NameTmModal from './NameTmModal';
import { useState } from 'react';
import { useQuery } from 'react-query';
import type { TplItem } from '../types';
import { getTplDetails } from '../services';
import OpacitySlider from './OpacitySlider';
import ToolBox from './ToolBox';
import type { SelectedItem } from './SelectDataPanel';
import DataSelectPanel from './SelectDataPanel';
import { useCreateLayers, useEditEffect, useSaveTpl } from '../hooks';
import { Checkbox } from 'antd';
import ConfirmSaveModal from './ConfirmSaveModal';
import { typeOpts } from '@/pages/ThematicMapList/components/Filter';
import LabelAndGeojsonVisibleControl from '@/pages/ThematicMap/components/LabelAndGeojsonVisibleControl';
import CanvasSizeControlContainer from './CanvasSizeControlContainer';

const RightPanel = () => {
  const { query, push, history } = useRouter();
  const [visible, setVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);
  const [imagePanelVisible, setImagePanelVisible] = useState(false);
  const { height } = useWindowSize();
  const [currentTmName, setCurrentTmName] = useAtom(currentTmNameAtom);
  const [accordionStatus, setAccordionStatus] = useAtom(accordionStatusAtom);
  const setLayerIds = useUpdateAtom(tplLayerIdsAtom);
  const setMapType = useUpdateAtom(mapTypeAtom);
  const setCurrentTmId = useUpdateAtom(currentTmIdAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);
  const [tileLayerOpacity, setTileLayerOpacity] = useAtom(tileLayerOpacityAtom);
  const createDataLayers = useCreateLayers();
  const [status, setStatus] = useAtom(tplStatusAtom);
  const setCanvasSize = useUpdateAtom(canvasSizeAtom);
  const [geojsonVisible, setGeojsonVisible] = useAtom(geojsonVisibleAtom);
  const [tileLabelVisible, setTileLabelVisible] = useAtom(tileLabelVisibleAtom);
  const saveTpl = useSaveTpl();
  const { data: details } = useQuery<TplItem>(
    ['tm-map-detail', query.id],
    () => getTplDetails(query.id),
    {
      enabled: !!query.id,
      staleTime: 0,
    },
  );
  const parsedDetailsContent = useEditEffect(details);

  const beforeSave = useCallback(() => {
    if (query.type === 'copy' || query.type === 'edit') {
      setVisible(true);
    } else {
      if (status === 1) {
        setConfirmModalVisible(true);
      } else {
        setVisible(true);
      }
    }
  }, [query.type, status]);

  useEffect(() => {
    if (!query.id) {
      const node = document.getElementById('map-container');

      setCanvasSize({
        width: (node?.offsetWidth || 0) - 4,
        height: (node?.offsetHeight || 0) - 4,
      });
    }
  }, [query.id, setCanvasSize]);

  useEffect(() => {
    return () => {
      setLayerIds([]);
      setCurrentTmName('');
      setCurrentTmId(null);
      setLayerLegends([]);
      setMapType('img');
      setGeojsonVisible(true);
      setTileLabelVisible(true);
    };
  }, [
    setCurrentTmId,
    setCurrentTmName,
    setGeojsonVisible,
    setLayerIds,
    setLayerLegends,
    setMapType,
    setTileLabelVisible,
  ]);

  const isViewPage = useMemo(() => query.type === 'view', [query.type]);
  const detailsMapType = useMemo(() => {
    if (details?.content) {
      const parsed = JSON.parse(details.content || '');
      return parsed.mapType;
    }

    return '';
  }, [details?.content]);

  const createLayers = useCallback(
    (values: SelectedItem[]) => {
      createDataLayers(values, () => {
        setPanelVisible(false);
      });
    },
    [createDataLayers],
  );

  return (
    <Right style={{ height: height - 120 }}>
      <DataSelectPanel
        visible={panelVisible}
        onOk={createLayers}
        onCancel={() => setPanelVisible(false)}
      />
      <SelectImageDataPanel
        visible={imagePanelVisible}
        onOk={(values) => {
          createLayers(values);
          setImagePanelVisible(false);
        }}
        onCancel={() => setImagePanelVisible(false)}
      />
      <RightInner>
        {query.type !== 'view' && (
          <ToolBox
            onDataLayerBtnClick={() => {
              setPanelVisible((prev) => {
                if (!prev) {
                  setImagePanelVisible(false);
                }
                return !prev;
              });
            }}
            dataPanelVisible={panelVisible}
            imagePanelVisible={imagePanelVisible}
            onImageLayerBtnClick={() => {
              setImagePanelVisible((prev) => {
                if (!prev) {
                  setPanelVisible(false);
                }
                return !prev;
              });
            }}
          />
        )}
        <PropPanel>
          <ScrollContainer>
            <SubTitle
              onClick={() =>
                setAccordionStatus((prev) => ({
                  ...prev,
                  range: !prev.range,
                }))
              }
            >
              <PanelTitle>
                <div className="line" />
                <span>基础设置</span>
              </PanelTitle>
              <Spacer />
              <i
                className="icomoon icon-arrow-down"
                style={{
                  transition: 'all .2s',
                  transform: `rotate(${accordionStatus.range ? '0' : '180deg'
                    })`,
                }}
              />
            </SubTitle>
            {accordionStatus.range && (
              <DarkContainer>
                {isViewPage ? (
                  <>
                    <DetailsViewLine>
                      <div className="label">行政区域</div>
                      <div className="value">{parsedDetailsContent.shouldShowAllRegion ? "全域" : details?.region}</div>
                    </DetailsViewLine>
                    <DetailsViewLine>
                      <div className="label">模板类型</div>
                      <div className="value">
                        {
                          typeOpts.find(
                            (item) => item.value === details?.cycleType,
                          )?.label
                        }
                      </div>
                    </DetailsViewLine>
                  </>
                ) : (
                  <>
                    <RegionCascader />
                    <DataScope />
                    <CheckboxContainer>
                      <Checkbox
                        checked={status === 1}
                        style={{ color: 'white' }}
                        onChange={(e) => {
                          setStatus(Number(e.target.checked));
                        }}
                      >
                        定期生成专题图
                      </Checkbox>
                    </CheckboxContainer>
                  </>
                )}
              </DarkContainer>
            )}
            <SubTitle
              onClick={() =>
                setAccordionStatus((prev) => ({
                  ...prev,
                  display: !prev.display,
                }))
              }
            >
              <PanelTitle>
                <div className="line" />
                <span>显示设置</span>
              </PanelTitle>
              <Spacer />
              <i
                className="icomoon icon-arrow-down"
                style={{
                  transition: 'all .2s',
                  transform: `rotate(${accordionStatus.display ? '0' : '180deg'
                    })`,
                }}
              />
            </SubTitle>
            {accordionStatus.display && (
              <>
                <DarkContainer>
                  {isViewPage ? (
                    <>
                      <DetailsViewLine>
                        <div className="label">地图模式</div>
                        <div className="value">
                          {!detailsMapType && '无'}
                          {detailsMapType === 'img' && '影像底图'}
                          {detailsMapType === 'vec' && '标准底图'}
                        </div>
                      </DetailsViewLine>
                      <DetailsViewLine>
                        <div className="label">画布尺寸</div>
                        <div className="value">
                          {parsedDetailsContent.width}px *{' '}
                          {parsedDetailsContent.height}px
                        </div>
                      </DetailsViewLine>
                    </>
                  ) : (
                    <>
                      <MapTypeSelect />
                      <LabelAndGeojsonVisibleControl
                        geojsonVisible={geojsonVisible}
                        setGeojsonVisible={(bool) => {
                          setGeojsonVisible(bool);
                        }}
                        tileLabelVisible={tileLabelVisible}
                        setTileLabelVisible={(bool) => {
                          setTileLabelVisible(bool);
                        }}
                      />
                      <OpacitySlider
                        value={tileLayerOpacity}
                        setValue={(val) => setTileLayerOpacity(val)}
                      />
                      <CanvasSizeControlContainer />
                    </>
                  )}
                </DarkContainer>
                <MapLayersInPanel />
              </>
            )}
          </ScrollContainer>
          <LayersActions>
            <Center
              onClick={() => {
                // history.goBack();
                history.push('/thematic-map-tpl-list');
              }}
            >
              <i className="icomoon icon-close" />
              {query.type === 'view' ? '返回' : '取消'}
            </Center>
            {query.type !== 'view' && (
              <Center
                onClick={() => {
                  beforeSave();
                }}
              >
                <i className="icomoon icon-save-line" />
                保存
              </Center>
            )}
          </LayersActions>
        </PropPanel>
      </RightInner>
      <NameTmModal
        name={currentTmName}
        visible={visible}
        onOk={(name) => {
          saveTpl({
            name,
            callback: () => {
              setVisible(false);
              setTimeout(() => {
                push('/thematic-map-tpl-list');
              }, 1200);
            },
          });
        }}
        onCancel={() => setVisible(false)}
      />
      <ConfirmSaveModal
        visible={confirmModalVisible}
        onOk={() => {
          setConfirmModalVisible(false);
          if (query.type === 'edit' && currentTmName) {
            saveTpl({
              name: currentTmName,
              callback: () => {
                setTimeout(() => {
                  push('/thematic-map-tpl-list');
                }, 1200);
              },
            });
            return;
          }
          setVisible(true);
        }}
        onCancel={() => {
          setConfirmModalVisible(false);
        }}
      />
    </Right>
  );
};

export default RightPanel;
