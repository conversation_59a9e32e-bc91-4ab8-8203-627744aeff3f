import { useAtom } from 'jotai';
import React, { useMemo, useCallback } from 'react';
import Moveable from 'react-moveable';
import {
  currentLayerIdAtom,
  tplLayerAtomFamily,
  tplLayerIdsAtom,
  viewStateAtom,
} from '../atoms';
import { useRouter } from '@/hooks';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useLayerCanDrop, useUpdateFamilyChildProps } from '../hooks';

const ImageLayerItem: React.FC<{
  isCurrent: boolean;
  id: string;
  index: number;
}> = ({ isCurrent, id, index }) => {
  const { query } = useRouter();
  const [layer] = useAtom(tplLayerAtomFamily({ id }));
  const { props } = layer;
  const [, setCurrentId] = useAtom(currentLayerIdAtom);
  const setViewState = useUpdateAtom(viewStateAtom);
  const drop = useLayerCanDrop();
  const update = useUpdateFamilyChildProps();

  const updateLayerProps = useCallback(
    (newVal: any) => {
      update({
        id,
        newVal,
      });
    },
    [id, update],
  );

  return (
    <React.Fragment key={id}>
      <Moveable
        key={id}
        target={
          isCurrent && query.type !== 'view' && layer.visible
            ? (document.querySelector(`#${id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        resizable={isCurrent}
        draggable={isCurrent}
        throttleDrag={0}
        throttleResize={0}
        onDrag={({ left, top }) => {
          updateLayerProps({
            top,
            left,
          });
        }}
        onResize={({ width, target, delta, direction }) => {
          const top = (target as HTMLDivElement).offsetTop;
          const left = (target as HTMLDivElement).offsetLeft;
          const newTop = direction[1] < 0 ? top - delta[1] : top;
          const newLeft = direction[0] < 0 ? left - delta[0] : left;

          updateLayerProps({
            top: newTop,
            left: newLeft,
            width,
          });
        }}
      />
      <div
        id={id}
        ref={drop}
        style={{
          position: 'absolute',
          top: props.top,
          left: props.left,
          width: props.width,
          zIndex: 1000 - index,
          display: layer.visible ? 'block' : 'none',
        }}
        onClick={() => {
          if (query.type === 'view') return;
          setCurrentId(layer.id);
        }}
        onWheel={(e) => {
          setViewState((prev) => {
            let zoom = (prev.zoom || 0) + (e.deltaY > 0 ? -0.5 : 0.5);
            zoom = zoom < 3 ? 3 : zoom;
            zoom = zoom > 18 ? 18 : zoom;

            return {
              ...prev,
              minZoom: 3,
              zoom,
              transitionDuration: 0,
            };
          });
        }}
      >
        <img src={props.url} alt="" width="100%" />
      </div>
    </React.Fragment>
  );
};

const ImageLayers = () => {
  const layerIds = useAtomValue(tplLayerIdsAtom);
  const [currentId] = useAtom(currentLayerIdAtom);

  const imageLayerIds = useMemo(() => {
    return layerIds.filter((layerId) => layerId.includes('image'));
  }, [layerIds]);

  return (
    <>
      {imageLayerIds.map((layerId) => {
        const isCurrent = layerId === currentId;
        const index = layerIds.findIndex((lyId) => layerId === lyId);
        return (
          <ImageLayerItem
            key={layerId}
            id={layerId}
            isCurrent={isCurrent}
            index={index}
          />
        );
      })}
    </>
  );
};

export default ImageLayers;
