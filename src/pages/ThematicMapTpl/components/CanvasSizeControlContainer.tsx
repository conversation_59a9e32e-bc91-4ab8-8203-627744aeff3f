import { useAtom } from 'jotai';
import React from 'react';
import { canvasSizeAtom } from '../atoms';
import { useReCalcLayerPos } from '../hooks';
import CanvasSizeControl from './CanvasSizeControl';

const CanvasSizeControlContainer = () => {
  const reCalcLayerPos = useReCalcLayerPos();
  const [canvasSize, setCanvasSize] = useAtom(canvasSizeAtom);

  return (
    <CanvasSizeControl
      width={canvasSize.width}
      height={canvasSize.height}
      resetSize={() => {
        const node = document.getElementById('map-container');
        setCanvasSize((prev) => {
          reCalcLayerPos({
            oldSize: {
              width: prev.width,
              height: prev.height,
            },
            newSize: {
              width: (node?.offsetWidth || 0) - 4,
              height: (node?.offsetHeight || 0) - 4,
            },
          });
          return {
            width: (node?.offsetWidth || 0) - 4,
            height: (node?.offsetHeight || 0) - 4,
          };
        });
      }}
      setWidth={(val) => {
        // if (+val > 1330 || +val < 808) {
        //   message.error('画布最大宽度为1330px，最大宽度为1330px');
        // } else {
        setCanvasSize((prev) => {
          reCalcLayerPos({
            oldSize: {
              width: prev.width,
              height: prev.height,
            },
            newSize: {
              width: +val,
              height: prev.height,
            },
          });

          return {
            ...prev,
            width: +val,
          };
        });
        // }
      }}
      setHeight={(val) => {
        // if (+val > 950 || +val < 592) {
        //   message.error('画布最大高度为950px，最小高度为592px');
        // }
        setCanvasSize((prev) => {
          reCalcLayerPos({
            oldSize: {
              width: prev.width,
              height: prev.height,
            },
            newSize: {
              width: prev.width,
              height: +val,
            },
          });
          return {
            ...prev,
            height: +val,
          };
        });
      }}
    />
  );
};

export default CanvasSizeControlContainer;
