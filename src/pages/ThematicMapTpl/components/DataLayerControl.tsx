import { DarkContainer } from '@/components/ui';
import React, { useMemo, useCallback } from 'react';
import { ConfigLabel } from './ui';
import { HorCenter, Spacer } from '@/components/ui';
import { Popover } from 'antd';
import { SketchPicker } from 'react-color';
import {
  currentLayerIdAtom,
  layerLegendsAtom,
  tplLayerAtomFamily,
} from '../atoms';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { dcolorLegends } from '@/pages/ThematicMap/utils';
import { LegendText } from '@/pages/ThematicMap/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { transformRgbColorToString } from '../utils';

const DataLayerControl = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const [legends, setLegends] = useAtom(layerLegendsAtom);

  const currentLegend = useMemo(() => {
    return legends.find((item) => item.id === currentLayerId);
  }, [currentLayerId, legends]);
  const current = useAtomValue(
    tplLayerAtomFamily({
      id: currentLayerId,
    }),
  );
  const updateLegend = useCallback(
    (val) => {
      setLegends((prev) => {
        const newLegends = prev.map((item) => {
          if (
            item.id === currentLayerId ||
            item.id.includes(currentLayerId || '')
          ) {
            return {
              ...item,
              props: {
                ...item.props,
                textColor: val,
              },
            };
          }
          return item;
        });
        return newLegends;
      });
    },
    [currentLayerId, setLegends],
  );

  // const match = useMemo(() => {
  //   const lowerType = currentLegend?.dataType.toLowerCase();
  //   if (currentLegend?.layerType === 'heatmap') {
  //     return (
  //       remoteSensingValuesAndColors[lowerType || ''] ||
  //       weatherValuesAndColors[lowerType || '']
  //     );
  //   }
  //   return stationPollutionValuesAndColors[lowerType || ''];
  // }, [currentLegend?.dataType, currentLegend?.layerType]);

  return (
    <DarkContainer
      style={{
        padding: '12px 0',
      }}
    >
      {/* <ConfigLabel>值域范围</ConfigLabel>
      <HorCenter>
        <InputNumber
          size="small"
          className="dark-form-item"
          style={{ flex: 1, marginRight: 8 }}
          min={0}
          max={20}
          defaultValue={1}
        />
        <span>-</span>
        <InputNumber
          size="small"
          className="dark-form-item"
          style={{ flex: 1, marginLeft: 8 }}
          min={0}
          max={20}
        />
      </HorCenter> */}
      {current.type === 'heatmap' && (
        <HorCenter style={{ marginTop: 12, paddingRight: 12 }}>
          <ConfigLabel>图例设置</ConfigLabel>
          <Spacer />
          <i
            className={`icomoon icon-${
              currentLegend?.visible ? 'visible' : 'hidden'
            }`}
            style={{ cursor: 'pointer' }}
            onClick={() => {
              setLegends((prev) =>
                prev.map((item) =>
                  item.id === currentLayerId
                    ? {
                        ...item,
                        visible: !item.visible,
                      }
                    : item,
                ),
              );
            }}
          />
        </HorCenter>
      )}
      <LegendText>
        <Popover
          content={
            <SketchPicker
              color={currentLegend?.props.textColor}
              onChange={(result) => updateLegend(result.rgb)}
            />
          }
          placement="leftBottom"
          getPopupContainer={getDarkContainer}
          trigger={['click']}
        >
          <div
            className="block"
            style={{
              background: transformRgbColorToString(
                currentLegend?.props.textColor,
              ),
            }}
          />
        </Popover>
        <div className="label">文</div>
      </LegendText>
      {current.type === 'colormap' &&
        current.props.dataType === 'DCOLOR' &&
        dcolorLegends.map((item) => {
          const lId = `${currentLayerId}-${item.id}`;
          const cur = legends.find((lItem) => lItem.id === lId);

          return (
            <HorCenter
              style={{ marginTop: 12, paddingRight: 12 }}
              key={item.id}
            >
              <ConfigLabel>图例 | {item.label}</ConfigLabel>
              <Spacer />
              <i
                className={`icomoon icon-${
                  cur?.visible ? 'visible' : 'hidden'
                }`}
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  setLegends((prev) =>
                    prev.map((legendItem) =>
                      legendItem.id === lId
                        ? {
                            ...legendItem,
                            visible: !legendItem.visible,
                          }
                        : legendItem,
                    ),
                  );
                }}
              />
            </HorCenter>
          );
        })}
      {/* <Popover
        getPopupContainer={getDarkContainer}
        trigger={['click']}
        content={
          <div>
            <Flex
              className="colors"
              style={{
                width: 300,
                padding: '12px 0',
              }}
            >
              {(match?.values || [])
                .slice()
                .reverse()
                .map((item: any) => (
                  <div
                    key={item.color}
                    className="item"
                    style={{
                      flex: 1,
                      background: item.color,
                      height: 8,
                    }}
                  />
                ))}
            </Flex>
            <Flex
              className="colors"
              style={{
                width: 300,
                padding: '12px 0',
              }}
            >
              {(match?.values || [])
                .slice()
                .reverse()
                .map((item: any) => (
                  <div
                    key={item.color}
                    className="item"
                    style={{
                      flex: 1,
                      background: item.color,
                      height: 8,
                    }}
                  />
                ))}
            </Flex>
          </div>
        }
      >
        <LegendBox>
          <Flex className="colors">
            {(match?.values || [])
              .slice()
              .reverse()
              .map((item: any) => (
                <div
                  key={item.color}
                  className="item"
                  style={{
                    background: item.color,
                  }}
                />
              ))}
          </Flex>

          <i className="icomoon icon-arrow-down" />
        </LegendBox>
      </Popover> */}
    </DarkContainer>
  );
};

export default DataLayerControl;
