import { Slider } from 'antd';
import { Label, OpacityValWrapper, SliderWrapper } from './ui';

const OpacitySlider: React.FC<{
  value: number;
  setValue: (val: number) => void;
}> = ({ value, setValue }) => {
  return (
    <>
      <Label style={{ marginTop: 12 }}>不透明度</Label>
      <SliderWrapper>
        <Slider
          className="flex-1"
          max={100}
          min={0}
          value={value}
          onChange={(val) => {
            setValue(val);
          }}
        />
        <OpacityValWrapper>
          <input
            value={value}
            onChange={(e) => {
              const { value: inputVal } = e.target;
              const val = Number(inputVal);

              if (val) {
                if (val > 100) {
                  setValue(100);
                } else if (val < 0) {
                  setValue(0);
                } else {
                  setValue(val);
                }
              } else {
                setValue(0);
              }
            }}
          />
          <div className="label">%</div>
        </OpacityValWrapper>
      </SliderWrapper>
    </>
  );
};

export default OpacitySlider;
