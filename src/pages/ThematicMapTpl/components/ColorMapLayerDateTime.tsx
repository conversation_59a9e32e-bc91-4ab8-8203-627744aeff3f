import { Flex } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { dateFormatter } from '@/utils';
import { DatePicker, Select } from 'antd';
import { useAtomValue } from 'jotai';
import moment from 'moment';
import React, { useMemo } from 'react';
import { useQuery } from 'react-query';
import { currentLayerIdAtom, tplLayerAtomFamily } from '../atoms';
import { useUpdateFamilyChildProps } from '../hooks';
import { fetchTextureList } from '../services';
import { ConfigLabel } from './ui';

const ColorMapLayerDateTime = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const update = useUpdateFamilyChildProps();
  const current = useAtomValue(
    tplLayerAtomFamily({
      id: currentLayerId,
    }),
  );
  const { startDate, endDate } = current.props;

  const { data } = useQuery(['meta-in-control', startDate], () =>
    fetchTextureList({
      agg: 'none',
      startDate,
      endDate,
      type: current.props.dataType,
    }),
  );

  const options = useMemo(() => {
    return (data || []).map((item: any) => ({
      label: moment(item.dateTime).format('HH:mm'),
      value: moment(item.dateTime).format('HH:mm'),
    }));
  }, [data]);

  return (
    <>
      <ConfigLabel>数据范围</ConfigLabel>
      <Flex>
        <DatePicker
          className="dark-form-item flex-1"
          getPopupContainer={getDarkContainer}
          value={moment(current.props.startDate)}
          disabledDate={(curDate) => {
            return curDate && curDate.isAfter(moment().endOf('day'));
          }}
          onChange={(val) => {
            if (val) {
              update({
                id: String(currentLayerId),
                newVal: {
                  startDate: val.format(dateFormatter),
                  endDate: val.format(dateFormatter),
                },
              });
            }
          }}
        />
        <Select
          className="flex-1 dark-form-item"
          getPopupContainer={getDarkContainer}
          onChange={(val) =>
            update({
              id: String(currentLayerId),
              newVal: {
                time: val,
              },
            })
          }
          style={{ marginLeft: 20 }}
          options={options}
          value={current.props.time}
        />
      </Flex>
    </>
  );
};

export default ColorMapLayerDateTime;
