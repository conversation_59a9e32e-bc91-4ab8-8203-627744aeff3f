import { useRouter } from '@/hooks';
import { useAtomValue } from 'jotai';
import React, { useCallback, useMemo } from 'react';
import {
  currentLayerIdAtom,
  tplLayerAtomFamily,
  tplLayerIdsAtom,
  scaleValueAtom,
} from '../atoms';
import { ScaleContainer } from './ui';
import Moveable from 'react-moveable';
import { useAtom } from 'jotai';
import { transformRgbColorToString } from '../utils';
import type { ScaleLayerProps } from '../types';

const ScaleLayer = () => {
  const { query } = useRouter();
  const [layerIds] = useAtom(tplLayerIdsAtom);
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const scaleLayerId = useMemo(() => {
    const find = layerIds.find((layer) => layer.includes('scale-layer'));
    return find || '';
  }, [layerIds]);
  const scaleVal = useAtomValue(scaleValueAtom);
  const [scaleLayer, setScaleLayer] = useAtom(
    tplLayerAtomFamily({ id: scaleLayerId }),
  );
  const isCurrent = currentLayerId === scaleLayer?.id;

  const setProps = useCallback(
    (newVal) => {
      setScaleLayer((prev: ScaleLayerProps) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      }));
    },
    [setScaleLayer],
  );

  return scaleLayer.type ? (
    <>
      <Moveable
        target={
          isCurrent && query.type !== 'view' && scaleLayer.visible
            ? (document.querySelector(`#${scaleLayer.id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        draggable={isCurrent}
        throttleDrag={0}
        throttleResize={0}
        onDrag={({ left: l, top: t }) => {
          setProps({
            top: t,
            left: l,
          });
        }}
        onResize={({ width, height }) => {
          setProps({
            width,
            height,
          });
        }}
      />

      <ScaleContainer
        id={scaleLayer.id}
        fontSize={scaleLayer.props.fontSize}
        color={transformRgbColorToString(scaleLayer.props.color)}
        borderStyle={scaleLayer.props.borderStyle}
        borderWidth={scaleLayer.props.borderWidth}
        style={{
          top: scaleLayer.props.top,
          left: scaleLayer.props.left,
          display: scaleLayer.visible ? 'flex' : 'none',
          transform:
            `translate(${scaleLayer.props.translate[0]}px, ` +
            ` ${scaleLayer.props.translate[1]}px)`,
          zIndex: 9999,
        }}
        onClick={() => {
          if (query.type === 'view') return;
          setCurrentLayerId(scaleLayer.id);
        }}
      >
        <div className="bar">
          <span>{scaleVal[1]}</span>
        </div>
      </ScaleContainer>
    </>
  ) : null;
};

export default ScaleLayer;
