import { useAtom } from 'jotai';
import React from 'react';
import {
  currentLayerId<PERSON>tom,
  tplLayerAtomFamily,
  tplLayerIdsAtom,
  viewStateAtom,
} from '../atoms';
import Moveable from 'react-moveable';
import { useRouter } from '@/hooks';
import { transformRgbColorToString } from '../utils';
import { useSetAtom as useUpdateAtom } from 'jotai';
import { useUpdateFamilyChildProps } from '../hooks';
import type { BorderStyle } from '../types';

const ArrowLayerItem: React.FC<{
  id: string;
  isCurrent: boolean;
  index: number;
}> = ({ id, isCurrent, index }) => {
  const { query } = useRouter();
  const update = useUpdateFamilyChildProps();
  const setViewState = useUpdateAtom(viewStateAtom);
  const [, setCurrentId] = useAtom(currentLayerIdAtom);
  const [layer] = useAtom(tplLayerAtomFamily({ id }));
  const { props } = layer;
  const strokeDashArray = {
    dashed: '10, 20',
    dotted: '5, 5',
    solid: '',
  };
  return (
    <React.Fragment key={id}>
      <Moveable
        key={id}
        target={
          isCurrent && query.type !== 'view' && layer.visible
            ? (document.querySelector(`#${id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={false}
        edge={false}
        zIndex
        draggable={isCurrent}
        scalable={isCurrent}
        rotatable={isCurrent}
        throttleDrag={1}
        throttleResize={1}
        throttleRotate={0.1}
        onDrag={({ translate }) => {
          update({
            id,
            newVal: {
              translate,
            },
          });
        }}
        onScale={({ scale }) => {
          update({
            id,
            newVal: {
              scale,
            },
          });
        }}
        onRotate={({ dist }) => {
          update({
            id,
            newVal: {
              rotate: dist,
            },
          });
        }}
        onMouseDown={() => setCurrentId(layer.id)}
      />
      <div
        id={id}
        onClick={() => setCurrentId(id)}
        onWheel={(e) => {
          setViewState((prev) => {
            let zoom = (prev.zoom || 0) + (e.deltaY > 0 ? -0.5 : 0.5);
            zoom = zoom < 3 ? 3 : zoom;
            zoom = zoom > 18 ? 18 : zoom;

            return {
              ...prev,
              minZoom: 3,
              zoom,
              transitionDuration: 0,
            };
          });
        }}
        className="target"
        style={{
          position: 'absolute',
          top: props.top,
          left: props.left,
          display: layer.visible ? 'block' : 'none',
          width: props.width,
          height: props.height,
          zIndex: 1000 - index,
          opacity: props.opacity / 100,
          transform:
            `translate(${props.translate[0]}px,` +
            ` ${props.translate[1]}px) ` +
            `scale(${props.scale[0]}, ${props.scale[1]})` +
            ` rotate(${props.rotate}deg)`,
        }}
      >
        <svg
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          width={48}
          height={48}
        >
          <path
            // eslint-disable-next-line max-len
            d="M263.232 576L640 931.84 554.24 1024 0 515.392 549.952 0 640 98.88 270.4 448H1024v128H263.232z"
            fill={transformRgbColorToString(layer.props.backgroundColor)}
            strokeWidth={layer.props.borderWidth}
            stroke={transformRgbColorToString(layer.props.borderColor)}
            strokeDasharray={
              strokeDashArray[layer.props.borderStyle as BorderStyle]
            }
          />
        </svg>
      </div>
    </React.Fragment>
  );
};

const ArrowLayers = () => {
  const [layerIds] = useAtom(tplLayerIdsAtom);
  const [currentId] = useAtom(currentLayerIdAtom);

  return (
    <>
      {layerIds.map((layerId, index) => {
        const isCurrent = layerId === currentId;

        return layerId.includes('arrow') ? (
          <ArrowLayerItem
            key={layerId}
            isCurrent={isCurrent}
            id={layerId}
            index={index}
          />
        ) : null;
      })}
    </>
  );
};

export default ArrowLayers;
