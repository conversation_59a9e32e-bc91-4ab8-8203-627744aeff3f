import {
  currentLayerIdAtom,
  layerCtrlVisibleAtom,
  tplLayerIdsAtom,
  layerLegendsAtom,
} from '../atoms';
import { Label, LayersContainer } from './ui';
import MapLayerItemInPanel from './MapLayerItemInPanel';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useAtom } from 'jotai';
import { Modal } from 'antd';
import React, { useEffect } from 'react';
import { useAtomValue } from 'jotai';
import { useRouter } from '@/hooks';

const MapLayersInPanel = () => {
  const { query } = useRouter();
  const [layerIds, setLayerIds] = useAtom(tplLayerIdsAtom);
  const [currentId] = useAtom(currentLayerIdAtom);
  const [, setLayerLegends] = useAtom(layerLegendsAtom);
  const ctrlVisible = useAtomValue(layerCtrlVisibleAtom);

  useEffect(() => {
    const handleKeydown = (e: any) => {
      if (
        e.key === 'Delete' &&
        currentId &&
        !currentId.includes('compass') &&
        !currentId.includes('scale')
      ) {
        Modal.confirm({
          title: '是否删除此图层？',
          zIndex: 10001,
          onOk() {
            setLayerIds((prev) => prev.filter((id) => id !== currentId));
            setLayerLegends((prev) =>
              prev.filter(
                (item) => item.id !== currentId && !item.id.includes(currentId),
              ),
            );
          },
        });
      }
    };
    window.addEventListener('keydown', handleKeydown);

    return () => {
      window.removeEventListener('keydown', handleKeydown);
    };
  }, [currentId, setLayerIds, setLayerLegends]);

  return (
    <DndProvider backend={HTML5Backend}>
      <LayersContainer>
        <Label>已添加图层</Label>
        {layerIds.map((id, index) => (
          <React.Fragment key={id}>
            <MapLayerItemInPanel
              currentId={currentId}
              key={id}
              id={id}
              index={index}
              ctrlVisible={ctrlVisible}
              typeInUrlQuery={query.type}
            />
          </React.Fragment>
        ))}
      </LayersContainer>
    </DndProvider>
  );
};

export default MapLayersInPanel;
