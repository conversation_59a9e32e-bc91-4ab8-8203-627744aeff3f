import { Block, Flex, HorCenter, Spacer } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { CaretDownOutlined } from '@ant-design/icons';
import { Input, InputNumber, Popover, Select, message, Checkbox } from 'antd';
import { useAtom } from 'jotai';
import { useCallback } from 'react';
import { SketchPicker } from 'react-color';
import { currentLayerIdAtom, tplLayerAtomFamily } from '../atoms';
import type { CircleLayerProps } from '../types';
import { transformRgbColorToString } from '../utils';
import OpacitySlider from './OpacitySlider';
import {
  ColorConfigRect,
  ConfigLabel,
  DarkContainer,
  Divider,
  ShapeConfItemContainer,
  SolidConfigBox,
  SolidOption,
} from './ui';

const CircleLayerControl = () => {
  const [currentId] = useAtom(currentLayerIdAtom);
  const [currentLayer, setCurrentLayer] = useAtom(
    tplLayerAtomFamily({ id: currentId }),
  );

  const circleClickHandle = useCallback(() => {
    const circle = document.getElementById(currentId || '');
    if (circle) {
      circle.click();
    }
  }, [currentId]);

  const updateLayerBaseProps = useCallback(
    (newVal: Record<string, any>) =>
      setCurrentLayer((prev: CircleLayerProps) => ({
        ...prev,
        ...newVal,
      })),
    [setCurrentLayer],
  );

  const updateLayerProps = useCallback(
    (newVal: Record<string, any>) =>
      setCurrentLayer((prev: CircleLayerProps) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      })),
    [setCurrentLayer],
  );

  return (
    <DarkContainer
      style={{
        padding: '12px 0',
      }}
    >
      <ConfigLabel>图层名称</ConfigLabel>
      <Input
        className="dark-form-item w-full mb-12"
        placeholder="请输入图层名称"
        value={currentLayer?.name}
        onChange={(e) => {
          const {
            target: { value },
          } = e;
          if (value === '') {
            message.error('图层名称不能为空');
          } else {
            updateLayerBaseProps({
              name: value.length > 30 ? String(currentLayer?.name) : value,
            });
          }
        }}
      />
      <Divider />
      <Flex style={{ marginTop: 12 }}>
        <Spacer>
          <Block padding="0 0 0 6px">
            <Checkbox
              style={{ color: 'white' }}
              checked={currentLayer?.props.as === 'circle'}
              onChange={(e) => {
                const { checked } = e.target;

                updateLayerProps({
                  as: checked ? 'circle' : 'rectangle',
                });
              }}
            >
              圆形
            </Checkbox>
          </Block>
        </Spacer>
        <Spacer>
          <Block padding="0 0 0 6px">
            <Checkbox
              style={{ color: 'white' }}
              checked={currentLayer?.props.as === 'rectangle'}
              onChange={(e) => {
                const { checked } = e.target;

                updateLayerProps({
                  as: checked ? 'rectangle' : 'circle',
                });
              }}
            >
              矩形
            </Checkbox>
          </Block>
        </Spacer>
      </Flex>
      <ConfigLabel marginTop="12px">输入中心经纬度</ConfigLabel>
      <Flex className="mb-12">
        <InputNumber
          min={1}
          max={180}
          className="dark-form-item flex-1"
          style={{ marginRight: 12 }}
          placeholder="经度"
          value={currentLayer?.props.lon}
          onChange={(val) => {
            const hasDecimal = `${val}`.includes('.');

            if (!val) {
              message.error('经度不能为空');
            } else if (hasDecimal && `${val}`.split('.')[1].length > 20) {
              message.error('请保留最多20位小数');
            } else {
              updateLayerProps({
                lon: Number(val),
              });
            }
          }}
        />
        <InputNumber
          min={0}
          max={90}
          className="dark-form-item flex-1"
          placeholder="纬度"
          value={currentLayer?.props.lat}
          onChange={(val) => {
            const hasDecimal = `${val}`.includes('.');
            if (!val) {
              message.error('纬度不能为空');
            } else if (hasDecimal && `${val}`.split('.')[1].length > 20) {
              message.error('请保留最多20位小数');
            } else {
              updateLayerProps({
                lat: Number(val),
              });
            }
          }}
        />
      </Flex>
      <Divider />
      <HorCenter className="mt-12">
        <InputNumber
          bordered={false}
          className="dark-form-item flex-1"
          value={currentLayer?.props.radius}
          onChange={(val) => {
            circleClickHandle();
            updateLayerProps({
              radius: val,
            });
          }}
        />
        <Select
          style={{ width: 90 }}
          className="dark-form-item"
          getPopupContainer={getDarkContainer}
          value={currentLayer?.props.unit}
          onChange={(val) =>
            updateLayerProps({
              unit: val,
              radius:
                val === 'km'
                  ? (currentLayer?.props.radius || 1000) / 1000
                  : (currentLayer?.props.radius || 1) * 1000,
            })
          }
          options={[
            {
              label: '公里',
              value: 'km',
            },
            {
              label: '米',
              value: 'm',
            },
          ]}
        />
      </HorCenter>
      <ShapeConfItemContainer className="mt-12">
        <div className="item">
          <ConfigLabel>填充</ConfigLabel>
          <Popover
            content={
              <SketchPicker
                color={currentLayer?.props.backgroundColor}
                onChange={(result) =>
                  updateLayerProps({
                    backgroundColor: result.rgb,
                  })
                }
              />
            }
            placement="rightTop"
            getPopupContainer={getDarkContainer}
            trigger={['click']}
          >
            <ColorConfigRect
              style={{
                backgroundColor: transformRgbColorToString(
                  currentLayer?.props.backgroundColor,
                ),
              }}
            />
          </Popover>
        </div>
        <div className="item">
          <ConfigLabel>描边</ConfigLabel>
          <Popover
            content={
              <SketchPicker
                color={currentLayer?.props.borderColor}
                onChange={(result) =>
                  updateLayerProps({
                    borderColor: result.rgb,
                  })
                }
              />
            }
            placement="rightTop"
            getPopupContainer={getDarkContainer}
            trigger={['click']}
          >
            <ColorConfigRect
              style={{
                border: `2px solid ${transformRgbColorToString(
                  currentLayer?.props.borderColor,
                )}`,
                background: 'transparent',
              }}
            />
          </Popover>
        </div>
        <div className="item">
          <ConfigLabel>线宽</ConfigLabel>
          <InputNumber
            bordered={false}
            size="small"
            className="dark-form-item"
            style={{ width: 80 }}
            min={0}
            max={20}
            value={currentLayer?.props.borderWidth}
            onChange={(val) =>
              updateLayerProps({
                borderWidth: val,
              })
            }
          />
        </div>
        <div className="item">
          <ConfigLabel>类型</ConfigLabel>
          <Popover
            content={
              <div>
                <SolidOption
                  onClick={() => {
                    updateLayerProps({
                      borderStyle: 'solid',
                    });
                  }}
                >
                  <div className="line" />
                </SolidOption>
                <SolidOption
                  borderStyle="dotted"
                  onClick={() => {
                    updateLayerProps({
                      borderStyle: 'dotted',
                    });
                  }}
                >
                  <div className="line" />
                </SolidOption>
                <SolidOption
                  borderStyle="dashed"
                  onClick={() => {
                    updateLayerProps({
                      borderStyle: 'dashed',
                    });
                  }}
                >
                  <div className="line" />
                </SolidOption>
              </div>
            }
            placement="leftTop"
            getPopupContainer={getDarkContainer}
            trigger={['click']}
          >
            <SolidConfigBox>
              <div
                className="line"
                style={{ borderBottomStyle: currentLayer?.props.borderStyle }}
              />
              <CaretDownOutlined />
            </SolidConfigBox>
          </Popover>
        </div>
      </ShapeConfItemContainer>
      <OpacitySlider
        value={Number(currentLayer?.props.opacity)}
        setValue={(val) =>
          updateLayerProps({
            opacity: val,
          })
        }
      />
      <Flex style={{ marginTop: 12 }}>
        <Spacer>
          <Block padding="0 0 0 6px">
            <Checkbox
              style={{ color: 'white' }}
              checked={currentLayer?.props.backgroundColor.a === 0}
              onChange={(e) => {
                const { checked } = e.target;
                const prevAlpha = currentLayer?.props.backgroundColor.a || 255;

                updateLayerProps({
                  backgroundColor: {
                    ...currentLayer?.props.backgroundColor,
                    a: checked ? 0 : prevAlpha,
                  },
                });
              }}
            >
              无填充
            </Checkbox>
          </Block>
        </Spacer>
      </Flex>
    </DarkContainer>
  );
};

export default CircleLayerControl;
