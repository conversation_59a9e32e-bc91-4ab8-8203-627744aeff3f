import { Spacer } from '@/components/ui';
import {
  rankColors,
  stationPollutionValuesAndColors,
  warningRankText,
} from '@/utils';
import { useAtomValue } from 'jotai';
import React from 'react';
import { useMemo } from 'react';
import { mapTypeAtom } from '../atoms';
import { LegendItem, LegendType, LegendWrapper } from './ui';

interface Props {
  type: string;
  id: string;
  source: string;
  top: number;
  left: number;
  visible: boolean;
  index: number;
  translate: number[];
  scale: number[];
  onClick: () => void;
}
const ScatterPointLayerLegend: React.FC<Props> = ({
  type,
  id,
  left,
  top,
  source,
  visible,
  index,
  translate,
  scale,
  onClick,
}) => {
  const mapType = useAtomValue(mapTypeAtom);
  const lowerType = type.toLowerCase();
  const target = stationPollutionValuesAndColors[lowerType] || {};
  const result = useMemo(() => {
    return [...(target?.values || [])].reverse();
  }, [target.values]);

  return (
    <LegendWrapper
      id={id}
      style={{
        display: visible ? 'flex' : 'none',
        position: 'absolute',
        zIndex: 1000 - index,
        top,
        left,
        cursor: 'move',
        wordBreak: 'keep-all',
        userSelect: 'none',
        color: mapType === 'img' ? 'white' : 'black',
        transform:
          `translate(${translate[0]}px, ` +
          ` ${translate[1]}px) scale(${scale[0]}, ${scale[1]})`,
      }}
      onClick={onClick}
    >
      {source === 'station' && (
        <>
          <LegendType>
            <p>{target.cn || target.formula}</p>
            {target.unit && <p>({target.unit})</p>}
          </LegendType>
          {result.map(
            (
              item: { min?: number; max?: number; color: string },
              idx: number,
            ) => (
              <LegendItem key={item.color}>
                <div
                  className="lump"
                  style={{
                    width: 30,
                    height: 8,
                    marginTop: 8,
                    background: item.color,
                  }}
                />
                <div
                  className="min"
                  style={{
                    transform: idx === 0 ? 'tranlateX(-50%)' : 'none',
                  }}
                >
                  {idx === 0 && typeof item.min !== 'undefined' ? item.min : ''}
                </div>
                <Spacer style={{ height: '1px' }} />
                <div className="max">{item.max}</div>
              </LegendItem>
            ),
          )}
        </>
      )}
      {source === 'level' && (
        <>
          <LegendType>报警等级</LegendType>
          {rankColors.slice(2).map((color, idx) => {
            return (
              <LegendItem
                key={color}
                style={{
                  width: 50,
                  textAlign: 'center',
                }}
              >
                <div
                  className="lump"
                  style={{
                    width: 50,
                    height: 8,
                    marginTop: 8,
                    background: color,
                  }}
                />
                <Spacer>{warningRankText.slice(2)[idx]}</Spacer>
              </LegendItem>
            );
          })}
        </>
      )}
    </LegendWrapper>
  );
};

export default ScatterPointLayerLegend;
