import { getDarkContainer } from '@/components/ui/utils';
import { InputNumber, Popover } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import React from 'react';
import { SketchPicker } from 'react-color';
import { currentLayerIdAtom, tplLayerAtomFamily } from '../atoms';
import { useUpdateFamilyChildProps } from '../hooks';
import { transformRgbColorToString } from '../utils';
import LineStyleChooser from './LineStyleChooser';
import {
  ColorConfigRect,
  ConfigLabel,
  DarkContainer,
  ShapeConfItemContainer,
} from './ui';

const ScaleLayerControl = () => {
  const id = useAtomValue(currentLayerIdAtom) as string;
  const [current] = useAtom(tplLayerAtomFamily({ id }));
  const update = useUpdateFamilyChildProps();

  return (
    <DarkContainer
      style={{
        padding: '12px 0',
      }}
    >
      <ShapeConfItemContainer>
        <div className="item">
          <ConfigLabel>字号</ConfigLabel>
          <InputNumber
            size="small"
            className="dark-form-item"
            style={{ width: 80 }}
            min={12}
            max={40}
            value={current.props.fontSize}
            onChange={(val) =>
              update({
                id,
                newVal: {
                  fontSize: val,
                },
              })
            }
          />
        </div>
        <div className="item">
          <ConfigLabel>线宽</ConfigLabel>
          <InputNumber
            size="small"
            className="dark-form-item"
            style={{ width: 80 }}
            min={0}
            max={20}
            defaultValue={1}
            value={current.props.borderWidth}
            onChange={(val) =>
              update({
                id,
                newVal: {
                  borderWidth: val,
                },
              })
            }
          />
        </div>
        <div className="item">
          <ConfigLabel>描边</ConfigLabel>
          <Popover
            content={
              <SketchPicker
                color={current.props.color}
                onChange={(val) => {
                  update({ id, newVal: { color: val.rgb } });
                }}
              />
            }
            placement="rightTop"
            getPopupContainer={getDarkContainer}
            trigger={['click']}
          >
            <ColorConfigRect
              style={{
                border: `2px solid ${transformRgbColorToString(
                  current.props.color,
                )}`,
                background: 'transparent',
              }}
            />
          </Popover>
        </div>
        <div className="item">
          <ConfigLabel>类型</ConfigLabel>
          <LineStyleChooser
            style={current.props.borderStyle}
            handleItemClick={(style) => {
              update({
                id,
                newVal: {
                  borderStyle: style,
                },
              });
            }}
          />
        </div>
      </ShapeConfItemContainer>
    </DarkContainer>
  );
};

export default ScaleLayerControl;
