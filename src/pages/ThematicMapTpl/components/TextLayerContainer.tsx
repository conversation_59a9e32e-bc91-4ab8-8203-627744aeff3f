import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { tplLayerIdsAtom } from '../atoms';
import TextLayerItem from './TextLayerItem';

const TextLayerContainer = () => {
  const layerIds = useAtomValue(tplLayerIdsAtom);

  const textLayerIds = useMemo(
    () => layerIds.filter((layerId) => layerId.includes('text')),
    [layerIds],
  );

  return (
    <>
      {textLayerIds.map((layerId) => (
        <TextLayerItem key={layerId} id={layerId} />
      ))}
    </>
  );
};

export default TextLayerContainer;
