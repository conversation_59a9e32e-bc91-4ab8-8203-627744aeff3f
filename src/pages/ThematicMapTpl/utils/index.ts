import { dateFormatter } from '@/utils';
import type { DurationInputArg2 } from 'moment';
import moment from 'moment';
import type { RGBColor } from 'react-color';
import type {
  CircleLayerProps,
  RectangleLayerProps,
  ScaleLayerProps,
} from '../types';

export const whiteRgba = { r: 255, g: 255, b: 255, a: 1 };
export const blackRgba = { r: 0, g: 0, b: 0, a: 1 };
export const redRgba = { r: 255, g: 0, b: 0, a: 1 };

export const rectangleLayerDefaultConfig: RectangleLayerProps['props'] = {
  top: 0,
  left: 0,
  backgroundColor: whiteRgba,
  borderStyle: 'solid',
  opacity: 100,
  borderWidth: 1,
  borderColor: whiteRgba,
  width: 200,
  height: 200,
};
export const circleLayerDefaultConfig: CircleLayerProps['props'] = {
  top: 0,
  left: 0,
  backgroundColor: { r: 255, g: 255, b: 255, a: 1 },
  borderStyle: 'solid',
  opacity: 100,
  borderWidth: 1,
  borderColor: redRgba,
  radius: 20,
  unit: 'km',
  lon: 0,
  lat: 0,
  mode: 'coordinate',
  as: 'circle',
};

export const textLayerDefaultConfig = {
  top: 20,
  left: 20,
  height: 16 * 1.5,
  fontSize: 16,
  fontFamily: 'Microsoft Yahei',
  italic: false,
  fontWeight: 400,
  color: blackRgba,
};

export const compassLayerDefaultConfig = {
  top: 20,
  left: 20,
  width: 90,
  height: 90,
  style: 'simple',
};

export const transformRgbColorToString = (rgbColor?: RGBColor) =>
  rgbColor
    ? `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, ${rgbColor.a})`
    : '';

export const scaleLayerDefaultConfig: ScaleLayerProps['props'] = {
  borderWidth: 4,
  color: { r: 0, g: 0, b: 0, a: 1 },
  borderStyle: 'solid',
  fontSize: 14,
  translate: [0, 0],
  top: 0,
  left: 0,
  type: 'horizontal',
  style: 'bar',
};

type DataCycleType = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

export const getDateByType = (type: DataCycleType = 'daily') => {
  const now = moment();
  const startOfType =
    type === 'daily'
      ? 'day'
      : (type.slice(0, type.length - 2) as DurationInputArg2);
  const startDate = moment(now)
    .subtract(1, startOfType)
    .startOf(startOfType)
    .format(dateFormatter);
  const endDate = moment(now)
    .subtract(1, startOfType)
    .endOf(startOfType)
    .format(dateFormatter);

  return {
    startDate,
    endDate,
  };
};
