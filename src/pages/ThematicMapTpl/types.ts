import type { RGBColor } from 'react-color';

export interface BaseLayerProps {
  id: string;
  name: string;
  type:
    | 'heatmap'
    | 'scatter-point'
    | 'compass'
    | 'scale'
    | 'text'
    | 'image'
    | 'circle'
    | 'rectangle'
    | 'arrow'
    | 'colormap';
  visible: boolean;
}

export interface RectangleLayerProps extends BaseLayerProps {
  props: {
    top: number;
    left: number;
    width: number;
    height: number;
    borderWidth: number;
    backgroundColor: RGBColor;
    borderColor: RGBColor;
    opacity: number;
    borderStyle: 'dotted' | 'solid' | 'dashed';
  };
}

export interface CircleLayerProps extends BaseLayerProps {
  props: {
    top: number;
    left: number;
    borderWidth: number;
    backgroundColor: RGBColor;
    borderColor: RGBColor;
    opacity: number;
    borderStyle: 'dotted' | 'solid' | 'dashed';
    radius: number;
    unit: 'km' | 'm';
    lon: number;
    lat: number;
    mode: 'coordinate' | 'address';
    as: 'rectangle' | 'circle';
  };
}

export interface ArrowLayerProps extends RectangleLayerProps {
  props: RectangleLayerProps['props'] & {
    width: number;
    height: number;
    translate: number[];
    scale: number[];
    rotate: number;
  };
}

export interface HeatmapLayerProps extends BaseLayerProps {
  props: {
    dataType: string;
    dateType: string;
    startDate?: string;
    endDate?: string;
    fetchUrl: string;
    textureUrl?: string;
    mapUrl?: string;
    colorRamp?: ColorRamp;
    region: number;
    decoder?: string;
  };
}

export type ColorRamp = Record<number, string>;

export interface TextLayerProps extends BaseLayerProps {
  props: {
    width: number;
    height: number;
    top: number;
    left: number;
    fontFamily: string;
    fontSize: number;
    italic: boolean;
    fontWeight: number;
    color: RGBColor;
    isTitle?: boolean;
  };
}

export interface ScaleLayerProps extends BaseLayerProps {
  props: {
    top: number;
    left: number;
    borderWidth: number;
    color: RGBColor;
    fontSize: number;
    borderStyle: 'dotted' | 'solid' | 'dashed';
    // 当前支持一种水平比例尺
    type: 'horizontal' | 'verticle';
    // 当前支持柱状比例尺
    style: 'bar';
    translate: [number, number];
  };
}

export interface ImageLayerProps extends BaseLayerProps {
  props: {
    top: number;
    left: number;
    width: number;
    url: string;
  };
}

export interface CompassLayerProps extends BaseLayerProps {
  props: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
}

export interface ScatterPointLayerProps extends BaseLayerProps {
  props: {
    dateType: 'date' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
    startDate: string;
    endDate: string;
    legendType: string;
    fetchUrl: string;
    dataType: string;
    color: string;
    style: 'triangle' | 'circle' | 'lozenge' | 'star' | 'square';
  };
}

export type LayerProps =
  | HeatmapLayerProps
  | ScatterPointLayerProps
  | CompassLayerProps
  | ScaleLayerProps
  | TextLayerProps
  | ImageLayerProps
  | RectangleLayerProps
  | CircleLayerProps;

export interface TplItem {
  id: number;
  content: string;
  name: string;
  datarange: string;
  region: string;
  cycleType: DateType;
  status: 0 | 1;
}

export interface SaveTmParams {
  id: number;
  agg: string;
  content: string;
  endDate: string;
  name: string;
  regionCode: number | string;
  startDate: string;
}

export interface TmMenuItem {
  elements: {
    displayName: string;
    internalName: string;
    dataUrl: string;
  }[];
  groupName: string;
  layerType: string;
}
export interface Legend {
  id: string;
  layerType: 'heatmap' | 'colormap';
  dataType: string; // PM25 | PM10 | ....
  visible: boolean;
  render?: boolean;
  props: {
    top: number;
    left: number;
    translate: number[];
    scale: number[];
    textColor?: RGBColor;
    label?: string;
    color?: string;
    from?: string;
    to?: string;
  };
}

export type BorderStyle = 'solid' | 'dotted' | 'dashed';

export interface SaveTplParams {
  id: number;
  cycleType: string;
  content: string;
  name: string;
  regionCode: number | string;
  status: number;
  nameTemplate?: string;
}

export type DateType = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
