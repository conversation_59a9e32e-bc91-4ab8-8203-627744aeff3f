import { dateFormatter } from '@/utils';
import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import type { Viewport } from 'deck.gl';
import { atom } from 'jotai';
import { atomFamily } from 'jotai/utils';
import moment from 'moment';
import type { DateType, LayerProps, Legend } from '../types';

export const viewStateAtom = atom<ViewStateProps>({
  latitude: 39.915156,
  longitude: 116.400819,
  zoom: 6,
  maxZoom: 18,
  minZoom: 3,
  pitch: 0,
  bearing: 0,
});
export const regionCodeAtom = atom<number | null>(null);
export const tileLayerOpacityAtom = atom<number>(100);
export const tmapNameAtom = atom<undefined | string>(undefined);
export const dateTypeMapping = {
  date: 'daily',
  week: 'weekly',
  month: 'monthly',
  quarter: 'quarterly',
  year: 'yearly',
  custom: 'custom',
};
export const dateTypeAtom = atom<DateType>('weekly');
export const dateAtom = atom({
  startDate: moment().subtract(1, 'week').startOf('week').format(dateFormatter),
  endDate: moment().subtract(1, 'week').endOf('week').format(dateFormatter),
});
export const dataScopeAtom = atom<{
  type: keyof typeof dateTypeMapping;
}>({
  type: 'date',
});
export const mapTypeAtom = atom<'img' | 'vec' | 'ter'>('img');
export const layersAtom = atom<LayerProps[]>([]);
export const currentLayerIdAtom = atom<string | null>(null);
export const needDisableContainerEventAtom = atom(false);
export const scaleValueAtom = atom<string[] | number[]>([0, 0, 0]);
export const currentTmIdAtom = atom<number | null>(null);
export const currentTmNameAtom = atom('');
export const accordionStatusAtom = atom({
  range: true,
  display: true,
  data: true,
});
export const tplStatusAtom = atom(1);
export const layerLegendsAtom = atom<Legend[]>([]);
export const layerCtrlVisibleAtom = atom(true);
export const isCascaderChangeAtom = atom(false);
export const tplLayerAtomFamily = atomFamily(
  (params: any) => atom(params),
  (a: any, b: any) => a.id === b.id,
);
export const tplLayerIdsAtom = atom<string[]>([]);
// 是否显示全域
export const shouldShowAllRegionAtom = atom(false);
// 记录已经调用过setTileUrl的图层，防止重复执行
// 将请求参数stringify然后进行比较
export const renderedLayersAtom = atom<string[]>([]);

export const canvasSizeAtom = atom({
  width: 0,
  height: 0,
});

export interface CanvasSize {
  width: number;
  height: number;
}

// 省市行政区边界可见性
export const geojsonVisibleAtom = atom(true);
export const tileLabelVisibleAtom = atom(true);

export const viewportAtom = atom<Viewport | null>(null);
