import { useMemo } from 'react';
import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import Map from './components/Map';
import { Container, TmPageMain } from './components/ui';
import RightPanel from './components/RightPanel';
import { Link } from 'react-router-dom';
import { useRouter } from '@/hooks';

const ThematicMapTpl = () => {
  const { query } = useRouter();

  const text = useMemo(() => {
    if (query.type === 'view') {
      return '模板详情';
    } else if (query.type === 'edit') {
      return '编辑模板';
    } else if (query.type === 'copy') {
      return '复制模板';
    }
    return '创建模板';
  }, [query.type]);
  return (
    <TmPageMain>
      <HelmetTitle title="专题图" />
      <PageHead title="专题图">
        <Link to="/thematic-map-tpl-list">模板列表</Link>
        <i className="icomoon icon-next" />
        <span className="text-primary">{text}</span>
      </PageHead>
      <Container>
        <Map />
        <RightPanel />
      </Container>
    </TmPageMain>
  );
};

export default ThematicMapTpl;
