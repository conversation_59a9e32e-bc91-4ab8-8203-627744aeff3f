import { Tile<PERSON>ayer, GeoJsonLayer, WebMercatorViewport } from 'deck.gl';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  getLevelByRegionCode,
  getNewViewState,
  getTileUrl,
  wmts,
} from '@/utils';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useAtomCallback } from 'jotai/utils';
import { Modal, message } from 'antd';
import type { CanvasSize } from '../atoms';
import {
  canvasSizeAtom,
  currentLayerIdAtom,
  currentTmIdAtom,
  currentTmNameAtom,
  dataScopeAtom,
  dateTypeAtom,
  tplLayerAtomFamily,
  tplLayerIdsAtom,
  layerLegendsAtom,
  layersAtom,
  mapTypeAtom,
  regionCodeAtom,
  // renderedLayersAtom,
  shouldShowAllRegion<PERSON>tom,
  tileLayerOpacityAtom,
  tplStatusAtom,
  viewStateAtom,
  dateAtom,
  geojsonVisibleAtom,
  tileLabelVisibleAtom,
  isCascaderChangeAtom,
} from '../atoms';
import { baseGeojsonLayerConfig, request } from '@/utils';
import { useMutation, useQueries, useQuery } from 'react-query';
import type {
  HeatmapLayerProps,
  LayerProps,
  Legend,
  SaveTplParams,
  TplItem,
} from '../types';
import { stringify } from 'qs';
import {
  fetchGeojsonByRegionCode,
  fetchGeojsonIncludeChild,
  fetchUserTextureMap,
} from '@/services/global';
// eslint-disable-next-line max-len
import type { RGBColor } from 'react-color';
import { useAtom } from 'jotai';
import { useDrop } from 'react-dnd';
import {
  circleLayerDefaultConfig,
  compassLayerDefaultConfig,
  getDateByType,
  rectangleLayerDefaultConfig,
  scaleLayerDefaultConfig,
  textLayerDefaultConfig,
} from '../utils';
import type { SelectedItem } from '../components/SelectDataPanel';
import uniqueId from 'lodash/uniqueId';
import domtoimage from 'dom-to-image';
import { createTpl, updateTpl } from '../services';
import { useCreateTextureLayer, useRouter } from '@/hooks';
import { dcolorLegends, isSpecialArea } from '@/pages/ThematicMap/utils';
import center from '@turf/center';
import { userInfoAtom } from '@/atoms';
import { useTextureToken } from '@/pages/Overview/hooks';

import { colorRamps, decoder } from '@/utils/dataDown';
import dayjs from 'dayjs';

export const useTileLayerWithLabel = () => {
  const mapType = useAtomValue(mapTypeAtom);
  const visible = useAtomValue(tileLabelVisibleAtom);

  const labelType = useMemo(() => {
    if (mapType === 'img') return 'cia';
    if (mapType === 'ter') return 'cta';
    return 'cva';
  }, [mapType]);

  const labeledTileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'tianditu-with-label',
      data: mapType ? wmts(labelType) : wmts('cva'),
      pickable: false,
      visible,
    });
  }, [labelType, mapType, visible]);

  return { labeledTileLayer };
};

export const useTileLayer = () => {
  const mapType = useAtomValue(mapTypeAtom);
  const opacity = useAtomValue(tileLayerOpacityAtom);
  const tileLayer = useMemo(
    () => [
      new TileLayer({
        ...tileLayerBaseConfig,
        id: 'img-tileLayer',
        opacity: opacity / 100,
        data: wmts('img'),
        visible: mapType === 'img',
      }),
      new TileLayer({
        ...tileLayerBaseConfig,
        id: 'vec-tileLayer',
        opacity: opacity / 100,
        data: wmts('vec'),
        visible: mapType === 'vec',
      }),
      new TileLayer({
        ...tileLayerBaseConfig,
        id: 'ter-tileLayer',
        opacity: opacity / 100,
        data: wmts('ter'),
        visible: mapType === 'ter',
      }),
    ]
    ,
    [mapType, opacity],
  );

  return { tileLayer };
};

export const useGeoJSONLayer = (d: any) => {
  const mapType = useAtomValue(mapTypeAtom);
  const visible = useAtomValue(geojsonVisibleAtom);

  const layer = useMemo(
    () =>
      new GeoJsonLayer({
        ...baseGeojsonLayerConfig,
        getLineColor: mapType === 'img' ? [255, 255, 255] : [99, 99, 99],
        filled: false,
        getFillColor: () => [40, 108, 255, 120],
        data: d,
        visible,
      }),
    [d, mapType, visible],
  );

  return layer;
};

export const useMapTextureLayers = () => {
  const [layerIds] = useAtom(tplLayerIdsAtom);
  const regionCode = useAtomValue(regionCodeAtom);
  const [shouldShowAllRegion] = useAtom(shouldShowAllRegionAtom);
  const [dataLayers, setDataLayers] = useState<HeatmapLayerProps[]>([]);
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const currentDataLayer = useAtomValue(
    tplLayerAtomFamily({ id: currentLayerId }),
  );
  const dateType = useAtomValue(dateTypeAtom);
  const legends = useAtomValue(layerLegendsAtom);
  const token = useTextureToken();
  const { createTextureLayer } = useCreateTextureLayer();
  // const [renderedLayers, setRenderedLayers] = useAtom(renderedLayersAtom);
  const getDataLayers = useAtomCallback(
    useCallback(
      (get) => {
        return layerIds
          .filter(
            (layerId) =>
              layerId.includes('heatmap') || layerId.includes('colormap'),
          )
          .map((id) => {
            return get(tplLayerAtomFamily({ id }));
          });
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [layerIds, currentDataLayer, currentDataLayer.props, legends],
    ),
  );
  const { startDate, endDate } = useAtomValue(dateAtom);

  useEffect(() => {
    const lys = getDataLayers();
    setDataLayers(lys);
  }, [getDataLayers, currentDataLayer, currentLayerId, currentDataLayer.props]);

  const { data: map } = useQuery(
    ['user-texture-map', regionCode, shouldShowAllRegion],
    () =>
      fetchUserTextureMap(
        Number(
          isSpecialArea(`${regionCode}`) && !shouldShowAllRegion
            ? regionCode
            : 150000,
        ),
      ),
    {
      enabled: Boolean(regionCode),
    },
  );
  const queriesResult = useQueries(
    dataLayers.map((layer: HeatmapLayerProps) => ({
      queryKey: [
        layer.id,
        regionCode,
        dateType,
        layer.props.dataType,
        startDate,
        endDate,
      ],
      queryFn: () => {
        const {
          type,
          props: { dataType },
        } = layer;
        let agg = dateType;
        if (type === 'colormap' || dataType === 'DMASK' || dataType === 'DCOLOR') {
          agg = 'none';
        }
        return request(
          `${layer.props.fetchUrl}?${stringify({
            agg,
            startDate,
            endDate,
            regionCode,
            type: layer.props.dataType,
          })}`,
        )
      }

    })),
  );

  const mapLayers = () => {
    return dataLayers.map((layer, index) => {
      const currentData = queriesResult[index].data || [];

      if (layer.type === 'heatmap') {
        const dataType = layer.props.dataType.toLowerCase() as any;
        if (!map || !currentData[0]?.timePoints) return null;

        // const {
        //   props,
        // } = layer;
        let agg = dateType;
        if (layer.props.dataType === 'DCOLOR') {
          agg = 'none';
        }


        const url = getTileUrl(
          {
            agg,
            type: layer.props.dataType.toUpperCase(),
            time: currentData[0].timePoints,
            token: token || '',
          },
          true,
        );
        const textureLayer = createTextureLayer({
          id: `data-texture-layer-${layer.id}`,
          dataUrl: url,
          decoder: decoder[dataType],
          colorRamp: colorRamps[dataType],
          visible: layer.visible && currentData && currentData.length > 0,
          isBitmapLayer: layer.props.dataType === 'DCOLOR'
        });

        return textureLayer;
      }
    });
  };

  return mapLayers();
};

export const useMapContainerRect = (container: HTMLDivElement | null) => {
  const [pos, setPos] = useState({
    top: 20,
    left: 20,
  });

  useEffect(() => {
    if (container) {
      const left =
        container.getBoundingClientRect().left +
        document.documentElement.scrollLeft;
      const top =
        container.getBoundingClientRect().top +
        document.documentElement.scrollTop;

      setPos({
        left,
        top,
      });
    }
  }, [container]);

  return pos;
};

/**
 * 根据当前图层类型设置control标题
 * @returns string
 */
export const useCurrentLayerConfigName = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const layers = useAtomValue(layersAtom);

  const currentLayer = useMemo(() => {
    return layers.find((item) => item.id === currentLayerId);
  }, [currentLayerId, layers]);

  const name = useMemo(() => {
    if (currentLayer) {
      switch (currentLayer.type) {
        case 'arrow':
          return '箭头';
        case 'circle':
          return '圆形';
        case 'heatmap':
        case 'scatter-point':
          return '数据';
        case 'text':
          return '文本';
        case 'image':
          return '图片';
        case 'rectangle':
          return '矩形';
        default:
          return '';
      }
    }

    return '';
  }, [currentLayer]);

  return name;
};

export const useUpdateCurrentLayer = <T extends LayerProps>() => {
  const setLayers = useUpdateAtom(layersAtom);
  const currentId = useAtomValue(currentLayerIdAtom);

  const updateLayerProps = useCallback(
    (newVal: Record<string, number | string | RGBColor | boolean>) => {
      setLayers((prev) =>
        prev.map((item) =>
          item.id === currentId
            ? ({
              ...item,
              props: {
                ...item.props,
                ...newVal,
              },
            } as T)
            : item,
        ),
      );
    },
    [currentId, setLayers],
  );

  const updateLayerBaseProps = useCallback(
    (newVal: Record<string, string | number>) => {
      setLayers((prev) =>
        prev.map((item) =>
          item.id === currentId
            ? ({
              ...item,
              ...newVal,
            } as T)
            : item,
        ),
      );
    },
    [currentId, setLayers],
  );

  return {
    updateLayerProps,
    updateLayerBaseProps,
  };
};

export const useUpdateFamilyChildProps = () => {
  const update = useAtomCallback(
    useCallback(
      (get, set, args: { id: string; newVal: Record<string, any> }) => {
        const { id, newVal } = args;
        const prev = get(tplLayerAtomFamily({ id }));
        set(tplLayerAtomFamily({ id }), {
          ...prev,
          props: {
            ...prev.props,
            ...newVal,
          },
        });
      },
      [],
    ),
  );

  return update;
};

export const useLayerCanDrop = () => {
  const [layerIds, setLayerIds] = useAtom(tplLayerIdsAtom);
  const viewState = useAtomValue(viewStateAtom);
  const mapType = useAtomValue(mapTypeAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const rect = useMapContainerRect(
    document.getElementById('map-container') as HTMLDivElement | null,
  );

  const viewport = useMemo(() => {
    return new WebMercatorViewport(viewState);
  }, [viewState]);

  const [, drop] = useDrop(
    () => ({
      accept: ['dragItem', 'toolBoxItem'],
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        isOverCurrent: monitor.isOver({ shallow: true }),
      }),
      drop(
        item: { id: string; top: number; left: number; type: string },
        monitor,
      ) {
        const clientOffset = monitor.getClientOffset();
        let id: any;
        let newLayer: any;
        if (item.type === 'new-text') {
          const lens = layerIds.filter((layer) =>
            layer.includes('text'),
          ).length;
          id = `text-layer-${new Date().getTime()}`;
          const name = `未命名文字图层${lens + 1}`;
          newLayer = {
            id,
            type: 'text',
            name,
            visible: true,
            props: {
              ...textLayerDefaultConfig,
              width: name.length * 16,
              color:
                mapType === 'img'
                  ? { r: 255, g: 255, b: 255, a: 1 }
                  : { r: 0, g: 0, b: 0, a: 1 },
              left: (clientOffset?.x || 0) - rect.left,
              top: (clientOffset?.y || 0) - rect.top,
            },
          };
        } else if (item.type === 'new-rectangle') {
          id = `rectangle-layer-${new Date().getTime()}`;
          const lens = layerIds.filter((layer) =>
            layer.includes('rectangle'),
          ).length;
          newLayer = {
            id,
            type: 'rectangle',
            name: `色块图层${lens + 1}`,
            visible: true,
            props: {
              ...rectangleLayerDefaultConfig,
              left: (clientOffset?.x || 0) - rect.left - 100,
              top: (clientOffset?.y || 0) - rect.top - 100,
            },
          };
        } else if (item.type === 'new-circle') {
          id = `circle-layer-${new Date().getTime()}`;
          const lens = layerIds.filter((layer) =>
            layer.includes('circle'),
          ).length;

          const [lon, lat] = viewport.unproject([
            (clientOffset?.x || 0) - rect.left,
            (clientOffset?.y || 0) - rect.top,
          ]);
          newLayer = {
            id,
            type: 'circle',
            name: `范围图层${lens + 1}`,
            visible: true,
            props: {
              ...circleLayerDefaultConfig,
              lon,
              lat,
            },
          };
        } else if (item.type === 'new-arrow') {
          id = `arrow-layer-${new Date().getTime()}`;
          const lens = layerIds.filter((layer) =>
            layer.includes('arrow'),
          ).length;
          newLayer = {
            id,
            type: 'arrow',
            name: `箭头图层${lens + 1}`,
            visible: true,
            props: {
              ...rectangleLayerDefaultConfig,
              width: 48,
              height: 48,
              left: (clientOffset?.x || 0) - rect.left,
              top: (clientOffset?.y || 0) - rect.top,
              translate: [-24, -24],
              scale: [1, 1],
              rotate: 0,
            },
          };
        }

        tplLayerAtomFamily(newLayer);
        setLayerIds((prev) => [id, ...prev]);
        setCurrentLayerId(id);

        return undefined;
      },
    }),
    [layerIds, rect, viewport],
  );

  return drop;
};

export const useCreateLayers = () => {
  const regionCode = useAtomValue(regionCodeAtom);
  const [, setLayerIds] = useAtom(tplLayerIdsAtom);
  const setLegends = useUpdateAtom(layerLegendsAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);

  const createDataLayers = useCallback(
    (values: SelectedItem[], callback: () => void) => {
      const newLayers = values.map((item) => {
        const id = `${item.layerType
          }-layer-${uniqueId()}-${new Date().getTime()}`;
        const name = `${item.groupName}-${item.displayName}`;
        const type = item.layerType;
        const fetchUrl = '/api/texture/time/point';

        return {
          id,
          name,
          type,
          visible: true,
          props: {
            dataType: item.internalName,
            fetchUrl,
            region: regionCode,
            dateType: 'daily',
          },
        } as HeatmapLayerProps;
      });
      const container = document.getElementById('canvas');
      const newLegends: any[] = newLayers.map((item) => {
        const { id, props, type } = item;
        if (props.dataType === 'DCOLOR') {
          return dcolorLegends.map((legendItem, index) => {
            return {
              id: id + '-' + legendItem.id,
              props: {
                label: legendItem.label,
                color: legendItem.color,
                from: legendItem.from,
                to: legendItem.to,
                left: container!.clientWidth - 140,
                top: container!.clientHeight - (8 - index) * 24,
                scale: [1, 1],
                translate: [0, 0],
                textColor: { r: 0, g: 0, b: 0, a: 1 },
              },
              layerType: type,
              dataType: props.dataType,
              visible: true,
              // 区分图例颜色获取对象
              render: type === 'heatmap' || type === 'colormap',
            };
          });
        }
        return {
          id,
          props: {
            left: 20,
            top: container!.clientHeight - 60,
            scale: [1, 1],
            translate: [0, 0],
            textColor: { r: 0, g: 0, b: 0, a: 1 },
          },
          layerType: item.type as 'heatmap',
          dataType: props.dataType,
          visible: true,
          // 区分图例颜色获取对象
          render: item.type === 'heatmap',
        };
      });

      setLegends((prev) => [
        ...prev,
        ...newLegends.reduce((prevState, cur) => {
          return Array.isArray(cur)
            ? [...prevState, ...cur]
            : [...prevState, cur];
        }, [] as Legend[]),
      ]);

      newLayers.forEach((layer) => {
        tplLayerAtomFamily(layer);
      });

      setLayerIds((prev) => [...newLayers.map((layer) => layer.id), ...prev]);
      setCurrentLayerId(newLayers[0].id);

      if (callback && typeof callback === 'function') {
        callback();
      }
    },
    [regionCode, setCurrentLayerId, setLayerIds, setLegends],
  );

  return createDataLayers;
};

export const useClearLayers = () => {
  const setLayerIds = useUpdateAtom(tplLayerIdsAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const setLegends = useUpdateAtom(layerLegendsAtom);

  const clearLayers = useCallback(() => {
    Modal.confirm({
      title: '确认清空所有图层吗？',
      onOk() {
        setLayerIds((prev) => {
          const result = prev.filter(
            (id) => id.includes('scale') || id.includes('compass'),
          );
          setCurrentLayerId(result[0]);
          return result;
        });

        setLegends([]);
      },
    });
  }, [setCurrentLayerId, setLayerIds, setLegends]);

  return clearLayers;
};

export const useExportTmToImage = () => {
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);

  const exportTm = useCallback(
    (name: string) => {
      const currentId = currentLayerId;
      setCurrentLayerId(null);
      const container = document.getElementById('map-container');
      if (container) {
        container.style.borderColor = 'black';
        container.style.borderRadius = '0';
      }

      domtoimage
        .toJpeg(document.getElementById('map-container') as HTMLDivElement)
        .then((url) => {
          const linkEl = document.createElement('a');
          linkEl.href = url;
          linkEl.setAttribute('download', name);

          document.body.appendChild(linkEl);
          linkEl.click();
          document.body.removeChild(linkEl);
          setCurrentLayerId(currentId);

          if (container) {
            container.style.borderColor = '#ddd';
            container.style.borderRadius = '10px';
          }
        });
    },
    [currentLayerId, setCurrentLayerId],
  );

  return exportTm;
};

export const useSaveTpl = () => {
  const { query } = useRouter();
  const regionCode = useAtomValue(regionCodeAtom);
  const layerLegends = useAtomValue(layerLegendsAtom);
  const tileLayerOpacity = useAtomValue(tileLayerOpacityAtom);
  const mapType = useAtomValue(mapTypeAtom);
  const [currentTmId, setCurrentTmId] = useAtom(currentTmIdAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);
  const layerIds = useAtomValue(tplLayerIdsAtom);
  const viewState = useAtomValue(viewStateAtom);
  const shouldShowAllRegion = useAtomValue(shouldShowAllRegionAtom);
  const canvasSize = useAtomValue(canvasSizeAtom);
  const tileLabelVisible = useAtomValue(tileLabelVisibleAtom);
  const geojsonVisible = useAtomValue(geojsonVisibleAtom);

  const createMutation = useMutation((params: Omit<SaveTplParams, 'id'>) =>
    createTpl(params),
  );
  const updateMutation = useMutation((params: SaveTplParams) =>
    updateTpl(params),
  );
  const save = useAtomCallback(
    useCallback(
      (get, set, args: { name: string; callback?: () => void }) => {
        const { name, callback } = args;
        const { zoom, longitude, latitude } = viewState;
        const layers = layerIds.map((layerId) => {
          return get(tplLayerAtomFamily({ id: layerId }));
        });
        const cycleType = get(dateTypeAtom);
        const status = get(tplStatusAtom);
        const mapping = {
          daily: 'yyyy年mm月dd日',
          weekly: 'yyyy年-ww周',
          monthly: 'yyyy年-mm月',
          quarterly: 'yyyy年-q季度',
          yearly: 'yyyy年',
        };

        const submitData = {
          name,
          regionCode,
          cycleType,
          status,
          content: JSON.stringify({
            width: canvasSize.width,
            height: canvasSize.height,
            shouldShowAllRegion,
            tileLabelVisible,
            geojsonVisible,
            legends: layerLegends,
            layers,
            opacity: tileLayerOpacity,
            regionCode,
            type: cycleType,
            mapType,
            viewState: {
              zoom,
              longitude,
              latitude,
            },
          }),
        } as Omit<SaveTplParams, 'id'>;

        if (status === 1) {
          submitData.nameTemplate = mapping[cycleType];
        }

        if (currentTmId) {
          updateMutation.mutate(
            { ...submitData, id: query.id },
            {
              onSuccess() {
                message.success('操作成功');
                setCurrentTmName(name);

                if (typeof callback === 'function') {
                  callback();
                }
              },
            },
          );
        } else {
          createMutation.mutate(submitData, {
            onSuccess(d) {
              message.success('操作成功');
              setCurrentTmId(d.data);
              setCurrentTmName(name);

              if (typeof callback === 'function') {
                callback();
              }
            },
          });
        }
      },
      [
        canvasSize.height,
        canvasSize.width,
        createMutation,
        currentTmId,
        geojsonVisible,
        layerIds,
        layerLegends,
        mapType,
        query.id,
        regionCode,
        setCurrentTmId,
        setCurrentTmName,
        shouldShowAllRegion,
        tileLabelVisible,
        tileLayerOpacity,
        updateMutation,
        viewState,
      ],
    ),
  );

  return save;
};

export const useEditEffect = (details: TplItem | undefined) => {
  const { query } = useRouter();
  const [parsedDetailsContent, setParsedDetailsContent] = useState<any>({});
  const setRegionCode = useUpdateAtom(regionCodeAtom);
  const setLayerIds = useUpdateAtom(tplLayerIdsAtom);
  const setDataScope = useUpdateAtom(dataScopeAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const setLayers = useUpdateAtom(layersAtom);
  const setMapType = useUpdateAtom(mapTypeAtom);
  const setCurrentTmId = useUpdateAtom(currentTmIdAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);
  const setTileLayerOpacity = useUpdateAtom(tileLayerOpacityAtom);
  const setViewState = useUpdateAtom(viewStateAtom);
  const setDate = useUpdateAtom(dateAtom);
  const setDateType = useUpdateAtom(dateTypeAtom);
  const setStatus = useUpdateAtom(tplStatusAtom);
  const setShouldShowAllRegion = useUpdateAtom(shouldShowAllRegionAtom);
  const setCanvasSize = useUpdateAtom(canvasSizeAtom);
  const setTileLabelVisible = useUpdateAtom(tileLabelVisibleAtom);
  const setGeojsonVisible = useUpdateAtom(geojsonVisibleAtom);

  useEffect(() => {
    if (details && query.id) {
      const { content, cycleType } = details;
      const { startDate, endDate } = getDateByType(cycleType);
      const parsedContent = JSON.parse(content);
      setParsedDetailsContent(parsedContent);

      setDate({
        startDate,
        endDate,
      });

      if (query.type !== 'copy') {
        setCurrentTmId(details.id);
      }
      setRegionCode(parsedContent.regionCode);
      setShouldShowAllRegion(parsedContent.shouldShowAllRegion);
      setTileLayerOpacity(parsedContent.opacity);
      setCanvasSize({
        width: parsedContent.width,
        height: parsedContent.height,
      });
      setDataScope({
        type: parsedContent.type,
      });
      setDateType(details.cycleType);
      setStatus(details.status);
      setTileLabelVisible(parsedContent.tileLabelVisible);
      setGeojsonVisible(parsedContent.geojsonVisible);

      parsedContent.layers
        .map((layer: HeatmapLayerProps) =>
          layer.type === 'heatmap' || layer.type === 'colormap'
            ? {
              ...layer,
              props: {
                ...layer.props,
                startDate,
                endDate,
              },
            }
            : layer,
        )
        .forEach((layer: any) => {
          tplLayerAtomFamily(layer);
        });
      setLayerIds(parsedContent.layers.map((layer: any) => layer.id));
      setMapType(parsedContent.mapType);
      setCurrentTmName(details.name);
      setCurrentLayerId(parsedContent.layers[0].id);
      setLayerLegends(parsedContent.legends || []);
      setViewState((prev) => ({
        ...prev,
        ...parsedContent.viewState,
      }));
    }
  }, [
    details,
    query.id,
    query.type,
    setCanvasSize,
    setCurrentLayerId,
    setCurrentTmId,
    setCurrentTmName,
    setDataScope,
    setDate,
    setDateType,
    setGeojsonVisible,
    setLayerIds,
    setLayerLegends,
    setLayers,
    setMapType,
    setRegionCode,
    setShouldShowAllRegion,
    setStatus,
    setTileLabelVisible,
    setTileLayerOpacity,
    setViewState,
  ]);

  useEffect(() => {
    return () => {
      setCanvasSize({
        width: 0,
        height: 0,
      });
    };
  }, [setCanvasSize]);

  return parsedDetailsContent;
};

export const useReCalcLayerPos = () => {
  const [layerIds] = useAtom(tplLayerIdsAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);

  const reCalcLayerPos = useAtomCallback(
    useCallback(
      (
        get,
        set,
        args: {
          oldSize: CanvasSize;
          newSize: CanvasSize;
        },
      ) => {
        const { oldSize, newSize } = args;
        const wRatio = newSize.width / oldSize.width;
        const hRatio = newSize.height / oldSize.height;

        setLayerLegends((prev) =>
          prev.map((legend) => {
            return {
              ...legend,
              props: {
                ...legend.props,
                top: legend.props.top * hRatio,
                left: legend.props.left * wRatio,
                translate: [
                  legend.props.translate[0] * wRatio,
                  legend.props.translate[1] * hRatio,
                ],
              },
            };
          }),
        );

        layerIds
          .filter(
            (layerId) =>
              !layerId.includes('colormap') && !layerId.includes('heatmap'),
          )
          .forEach((layerId) => {
            const atom = tplLayerAtomFamily({ id: layerId });
            const layer = get(atom);

            if (layer.type === 'compass') {
              set(atom, {
                ...layer,
                props: {
                  ...layer.props,
                  left: newSize.width - layer.props.width - 20,
                  top: layer.props.top * hRatio,
                },
              });
            } else {
              set(atom, {
                ...layer,
                props: {
                  ...layer.props,
                  left: layer.props.left * wRatio,
                  top: layer.props.top * hRatio,
                },
              });
            }
          });
      },
      [layerIds, setLayerLegends],
    ),
  );

  return reCalcLayerPos;
};

export const useMapEffect = () => {
  const { query } = useRouter();
  const [geojsonData, setGeojsonData] = useState([]);
  const shouldShowAllRegion = useAtomValue(shouldShowAllRegionAtom);
  const userInfo = useAtomValue(userInfoAtom);
  const mapContainer = document.getElementById('map-container');
  const [layers, setLayers] = useAtom(layersAtom);
  const [, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const [, setViewState] = useAtom(viewStateAtom);
  const [regionCode, setRegionCode] = useAtom(regionCodeAtom);
  const [layerIds, setLayerIds] = useAtom(tplLayerIdsAtom);
  const canvasSize = useAtomValue(canvasSizeAtom);
  const queryKeys = useMemo(
    () => ['user-region-geojson', regionCode, shouldShowAllRegion],
    [regionCode, shouldShowAllRegion],
  );
  const isCascaderChange = useAtomValue(isCascaderChangeAtom);
  const level = useMemo(
    () => (shouldShowAllRegion ? 1 : getLevelByRegionCode(String(regionCode))),
    [regionCode, shouldShowAllRegion],
  );

  useEffect(() => {
    if (query.id) {
      return;
    }

    if (mapContainer) {
      const { clientWidth, clientHeight } = mapContainer;
      const compassLayerId = `compass-layer-${new Date().getTime()}`;
      const scaleLayerId = `scale-layer-${new Date().getTime()}`;

      if (layerIds.length === 0) {
        tplLayerAtomFamily({
          id: compassLayerId,
          type: 'compass',
          name: '指南针',
          visible: true,
          props: {
            ...compassLayerDefaultConfig,
            left: clientWidth - 120,
          },
        });
        tplLayerAtomFamily({
          id: scaleLayerId,
          type: 'scale',
          name: '比例尺',
          visible: true,
          props: {
            ...scaleLayerDefaultConfig,
            top: clientHeight - 60,
            left: 40,
          },
        });
        setLayerIds((prev) => [...prev, compassLayerId, scaleLayerId]);
      }
    }
  }, [
    layers.length,
    setLayers,
    query,
    mapContainer,
    setCurrentLayerId,
    setLayerIds,
    layerIds.length,
  ]);

  const { data } = useQuery(
    queryKeys,
    () =>
      fetchGeojsonIncludeChild({
        code: Number(shouldShowAllRegion ? 150000 : regionCode),
        level,
      }),
    {
      enabled: !!regionCode && level === 1,
    },
  );

  useEffect(() => {
    setGeojsonData(data || []);
  }, [data]);

  const { data: levelGeojson } = useQuery(
    queryKeys,
    () => fetchGeojsonByRegionCode(Number(regionCode === 100000 ? 150000 : regionCode)),
    {
      enabled: !!regionCode && level !== 1,
    },
  );
  useEffect(() => {
    if (userInfo?.regionCode && !query.id) {
      setRegionCode(userInfo.regionCode);
    }
  }, [query.id, setRegionCode, userInfo?.regionCode]);

  const isNotLevel1 = useMemo(() => level === 3 || level === 2, [level]);

  useEffect(() => {
    if (
      (!data && !levelGeojson) ||
      (query.id && !isCascaderChange) ||
      (query.tplId && !isCascaderChange) ||
      canvasSize.width < 400
    ) {
      return;
    }
    const newViewState = getNewViewState(
      isNotLevel1 ? levelGeojson : data,
      {},
      Number(canvasSize.width),
      Number(canvasSize.height),
      60,
    );
    const {
      geometry: { coordinates },
    } = center(isNotLevel1 ? levelGeojson : data);

    setViewState((prev) => ({
      ...prev,
      ...newViewState,
      longitude: coordinates[0],
      latitude: coordinates[1],
      transitionDuration: 800,
    }));
    // 不能将viewState作为依赖项，会导致无限重复渲染
  }, [
    data,
    setViewState,
    levelGeojson,
    regionCode,
    query.id,
    query.tplId,
    isCascaderChange,
    isNotLevel1,
    canvasSize.width,
    canvasSize.height,
  ]);

  return geojsonData;
};
