import { FilterContainer } from '@/components/ui';
import { dateFormatter } from '@/utils';
import { Col, Select, Form, DatePicker } from 'antd';
import { useAtom } from 'jotai';
import { useMemo } from 'react';
import { useEffect } from 'react';
import { fetchParamsAtom } from '../atoms';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

const { Item: FormItem } = Form;

const Filter = () => {
  const [params, setParams] = useAtom(fetchParamsAtom);
  const { dataStart, dataEnd, type, arriveEnd, arriveStart, successful } =
    params;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      type,
    });
  }, [form, type]);

  const successfulSelectVal = useMemo(() => {
    switch (successful) {
      case true:
        return '1';
      case false:
        return '0';
      default:
        return '';
    }
  }, [successful]);

  useEffect(() => {
    form.setFieldsValue({
      successful: successfulSelectVal,
    });
  }, [form, successfulSelectVal]);

  return (
    <Form form={form} style={{ marginBottom: '-22px' }}>
      <FilterContainer gutter={12}>
        <Col>
          <FormItem name="type">
            <Select
              allowClear
              placeholder="请选择数据类型"
              value={type}
              style={{ width: 160 }}
              onChange={(val) => {
                setParams((prev) => ({
                  ...prev,
                  type: val ?? '',
                  page: 0,
                }));
              }}
              options={[
                {
                  label: '全部数据类型',
                  value: '',
                },
                {
                  label: '卫星监测',
                  value: 1,
                },
                {
                  label: '地观监测',
                  value: 3,
                },
              ]}
            />
          </FormItem>
        </Col>
        <Col>
          <FormItem name="successful">
            <Select
              allowClear
              placeholder="请选择接收状态"
              value={successfulSelectVal}
              style={{ width: 160 }}
              onChange={(val) => {
                let successfulStatus: string | boolean = '';

                if (val === '1') {
                  successfulStatus = true;
                } else if (val === '0') {
                  successfulStatus = false;
                }

                setParams((prev) => ({
                  ...prev,
                  successful: successfulStatus,
                  page: 0,
                }));
              }}
              options={[
                {
                  label: '全部接收状态',
                  value: '',
                },
                {
                  label: '接收成功',
                  value: '1',
                },
                {
                  label: '接收失败',
                  value: '0',
                },
              ]}
            />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="数据时间">
            <RangePicker
              allowClear
              className="w-full"
              disabledDate={(current) => {
                return current && current.isAfter(dayjs().endOf('day'));
              }}
              onChange={(val) => {
                if (val && val.length === 2) {
                  const [start, end] = val;
                  if (start && end) {
                    setParams((prev) => ({
                      ...prev,
                      page: 0,
                      dataStart: start?.format(dateFormatter),
                      dataEnd: end.format(dateFormatter),
                    }));
                  }
                } else {
                  setParams((prev) => ({
                    ...prev,
                    page: 0,
                    dataStart: '',
                    dataEnd: '',
                  }));
                }
              }}
              value={
                dataStart && dataEnd
                  ? [dayjs(dataStart), dayjs(dataEnd)]
                  : null
              }

            />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="送达时间">
            <RangePicker
              allowClear
              className="w-full"
              value={
                arriveStart && arriveEnd
                  ? [dayjs(arriveStart), dayjs(arriveEnd)]
                  : null
              }
              onChange={(val) => {
                if (val && val.length === 2) {
                  const [start, end] = val;
                  if (start && end) {
                    setParams((prev) => ({
                      ...prev,
                      page: 0,
                      arriveStart: start?.format(dateFormatter),
                      arriveEnd: end.format(dateFormatter),
                    }));
                  }
                } else {
                  setParams((prev) => ({
                    ...prev,
                    page: 0,
                    arriveStart: '',
                    arriveEnd: '',
                  }));
                }
              }}
              disabledDate={(current) => {
                return current && current.isAfter(dayjs().endOf('day'));
              }}
            />
          </FormItem>
        </Col>
      </FilterContainer>
    </Form>
  );
};

export default Filter;
