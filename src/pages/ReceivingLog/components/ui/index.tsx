import { Flex, HorCenter } from '@/components/ui';
import { getColorFromTheme } from '@/components/ui/utils';
import { Row } from 'antd';
import styled, { css } from 'styled-components';
import { Link } from 'react-router-dom';

export const FilterContainer = styled(Row)`
  margin: 30px 0 20px;
`;

export const ReceiveProgress = styled(HorCenter)<{
  percentage: number;
}>`
  margin-left: 20px;

  .bar-wrapper {
    width: 60px;
    height: 4px;
    overflow: hidden;
    background: #e8e8e8;
    border-radius: 2px;

    .bar {
      width: ${({ percentage }) => `${percentage}%`};
      height: 4px;
      background: ${({ theme }) => theme.colors.primary};
      border-radius: 2px;
    }
  }
  .num {
    padding-left: 4px;
    font-size: 12px;
  }
`;

export const PageTabs = styled(Flex)`
  margin-top: 40px;
  border-bottom: 1px solid #e6e6e6;
`;

export const PageTabItemBubble = styled.div`
  position: absolute;
  top: -4px;
  right: -10px;
  font-size: 12px;
  font-weight: 400;
`;

export const PageTabItem = styled(Link)<{
  active?: number;
}>`
  position: relative;
  display: block;
  margin-right: 30px;
  padding: 0 20px 6px;
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  border-bottom: 3px solid transparent;

  ${({ active }) =>
    active &&
    css`
      pointer-events: none;
    `}

  ${(props) => {
    return props.active
      ? css`
          color: ${getColorFromTheme('primary')};
          border-color: ${getColorFromTheme('primary')};
        `
      : null;
  }}

  ${PageTabItemBubble} {
    color: ${(props) => (props.active ? props.theme.colors.primary : '')};
  }
`;
