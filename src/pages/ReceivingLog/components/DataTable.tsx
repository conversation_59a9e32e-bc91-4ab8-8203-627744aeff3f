import React from 'react';
import { Table } from 'antd';
import { useQuery } from 'react-query';
import type { LogItem } from '../tableCols';
import { columns } from '../tableCols';
import { useAtom } from 'jotai';
import { fetchParamsAtom } from '../atoms';
import type { ApiListData } from '@/utils/types';
import { fetchList } from '../services';

const DataTable = () => {
  const [params, setParams] = useAtom(fetchParamsAtom);
  const { data, isLoading } = useQuery<ApiListData<LogItem>>(
    ['log-list', params],
    () => fetchList(params),
    {
      cacheTime: 0,
      staleTime: 0,
    }
  );

  return (
    <div>
      <Table
        rowKey="id"
        loading={isLoading}
        dataSource={data?.content}
        columns={columns}
        pagination={{
          current: (data?.pageable.pageNumber || 0) + 1,
          total: data?.totalElements,
          pageSize: data?.size,
          size: 'small',
          position: ['bottomCenter'],
          // style: {
          //   marginTop: 40,
          // },
        }}
        onChange={({ current, pageSize }) => {
          setParams((prev) => ({
            ...prev,
            page: (current || 1) - 1,
            size: pageSize ?? prev.size,
          }));
        }}
      />
    </div>
  );
};

export default DataTable;
