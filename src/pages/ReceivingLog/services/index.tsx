import { request } from '@/utils';
import { stringify } from 'qs';
import omitBy from 'lodash/omitBy';

interface Params {
  page: number;
  arriveEnd: string;
  arriveStart: string;
  dataEnd: string;
  dataStart: string;
  type: string | number;
  successful?: boolean | string;
}

export const fetchList = (params: Params) =>
  request(
    `/api/data/log/list?${stringify(omitBy(params, (item) => item === ''))}`,
  );
