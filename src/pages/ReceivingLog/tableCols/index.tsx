import { HorCenter, FailedMessage } from '@/components/ui';
import { dateFormatter } from '@/utils';
import { Badge, Tooltip } from 'antd';
import type { ColumnType } from 'antd/lib/table';
import moment from 'moment';

export interface LogItem {
  arriveTime: string;
  dataTime: string;
  format: string;
  id: number;
  message: string;
  name: string;
  source: string;
  success: boolean;
  type: string;
}

export const columns: ColumnType<LogItem>[] = [
  {
    title: '序号',
    render(_t, _r, index) {
      return `${index + 1}`;
    },
  },
  {
    title: '数据名称',
    dataIndex: 'name',
  },
  {
    title: '数据来源',
    dataIndex: 'source',
  },
  {
    title: '数据种类',
    dataIndex: 'type',
  },
  {
    title: '数据格式',
    dataIndex: 'format',
  },
  {
    title: '接收状态',
    dataIndex: 'success',
    render(text, record) {
      const colors = ['#286cff', '#f44336', '#f48736'];

      return (
        <HorCenter>
          <Badge
            color={text ? colors[0] : colors[1]}
            text={text ? '接收成功' : '接收失败'}
          />{' '}
          {!text && record && record.message && (
            <Tooltip title={record.message}>
              <FailedMessage width="200px">{record.message}</FailedMessage>
            </Tooltip>
          )}
        </HorCenter>
      );
    },
  },
  {
    title: '数据时间',
    dataIndex: 'dataTime',
    render(text, record) {
      const shouldFormat = record && record.source === 'S5P';

      return shouldFormat ? moment(text).format(dateFormatter) : text;
    },
  },
  {
    title: '送达时间',
    dataIndex: 'arriveTime',
  },
];
