import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useEffect } from 'react';
import { useQuery } from 'react-query';
import { reportview } from '../Report/services';
import { useAtom } from 'jotai';
import { markdownAtom } from './atom';
import 'github-markdown-css';
import './ReportContent.css'

const ReportContent = ({ reportId }: { reportId: number }) => {
  const [markdown, setMarkdown] = useAtom(markdownAtom)

  const { data: markdownData } = useQuery(
    ['reportview', reportId],
    () => reportview({ fileId: reportId, type: "permanent" }),
    {
      enabled: !!reportId,
    }
  )

  useEffect(() => {
    setMarkdown(markdownData)
  }, [markdownData, setMarkdown])

  return (
    <div className="markdown-body markdown-container ">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
      >
        {markdown}
      </ReactMarkdown>
    </div>
  );
};
export default ReportContent;