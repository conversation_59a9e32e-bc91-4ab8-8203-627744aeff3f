import { AlignmentType, BorderStyle, Document, ImageRun, Packer, Paragraph, Table, TableCell, TableRow, TextRun } from 'docx';
// @ts-ignore
import { saveAs } from 'file-saver';
import { useState } from 'react';

// 异步获取图片二进制数据
const convertToBinary = async (url: string): Promise<Uint8Array> => {
  const response = await fetch(url);
  const arrayBuffer = await response.arrayBuffer();
  return new Uint8Array(arrayBuffer);
};

// 处理 HTML 表格，将其转换为 docx.Table，并添加简单样式
const parseTable = (tableEl: HTMLTableElement): Table => {
  const rows = Array.from(tableEl.querySelectorAll('tr')).map((row, rowIndex) => {
    const cells = Array.from(row.querySelectorAll('th, td')).map((cell, cellIndex) => {
      // 为表头单元格加上背景色和粗体
      const isHeader = cell.tagName.toLowerCase() === 'th';
      return new TableCell({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: cell.textContent?.trim() || '',
                bold: isHeader,
                color: isHeader ? '000000' : '000000',
              }),
            ],
          }),
        ],
        shading: isHeader ? undefined : rowIndex % 2 === 0 ? { fill: 'F7FAFC' } : undefined,
        borders: {
          top: { style: BorderStyle.SINGLE, size: 1, color: 'CCCCCC' },
          bottom: { style: BorderStyle.SINGLE, size: 1, color: 'CCCCCC' },
          left: { style: BorderStyle.SINGLE, size: 1, color: 'CCCCCC' },
          right: { style: BorderStyle.SINGLE, size: 1, color: 'CCCCCC' },
        },
        verticalAlign: 'center',// 垂直居中
      });
    });
    return new TableRow({
      children: cells,
      height: {// 设置行高
        value: rowIndex === 0 ? 600 : 600,
        rule: 'exact',//固定高度 有可能被切断
      },
    });
  });
  return new Table({
    rows,
    width: { size: 100, type: 'pct' },
  });
};

const spacing = {
  before: 150,
  after: 150,
};

// 辅助函数：根据内联样式字符串，解析出 text-align 的值（仅处理 text-align 示例）
// @ts-ignore
const getTextAlignFromStyle = (styleStr: string): AlignmentType | undefined => {
  const match = styleStr.match(/text-align\s*:\s*(\w+)/i);
  if (match) {
    const align = match[1].toLowerCase();
    if (align === 'center') return AlignmentType.CENTER;
    if (align === 'right') return AlignmentType.RIGHT;
    if (align === 'left') return AlignmentType.LEFT;
  }
  return undefined;
};
// 递归解析单个 HTML 元素，返回对应的 docx 元素数组，同时尝试应用简单的样式
const parseHtmlElement = async (element: Element): Promise<(Paragraph | Table | ImageRun)[]> => {
  const results: (Paragraph | Table | ImageRun)[] = [];
  const tagName = element.tagName.toUpperCase();
  // @ts-ignore
  let styleAlign: AlignmentType | undefined = undefined;
  if (element.hasAttribute('style')) {
    styleAlign = getTextAlignFromStyle(element.getAttribute('style') || '');
  }

  switch (tagName) {
    case 'H1':
      results.push(
        new Paragraph({
          heading: 'Heading1',
          alignment: styleAlign || AlignmentType.LEFT,
          spacing: spacing,
          children: [
            new TextRun({
              text: element.textContent?.trim() || '',
              color: '000000',
              bold: true,
            }),
          ],
        }),
      );
      break;
    case 'H2':
      results.push(
        new Paragraph({
          heading: 'Heading2',
          alignment: styleAlign || AlignmentType.LEFT,
          spacing: spacing,
          children: [
            new TextRun({
              text: element.textContent?.trim() || '',
              color: '000000',
              bold: true,
            }),
          ],
        }),
      );
      break;
    case 'H3':
      results.push(
        new Paragraph({
          heading: 'Heading3',
          alignment: styleAlign || AlignmentType.LEFT,
          spacing: spacing,
          children: [
            new TextRun({
              text: element.textContent?.trim() || '',
              color: '333333',
              bold: true,
            }),
          ],
        }),
      );
      break;
    case 'H4':
      results.push(
        new Paragraph({
          heading: 'Heading4',
          alignment: styleAlign || AlignmentType.LEFT,
          spacing: spacing,
          children: [
            new TextRun({
              text: element.textContent?.trim() || '',
              color: '000000',
            }),
          ],
        }),
      );
      break;
    case 'P':
      results.push(
        new Paragraph({
          text: element.textContent?.trim() || '',
          alignment: styleAlign || AlignmentType.LEFT,
          spacing: spacing,
          indent: {
            firstLine: 400, // 首行缩进（400dxa≈0.7cm）
          },
        }),
      );
      break;
    case 'STRONG': {
      results.push(
        new Paragraph({
          children: [
            new TextRun({
              text: element.textContent?.trim() || '',
              color: '000000',
              bold: true,
            }),
          ],
        }),
      );
    }
    case 'HR':
    case "hr": {
      results.push(
        new Paragraph({
          children: [],
          border: {
            bottom: {
              style: BorderStyle.SINGLE,
              size: 12,  // 线宽 2-4px
              color: "d1d9e0", // 黑色
            }
          },
          spacing: {
            before: 1,
            after: 1
          },
        }),
      );
      break;
    }
    case 'IMG': {
      try {
        const binaryData = await convertToBinary((element as HTMLImageElement).src);
        results.push(
          new Paragraph({
            children: [
              // @ts-ignore
              new ImageRun({
                data: binaryData,
                transformation: { width: 300, height: 200 },
              }),
            ],
            alignment: AlignmentType.CENTER,
          }),
        );
      } catch (error) {
        console.error('加载图片失败：', (element as HTMLImageElement).src, error);
      }
      break;
    }
    case 'TABLE': {
      results.push(parseTable(element as HTMLTableElement));
      results.push(
        new Paragraph({
          spacing: {
            after: 200
          },
          children: [],
        })
      );
      break;
    }
    case 'UL':
    case 'OL': {
      // 处理列表：这里只做简单处理，不区分有序与无序
      for (const child of Array.from(element.children)) {
        if (child.tagName.toUpperCase() === 'LI') {
          results.push(
            new Paragraph({
              text: child.textContent?.trim() || '',
              bullet: { level: 0 },
              alignment: styleAlign || AlignmentType.LEFT,
              indent: { left: 820 }, // 设置左缩进
              spacing: spacing
            }),
          );
        }
      }
      break;
    }
    default: {
      // 对未知标签或容器标签，递归处理其子节点
      for (const child of Array.from(element.childNodes)) {
        if (child.nodeType === Node.ELEMENT_NODE) {
          const childResults = await parseHtmlElement(child as Element);
          results.push(...childResults);
        } else if (child.nodeType === Node.TEXT_NODE) {
          const text = child.textContent?.trim();
          if (text) {
            results.push(
              new Paragraph({
                text,
                alignment: styleAlign || AlignmentType.LEFT,
              }),
            );
          }
        }
      }
    }
  }
  return results;
};
// 解析整个 HTML 内容
const parseHtmlContent = async (htmlContent: string): Promise<(Paragraph | Table | ImageRun)[]> => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  const bodyElements = Array.from(doc.body.children);
  let docxElements: (Paragraph | Table | ImageRun)[] = [];
  for (const el of bodyElements) {
    const elements = await parseHtmlElement(el);
    docxElements.push(...elements);
  }
  return docxElements;
};



export const useExportToDocx = () => {
  const [downLoading, setDownLoading] = useState(false);
  const markdownToDocx = async (reportRef: any, name: string) => {
    if (!reportRef.current || downLoading) return;
    try {
      setDownLoading(true);
      // 获取报告内容
      const reportContent = reportRef.current.innerHTML;
      const docxElements = await parseHtmlContent(reportContent);
      const doc = new Document({
        sections: [
          {
            // @ts-ignore
            children: docxElements,
          },
        ],
      });
      const blob = await Packer.toBlob(doc);
      saveAs(blob, `${name}.docx`);
      setTimeout(() => {
        setDownLoading(false);
      }, 2000)

    } catch (error) {
      console.error("导出Word文档失败:", error);
      alert("导出失败，请重试或检查控制台错误信息");
    }
  }

  return {
    markdownToDocx,
    downLoading
  }


};