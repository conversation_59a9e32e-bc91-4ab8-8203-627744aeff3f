/* 基础容器样式 */
.markdown-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, sans-serif;
  color: #333;
  line-height: 1.6;
}
.markdown-body,
.markdown-body table tr{
  background-color: #fff !important;
  color: #000 !important;
}
.markdown-body hr {
  background-color: #d1d9e0 !important;
}

.markdown-body table {
  display: table !important;
  min-width: 100% !important;
}
.markdown-container table th{
  text-align: left;

}
/* 段落和列表样式 */
.markdown-container p {
  text-indent: 2em; /* 使用text-indent实现首行缩进 */
  line-height: 1.7;
}

.markdown-container ul {
  margin-left: 1.5rem !important;
  margin: 1rem 0;
  list-style: disc !important;

}

.markdown-container li {
  margin-bottom: 0.5rem;
  list-style: disc !important;
}
.markdown-container li p {
  margin-left: -2rem;
}


