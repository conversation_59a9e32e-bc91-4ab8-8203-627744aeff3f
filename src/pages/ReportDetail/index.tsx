import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { PageMain } from '@/components/ui';
import { useRouter } from '@/hooks';
import { Link } from 'react-router-dom';
import ReportContent from './ReportContent';
import { deleteReport, getReportList } from '../Report/services';
import { filtersAtom } from '../Report/atom';
import { useAtom, useAtomValue } from 'jotai';
import { useQueryClient } from 'react-query';
import { markdownAtom } from './atom';
import { useMemo, useRef } from 'react';
import { useExportToDocx } from './hooks';
import modal from 'antd/es/modal';


const ReportDetail = () => {
  const { query, history } = useRouter();
  const { id, reportId, name, isCurrentUser } = query
  const [filters, setFilters] = useAtom(filtersAtom);
  const queryClient = useQueryClient();
  const markdownString = useAtomValue(markdownAtom)
  const reportRef = useRef<HTMLDivElement>(null);

  const { markdownToDocx, downLoading } = useExportToDocx()
  const reportDelete = () => {
    const config = {
      title: '删除报告?',
      content: (
        <div>确定要删除此报告吗？</div>
      ),
      onOk: () => {
        deleteReport(id).then(() => {
          getReportList({
            ...filters,
          }).then((result) => {
            setFilters((p) => ({
              ...p,
              page: result.totalPages > 0 && filters.page && filters.page > result.totalPages - 1 ? result.totalPages - 1 : filters.page
            }))
            queryClient.invalidateQueries('list');
            history.push('/report');
          })
        })
      }
    };
    modal.confirm(config)

  }

  const downloadReport = () => {
    if (markdownString) {
      markdownToDocx(reportRef, name)
    }
  }

  const disabled = useMemo(() => {
    return downLoading || !markdownString
  }, [downLoading, markdownString])

  const isUser = useMemo(() => {
    return isCurrentUser === 'true'
  }, [isCurrentUser])

  return (
    <PageMain style={{ background: "#F7F7F9" }}>
      <HelmetTitle title="分析报告" />
      <PageHead title="分析报告">
        <Link to="/report">分析报告</Link>
        <i className="icomoon icon-next" />
        <span className="text-primary">报告详情</span>
      </PageHead>
      <div className='mt-[20px] flex justify-center mr-[202px] ml-[329px]'>
        <div className='bg-[#fff] flex-1  rounded-[10px] overflow-x-auto markdown-container'
          style={{
            boxShadow: '0px 0px 30px 0px rgba(220,220,220,0.3)',
            padding: '60px 120px',
            minHeight: 'calc(100vh - 120px)',
            minWidth: '900px'
          }}

        >
          <div
            ref={reportRef}
          >
            <ReportContent reportId={reportId} />
          </div>
        </div>
        <div className='ml-[20px] w-[108px]'>
          <div className='flex justify-center items-center w-[108px] gap-[8px] cursor-pointer bg-[#fff] rounded-[21px] py-[11px]'
            onClick={() => {
              if (disabled) return;
              downloadReport()
            }}
          >
            <img src={`/assets/images/report/download-${disabled ? 2 : 1}.png`} alt="" style={{ width: '14px', height: '14px' }} />
            <span style={{ color: disabled ? '#ccc' : "" }}>下载</span>
          </div>
          <div className='flex justify-center items-center w-[108px] gap-[8px] cursor-pointer bg-[#fff] rounded-[21px] mt-[12px] py-[11px]'
            onClick={() => {
              if (!isUser) return;
              reportDelete()
            }}
          >
            <img src={`/assets/images/report/delete-${isUser ? 1 : 2}.png`} alt="" style={{ width: '14px', height: '14px' }} />
            <span style={{ color: isUser ? '#F44436' : "#ccc" }} >删除</span>
          </div>
        </div>
      </div>
    </PageMain>
  );
};
export default ReportDetail;