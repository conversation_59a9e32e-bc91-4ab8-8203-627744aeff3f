import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { PageMain } from '@/components/ui';
import React from 'react';
import Filter from './components/Filter';
import UserTable from './components/UserTable';

const UserManagement = () => {
  return (
    <PageMain>
      <HelmetTitle title="用户管理" />
      <PageHead title="用户管理">
        <span>系统管理</span>
        <i className="icomoon icon-next" />
        <span className="text-primary">用户管理</span>
      </PageHead>
      <Filter />
      <UserTable />
    </PageMain>
  );
};

export default UserManagement;
