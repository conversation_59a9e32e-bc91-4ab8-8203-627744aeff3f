import { OutlinedButton } from '@/components/ui';
import { SearchOutlined } from '@ant-design/icons';
import { Input, Cascader, Col } from 'antd';
import { useEffect, useMemo } from 'react';
import type { UserFormData } from '../types';
import BatchOperationModal from './BatchOperationModal';
import UserFormModal from './UserFormModal';
import { FilterBtnsContainer, FilterContainer } from './ui';
import { useAtomValue } from 'jotai';
import { userInfoAtom } from '@/atoms';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import useFilter from '../hooks/useFilter';

const Filter = () => {
  // 界面所需数据
  const {
    fetchParams,
    handleSetFetchParams,
    currentUser,
    setCurrentUser,
    visible,
    handleSetVisible,
    editUser,
    createUser,
  } = useFilter();
  const userInfo = useAtomValue(userInfoAtom);
  const { options, cascaderValue } = useCascaderOptionsAndMatchValues(
    fetchParams.regionCode,
  );
  const createBtnVisible = useMemo(() => {
    return userInfo?.permissions.includes('1005');
  }, [userInfo?.permissions]);
  // 数据监听
  useEffect(() => {
    if (userInfo?.regionCode) {
      handleSetFetchParams({ regionCode: userInfo.regionCode });
    }
  }, [handleSetFetchParams, userInfo?.regionCode]);

  return (
    <FilterContainer gutter={12}>
      <Col>
        <Input
          allowClear
          prefix={<SearchOutlined />}
          placeholder="请输入用户名/手机号"
          value={fetchParams.name}
          onChange={(e) => {
            handleSetFetchParams({
              name: e.target.value,
              page: 0,
            });
          }}
        />
      </Col>
      <Col>
        <Cascader
          changeOnSelect
          options={options}
          className="w-full"
          value={cascaderValue}
          fieldNames={{
            label: 'name',
            value: 'code',
          }}
          onChange={(val) => {
            handleSetFetchParams({
              regionCode:
                val?.length > 0 ? +val[val?.length - 1] : userInfo!.regionCode,
              page: 0,
            });
          }}
        />
      </Col>
      {createBtnVisible && (
        <FilterBtnsContainer flex="1">
          <OutlinedButton onClick={() => handleSetVisible({ userForm: true })}>
            新增用户
          </OutlinedButton>
        </FilterBtnsContainer>
      )}
      <BatchOperationModal
        visible={visible.batchEdit}
        onCancel={() => handleSetVisible({ batchEdit: false })}
      />
      <UserFormModal
        visible={visible.userForm}
        current={currentUser}
        onOk={(values: UserFormData) => {
          if (currentUser) {
            editUser(values);
          } else {
            createUser(values);
          }
        }}
        onCancel={() => {
          handleSetVisible({ userForm: false });
          setCurrentUser(null);
        }}
      />
    </FilterContainer>
  );
};
export default Filter;
