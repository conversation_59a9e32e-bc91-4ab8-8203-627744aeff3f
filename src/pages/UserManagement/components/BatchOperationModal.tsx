import DarkModal from '@/components/global/DarkModal';
import { StyledButton } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { Form, Cascader, Row, Col, Switch, Select } from 'antd';
import React from 'react';
import {
  GrayTextFormItem,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalTitle,
} from './ui';

const { Item: FormItem } = Form;

interface Props {
  visible: boolean;
  onCancel: () => void;
}

const options = [
  {
    label: '广西',
    value: '广西',
    children: [
      {
        label: '南宁',
        value: '南宁',
      },
      {
        label: '防城港',
        value: '防城港',
      },
    ],
  },
];
const BatchOperationModal: React.FC<Props> = ({ visible, onCancel }) => {
  return (
    <DarkModal visible={visible} onCancel={onCancel}>
      <ModalContent>
        <ModalTitle>批量修改</ModalTitle>
        <ModalBody>
          <Form>
            <Row gutter={12}>
              <Col span={12}>
                <FormItem>
                  <Cascader
                    options={options}
                    className="w-full dark-form-item"
                    getPopupContainer={getDarkContainer}
                  />
                </FormItem>
              </Col>
              <Col span={12}>
                <GrayTextFormItem label="用户状态">
                  <Switch defaultChecked={true} />
                </GrayTextFormItem>
              </Col>
              <Col span={24}>
                <FormItem>
                  <Select
                    mode="multiple"
                    placeholder="请选择权限"
                    className="w-full dark-form-item"
                    getPopupContainer={getDarkContainer}
                    options={[
                      {
                        label: '污染报警',
                        value: '污染报警',
                      },
                      {
                        label: '污染预警',
                        value: '污染预警',
                      },
                      {
                        label: '污染源管理',
                        value: '污染源管理',
                      },
                    ]}
                  />
                </FormItem>
              </Col>
            </Row>
          </Form>
        </ModalBody>
        <ModalFooter>
          <StyledButton>以后再说</StyledButton>
          <StyledButton variant="primary">提交</StyledButton>
        </ModalFooter>
      </ModalContent>
    </DarkModal>
  );
};

export default BatchOperationModal;
