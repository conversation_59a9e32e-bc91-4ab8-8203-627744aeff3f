import { Table } from 'antd';
import useTable from '../hooks/useTable';

const UserTable = () => {
  const { data, isLoading, pagination, columns, handleSetFetchUsersParams } =
    useTable();

  return (
    <div>
      <Table
        rowKey="id"
        loading={isLoading}
        columns={columns}
        dataSource={data?.content}
        pagination={pagination}
        onChange={({ current }) => {
          handleSetFetchUsersParams({ page: (current || 1) - 1 });
        }}
      />
    </div>
  );
};

export default UserTable;
