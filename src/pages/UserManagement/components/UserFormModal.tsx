import { userInfoAtom } from '@/atoms';
import DarkModal from '@/components/global/DarkModal';
import { StyledButton } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import formRules from '@/utils/formRules';
import { Form, Cascader, Row, Col, Select, Input } from 'antd';
import { useAtomValue } from 'jotai';
import React, { } from 'react';
import { useEffect } from 'react';
import { useCallback } from 'react';
import type { User } from '../types';
import {
  DarkInput,
  GrayTextFormItem,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalTitle,
} from './ui';

const { Item: FormItem } = Form;

interface Props {
  visible: boolean;
  current: User | null;
  onCancel: () => void;
  onOk: (values: any) => void;
}

export const permissions = [
  { value: '1004', label: '专题图' },
  { value: '1005', label: '系统管理' },
  { value: '1006', label: '综合评估' },
  { value: '1007', label: '沙尘事件' },
  { value: '1008', label: '分析报告' },
];

const UserFormModal: React.FC<Props> = ({
  visible,
  current,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();
  const userInfo = useAtomValue(userInfoAtom);
  const submit = useCallback(() => {
    form.validateFields().then((values) => {
      onOk({
        ...values,
        regionCode:values.regionCode[values.regionCode.length - 1],
      });
    });
  }, [form, onOk]);
  const { twoStageOptions } = useCascaderOptionsAndMatchValues(
    Number(userInfo?.regionCode),
  );
  const { cascaderValue } = useCascaderOptionsAndMatchValues(
    current?.regionCode || userInfo?.regionCode,
  );

  useEffect(() => {
    form.setFieldsValue({
      ...(current || {}),
      regionCode: cascaderValue,
    });

    return () => {
      form.resetFields();
    };
  }, [cascaderValue, current, visible, form]);

  return (
    <DarkModal open={visible} onCancel={onCancel} width={600}>
      <ModalContent id='dark-container-form'>
        <ModalTitle>{current ? '修改用户' : '新增用户'}</ModalTitle>
        <ModalBody>
          <Form form={form}>
            <Row gutter={12}>
              <Col span={24}>
                <FormItem
                  name="name"
                  rules={formRules.required('请输入用户名')}
                >
                  <DarkInput
                    placeholder="用户名"
                    size="large"
                    className="dark-form-item"
                  />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  name="regionCode"
                  rules={formRules.required('请选择用户区域')}
                >
                  <Cascader
                    options={twoStageOptions}
                    value={cascaderValue}
                    fieldNames={{
                      label: 'name',
                      value: 'code',
                    }}
                    changeOnSelect
                    className="w-full dark-form-item"
                    allowClear={userInfo?.regionCode===150000?true:false}
                    getPopupContainer={() => document.getElementById('dark-container-form') as HTMLDivElement}
                    onChange={(val) => {
                      if (!val) {
                        form.setFieldsValue({
                          ...(current || {}),
                          regionCode: [Number(userInfo?.regionCode)],
                        });
                      }
                    }}
                  />
                </FormItem>
              </Col>
              <Col span={12}>
                <GrayTextFormItem name="phoneNumber" rules={formRules.mobile()}>
                  <Input placeholder="手机号" className="dark-form-item" />
                </GrayTextFormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  name="permissions"
                  rules={formRules.required('请选择用户权限')}
                >
                  <Select
                    mode="multiple"
                    placeholder="请选择权限"
                    className="w-full dark-form-item"
                    getPopupContainer={getDarkContainer}
                    options={permissions}
                  />
                </FormItem>
              </Col>
            </Row>
          </Form>
        </ModalBody>
        <ModalFooter>
          <StyledButton onClick={onCancel}>以后再说</StyledButton>
          <StyledButton variant="primary" onClick={submit}>
            提交
          </StyledButton>
        </ModalFooter>
      </ModalContent>
    </DarkModal>
  );
};

export default UserFormModal;
