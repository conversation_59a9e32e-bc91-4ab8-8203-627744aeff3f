import { stringify } from 'qs';
import { request } from '@/utils';
import type { UserFormData, EditUserData } from '../types';

export interface FetchUsersParams {
  name: string;
  page: number;
  regionCode: number | undefined;
  size: number;
}

// 用户列表
export const fetchUsers = (params: FetchUsersParams) => {
  return request(`/api/user/list?${stringify(params)}`);
};

// 新增用户
export const createUser = (newUser: UserFormData) =>
  request(`/api/user/create`, {
    method: 'POST',
    body: JSON.stringify(newUser),
  });

// 编辑用户
export const updateUser = (user: EditUserData) =>
  request(`/api/user/update`, {
    method: 'POST',
    body: JSON.stringify(user),
  });

// 用户停用
export const disableUser = (id: number) =>
  request(`/api/user/disable?id=${id}`, {
    method: 'POST',
  });

// 用户启用
export const enableUser = (id: number) =>
  request(`/api/user/enable?id=${id}`, { method: 'POST' });

// 删除用户
export const deleteUser = (id: number) =>
  request(`/api/user/delete?id=${id}`, {
    method: 'POST',
  });

// 重置密码
export const resetPassword = (id: number) =>
  request(`/api/user/password/reset?id=${id}`, {
    method: 'POST',
  });

// 批量删除用户
export const deleteUsers = (userIds: number[]) =>
  request(`/api/user/batch/delete`, {
    method: 'POST',
    body: stringify({ userIds }),
  });

export interface UpdateUsersParams {
  userIds: number[];
  enable: boolean;
  permissions: number[];
  regionCode: number;
}
// 批量修改用户
export const updateUsers = (params: UpdateUsersParams) =>
  request(`/api/user/batch/delete`, {
    method: 'POST',
    body: JSON.stringify(params),
  });
