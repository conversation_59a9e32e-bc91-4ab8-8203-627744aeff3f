import { useMutation, useQuery } from 'react-query';
import type { ApiListData } from '@/utils/types';
import type { User } from '../types';
import type { TablePaginationConfig } from 'antd';
import { Modal, message } from 'antd';
import { useSet<PERSON>tom as useUpdateAtom } from 'jotai';
import { currentUserAtom, visibleAtom } from '../atoms';
import { useCallback } from 'react';

import { useMemo } from 'react';
import { userTableColumns } from '../tableCols';
import usePublic from './usePublic';

const Table = () => {
  const { queryClient, fetchUsersParams, handleSetFetchUsersParams, services } =
    usePublic();

  // -- 数据库 操作
  //  根据 fetchUsersParams 生成计算属性 usersListQueryKey
  const usersListQueryKey = useMemo(
    () => ['users', fetchUsersParams],
    [fetchUsersParams],
  );
  // 根据 fetchUsersParams 调取数据
  const { data, isLoading } = useQuery<ApiListData<User>>(
    usersListQueryKey,
    () => services.fetchUsers(fetchUsersParams),
    {
      enabled: Boolean(fetchUsersParams.regionCode),
    },
  );
  // 根据 data 计算出 pagination
  const pagination: TablePaginationConfig = useMemo(
    () => ({
      current: (data?.pageable.pageNumber || 0) + 1,
      total: data?.totalElements,
      size: 'small',
      position: ['bottomCenter'],
      style: {
        marginTop: 40,
      },
    }),
    [data],
  );

  // 查询 usersListQueryKey 数据，合并更新
  const udpateUserEnabledStatus = useCallback(
    (id) => {
      const prev = queryClient.getQueryData(
        usersListQueryKey,
      ) as ApiListData<User>;
      queryClient.setQueryData(usersListQueryKey, {
        ...prev,
        content: prev?.content.map((user) =>
          user.id === id ? { ...user, enabled: !user.enabled } : user,
        ),
      });
    },
    [queryClient, usersListQueryKey],
  );

  // 删除 匹配 数据
  const deleteMatchUser = useCallback(
    (id) => {
      const prev = queryClient.getQueryData(
        usersListQueryKey,
      ) as ApiListData<User>;
      queryClient.setQueryData(usersListQueryKey, {
        ...prev,
        content: prev?.content.filter((user) => user.id !== id),
      });
    },
    [queryClient, usersListQueryKey],
  );

  const setCurrentUser = useUpdateAtom(currentUserAtom);
  const setVisible = useUpdateAtom(visibleAtom);

  const disableMutation = useMutation(
    (id: number) => services.disableUser(id),
    {
      onSuccess: (_data, id) => {
        udpateUserEnabledStatus(id); // 更新数据
        message.success('操作成功');
      },
    },
  );

  const enableMutation = useMutation((id: number) => services.enableUser(id), {
    onSuccess: (_data, id) => {
      udpateUserEnabledStatus(id); // 更新数据
      message.success('操作成功');
    },
  });

  const deleteMutation = useMutation((id: number) => services.deleteUser(id), {
    onSuccess: (_data, id) => {
      message.success('操作成功');
      deleteMatchUser(id); // 删除匹配
    },
  });

  const resetPasswordMutation = useMutation(
    (id: number) => services.resetPassword(id),
    {
      onSuccess: () => {
        message.success('操作成功');
      },
    },
  );

  const columns = userTableColumns({
    enableUser: (id) => {
      enableMutation.mutate(id);
    },
    disableUser: (id) => {
      disableMutation.mutate(id);
    },
    editUser: (user) => {
      setCurrentUser(user);
      setVisible((prev) => ({
        ...prev,
        userForm: true,
      }));
    },
    deleteUser: (id) => {
      Modal.confirm({
        title: '确定删除此用户吗？',
        onOk() {
          deleteMutation.mutate(id);
        },
      });
    },
    resetPassword: (id) => {
      Modal.confirm({
        title: '确定重置此用户密码吗？',
        onOk() {
          resetPasswordMutation.mutate(id);
        },
      });
    },
  });

  return {
    data,
    isLoading,
    pagination,
    columns,
    handleSetFetchUsersParams,
  };
};

export default Table;
