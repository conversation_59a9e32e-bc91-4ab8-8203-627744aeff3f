import { useAtom } from 'jotai';
import { currentUserAtom, visibleAtom } from '../atoms';
import { useCallback } from 'react';
import { useMutation } from 'react-query';
import type { EditUserData, UserFormData } from '../types';
import { message } from 'antd';
import usePublic from './usePublic';

interface IVisible {
  userForm?: boolean;
  batchEdit?: boolean;
}

const useFilter = () => {
  const {
    queryClient,
    services,
    fetchUsersParams: fetchParams,
    handleSetFetchUsersParams: handleSetFetchParams,
  } = usePublic();
  // --atom--
  // currentUser
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);

  // fetchParams
  // visible
  const [visible, setVisible] = useAtom(visibleAtom);
  const handleSetVisible = useCallback(
    (obj: IVisible) => {
      setVisible((prev) => ({
        ...prev,
        ...obj,
      }));
    },
    [setVisible],
  );

  // --后台交互--
  // createUser
  const createUserMutation = useMutation((newUser: UserFormData) =>
    services.createUser(newUser),
  );
  // updateUser
  const updateUserMutation = useMutation((user: EditUserData) =>
    services.updateUser(user),
  );

  // -- 混合处理--

  const createUser = useCallback(
    (newUser: UserFormData) => {
      createUserMutation.mutate(newUser, {
        onSuccess: () => {
          message.success('操作成功');
          handleSetVisible({ userForm: false });
          queryClient.invalidateQueries('users');

          setCurrentUser(null);
        },
      });
    },
    [createUserMutation, queryClient, setCurrentUser, handleSetVisible],
  );
  const editUser = useCallback(
    (user: UserFormData) => {
      if (currentUser) {
        updateUserMutation.mutate(
          {
            ...user,
            id: currentUser.id,
          },
          {
            onSuccess: () => {
              message.success('操作成功');
              handleSetVisible({ userForm: false });
              queryClient.invalidateQueries(['users']);
              queryClient.invalidateQueries(['user-info']);

              setCurrentUser(null);
            },
          },
        );
      }
    },
    [
      currentUser,
      handleSetVisible,
      queryClient,
      setCurrentUser,
      updateUserMutation,
    ],
  );

  return {
    fetchParams,
    handleSetFetchParams,
    visible,
    handleSetVisible,
    currentUser,
    setCurrentUser,
    createUserMutation,
    updateUserMutation,
    createUser,
    editUser,
  };
};

export default useFilter;
