import { use<PERSON>tom } from 'jotai';
import { fetchUsersParamsAtom } from '../atoms';
import { useCallback } from 'react';
import { useQueryClient } from 'react-query';
import * as services from '../services';

interface IMergeFetchUsersParams {
  name?: string;
  page?: number;
  regionCode?: number | undefined;
  size?: number;
}

const usePublic = () => {
  const queryClient = useQueryClient();
  const [fetchUsersParams, setFetchUsersParams] = useAtom(fetchUsersParamsAtom);

  const handleSetFetchUsersParams = useCallback(
    (obj: IMergeFetchUsersParams) => {
      setFetchUsersParams((prev) => ({ ...prev, ...obj }));
    },
    [setFetchUsersParams],
  );

  return {
    services,
    queryClient,
    fetchUsersParams,
    handleSetFetchUsersParams,
  };
};

export default usePublic;
