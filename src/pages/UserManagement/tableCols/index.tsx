import { TableActionLink } from '@/components/ui';
import { Modal, Switch } from 'antd';
import type { ColumnType } from 'antd/lib/table';
import { permissions } from '../components/UserFormModal';
import type { User } from '../types';

interface TableActions {
  enableUser: (id: number) => void;
  disableUser: (id: number) => void;
  deleteUser: (id: number) => void;
  resetPassword: (id: number) => void;
  editUser: (user: User) => void;
}

export const userTableColumns: (actions: TableActions) => ColumnType<User>[] =
  ({ enableUser, disableUser, editUser, deleteUser, resetPassword }) => [
    {
      title: '序号',
      render(_text, _record, index) {
        return `${index + 1}`;
      },
    },
    {
      title: '用户名',
      dataIndex: 'name',
    },
    {
      title: '行政区域',
      dataIndex: 'region',
    },
    {
      title: '手机号',
      dataIndex: 'phoneNumber',
    },
    {
      title: '是否启用',
      dataIndex: 'status',
      render(text, record) {
        return (
          <Switch
            checked={record.enabled}
            onChange={(val) => {
              Modal.confirm({
                title: `确定${val ? '启用' : '禁用'}此用户吗？`,
                onOk() {
                  if (val) {
                    enableUser(record.id);
                  } else {
                    disableUser(record.id);
                  }
                },
              });
            }}
          />
        );
      },
    },
    {
      title: '登录次数',
      dataIndex: 'totalLoginTimes',
    },
    {
      title: '最近登录',
      dataIndex: 'lastLoginTime',
      render(text) {
        return text || '-';
      },
    },
    {
      title: '用户权限',
      dataIndex: 'permissions',
      render(permisionsArr: string[]) {
        return permisionsArr
          .map(
            (p) =>
              permissions.find((permission) => permission.value === p)?.label ||
              '未知权限',
          )
          .join('、');
      },
    },
    {
      title: '操作',
      render(_t, record) {
        return (
          <>
            <TableActionLink
              color="primary"
              onClick={() => {
                editUser(record);
              }}
            >
              修改
            </TableActionLink>
            <TableActionLink
              color="danger"
              onClick={() => deleteUser(record.id)}
            >
              删除
            </TableActionLink>
            <TableActionLink onClick={() => resetPassword(record.id)}>
              重置密码
            </TableActionLink>
          </>
        );
      },
    },
  ];
