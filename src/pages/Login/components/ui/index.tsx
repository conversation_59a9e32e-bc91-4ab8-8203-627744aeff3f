import { Center, Flex } from '@/components/ui';
import { getColorFromTheme } from '@/components/ui/utils';
import styled, { keyframes } from 'styled-components';

export const yoy = keyframes`
  0% {
    transform: translate(0, 0)
  }

  50% {
    transform: translate(10px, 20px)
  }

  to {
    transform: translate(0)
  }
`;

export const Container = styled(Flex)`
  position: relative;
  background: black url('/assets/images/login-bg.jpg') center center no-repeat;
  color: white;
`;

export const Satellite = styled.div`
  position: absolute;
  width: 357px;
  height: 185px;
  transition: transform 0.3s;
  animation: ${yoy} infinite linear 8s;

  img {
    width: 100%;
  }
`;

export const Left = styled.div`
  position: relative;
  flex: 1;
  min-height: 100vh;
`;

export const Right = styled(Flex)`
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-height: 100vh;
`;

export const FormWrapper = styled.div`
  width: 520px;
  padding-left: 80px;
  padding-bottom: 80px;
`;

export const SubTitle = styled.p`
  margin: 0 0 8px 0;
  font-size: 20px;
`;

export const Title = styled.h1`
  margin-bottom: 30px;
  font-size: 26px;
  font-weight: 700;
`;

export const FormGroup = styled.div`
  margin-bottom: 12px;
`;

export const Label = styled.label`
  display: block;
  margin-bottom: 12px;
  padding-left: 12px;
  font-size: 12px;
`;

export const Input = styled.input`
  width: 440px;
  height: 54px;
  padding: 0 12px;
  font-size: 16px;
  background: #2e3035;
  border-radius: 4px;
  border: none;
  outline: none;
`;

export const Button = styled.button`
  width: 440px;
  height: 54px;
  margin-top: 18px;
  font-size: 18px;
  color: white;
  background: ${getColorFromTheme('primary')};
  border-radius: 4px;
  border: none;
  cursor: pointer;

  &:hover {
    background: ${getColorFromTheme('hoveredPrimary')};
  }
`;

export const Intro = styled.div`
  margin-top: 12px;
  padding-left: 12px;
  font-size: 12px;
  text-align: center;

  a {
    color: ${getColorFromTheme('primary')};

    &:hover {
      text-decoration: underline;
    }
  }
`;

export const ForgetPsw = styled.div`
  margin-top: 60px;
  padding-top: 18px;
  font-size: 14px;
  text-align: center;
  border-top: 1px solid #2e3035;

  a {
    color: ${getColorFromTheme('primary')};
  }
`;

export const CaptchaWrapper = styled(Center)`
  flex: 1;
  height: 54px;
  background: #2e3035;
  border-radius: 4px;
`;
