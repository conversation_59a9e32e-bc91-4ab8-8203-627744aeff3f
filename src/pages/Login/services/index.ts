import { request } from '@/utils';

export interface LoginDTO {
  phoneNumber: string;
  password: string;
  code: string;
}

export interface LoginRespData {
  enabled: boolean;
  id: number;
  lastLoginTime: string;
  name: string;
  permissions: string[];
  phoneNumber: string;
  region: string;
  regionCode: number;
  role: number;
  token: string;
  totalLoginTimes: number;
  province: string;
}

export const login: (data: LoginDTO) => Promise<LoginRespData> = (data: {
  phoneNumber: string;
  password: string;
}) =>
  request(
    '/api/user/login',
    {
      method: 'POST',
      body: JSON.stringify(data),
    },
    true,
  );
