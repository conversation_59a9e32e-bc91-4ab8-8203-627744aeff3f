import { useCallback, useRef, useState } from 'react';
import Captcha from 'react-captcha-code';
import {
  Container,
  FormGroup,
  FormWrapper,
  Title,
  Label,
  Input,
  Left,
  Right,
  Button,
  Satellite,
  SubTitle,
  CaptchaWrapper,
} from './components/ui';
import HelmetTitle from '@/components/global/HealmetTitle';
import { useWindowSize } from '@/hooks';
import { HorCenter } from '@/components/ui';
import { useLogin, useLoginFormData } from './hooks';

const Login = () => {
  const [captchaVal, setCaptchaVal] = useState('');
  let { width, height } = useWindowSize();
  const { state, update } = useLoginFormData();
  const { submit } = useLogin();
  const captchaRef = useRef<any>(null);
  const handleChange = (captcha: string) => setCaptchaVal(captcha);
  // const { query } = useRouter();

  const handleSubmit = useCallback(() => {
    submit(state, captchaVal);
  }, [captchaVal, state, submit]);

  const handleClick = useCallback(() => {
    (captchaRef.current as any).refresh();
  }, []);

  height = height < 600 ? 600 : height;
  width = width < 1300 ? 1300 : width;

  return (
    <Container style={{ minHeight: height }}>
      <input type="hidden" value={captchaVal} id="captchaVal" />
      <HelmetTitle title="登录" />
      <Satellite
        style={{
          top: `${height / 2 - 100 - 185}px`,
          left: `${width / 2 - 148 - 357}px `,
        }}
      >
        <img src="/assets/images/sat.png" alt="" />
      </Satellite>
      <Left />
      <Right>
        <FormWrapper>
          <SubTitle>欢迎进入</SubTitle>
          <Title>
            内蒙古自治区
            <br />
            环境空气卫星遥感监测数据应用平台
          </Title>
          <FormGroup>
            <Label>请输入手机号</Label>
            <Input
              placeholder="手机号码"
              name="phoneNumber"
              value={state.phoneNumber}
              onChange={(e) =>
                update({
                  phoneNumber: e.target.value,
                })
              }
            />
          </FormGroup>
          <FormGroup>
            <Label>请输入密码</Label>
            <Input
              placeholder="密码"
              name="password"
              type="password"
              value={state.password}
              onChange={(e) =>
                update({
                  password: e.target.value,
                })
              }
            />
          </FormGroup>
          <FormGroup>
            <Label>请输入验证码</Label>
            <HorCenter>
              <Input
                placeholder="验证码"
                style={{ marginRight: 12 }}
                value={state.code}
                onChange={(e) =>
                  update({
                    code: e.target.value,
                  })
                }
              />
              <CaptchaWrapper>
                <Captcha
                  height={40}
                  ref={captchaRef}
                  onChange={handleChange}
                  onClick={handleClick}
                  bgColor="rgba(0,0,0,0)"
                />
              </CaptchaWrapper>
            </HorCenter>
          </FormGroup>
          <Button onClick={handleSubmit}>登录</Button>
        </FormWrapper>
      </Right>
    </Container>
  );
};

export default Login;
