import { userInfoAtom } from '@/atoms';
import { mobileRegex } from '@/utils';
import { message } from 'antd';
import { useSetAtom as useUpdateAtom } from 'jotai';
import { useCallback, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import type { LoginDTO, LoginRespData } from '../services';
import { login } from '../services';

// 表单数据及更新方法
export const useLoginFormData = () => {
  const [state, setState] = useState<LoginDTO>({
    phoneNumber: '',
    password: '',
    code: '',
  });

  const update = useCallback((newVal: Record<string, string>) => {
    setState((prev) => ({
      ...prev,
      ...newVal,
    }));
  }, []);

  return useMemo(
    () => ({
      state,
      update,
    }),
    [state, update],
  );
};

// 登录动作
export const useLogin = () => {
  const setUserInfo = useUpdateAtom(userInfoAtom);
  const mutation = useMutation((loginState: LoginDTO) => login(loginState));

  const submit = useCallback(
    (state: LoginDTO, captchaVal: string) => {
      if (!state.phoneNumber.trim()) {
        message.error('请输入手机号码');
      } else if (!mobileRegex.test(state.phoneNumber)) {
        message.error('手机号码格式错误');
      } else if (!state.password.trim()) {
        message.error('请输入密码');
      } else if (!state.code) {
        message.error('请输入验证码');
      } else if (state.code !== captchaVal) {
        message.error('验证码输入错误');
      } else {
        mutation.mutate(state, {
          onSuccess: async (d: LoginRespData) => {
            if (d?.token) {
              localStorage.setItem('__NMENG_TOKEN__', d?.token);
              const { token, ...rest } = d;
              setUserInfo({
                ...rest,
                token: null,
              });
            }
            // window.location.href = query.from
            //   ? decodeURIComponent(query.from)
            //   : '/';
            window.location.href = '/';
          },
        });
      }
    },
    [mutation, setUserInfo],
  );

  return {
    submit,
  };
};
