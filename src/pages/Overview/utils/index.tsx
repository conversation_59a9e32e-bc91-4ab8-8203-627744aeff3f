import type { IconLayerProps, PathLayerProps } from '@deck.gl/layers';
import { BitmapLayer } from 'deck.gl';

/**
 * 日期时间格式化
 * @param separator 分隔符 / | -
 * @param isInt 整点
 * @returns string
 */
export const getDatetimeFormatter = (separator = '/', isInt = false) => {
  const hm = isInt ? '00:00' : 'mm:ss';
  return `YYYY${separator}MM${separator}DD HH:${hm}`;
};

// 瓦片图层设置
export const tileLayerBaseConfig = {
  minZoom: 0,
  maxZoom: 18,
  wrapLongitude: false,
  pickable: true,
  loadOptions: {
    // @workaround: TileLayer 无法为image/jpg这种类型的瓦片选择loader，这是deck.gl的BUG
    // 这里通过设置指定的mimeType来绕过这个问题，具体的设置含义参考
    // https://loaders.gl/modules/core/docs/api-reference/loader-options
    mimeType: 'image/jpeg',
  },
  renderSubLayers: (props: any) => {
    const {
      bbox: { west, south, east, north },
    } = props.tile;
    return new BitmapLayer(props, {
      data: null,
      image: props.data,
      bounds: [west, south, east, north],
    });
  },
};

// 测量距离点图层设置
export const pointsLayerBaseConfig: IconLayerProps<any> = {
  id: 'distance-points-layer',
  iconAtlas: '/assets/map/point.png',
  iconMapping: {
    marker: {
      x: 0,
      y: 0,
      width: 20,
      height: 20,
    },
  },
  getIcon: () => 'marker',
  getPosition: (d) => d.coordinate,
  pickable: false,
  getSize: 20,
  sizeScale: 0.5,
};

// 测量距离点连线图层设置
export const distancePathLayerBaseConfig: PathLayerProps<[number, number][]> = {
  id: 'distance-path-layer',
  pickable: false,
  widthScale: 20,
  widthMinPixels: 2,
  widthMaxPixels: 2,
  getColor: () => [40, 108, 255],
  getWidth: () => 15,
  getPath: (d) => {
    return d;
  },
};

export const dcolorLegends = [
  {
    id: 1,
    color: '#7e0023',
    label: '冰云 厚云 高层云',
  },
  {
    id: 2,
    color: '#000',
    label: '薄卷云 凝结尾流',
  },
  {
    id: 3,
    color: '#99703C',
    label: '厚云 中层云',
  },

  {
    id: 4,
    from: '#EA6CC3',
    to: '#E23A39',
    label: '沙尘',
  },
  {
    id: 5,
    color: '#BAAF3B',
    label: '冷气团 低层云',
  },
  {
    id: 6,
    color: '#AA64D4',
    label: '热气团 低层云',
  },
  {
    id: 7,
    color: '#3F7754',
    label: '薄云 中层云',
  },
];
