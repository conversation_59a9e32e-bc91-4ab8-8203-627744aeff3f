import dayjs from 'dayjs';
import { atom } from 'jotai';
import { TimelineListItem } from '../components/Timeline/TimelineItem';
import { getDatetimeFormatter } from '../utils';

export const activeTimelineItemAtom = atom<TimelineListItem | undefined>(undefined);

export const activeTimePointAtom = atom((get) => {
  const timelineItem = get(activeTimelineItemAtom);
  if (timelineItem) {
    return dayjs(timelineItem.value).format(getDatetimeFormatter());
  }
  return undefined;
});

export const activeTimePointHourlyAtom = atom((get) => {
  const timelineItem = get(activeTimelineItemAtom);
  if (timelineItem) {
    return dayjs(timelineItem.value).format(getDatetimeFormatter('/', true));
  }
  return undefined;
});

export const isSelectTheLastPointOfTimelineAtom = atom(false);

export const dustTimePointsAtom = atom<TimelineListItem[]>([]);

export const timePointsRangeAtom = atom((get) => {
  const timePoints = get(timePointsAtom);
  if (timePoints.length > 0) {
    return [timePoints[0].value, timePoints[timePoints.length - 1].value];
  }
  return ['', ''];
});

export const activeTimePointPercentageAtom = atom((get) => {
  const timePoints = get(timePointsAtom);
  const activeTimelineItem = get(activeTimelineItemAtom);
  if (activeTimelineItem) {
    const index = timePoints.findIndex((item) => item.value === activeTimelineItem.value);
    return index / timePoints.length;
  }
  return 0;
});

export const frameAtom = atom(0);

export const startPollAtom = atom(false);

export const loadingTileAtom = atom<boolean>(false);
