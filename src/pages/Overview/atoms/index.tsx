import type { AqiPollutionType } from '@/utils';
import type { Position2D, Position3D } from '@deck.gl/core';
import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import turfDistance from '@turf/distance';
import { FlyToInterpolator } from 'deck.gl';
import { atom } from 'jotai';
import { atomWithReset } from 'jotai/utils';
import { Agg } from '../types';

export * from './timeline';

/**
 * 地图全局状态
 */
// 地图视图数据
export const viewStateAtom = atom<ViewStateProps>({
  latitude: 45.3719945,
  longitude: 111.623147,
  zoom: 6,
  maxZoom: 18,
  minZoom: 3,
  pitch: 0,
  bearing: 0,
  transitionInterpolator: new FlyToInterpolator(),
  transitionDuration: 1000,
});

// 保存计算后的viewState
export const calculatedViewStateAtom = atom<ViewStateProps | null>(null);
// export const viewStateAtom = atomWithReset<any>(null);

// 鼠标hover地图时拾取经纬度
export const coordinateAtom = atom<Position2D | Position3D | []>([]);
// 时间轴时间选择控件，默认从上周一到当天
export const datePickerValAtom = atom<[string, string] | undefined>();

export const aggAtom = atom<Agg>('hourly');

/**
 * 面板显示隐藏状态
 */
// 污染物面板
export const pollutionPanelVisibleAtom = atom(false);
// 警报面板是否显示
export const alertPanelVisibleAtom = atom(false);

/**
 * 面板手风琴开关状态
 */
// 污染物手风琴状态
export const pollutionAccordionStatusAtom = atom<0 | 1 | null>(0);

// 国控站类型
export const nationStationPoTypeAtom = atom<AqiPollutionType>('pm25');

// 污染物卫星遥感内容展现形式 'list' | 'chart'
export const displayTypeAtom = atom<'list' | 'chart'>('list');

/**
 * 时间轴相关状态
 */
// 时间轴相关
export const timelineStatusAtom = atomWithReset<{
  isStarted: boolean;
  speed: number;
  current: number;
  paused: boolean;
}>({
  isStarted: false,
  speed: 1,
  current: -1,
  paused: false,
});

/**
 * 图层显示隐藏效果
 */
// 污染物图层开关
export const pollutionLayersVisibleAtom = atom<{
  satiRemote: boolean;
  nationStation: boolean;
}>({
  satiRemote: true,
  nationStation: true,
});

// 地图图层状态
export type MapLayerStatus = {
  label: string;
  type: 'vec' | 'img' | 'ter';
  visible: boolean;
};
export const mapTypeAtom = atom<'img' | 'vec' | 'ter'>('vec');
export const mapLayerStatusAtom = atom<MapLayerStatus[]>([
  {
    label: '标准底图',
    type: 'vec',
    visible: true,
  },
  {
    label: '影像底图',
    type: 'img',
    visible: false,
  },
  {
    label: '地形底图',
    type: 'ter',
    visible: false,
  },
]);

// 是否渲染动态风场
export const showWindMapAtom = atom<boolean>(false);

// 气象图层状态
// 气象图层请求纹理图type 与卫星遥感type互斥
export const fetchTextureWeatherTypeAtom = atom<string | null>(null);
export const weatherLayersStatusAtom = atom([
  {
    label: '风速',
    value: 'wiu-wiv',
    visible: false,
  },
  {
    label: '风向',
    value: 'wiu-wiv',
    visible: false,
  },
  {
    label: '湿度',
    value: 'rh',
    visible: false,
  },
  {
    label: '气压',
    value: 'prs',
    visible: false,
  },
  {
    label: '降水',
    value: 'pre',
    visible: false,
  },
  {
    label: '温度',
    value: 'tmp',
    visible: false,
  },
  {
    label: '能见度',
    value: 'vis',
    visible: false,
  },
]);

export const timelineVisibleAtom = atom(() => {
  // const { satiRemote } = get(pollutionLayersVisibleAtom);
  // const weatherLayersVisible = get(weatherLayersStatusAtom);
  // const hasWeatherLayerVisible = weatherLayersVisible.some(
  //   (item) => item.visible,
  // );

  // return satiRemote || hasWeatherLayerVisible;
  return true;
});

// 国控、省控、卫星遥感污染物详情时间点
export const pointInTimeAtom = atom<null | string>(null);

/**
 * 测距、测量面积状态
 */
export interface DistancePoint {
  id: string;
  coordinate: [number, number];
  x: number;
  y: number;
  distance: number;
}
export interface AreaPoint {
  id: string;
  coordinate: [number, number];
  x: number;
  y: number;
}
export interface DistanceGroup {
  id: string;
  points: DistancePoint[];
  isEditing: boolean;
}
export interface AreaGroup {
  id: string;
  points: AreaPoint[];
  isEditing: boolean;
}
export const measureDisStatusAtom = atom(false);
export const measureAreaStatusAtom = atom(false);
// 更新时重新计算点与点间距离
export const distanceGroupsAtom = atom<DistanceGroup[], (prev: DistanceGroup[]) => DistanceGroup[]>(
  [],
  (get, set, updater: ((prev: DistanceGroup[]) => DistanceGroup[]) | DistanceGroup[]) => {
    const oldVal = get(distanceGroupsAtom);
    const newVal = typeof updater === 'function' ? updater(oldVal) : updater;

    const calcValue = newVal.map((group) => {
      return {
        ...group,
        points: group.points.map((point, index) => {
          return {
            ...point,
            distance: group.points.reduce((prev, cur, i) => {
              if (i > 0 && i <= index) {
                return (
                  prev +
                  turfDistance(group.points[i - 1].coordinate, cur.coordinate, {
                    units: 'kilometers',
                  })
                );
              }

              return prev;
            }, 0),
          };
        }),
      };
    }, []);

    set(distanceGroupsAtom, calcValue as any);
  },
);
export const areaGroupsAtom = atom<AreaGroup[]>([]);

/**
 * 数据图层数据拾取
 */
// 地图拾取数据
export const textureLayerPickerValueAtom = atom<null | string>(null);

// 卫星遥感请求数据参数
export const remotePoTypeAtom = atom<string | null>('pm10');

export interface TexturePoint {
  timePoints: string;
  name: string;
  size: number;
}

export interface TextureDataItem {
  url?: string;
  dateTime?: string;
  name: string;
  timePoints: string;
  size: number;
}
export interface AqiItem {
  name?: string;
  type: 'province' | 'nation' | 1 | 2;
  region?: string;
  lon: number;
  lat: number;
  date?: string;
  co: number | null;
  no?: number | null;
  no2: number | null;
  o3: number | null;
  pm10: number | null;
  pm25: number | null;
  so2: number | null;
  aqi?: number | null;
  placeName?: string;
  stationNumber?: string;
}

// 卫星遥感图层是否可见
export const textureLayerVisibleAtom = atom(true);

export const textureLayerDataAtom = atom<TexturePoint[]>([]);
export const colorLayerTypeAtom = atom<'TCOLOR' | 'DCOLOR' | 'DMASK' | ''>('');
export const dustRangeLayerVisibleAtom = atom(false);
export const dustForecastLayerVisibleAtom = atom(false);

export const isSelectedImageLayerAtom = atom((get) => {
  const colorLayerType = get(colorLayerTypeAtom);
  const dustRangeLayerVisible = get(dustRangeLayerVisibleAtom);
  const dustForecastLayerVisible = get(dustForecastLayerVisibleAtom);
  return colorLayerType !== '' || dustRangeLayerVisible || dustForecastLayerVisible;
});

export const isSelectedWeatherOrPollutionLayerAtom = atom((get) => {
  const fetchTextureWeatherType = get(fetchTextureWeatherTypeAtom);
  const remotePoType = get(remotePoTypeAtom);
  return !!fetchTextureWeatherType || !!remotePoType;
});

export const tileLayerVisibleAtom = atom({
  TCOLOR: true,
  DCOLOR: true,
  DMASK: true,
  SAND_SOURCE: true,
});
export const sandSourceLayerVisibleAtom = atom(false);

export const timeTypeAtom = atom<'time' | 'date'>('date');
export const stationDataAtom = atom<AqiItem[]>([]);
export const stationLayerPickValueAtom = atom<AqiItem | undefined>(undefined);

export const customLegendModalAtom = atomWithReset<
  | {
      pltType: string;
      legendType: string;
    }
  | undefined
>(undefined);

/**
 * 播放轴颗粒度为小时，未选中其他污染物，开启影像图：坐标轴变为半小时一个点，按照播放轴颗粒度半小时确定默认选中时间范围和最大时间范围；
 * 播放轴颗粒度为小时，选中了其他污染物，开启影像图：坐标轴还是1小时一个点，展示影像图的整点数据；
 */
export const isAggHalfHourAtom = atom((get) => {
  const agg = get(aggAtom);
  const remotePoType = get(remotePoTypeAtom);
  const fetchTextureWeatherType = get(fetchTextureWeatherTypeAtom);
  const colorLayerType = get(colorLayerTypeAtom);
  const pollutionLayersVisible = get(pollutionLayersVisibleAtom);
  const dustRangeLayerVisible = get(dustRangeLayerVisibleAtom);
  const dustForecastLayerVisible = get(dustForecastLayerVisibleAtom);
  const isHalfHour =
    agg === 'hourly' &&
    (!remotePoType || !pollutionLayersVisible.satiRemote) &&
    !fetchTextureWeatherType &&
    (!!colorLayerType || dustForecastLayerVisible || dustRangeLayerVisible);
  return isHalfHour;
});

export const openDownloadModalAtom = atom(false);

export const loadingCloudTileAtom = atom<boolean>(false);
export const loadingDColorTileAtom = atom<boolean>(false);

export const hoveredSandRangeDataAtom = atom<
  | {
      position: [number, number];
      properties: any;
    }
  | undefined
>(undefined);

export const hoverSandForecastDataAtom = atom<
  | {
      position: [number, number];
      properties: any;
    }
  | undefined
>(undefined);

export const selectedSandSourceDataAtom = atom<
  | {
      coordinate: [number, number];
      properties: any;
    }
  | undefined
>(undefined);
