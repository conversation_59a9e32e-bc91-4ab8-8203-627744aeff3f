export interface FirespotDataItem {
  area: number;
  id: number;
  lat: number;
  lon: number;
  qcf: number;
  region: '';
  temperature: number;
  timestamp: string;
  type: string;
}
export interface PollutionDetails {
  address: string;
  code: string;
  create: string;
  city: string;
  blowdownInfoModels: {
    type: string;
    value: number;
  }[];
  county: string;
  contact: [string, null | string];
  foundTime: string;
  id: number;
  industry: string;
  lat: number;
  level: 1 | 99;
  lon: number;
  muted: boolean;
  name: string;
  region: string;
  regionCode: number;
  status: number;
  type: string[];
  station: boolean;
  stationNames: string[];
}

export interface CheckRecord {
  actualValue: number;
  datetime: string;
  level: number;
  percent: number;
  pollutionType: string;
  thresholdValue: number;
}

export type Agg = 'hourly' | 'daily' | 'monthly' | 'yearly' | 'custom';

export enum AGG_TO_DATE_TYPE {
  hourly = 'date',
  daily = 'date',
  monthly = 'month',
  yearly = 'year',
  custom = 'date',
}
