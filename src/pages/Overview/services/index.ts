import { request } from '@/utils';
import { stringify } from 'qs';

export interface FetchTextureListParams {
  endDate: string;
  startDate: string;
  type: string;
  agg: string;
}

export interface TextureMap {
  minLng: number;
  maxLng: number;
  minLat: number;
  maxLat: number;
  url: string;
}

export const fetchLatestWindTexture = (datetime: string) => {
  return request(`/api/texture/range/point?type=WIU-WIV&window=2&dateTime=${datetime}`);
};

export const fetchTextureList = (params: FetchTextureListParams) => request(`/api/texture/meta?${stringify(params)}`);

export const fetchSopDetails = (id: number) => request(`/api/sop?id=${id}`);

export const fetchUserTextureMap = (): Promise<TextureMap> =>
  new Promise((resolve) => {
    resolve({
      maxLat: 56.3754545083708,
      maxLng: 135.415325243092,
      minLat: 31.0186153819943,
      minLng: 86.1010555779645,
      url: '/150000.png',
    });
  });

export const fetchCheckRecord = (id: number) => request(`/api/sop/record?id=${id}`);

export interface UpdatePoParams {
  id: number;
  level: number;
  status: number;
}
export const updatePollution = (params: UpdatePoParams) =>
  request('/api/sop/state/update', {
    method: 'POST',
    body: JSON.stringify(params),
  });

export const getEarlyWarningList = (date: string) => request(`/api/forecast/list?date=${date}`);

export const getSuspectedPollutionSourceById = (id: number) => request(`/api/sop/suspected/pollution/source?id=${id}`);

export interface GetAqiParams {
  date: string;
  regionCode: number;
  type?: 'nation' | 'province' | 'all';
}

export interface AqiItem {
  name?: string;
  type: 'province' | 'nation' | 1 | 2;
  region?: string;
  lon: number;
  lat: number;
  date?: string;
  co: number | null;
  no?: number | null;
  no2: number | null;
  o3: number | null;
  pm10: number | null;
  pm25: number | null;
  so2: number | null;
  aqi?: number | null;
  placeName?: string;
  stationNumber?: string;
}
export const getAqiList = (params: GetAqiParams) => request(`/api/data/aqi/list?${stringify(params)}`);

interface GetEvaSatelliteParams {
  time: string;
  regionCode: number;
  type: string;
  agg: string;
}

export const getEvaSatelliteList = (params: GetEvaSatelliteParams) => request(`/api/evaluation/satellite/list?${stringify(params)}`);
interface GetEvaluationSatelliteCustomListParams {
  agg: string;
  regionCode: number;
  startDate: string;
  endDate: string;
  type: string;
}
export const getEvaluationSatelliteCustomList = (params: GetEvaluationSatelliteCustomListParams) =>
  request(`/api/evaluation/satellite/custom/list?${stringify(params)}`);

interface GetFirespotParams {
  date: string;
  regionCode: number;
  type: string;
}

export const getFirespot = (params: GetFirespotParams) => request(`/api/data/firespot/list?${stringify(params)}`);

export interface GetAlertCycleListParams {
  date: string;
  regionCode: number;
  types: string;
}
export const getAlertCycleList = (params: GetAlertCycleListParams) => request(`/api/alert/cycle/list?${stringify(params)}`);

export interface StationParams {
  regionCode: number;
  date: string;
}

export interface GetAnalysisReportCreateParams {
  regionCode: number;
  reportType: 1 | 2;
  startDate: string;
  endDate: string;
}
export const getAnalysisReportCreate = (params: GetAnalysisReportCreateParams) =>
  request(`
/api/analysis/report/create?${stringify(params)}`);
