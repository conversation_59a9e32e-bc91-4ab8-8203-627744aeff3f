import { request } from "@/utils"
import { stringify } from "qs"
import { useMemo } from "react";
import { useQuery } from "react-query";

/** 纹理瓦片token */
type TGetTextureTokenParams =  {
    /** 域名,多个英文逗号间隔,默认所有域名都可访问  */
    domains?: string;
    /** 类型:PM25...  */
    type?: string;
};

/** 纹理瓦片token */ 
export const getTextureToken = (params: TGetTextureTokenParams ) => {
    return request(`/api/texture/token?${stringify(params)}`) as Promise<{token: string}>
}
export const useTextureTokenWithType = (type: string) => {
    const params = useMemo(() => (type ? { type } : {}), [type]);

  const { data } = useQuery<{ token: string }>(
    ['texture-token', type],
    () => getTextureToken(params),
    {
      enabled: true,
    },
  );

  return data?.token;
}