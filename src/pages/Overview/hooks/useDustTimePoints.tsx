import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';

import { regionAtom } from '@/atoms';
import { getDustRecordTimePoint } from '@/components/DustDetail/services';
import dayjs from 'dayjs';
import { datePickerValAtom, isAggHalfHourAtom, isSelectedImageLayerAtom } from '../atoms';
import { activeTimelineItemAtom, dustTimePointsAtom, isSelectTheLastPointOfTimelineAtom } from '../atoms/timeline';
import { TimelineListItem } from '../components/Timeline/TimelineItem';

export default function useDustTimePoints() {
  const activeTimelineItem = useAtomValue(activeTimelineItemAtom);
  const setIsSelectTheLastPointOfTimeline = useSetAtom(isSelectTheLastPointOfTimelineAtom);
  const setTimePoints = useSetAtom(dustTimePointsAtom);
  const datePickerVal = useAtomValue(datePickerValAtom);
  const isHalfHour = useAtomValue(isAggHalfHourAtom);
  const regionCode = useAtomValue(regionAtom);

  const params = useMemo(
    () => ({
      // 固定区域，无论什么情况都传 100000
      regionCode: regionCode === 0 ? 100000 : regionCode,
      startTime: datePickerVal?.[0],
      endTime: datePickerVal?.[1],
    }),
    [datePickerVal, regionCode],
  );

  const { data } = useQuery(['/api/dust/record/time/point', params], () => getDustRecordTimePoint(params), {
    enabled: !!datePickerVal?.[0] && !!datePickerVal?.[1] && !!params.regionCode,
  });

  useEffect(() => {
    const isSelectTheLast = Array.isArray(data) && data.length > 0 && data[data.length - 1].dataTime === activeTimelineItem?.value;

    setIsSelectTheLastPointOfTimeline(isSelectTheLast);
  }, [activeTimelineItem?.value, data, setIsSelectTheLastPointOfTimeline]);

  const isSomeImageLayerVisible = useAtomValue(isSelectedImageLayerAtom);

  const timePoints: TimelineListItem[] = useMemo(() => {
    return (data || [])
      .map((item) => {
        const { dataTime, count, sandSite } = item;
        return {
          id: `${dataTime}-${count}-${sandSite}`,
          label: dataTime,
          detailLabel: dataTime,
          value: dataTime,
          haveDust: isSomeImageLayerVisible ? count > 0 : false,
          isStartPoint: isSomeImageLayerVisible ? sandSite : false, // 0：是，1：否
        };
      })
      .filter((item) => {
        if (isHalfHour) {
          return true;
        }
        // 用 dayjs 过滤半点数据
        return dayjs(item.label).minute() !== 30;
      });
  }, [data, isHalfHour, isSomeImageLayerVisible]);

  useEffect(() => {
    setTimePoints(timePoints);
  }, [setTimePoints, timePoints]);

  return timePoints;
}
