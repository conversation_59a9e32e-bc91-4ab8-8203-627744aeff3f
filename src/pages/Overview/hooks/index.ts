import { regionAtom, userInfoAtom } from '@/atoms';
import { useCreateTextureLayer, useInterval, useWindowSize } from '@/hooks';
import WindMapLayer from '@/layers/wind-layer/windmap-layer';
import { fetchGeojsonByRegionCode, fetchGeojsonIncludeChild, fetchSopDicList, fetchTextureTimePoints, fetchTextureToken } from '@/services/global';
import type { DicData, Industry } from '@/types';
import {
  colorRamps,
  dateFormatter,
  datetimeFormatter,
  decoder,
  getColorByValue,
  getLevelByRegionCode,
  getNewViewState,
  getTileUrl,
  hourFormatter,
  stationPollutionValuesAndColors,
  stationTypeOptions,
} from '@/utils';
import { distancePathLayerBaseConfig, pointsLayerBaseConfig, tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import type { RGBColor } from '@deck.gl/core/utils/color';
import center from '@turf/center';
import type { AllGeoJSON } from '@turf/helpers';
import { message, Modal } from 'antd';
import { FlyToInterpolator, GeoJsonLayer, IconLayer, PathLayer, TileLayer } from 'deck.gl';
import type { SetStateAction } from 'jotai';
import { useAtom, useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import uniqueId from 'lodash/uniqueId';
import moment from 'moment';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import type { AreaPoint, DistanceGroup, DistancePoint } from '../atoms';
import { activeTimelineItemAtom, aggAtom, areaGroupsAtom, distanceGroupsAtom, measureAreaStatusAtom, measureDisStatusAtom } from '../atoms';
import type { TextureDataItem, TexturePoint } from '../atoms/index';
import {
  calculatedViewStateAtom,
  colorLayerTypeAtom,
  datePickerValAtom,
  fetchTextureWeatherTypeAtom,
  mapTypeAtom,
  nationStationPoTypeAtom,
  pointInTimeAtom,
  pollutionLayersVisibleAtom,
  remotePoTypeAtom,
  showWindMapAtom,
  stationDataAtom,
  stationLayerPickValueAtom,
  textureLayerDataAtom,
  tileLayerVisibleAtom,
  timelineStatusAtom,
  viewStateAtom,
} from '../atoms/index';
import type { AqiItem, UpdatePoParams } from '../services';
import { fetchCheckRecord, fetchLatestWindTexture, fetchUserTextureMap, getAqiList, updatePollution } from '../services';
import type { CheckRecord, PollutionDetails } from '../types';
// eslint-disable-next-line max-len
import { DUST_EXTENT } from '@/configs';
import type { TreeData } from '@/types';
import useGetLegend from './useGetLegend';

export const wmts = (type: 'img' | 'vec' | 'cia' | 'cva' | 'cta' | 'ter' | 'ibo') =>
  [0, 1, 2, 3, 4, 5, 6, 7].map(
    (idx) =>
      `https://t${idx}.tianditu.gov.cn/${type}_w/wmts?tk=e823900c00dafd3baf2de710a4259819&layer=${type}&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}`,
  );

export const useTextureToken = () => {
  const { data } = useQuery<{ token: string }>(['texture-token'], () => fetchTextureToken(), {
    staleTime: 1000 * 60 * 5,
  });

  return data?.token;
};

// 瓦片标记图层
export const useTileLayerWithLabel = () => {
  const tileLayerVisible = useAtomValue(tileLayerVisibleAtom);
  const colorLayerType = useAtomValue(colorLayerTypeAtom);

  const mapType = useAtomValue(mapTypeAtom);
  const labelType = useMemo(() => {
    if (mapType === 'img') return 'cia';
    if (mapType === 'ter') return 'cta';
    return 'cva';
  }, [mapType]);

  const visible = useMemo(() => {
    if (colorLayerType === '') {
      return true;
    }

    return tileLayerVisible[colorLayerType];
  }, [colorLayerType, tileLayerVisible]);

  const tileLayer = useMemo(() => {
    if (!mapType) {
      return null;
    }
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'tianditu-with-label',
      data: wmts(labelType),
      pickable: false,
      visible,
    });
  }, [mapType, labelType, visible]);

  return tileLayer;
};
// 境界图层
export const useFrontierTileLayerWithLabel = (visible: boolean, opacity: number) => {
  const tileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'tianditu-frontier-layer',
      data: wmts('ibo'),
      opacity,
      pickable: false,
      visible,
    });
  }, [opacity, visible]);

  return tileLayer;
};

// 基础瓦片图层
export const useTileLayer = () => {
  const [disGroups, setDisGroups] = useAtom(distanceGroupsAtom);
  const [areaGroups, setAreaGroups] = useAtom(areaGroupsAtom);
  const isMeasuringDis = useAtomValue(measureDisStatusAtom);
  const isMeasuringArea = useAtomValue(measureAreaStatusAtom);
  const mapType = useAtomValue(mapTypeAtom);

  const tileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'tianditu-tile-layer',
      data: wmts(mapType),
      updateTriggers: {
        isMeasuringDis,
        disGroups,
        areaGroups,
        isMeasuringArea,
      },
      onClick(info, e) {
        if (!isMeasuringDis && !isMeasuringArea) return;
        // 右键
        // @ts-ignore
        if (e.rightButton) {
          setDisGroups((prev: DistanceGroup[]) =>
            prev.map((group) => ({
              ...group,
              isEditing: false,
            })),
          );
          return;
        }
        const { x, y, coordinate } = info;

        if (coordinate) {
          const [lng, lat] = coordinate;
          if (isMeasuringDis) {
            const isEditing = disGroups.some((group) => group.isEditing);
            const newPoint = (id: string) => ({
              id,
              coordinate: [lng, lat],
              x,
              y,
              distance: 0,
            });
            if (!isEditing) {
              const groupId = uniqueId('distance-group-');
              const pointId = uniqueId('distance-point-');

              setDisGroups((prev) => [
                ...prev,
                {
                  id: groupId,
                  points: [newPoint(pointId) as DistancePoint],
                  isEditing: true,
                },
              ]);
            } else {
              setDisGroups((prev: DistanceGroup[]) =>
                prev.map((group: DistanceGroup) => {
                  const pointId = uniqueId('distance-point-');
                  return group.isEditing
                    ? {
                        ...group,
                        points: [...group.points, newPoint(pointId) as DistancePoint],
                      }
                    : group;
                }),
              );
            }
          }

          if (isMeasuringArea) {
            const isEditing = areaGroups.some((group) => group.isEditing);
            const newPoint = {
              id: uniqueId('area-point-'),
              x,
              y,
              coordinate: [lng, lat],
            } as AreaPoint;

            if (isEditing) {
              setAreaGroups(
                areaGroups.map((group) => {
                  return group.isEditing
                    ? {
                        ...group,
                        points: [group.points[0], newPoint],
                        isEditing: false,
                      }
                    : {
                        ...group,
                        isEditing: false,
                      };
                }),
              );
            } else {
              setAreaGroups([
                ...areaGroups,
                {
                  id: uniqueId('area-group-'),
                  isEditing: true,
                  points: [newPoint],
                },
              ]);
            }
          }
        }
      },
      onHover(info) {
        const { x, y, coordinate } = info;
        if (isMeasuringArea && coordinate) {
          const [lng, lat] = coordinate;

          const newPoint = {
            id: uniqueId('area-point-'),
            x,
            y,
            coordinate: [lng, lat],
          } as AreaPoint;

          setAreaGroups((prev) =>
            prev.map((group) =>
              group.isEditing
                ? {
                    ...group,
                    points: [group.points[0], newPoint],
                  }
                : group,
            ),
          );
        }
      },
    });
  }, [mapType, areaGroups, disGroups, isMeasuringArea, isMeasuringDis, setAreaGroups, setDisGroups]);
  return tileLayer;
};

// 测量面积点图层
export const useAreaPointsLayer = () => {
  const areaGroups = useAtomValue(areaGroupsAtom);
  // 测量距离点图层
  const layer = useMemo(() => {
    const data = areaGroups.reduce((prev: any, cur) => {
      const { points } = cur;
      const arr = points.map((point) => {
        const [lng, lat] = point.coordinate;

        return { coordinate: [lng, lat] };
      });

      return [...prev, ...arr];
    }, [] as { coordinate: [number, number] }[]);
    return new IconLayer<{
      coordinate: [number, number];
    }>({
      ...pointsLayerBaseConfig,
      id: 'area-point-layer',
      data,
    });
  }, [areaGroups]);

  return layer;
};

// 测量距离点图层
export const useDistancePointsLayer = () => {
  const disGroups = useAtomValue(distanceGroupsAtom);

  const layer = useMemo(() => {
    const data = disGroups.reduce((prev: any, cur) => {
      const { points } = cur;
      const arr = points.map((point) => {
        const [lng, lat] = point.coordinate;

        return { coordinate: [lng, lat] };
      });

      return [...prev, ...arr];
    }, [] as { coordinate: [number, number] }[]);
    return new IconLayer<{
      coordinate: [number, number];
    }>({
      ...pointsLayerBaseConfig,
      data,
    });
  }, [disGroups]);

  return layer;
};

// 测量距离点连线图层
export const useDistancePathLayer = () => {
  const disGroups = useAtomValue(distanceGroupsAtom);
  const layer = useMemo(() => {
    return new PathLayer<[number, number][]>({
      ...distancePathLayerBaseConfig,
      data: disGroups.reduce((prev: any, cur) => {
        const points = cur.points.map((point) => point.coordinate);
        return [...prev, points];
      }, []),
    });
  }, [disGroups]);

  return layer;
};

export const AlertColors: Record<number, RGBColor> = {
  4: [126, 0, 35],
  3: [153, 0, 76],
  2: [255, 0, 0],
  1: [255, 126, 0],
};

// GeoJSON图层
export const useGeoJSONLayer = () => {
  const userInfo = useAtomValue(userInfoAtom);
  const mapType = useAtomValue(mapTypeAtom);
  const regionCode = useAtomValue(regionAtom);
  const colorLayerType = useAtomValue(colorLayerTypeAtom);
  const provinceCode = useMemo(() => {
    if (userInfo?.regionCode) {
      return `${`${userInfo.regionCode}`.substring(0, 2)}0000`;
    }
    return '';
  }, [userInfo?.regionCode]);

  const { data: geojson } = useQuery(
    ['geojson-layer-data'],
    () =>
      fetchGeojsonIncludeChild({
        code: Number(provinceCode),
        level: 1,
      }),
    {
      enabled: Boolean(provinceCode),
    },
  );

  const highlightedGeojson = useMemo(() => {
    const level = getLevelByRegionCode(Number(userInfo?.regionCode));
    if ((regionCode === userInfo?.regionCode || regionCode === 0) && level === 1) {
      return geojson?.features;
    }
    return geojson?.features?.filter((item) => regionCode === Number(item.properties.adcode)) ?? [];
  }, [geojson?.features, regionCode, userInfo?.regionCode]);

  const geojsonLayer = useMemo(
    () =>
      new GeoJsonLayer({
        id: 'geojson-layer',
        pickable: true,
        stroked: true,
        lineWidthUnits: 'pixels',
        getLineWidth: 2,
        lineWidthMinPixels: 2,
        // @ts-ignore
        data: geojson || [],
        getLineColor: () => {
          return [88, 88, 88, 255];
        },
        filled: false,
        getFillColor: () => [0, 0, 0, 0],
        updateTriggers: {
          getLineColor: [regionCode],
        },
      }),
    [geojson, regionCode],
  );

  const highlightGeojsonLayer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'geojson-layer-highlight',
      lineWidthUnits: 'pixels',
      pickable: true,
      stroked: true,
      getLineWidth: 2,
      lineWidthMinPixels: 2,
      // @ts-ignore
      data: highlightedGeojson || [],
      getLineColor: () => {
        if (colorLayerType === '' && mapType !== 'img') {
          return [88, 88, 88, 255];
        }
        return [255, 255, 255, 255];
      },
      filled: false,
      getFillColor: () => [0, 0, 0, 0],
      updateTriggers: {
        getLineColor: [mapType, colorLayerType],
      },
    });
  }, [colorLayerType, highlightedGeojson, mapType]);

  return [geojsonLayer, highlightGeojsonLayer];
};

export const useWindMapLayer = () => {
  const showWindMap = useAtomValue(showWindMapAtom);
  const currentTime = useAtomValue(pointInTimeAtom);
  const token = useTextureToken();

  const { data: map } = useQuery(['user-texture-map'], fetchUserTextureMap);

  const queryKeys = useMemo(() => ['wind-texture', currentTime], [currentTime]);

  const { data: wind } = useQuery<TextureDataItem | null>(
    queryKeys,
    () => {
      if (!currentTime) return null;
      return fetchLatestWindTexture(currentTime);
    },
    {
      enabled: Boolean(showWindMap),
    },
  );

  const layer = useMemo(() => {
    if (!showWindMap || !map) return null;
    return new WindMapLayer({
      id: 'windmap-layer',
      map,
      colorRamp: colorRamps['wiu-wiv'],
    });
  }, [showWindMap, map]);

  useEffect(() => {
    if (layer && wind) {
      const url = getTileUrl({
        agg: 'none',
        time: wind.timePoints,
        token: token || '',
        type: 'WIU-WIV',
      });

      layer.setTileUrl(url);
    }
  }, [layer, token, wind]);

  return layer;
};

export const useTextureLayer = () => {
  const date = useAtomValue(datePickerValAtom);
  const setTextureData = useUpdateAtom(textureLayerDataAtom);
  const pollutionLayersVisible = useAtomValue(pollutionLayersVisibleAtom);
  const remoteType = useAtomValue(remotePoTypeAtom);
  const weatherType = useAtomValue(fetchTextureWeatherTypeAtom);
  const agg = useAtomValue(aggAtom);
  const timelinePlayStatus = useAtomValue(timelineStatusAtom);
  const [activeTimelineItem] = useAtom(activeTimelineItemAtom);
  const { isStarted, current } = timelinePlayStatus;
  const [dataIndex, setDataIndex] = useState(0);
  const token = useTextureToken();
  const { createTextureLayer } = useCreateTextureLayer();
  const type = useMemo(() => String(weatherType || remoteType).toLowerCase(), [remoteType, weatherType]);
  const { decoder: customDecoder, colorRamp: customColorRamp } = useGetLegend(String(type).toLocaleLowerCase(), 'remote');

  const queryKeys = useMemo(
    () => ['texture-list', remoteType, weatherType, date ? date[0] : null, date ? date[1] : null, agg],
    [agg, date, remoteType, weatherType],
  );
  const params = useMemo(
    () => ({
      type: (weatherType || remoteType)?.toUpperCase(),
      startDate: (date && date[0]) || '',
      endDate: (date && date[1]) || '',
      agg,
    }),
    [agg, date, remoteType, weatherType],
  );

  const { data } = useQuery<TexturePoint[]>(
    queryKeys,
    () =>
      fetchTextureTimePoints({
        ...params,
        type: String(params.type),
      }),
    {
      enabled: Boolean(date && date[0] && date[1] && (pollutionLayersVisible.satiRemote || weatherType)),
    },
  );

  useEffect(() => {
    if (pollutionLayersVisible.satiRemote && data) {
      setTextureData(data);
    }
  }, [data, pollutionLayersVisible.satiRemote, setTextureData]);

  const { data: map } = useQuery(['user-texture-map'], fetchUserTextureMap, {});

  const textureData = useMemo(() => data || [], [data]);

  const layer = useMemo(() => {
    const find = textureData.find(
      (v) => moment(v.timePoints).format(datetimeFormatter) === moment(activeTimelineItem?.value).format(datetimeFormatter),
    );

    if (!find) {
      return null;
    }
    return createTextureLayer({
      id: `data-texture-layer-${weatherType || remoteType}`,
      dataUrl: getTileUrl(
        {
          token: token || '',
          time: moment(find.timePoints).format('YYYY/MM/DD HH:00:00'),
          agg,
          type: String(params.type),
        },
        true,
      ),
      decoder: customDecoder,
      colorRamp: customColorRamp,
      visible: Boolean(pollutionLayersVisible.satiRemote || weatherType),
    });
  }, [
    textureData,
    createTextureLayer,
    weatherType,
    remoteType,
    token,
    agg,
    params.type,
    customDecoder,
    customColorRamp,
    pollutionLayersVisible.satiRemote,
    activeTimelineItem?.value,
  ]);

  useEffect(() => {
    if (!textureData || textureData.length === 0) return;
    if (isStarted && layer) {
      // layer.setTileUrl(textureData[current].url);
    }
  }, [dataIndex, isStarted, textureData, layer, current]);

  useEffect(() => {
    if (!isStarted) {
      setDataIndex(current);
    }
  }, [current, isStarted]);

  useEffect(() => {
    // eslint-disable-next-line max-len
    if (
      !isStarted &&
      current > -1 &&
      current < textureData.length &&
      layer &&
      textureData[current] &&
      (pollutionLayersVisible.satiRemote || weatherType)
    ) {
      // layer.setTileUrl(textureData[current].url);
    }
  }, [current, isStarted, textureData, layer, pollutionLayersVisible.satiRemote, weatherType]);

  return layer;
};

// 一张图初始化时根据用户所在地区geojson及window大小计算缩放级别
// 同时计算中心点坐标

export const useSetCalculatedViewState = () => {
  const [viewState, setViewState] = useAtom(viewStateAtom);
  const viewStateRef = useRef(viewState);
  const setCalculatedViewState = useUpdateAtom(calculatedViewStateAtom);
  const regionCode = useAtomValue(regionAtom);
  const { width, height } = useWindowSize();

  const { data: geojson } = useQuery(['calc-view-state-geojson', regionCode], () => fetchGeojsonByRegionCode(Number(regionCode)), {
    enabled: Boolean(regionCode),
  });

  useEffect(() => {
    if (geojson) {
      const centroidResult = center(geojson as AllGeoJSON);
      const newViewState = getNewViewState(geojson, viewStateRef.current, width, height, 124);

      setViewState((prev) => {
        const state: ViewStateProps = {
          ...prev,
          longitude: centroidResult.geometry.coordinates[0],
          latitude: centroidResult.geometry.coordinates[1],
          zoom: newViewState.zoom || 4,
          transitionInterpolator: new FlyToInterpolator(),
          transitionDuration: 1000,
        };
        setCalculatedViewState((prevState) => {
          if (prevState) return prevState;

          return state;
        });
        return state;
      });
    }
  }, [height, setCalculatedViewState, setViewState, geojson, width]);

  return useMemo(
    () => ({
      geojson,
    }),
    [geojson],
  );
};
/**
 * 键盘事件
 * @param fn 键盘事件keydown回调
 */
export const useHandleKeyDown = (fn: (e: KeyboardEvent) => void) => {
  useEffect(() => {
    document.addEventListener('keydown', fn, false);
    return () => {
      document.removeEventListener('keydown', fn, false);
    };
  }, [fn]);
};

export const useFormattedTextureData = (textureData: TexturePoint[]) => {
  const formattedData = useMemo(() => {
    return (textureData || []).reduce(
      (
        prev: Record<
          string,
          {
            dateTime: string;
            index: number;
          }[]
        >,
        cur,
        index: number,
      ) => {
        const { timePoints: dateTime } = cur;
        const day = moment(dateTime).format('MM-DD');

        if (prev[day]) {
          return {
            ...prev,
            [day]: [
              ...prev[day],
              {
                dateTime,
                index,
              },
            ],
          };
        }
        return {
          ...prev,
          [day]: [
            {
              dateTime,
              index,
            },
          ],
        };
      },
      {} as Record<string, { dateTime: string; index: number }[]>,
    );
  }, [textureData]);

  return formattedData;
};

export const useTimelineInterval = (textureData: TexturePoint[], isStarted: boolean) => {
  const setTimelineStatus = useUpdateAtom(timelineStatusAtom);
  useInterval(
    () => {
      setTimelineStatus((prev) => {
        if (prev.current + 1 === (textureData || []).length) {
          return {
            ...prev,
            current: 0,
          };
        }

        return {
          ...prev,
          current: prev.current + 1,
        };
      });
    },
    isStarted ? 5000 : null,
  );
};

interface TimelineEffectParams {
  textureData: TexturePoint[];
  setTimelineStatus: any;
  timelineBodyRef: React.RefObject<HTMLDivElement | null>;
  timelineWrapperRef: React.RefObject<HTMLDivElement | null>;
  dialWidth: number;
  setScrollLeft: any;
  current: number;
  setPointInTime: (update: SetStateAction<string | null>) => void;
  setDialWidth: React.Dispatch<React.SetStateAction<number>>;
}

export const useTimelineEffect = (params: TimelineEffectParams) => {
  const { textureData, setTimelineStatus, timelineBodyRef, timelineWrapperRef, dialWidth, setScrollLeft, current, setPointInTime, setDialWidth } =
    params;
  useEffect(() => {
    if (textureData && textureData.length > 0) {
      setTimelineStatus((prev: { isStarted: boolean; speed: number; current: number; paused: boolean }) => ({
        ...prev,
        current: textureData.length - 1,
      }));
    }
  }, [setTimelineStatus, textureData]);

  useEffect(() => {
    if (timelineBodyRef.current && timelineWrapperRef.current) {
      const { offsetWidth } = timelineWrapperRef.current;
      const count = Math.ceil(offsetWidth / dialWidth);
      setScrollLeft((current - count / 2) * dialWidth);
    }
  }, [current, dialWidth, setScrollLeft, timelineBodyRef, timelineWrapperRef]);

  useEffect(() => {
    if (textureData && textureData.length > 0 && textureData[current]) {
      const find = textureData[current];
      setPointInTime(moment(find.timePoints).format(datetimeFormatter));
    }
  }, [current, setPointInTime, textureData]);

  useEffect(() => {
    if ((textureData || []).length === 0) {
      setDialWidth(10);
    }
    if (textureData && timelineWrapperRef.current) {
      const { offsetWidth } = timelineWrapperRef.current;
      if ((textureData || []).length * 10 > offsetWidth) {
        setDialWidth(10);
      } else {
        setDialWidth(offsetWidth / (textureData || []).length);
      }
    }
  }, [setDialWidth, textureData, timelineWrapperRef]);
};

export const useStationIconLayer = () => {
  const regionCode = useAtomValue(regionAtom);
  const nationStationType = useAtomValue(nationStationPoTypeAtom);
  const visible = useAtomValue(pollutionLayersVisibleAtom);
  const { nationStation: nationStationVisible } = visible;
  const setStationLayerPickedValue = useUpdateAtom(stationLayerPickValueAtom);
  const activeTimelineItem = useAtomValue(activeTimelineItemAtom);
  const setStationData = useUpdateAtom(stationDataAtom);
  const userInfo = useAtomValue(userInfoAtom);

  const { data } = useQuery<AqiItem[]>(
    ['station-data', activeTimelineItem, regionCode],
    () =>
      getAqiList({
        regionCode: regionCode || Number(userInfo?.regionCode),
        date: moment(activeTimelineItem?.value).format(hourFormatter),
        type: 'all',
      }),
    {
      retry: false,
      enabled: Boolean(nationStationVisible && activeTimelineItem?.value && (regionCode || userInfo?.regionCode)),
    },
  );

  useEffect(() => {
    setStationData(data || []);
  }, [data, setStationData]);

  interface LayerDataItem extends AqiItem {
    dataType: string | undefined;
    value: number;
  }

  const layerData: LayerDataItem[] = useMemo(() => {
    if (!nationStationVisible) return [];
    const getMatchStationType = (value: string) => {
      return stationTypeOptions.find((item) => item.value === value);
    };

    if (nationStationVisible) {
      return (data ?? [])
        .map((item) => ({
          ...item,
          dataType: getMatchStationType(nationStationType)?.label,
          value: Number(item[nationStationType]),
        }))
        .filter((item) => item.type === 'nation');
    }
    return (data ?? []).map((item) => ({
      ...item,
      dataType: getMatchStationType(nationStationType)?.label,
      value: Number(item[nationStationType]),
    }));
  }, [data, nationStationType, nationStationVisible]);

  const getIconMaskColor = useCallback(
    (dataItem: { type: 'nation' | 'province' | 1 | 2; value: number }) => {
      let nationDataKey: string = nationStationType;

      // if (nationStationType === 'no2') {
      //   nationDataKey = 'no2-hour';
      // }
      if (nationStationType === 'o3') {
        nationDataKey = 'o3-hour';
      }
      if (nationStationType === 'co') {
        nationDataKey = 'co-hour';
      }

      const color =
        getColorByValue(
          stationPollutionValuesAndColors[
            nationDataKey as 'pm25' | 'pm10' | 'aqi' | 'no2-hour' | 'no2-day' | 'co-hour' | 'co-day' | 'o3-hour' | 'o3-day'
          ].values,
          dataItem.value,
        ) || 'rgb(0,0,0)';

      return color
        .replace('rgb(', '')
        .replace(')', '')
        .split(',')
        .map((item) => +item);
    },
    [nationStationType],
  );
  const bg = new IconLayer<LayerDataItem>({
    id: 'station-icon-layer',
    data: layerData,
    getIcon: () => ({
      url: '/assets/images/station-bg.png',
      width: 68,
      height: 78,
      anchorY: 78,
      mask: true,
    }),
    getColor: (d) => getIconMaskColor(d) as RGBColor,
    getPosition: (d) => [d.lon, d.lat],
    pickable: true,
    getSize: 68,
    sizeScale: 0.45,
    visible: nationStationVisible,
    onHover(info) {
      if (info && info.object) {
        setStationLayerPickedValue(info.object);
      } else {
        setStationLayerPickedValue(undefined);
      }
    },
  });

  const text = new IconLayer<LayerDataItem>({
    id: 'station-icon-layer1',
    data: layerData,
    getIcon: () => ({
      url: '/assets/images/station-guo.png',
      width: 68,
      height: 78,
      anchorY: 78,
    }),
    getPosition: (d) => [d.lon, d.lat],
    pickable: false,
    getSize: 68,
    sizeScale: 0.45,
    visible: nationStationVisible,
  });

  return [bg, text];
};

export const useDMaskLayer = () => {
  const date = useAtomValue(datePickerValAtom);
  const colorLayerType = useAtomValue(colorLayerTypeAtom);
  const [url, setUrl] = useState('');
  const token = useTextureToken();
  const activeTimelineItem = useAtomValue(activeTimelineItemAtom);

  const queryKeys = useMemo(() => ['color-layer-meta', colorLayerType, date ? date[0] : null, date ? date[1] : null], [colorLayerType, date]);
  const { data: map } = useQuery(['user-texture-map'], fetchUserTextureMap);

  const { data } = useQuery<TexturePoint[]>(
    queryKeys,
    () =>
      fetchTextureTimePoints({
        type: colorLayerType.toUpperCase(),
        startDate: (date && date[0]) || '',
        endDate: (date && date[1]) || '',
        agg: 'none',
      }),
    {
      enabled: Boolean(date && date[0] && date[1] && colorLayerType),
    },
  );

  useEffect(() => {
    if (colorLayerType) {
      setUrl(
        getTileUrl(
          {
            token: token || '',
            agg: 'none',
            type: 'DMASK',
            time: moment(activeTimelineItem?.value).format('YYYY/MM/DD HH:00'),
          },
          true,
        ),
      );
    }
  }, [activeTimelineItem?.value, colorLayerType, token]);

  const { createTextureLayer } = useCreateTextureLayer();

  const layer = useMemo(() => {
    if (!map || !url) {
      return null;
    }

    return createTextureLayer({
      id: `data-texture-layer-dmask`,
      dataUrl: url,
      decoder: decoder.dmask,
      colorRamp: colorRamps.dmask,
      visible: Boolean(date && date[0] && date[1] && colorLayerType === 'DMASK'),
      extent: DUST_EXTENT,
      needMask: false,
    });
  }, [colorLayerType, createTextureLayer, date, map, url]);

  return layer;
};

export const useMapLayers = () => {
  const tileLayer = useTileLayer();
  const labeledTileLayer = useTileLayerWithLabel();
  const distancePathLayer = useDistancePathLayer();
  const distancePointsLayer = useDistancePointsLayer();
  const areaPointsLayer = useAreaPointsLayer();
  const geojsonLayer = useGeoJSONLayer();
  const textureLayer = useTextureLayer();
  const dMaskerLayer = useDMaskLayer();
  const windMapLayer = useWindMapLayer();
  const stationIconLayer = useStationIconLayer();

  return {
    tileLayer,
    geojsonLayer,
    textureLayer,
    windMapLayer,
    labeledTileLayer,
    areaPointsLayer,
    distancePathLayer,
    distancePointsLayer,
    stationIconLayer,
    dMaskerLayer,
  };
};

export const useDetailCheckRecords = (poId: number | undefined) => {
  const { data: checkRecordData } = useQuery<CheckRecord[]>(['check-record', poId], () => fetchCheckRecord(poId || 0), {
    enabled: !!poId,
    staleTime: 0,
    cacheTime: 0,
  });

  const records: Record<string, CheckRecord[]> | null = useMemo(() => {
    if (checkRecordData && checkRecordData.length > 0) {
      return checkRecordData.slice(0, 4).reduce((prev: Record<string, CheckRecord[]>, cur: CheckRecord) => {
        const date = moment(cur.datetime).format(dateFormatter);

        return prev[date]
          ? {
              ...prev,
              [date]: [...prev[date], cur],
            }
          : {
              ...prev,
              [date]: [cur],
            };
      }, {} as Record<string, CheckRecord[]>);
    }

    return null;
  }, [checkRecordData]);

  return records;
};

export const useUpdatePollution = (data: PollutionDetails | undefined, poId: number | undefined) => {
  const queryClient = useQueryClient();
  const updatePoMutation = useMutation((params: UpdatePoParams) => updatePollution(params));

  const update = useCallback(
    (params: any) => {
      const { id, status, level } = data ?? {};

      Modal.confirm({
        title: '确定修改污染源吗？',
        onOk() {
          updatePoMutation.mutate(
            {
              id,
              status,
              level,
              ...params,
            },
            {
              onSuccess() {
                const listQueryKeys = ['search-result'];

                queryClient.invalidateQueries(listQueryKeys, {
                  exact: false,
                });
                queryClient.invalidateQueries(['sop-details', poId]);
                message.success('操作成功');
              },
            },
          );
        },
      });
    },
    [data, poId, queryClient, updatePoMutation],
  );

  return update;
};

export const useIndustryTreeData = () => {
  const { data: dicData } = useQuery<DicData>(['dic-list'], fetchSopDicList);

  const filterOptions = useMemo(() => {
    if (!dicData) return null;

    const { levelList, industryList, typeList } = dicData;
    const levelOpts = levelList.map((item) => ({
      label: item.displayName,
      value: item.code,
    }));
    const typeOpts = typeList.map((item) => ({
      label: item.displayName,
      value: item.internalName,
    }));
    const industryOpts = industryList.reduce((prev: TreeData[], cur: Industry) => {
      const findFirstLevel = prev.find((item) => item.key === cur.firstLevelCode);

      if (findFirstLevel) {
        return prev.map((item) =>
          item.key === cur.firstLevelCode
            ? {
                ...item,
                children: [
                  ...item.children,
                  {
                    title: cur.secondLevelName,
                    key: cur.secondLevelCode,
                  },
                ],
              }
            : item,
        );
      }
      return [
        ...prev,
        {
          title: cur.firstLevelName,
          key: cur.firstLevelCode,
          children: [
            {
              key: cur.secondLevelCode,
              title: cur.secondLevelName,
            },
          ],
        },
      ];
    }, [] as TreeData[]);

    return {
      levelOptions: levelOpts,
      typeOptions: typeOpts,
      industryOptions: industryOpts,
    };
  }, [dicData]);

  return filterOptions;
};

export const useColorImageLayer = () => {
  const colorLayerType = useAtomValue(colorLayerTypeAtom);
  const [activeTimelineItem] = useAtom(activeTimelineItemAtom);
  const [url, setUrl] = useState('');
  const token = useTextureToken();

  const visible = useMemo(() => colorLayerType === 'TCOLOR', [colorLayerType]);

  useEffect(() => {
    if (visible) {
      setUrl(
        getTileUrl(
          {
            token: token || '',
            agg: 'none',
            type: colorLayerType,
            time: activeTimelineItem?.value || '',
          },
          true,
        ),
      );
    }
  }, [activeTimelineItem?.value, colorLayerType, token, visible]);

  const layer = useMemo(() => {
    if (!url) {
      return null;
    }

    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'overview-color-image-layer',
      data: url,
      maxZoom: 6,
      visible,
      extent: DUST_EXTENT,
    });
  }, [url, visible]);

  return layer;
};
