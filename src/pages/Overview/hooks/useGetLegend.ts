import {
  decoderFactory,
  remoteSensingValuesAndColors,
  request,
  stationPollutionValuesAndColors,
  weatherValuesAndColors,
} from '@/utils';
import type { IColorLevel } from '@/utils/format';
import { array_, number_, number_fixed, object_ } from '@/utils/format';
import { message } from 'antd';
import { first, last } from 'lodash';
import { useMemo } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';

type TRibbonModel = {
  /** id  */
  id: number;
  /** 最大值 例：200 */
  max: number;
  /** 最小值 例：20 */
  min: number;
  /** 类型名称 例：PM25 */
  pollutionType: string;
  /** 类型,1:遥感数据，2:站点数据 例：1 */
  type: number;
};

type TRibbonParam = {
  /** 最大值 例：200 */
  max: number;
  /** 最小值 例：20 */
  min: number;
  /** 污染物类型名称 例：PM25 */
  pollutionType: string;
  /** 数据类型，1:遥感数据，2:站点数据 例：1 */
  type: number;
};

// 删除legend
export const fetchRibbonDel = (id: number) => {
  return request(`/api/ribbon/delete?id=${id}`, {
    method: 'POST',
    body: JSON.stringify({ id }),
  }) as Promise<unknown>;
};

/** 色带列表 */
export const getRibbonList = () => {
  return request(`/api/ribbon/list`) as Promise<TRibbonModel[]>;
};

/** 色带更新或新增 */
export const postRibbonUpdate = (params: TRibbonParam) => {
  return request('/api/ribbon/update', {
    method: 'POST',
    body: JSON.stringify(params),
  }) as Promise<unknown>;
};

export const getMinMAxByLegned = (legendInfo: {
  unit: string;
  formula: string;
  equalMax: boolean;
  values: { min: number; max?: number; color: string }[];
  cn: string;
}) => {
  const { values } = legendInfo || {};
  const info = array_(values);
  const len = info.length;
  if (len === 0) return { min: 0, max: 0 };
  const firstLevel = first(info);
  const endLevel = last(info);
  const { min: topLevelMin } = object_(firstLevel) as IColorLevel;
  const { min: endLevelMin, max: endLevelMax } = object_(
    endLevel,
  ) as IColorLevel;
  return {
    max: number_(topLevelMin),
    min:
      typeof endLevelMin === 'number'
        ? number_(endLevelMin)
        : number_(endLevelMax),
  };
};

export const DIFF_LAYER_PREFIX = 'diff_';
export const DIFF_PERSANGE_LAYER_PREFIX = 'percentage_diff_';

function getColorRamps(val: { max?: number; min?: number; color: string }[]) {
  const info = array_(val);
  const len = info.length;
  if (len === 0) return {};
  const firstLevel = first(info);
  const endLevel = last(info);
  const {
    min: topLevelMin,
    max: topLevelMax,
    color: topLevelColor,
  } = object_(firstLevel);
  const {
    max: endLevelMax,
    min: endLevelMin,
    color: endLevelColor,
  } = object_(endLevel);

  const realMax =
    typeof topLevelMax === 'number' ? topLevelMax : (topLevelMin as number);
  const realMin =
    typeof endLevelMin === 'number' ? endLevelMin : (endLevelMax as number);

  const diff = realMax - realMin;

  if (diff === 0) {
    return {
      '1.00': topLevelColor,
      '0.00': endLevelColor,
    };
  }
  const dict = info.reduce((d, model, idx) => {
    if (idx === 0) return { ...d, '1.00': model.color };
    if (idx === info?.length - 1) return { ...d, '0.00': model.color };
    // 去除头尾之后，此时min、max必然存在值
    const { min, max, color } = model;
    const modelDiff = (max === realMax ? min : max) - realMin;
    const per = (modelDiff / diff).toFixed(2);
    return { ...d, [per]: color };
  }, {} as Record<string, string>);
  return dict;
}

export const colorRamps: Record<string, Record<string, string>> = {
  fnr: {
    '1.00': 'rgb(212, 57 ,48)',
    0.66: 'rgb(251, 210, 112)',
    0.5: 'rgb(254, 254, 200)',
    0.33: 'rgb(15,106,189)',
    0.0: 'rgb(11,56, 164)',
  },
  hid: {
    '1.00': '#ff1402',
    0.667: '#ff7805',
    0.333: '#ffd270',
    0.0: '#fade86',
  },
  o3mca: {
    '0.800': '#d43931',
    '0.667': '#ffd270',
    '0.333': '#2662d6',
    '0.000': '#2662d6',
  },
};

const replaceDecoder = (
  type: string,
  values: { min?: number; max?: number }[],
) => {
  const decoder = decoderFactory(type);
  if (!decoder) return '';
  const info = array_(values);
  if (info.length < 2) return '';
  const top = first(info);
  const end = last(info);
  const { min: max } = top as { min: number };
  // const { max: min } = end as { max: number };
  // 最小的色带有min和max，最大的色带没有min只有max
  // console.log(end?.min);
  const min = isNaN(end?.min) ? end?.max : end?.min;
  let decoderResult = decoder.replace(/\s/g, '');
  if (number_(min) < 0) {
    decoderResult = decoder.replaceAll(
      '{min}',
      `(0.00 ${number_(min).toFixed(2)})`,
    );
  } else {
    decoderResult = decoder.replaceAll('{min}', `${number_(min).toFixed(2)}`);
  }
  if (number_(max) < 0) {
    decoderResult = decoderResult.replaceAll(
      '{max}',
      `(0.00 ${number_(max).toFixed(2)})`,
    );
  } else {
    decoderResult = decoderResult.replaceAll(
      '{max}',
      `${number_(max).toFixed(2)}`,
    );
  }
  return decoderResult;
};

const defaultLegendInfo: Record<string, Record<string, any>> = {
  remote: { ...remoteSensingValuesAndColors, ...weatherValuesAndColors },
  station: stationPollutionValuesAndColors,
};

export default function useGetLegend(pollution: string, legendType: string) {
  const { data } = useQuery(['getRibbonList'], () => getRibbonList(), {
    enabled: true,
  });

  const pollutionInfo = useMemo(() => {
    const typeDict: Record<1 | 2, string> = {
      1: 'remote',
      2: 'station',
    };
    return array_(data).find((e) => {
      return (
        e?.pollutionType === String(pollution).toUpperCase() &&
        typeDict[e?.type as 1 | 2] === legendType
      );
    });
  }, [data, legendType, pollution]);

  const isDiffLayer = new RegExp(`^${DIFF_LAYER_PREFIX}`).test(pollution);
  const isDiffPercentageLayer = new RegExp(
    `^${DIFF_PERSANGE_LAYER_PREFIX}`,
  ).test(pollution);
  // eslint-disable-next-line no-param-reassign
  pollution = pollution?.replace(
    new RegExp(`^${DIFF_LAYER_PREFIX}|^${DIFF_PERSANGE_LAYER_PREFIX}`),
    '',
  );

  const defaultLegend = useMemo<{
    unit: string;
    formula: string;
    equalMax: boolean;
    values: { min: number; max?: number; color: string; lv: string }[];
    cn: string;
  }>(() => {
    return defaultLegendInfo?.[legendType]?.[pollution] || { values: [] };
  }, [legendType, pollution]);

  const { min: defaultMinByLegend, max: defaultMaxByLegend } =
    getMinMAxByLegned(defaultLegend);
  const defaultAbsMax = isDiffPercentageLayer
    ? 100
    : Math.max(Math.abs(defaultMinByLegend), Math.abs(defaultMaxByLegend));

  const legendInfo = useMemo<{
    unit: string;
    formula: string;
    equalMax: boolean;
    values: { min?: number; max?: number; color: string }[];
    cn: string;
    id?: number;
  }>(() => {
    if (
      !isDiffLayer &&
      !isDiffPercentageLayer &&
      (!pollutionInfo || String(pollution).toUpperCase() === 'FNR')
    ) {
      return defaultLegend;
    } else {
      //@ts-ignore
      const { max, min, id } = !!pollutionInfo
        ? pollutionInfo
        : { min: -defaultAbsMax / 10, max: defaultAbsMax / 10, id: 1 };
      const levelLen = array_(defaultLegend?.values).length;
      if (levelLen === 0 || number_(min) >= number_(max))
        return { ...defaultLegend, values: [] };
      const colorList = array_(defaultLegend?.values as { color: string }[])
        .map((info) => info?.color)
        .reverse();
      if (levelLen === 1)
        return {
          ...defaultLegend,
          id,
          values: [{ min, max, color: colorList[0] }],
        };
      if (levelLen === 2)
        return {
          ...defaultLegend,
          id,
          values: [
            { max, color: colorList[0] },
            { min: max, color: colorList[1] },
          ].reverse(),
        };
      const diff = (number_(max) - number_(min)) / (levelLen - 2);
      const list = [...Array(levelLen).keys()].map((lv) => {
        // 部分污染物存在等级名称， 需要提取污染物
        const { lv: levelName = '' } = array_(
          defaultLegend?.values,
        )?.reverse()?.[lv];
        // min数据只有最大值
        if (lv === 0)
          return {
            lv: levelName,
            max: number_fixed(min, 2),
            color: colorList[lv],
          };
        else if (lv === levelLen - 2)
          return {
            lv: levelName,
            min: number_fixed((lv - 1) * diff + min, 2),
            max: max,
            color: colorList[lv],
          };
        else if (lv !== levelLen - 1)
          return {
            lv: levelName,
            min: number_fixed((lv - 1) * diff + min, 2),
            max: number_fixed(lv * diff + min, 2),
            color: colorList[lv],
          };
        // max数据只有最小值
        return { lv: levelName, min: max, color: colorList[lv] };
      });
      return { ...defaultLegend, id, values: list.reverse() };
    }
  }, [
    defaultAbsMax,
    defaultLegend,
    isDiffLayer,
    isDiffPercentageLayer,
    pollution,
    pollutionInfo,
  ]);

  const decoder = useMemo(() => {
    return replaceDecoder(pollution, legendInfo?.values);
  }, [legendInfo, pollution]);

  const colorRamp = useMemo(() => {
    const dict: Record<string, any> = {
      fnr: {
        '1.00': 'rgb(212, 57 ,48)',
        0.66: 'rgb(251, 210, 112)',
        0.5: 'rgb(254, 254, 200)',
        0.33: 'rgb(15,106,189)',
        0.0: 'rgb(11,56, 164)',
      },
      hid: {
        '1.00': '#ff1402',
        0.667: '#ff7805',
        0.333: '#ffd270',
        0.0: '#fade86',
      },
      o3mca: {
        '0.800': '#d43931',
        '0.667': '#ffd270',
        '0.333': '#2662d6',
        '0.000': '#2662d6',
      },
    };
    if (Object.keys(dict).includes(String(pollution).toLowerCase())) {
      return dict[pollution];
    }
    return getColorRamps(legendInfo?.values);
  }, [legendInfo?.values, pollution]);

  const queryClient = useQueryClient();

  const handleSetLegend = useMutation(
    (info: { max: number; min: number; pollutionType: string; type: 1 | 2 }) =>
      postRibbonUpdate(info),
    {
      onSuccess() {
        message.success('设定成功');
        setTimeout(() => queryClient.invalidateQueries(['getRibbonList']), 500);
      },
    },
  );

  const handleDelLegend = useMutation(
    (id: number) => {
      if (id !== undefined) return fetchRibbonDel(id);
      return Promise.reject();
    },
    {
      onSuccess() {
        message.success('重置成功');
        setTimeout(() => queryClient.invalidateQueries(['getRibbonList']), 500);
      },
    },
  );

  return {
    legendInfo,
    decoder,
    colorRamp,
    handleSetLegend,
    defaultLegend,
    handleDelLegend,
    pollutionInfo,
  };
}
