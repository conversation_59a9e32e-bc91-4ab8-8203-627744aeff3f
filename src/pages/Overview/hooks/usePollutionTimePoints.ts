import { fetchTextureTimePoints } from '@/services/global';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { aggAtom, datePickerValAtom, fetchTextureWeatherTypeAtom, pollutionLayersVisibleAtom, remotePoTypeAtom, TexturePoint } from '../atoms';
import { TimelineListItem } from '../components/Timeline/TimelineItem';

export default function usePollutionTimePoints() {
  const date = useAtomValue(datePickerValAtom);
  const pollutionLayersVisible = useAtomValue(pollutionLayersVisibleAtom);
  const remoteType = useAtomValue(remotePoTypeAtom);
  const weatherType = useAtomValue(fetchTextureWeatherTypeAtom);
  const agg = useAtomValue(aggAtom);

  const params = useMemo(
    () => ({
      type: (weatherType || remoteType).toUpperCase(),
      // type: 'DMASK',
      startDate: (date && date[0]) || '',
      endDate: (date && date[1]) || '',
      agg: agg,
    }),
    [agg, date, remoteType, weatherType],
  );

  const queryKeys = useMemo(
    () => ['texture-list', remoteType, weatherType, date ? date[0] : null, date ? date[1] : null],
    [date, remoteType, weatherType],
  );

  const { data } = useQuery<TexturePoint[]>(queryKeys, () => fetchTextureTimePoints(params), {
    enabled: Boolean(date && date[0] && date[1] && (pollutionLayersVisible.satiRemote || weatherType)),
  });

  const timePoints: TimelineListItem[] = useMemo(() => {
    return (data || []).map((item) => {
      const { name, timePoints } = item;
      return {
        id: name + '-' + timePoints,
        label: timePoints,
        detailLabel: timePoints,
        value: timePoints,
        haveDust: false,
        isStartPoint: false,
      };
    });
  }, [data]);

  return timePoints;
}
