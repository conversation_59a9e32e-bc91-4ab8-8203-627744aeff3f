import { regionAtom } from '@/atoms';
import { getDustRecordTimePoint } from '@/components/DustDetail/services';
import { ensureArray } from '@/utils/type';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { activeTimelineItemAtom, dustTimePointsAtom, isSelectTheLastPointOfTimelineAtom } from '../atoms';
import { TimelineListItem } from '../components/Timeline/TimelineItem';

export const useTimePoints = () => {
  const regionCode = useAtomValue(regionAtom);
  const activeTimelineItem = useAtomValue(activeTimelineItemAtom);
  const setIsSelectTheLastPointOfTimeline = useSetAtom(isSelectTheLastPointOfTimelineAtom);
  const setTimePoints = useSetAtom(dustTimePointsAtom);

  const { data } = useQuery(['/api/dust/record/time/point', regionCode], () => getDustRecordTimePoint({ regionCode }), {
    enabled: !!regionCode,
  });

  console.log('🚀 ~ const{data}=useQuery ~ data:', data, regionCode);
  useEffect(() => {
    const isSelectTheLast = Array.isArray(data) && data.length > 0 && data[data.length - 1].dataTime === activeTimelineItem?.value;

    setIsSelectTheLastPointOfTimeline(isSelectTheLast);
  }, [activeTimelineItem?.value, data, setIsSelectTheLastPointOfTimeline]);

  const timePoints: TimelineListItem[] = useMemo(() => {
    return ensureArray(data).map((item) => {
      const { dataTime, count, sandSite } = item;
      return {
        id: `${dataTime}-${count}-${sandSite}`,
        label: dataTime,
        detailLabel: dataTime,
        value: dataTime,
        haveDust: count > 0,
        isStartPoint: sandSite, // 0：是，1：否
      };
    });
  }, [data]);

  useEffect(() => {
    setTimePoints(timePoints);
  }, [setTimePoints, timePoints]);

  return timePoints;
};
