import { DUST_EXTENT } from '@/configs';
import DynamicTextureLayer from '@/layers/dynamic-texture-layer';
import DynamicTileLayer from '@/layers/dynamic-texture-layer/dynamic-tile-layer';
import { getTileUrl } from '@/utils';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { useSetAtom } from 'jotai';
import { useMemo } from 'react';
import { loadingDColorTileAtom } from '../../atoms';
import { TimelineListItem } from '../../components/Timeline/TimelineItem';
import { tweenFrameCount } from '../../configs';
import { useTextureTokenWithType } from '../useTextureTokenWithType';

export const useDynamicTextureTileLayer = ({
  timePoints = [],
  frame = 0,
  visible,
}: {
  frame: number;
  timePoints: TimelineListItem[];
  visible: boolean;
}) => {
  const setLoadingTile = useSet<PERSON>tom(loadingDColorTile<PERSON>tom);
  const token = useTextureTokenWithType('DCOLOR');
  const fps = 2;

  const timeList = useMemo(() => {
    return timePoints.map((item) => item.value);
  }, [timePoints]);

  const data = useMemo(() => {
    return timeList
      .map((time) =>
        getTileUrl(
          {
            agg: 'none',
            time,
            token: token || '',
            type: 'DCOLOR',
          },
          true,
        ),
      )
      .join(',');
  }, [timeList, token]);

  const dynamicTextureTileLayer = useMemo(() => {
    if (!token || !visible) return;
    return new DynamicTileLayer({
      ...tileLayerBaseConfig,
      id: `dynamic-texture-tile-DCOLOR`,
      data,
      visible,
      extent: DUST_EXTENT,
      maxZoom: 3,
      minZoom: 3,
      tileSize: 256,
      colorFormat: 'RGBA',
      pickable: false,
      min: 0,
      max: 100,
      fps: fps,
      frame: frame,
      normalFrame: tweenFrameCount,
      onReady: () => {
        setLoadingTile(false);
      },
      onLoading: () => {
        setLoadingTile(true);
      },
      // 色带更改后，重新渲染图层
      shouldUpdate: (prevProps: any, nextProps: any) => {
        return (
          prevProps.colorRamp !== nextProps.colorRamp ||
          prevProps.dataUrl !== nextProps.dataUrl ||
          prevProps.visible !== nextProps.visible ||
          prevProps.min !== nextProps.min ||
          prevProps.max !== nextProps.max ||
          prevProps.filters !== nextProps.filters ||
          prevProps.filtersChannel !== nextProps.filtersChannel
        );
      },
      // @ts-ignore
      renderSubLayers: (props) => {
        const {
          bbox: { west, south, east, north },
          //   index: { x, y, z },
        } = props.tile;
        return props?.data
          ? new DynamicTextureLayer(props, {
              pickable: true,
              data: null,
              imageList: props.data,
              bounds: [west, south, east, north],
              // smooth: true,
            })
          : null;
      },
      updateTriggers: {
        renderSubLayers: [frame, fps],
      },
      //@ts-ignore
      getTileData: (tile) => {
        return tile.url.split(',');
      },
    });
  }, [data, frame, setLoadingTile, token, visible]);

  return { dynamicTextureTileLayer };
};
