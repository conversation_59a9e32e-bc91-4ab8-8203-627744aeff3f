import { TOKEN_KEY } from '@/configs';
import { useCreateTextureLayer } from '@/hooks';
import { fetchTextureTimePoints, fetchUserTextureMap } from '@/services/global';
import { getTextureUrl, getTileUrl, hourFormatter } from '@/utils';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import moment from 'moment';
import { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { useTextureToken } from '..';
import {
  activeTimelineItemAtom,
  aggAtom,
  datePickerValAtom,
  fetchTextureWeatherTypeAtom,
  pollutionLayersVisibleAtom,
  remotePoTypeAtom,
  textureLayerDataAtom,
  TexturePoint,
} from '../../atoms';
import useGetLegend from '../useGetLegend';

export const useTextureLayer = () => {
  const date = useAtomValue(datePickerValAtom);
  const setTextureData = useUpdateAtom(textureLayerDataAtom);
  const pollutionLayersVisible = useAtomValue(pollutionLayersVisibleAtom);
  const remoteType = useAtomValue(remotePoTypeAtom);
  const weatherType = useAtomValue(fetchTextureWeatherTypeAtom);
  const activeTimelineItem = useAtomValue(activeTimelineItemAtom);
  const datePickerVal = useAtomValue(datePickerValAtom);
  const token = useTextureToken();
  const neimengToken = localStorage.getItem(TOKEN_KEY);
  const { createTextureLayer } = useCreateTextureLayer();
  const type = useMemo(() => String(weatherType || remoteType).toLowerCase(), [remoteType, weatherType]);
  const { decoder: customDecoder, colorRamp: customColorRamp } = useGetLegend(String(type).toLocaleLowerCase(), 'remote');
  const agg = useAtomValue(aggAtom);

  const queryKeys = useMemo(
    () => ['texture-list', remoteType, weatherType, date ? date[0] : null, date ? date[1] : null],
    [date, remoteType, weatherType],
  );
  const params = useMemo(
    () => ({
      type: (weatherType || remoteType)?.toUpperCase(),
      startDate: (date && date[0]) || '',
      endDate: (date && date[1]) || '',
      agg: ['pm25', 'pm10'].includes(remoteType) ? 'none' : 'daily',
    }),
    [date, remoteType, weatherType],
  );

  const { data } = useQuery<TexturePoint[]>(queryKeys, () => fetchTextureTimePoints(params), {
    enabled: Boolean(date && date[0] && date[1] && (pollutionLayersVisible.satiRemote || weatherType)),
  });

  useEffect(() => {
    if (pollutionLayersVisible.satiRemote && data) {
      setTextureData(data);
    }
  }, [data, pollutionLayersVisible.satiRemote, setTextureData]);

  const { data: map } = useQuery(['user-texture-map'], fetchUserTextureMap, {});

  const layer = useMemo(() => {
    if (!map) {
      return null;
    }
    return createTextureLayer({
      id: `data-texture-layer-${weatherType || remoteType}`,
      dataUrl:
        agg === 'custom'
          ? getTextureUrl(
              {
                startDate: datePickerVal?.[0] || '',
                endDate: datePickerVal?.[1] || '',
                type: params.type,
                tileToken: token || '',
              },
              true,
            )
          : getTileUrl(
              {
                token: token || '',
                time: moment(activeTimelineItem?.value).format(hourFormatter),
                agg: agg,
                type: params.type,
              },
              true,
            ),
      decoder: customDecoder,
      colorRamp: customColorRamp,
      visible: Boolean(pollutionLayersVisible.satiRemote || weatherType),
    });
  }, [
    activeTimelineItem?.value,
    agg,
    createTextureLayer,
    customColorRamp,
    customDecoder,
    datePickerVal,
    map,
    params.type,
    pollutionLayersVisible.satiRemote,
    remoteType,
    token,
    weatherType,
  ]);

  return layer;
};
