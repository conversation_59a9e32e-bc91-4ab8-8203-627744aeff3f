import { request } from '@/utils';
import { GeoJsonLayer } from 'deck.gl';
import { useSetAtom } from 'jotai';
import { isNil } from 'lodash';
import { stringify } from 'qs';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { hoverSandForecastDataAtom } from '../../atoms';

type TDustRecordRegionInfoModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 沙尘事件Id 例：1 */
  eventId: number;
  /** 数据时间  */
  dataTime: string;
  /** 行政区编码 例：110000 */
  regionCode: number;
  /** 行政区名称 例：北京 */
  region: string;
  /** 沙尘影响面积 例：10 */
  area: number;
};

type TDustRecordInfoModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 沙尘事件ID 例：1 */
  eventID: number;
  /** 是否为起沙地，0：是，1：否 例：1 */
  sendSite: number;
  /** 沙团矢量  */
  geom: string;
  /** 沙团面积 例：10 */
  area: number;
  /** 沙团影响行政区信息  */
  regions: TDustRecordRegionInfoModel[];
  /**   */
  warningModel: TDustWarningModel;
};

type TDustWarningModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 角度信息  */
  direction: number;
  /** 影响行政区  */
  regions: TKeyValuePairModelIntegerString[];
  /**   */
  warnTime: string;
};

type TKeyValuePairModelIntegerString = {
  /** 名称  */
  key: number;
  /** 值 例：value */
  value: string;
};

/** 获取当前时间点的所有沙团信息 */
type TGetDustRecordInfosParams = {
  /** 行政区划代码  */
  regionCode?: number;
  /** 沙尘事件ID  */
  eventId?: number;
  /** 数据时间 yyyy/MM/dd HH:mm:ss  */
  dataTime: string;
};

/** 获取当前时间点的所有沙团信息 */
export const getDustRecordInfosWithoutEventId = (params: TGetDustRecordInfosParams) => {
  return request(`/api/dust/record/infos?${stringify(params)}`) as Promise<TDustRecordInfoModel[]>;
};

type Params = {
  isDustForecastLayerVisible: boolean;
  activeTimePoint: string | undefined;
  regionCode: number | undefined;
  isSelectTheLastPointOfTimeline: boolean;
};

const mock_geojson = {
  type: 'Feature',
  geometry: {
    type: 'Polygon',
    coordinates: [
      [
        [112, 44],
        [115, 44],
        [115, 41],
        [112, 41],
        [112, 44],
      ],
    ],
  },
  properties: {},
};

export default function useDustForecastLayer({ activeTimePoint, isDustForecastLayerVisible, regionCode, isSelectTheLastPointOfTimeline }: Params) {
  const setHoveredSandForecastData = useSetAtom(hoverSandForecastDataAtom);

  const { data } = useQuery(
    ['/api/dust/record/infos', activeTimePoint],
    () =>
      getDustRecordInfosWithoutEventId({
        dataTime: activeTimePoint!,
        regionCode: regionCode === 0 ? 100000 : regionCode,
      }),
    {
      enabled: !!activeTimePoint && !isNil(regionCode),
      keepPreviousData: true,
    },
  );

  const dustGeojson = useMemo(() => {
    try {
      const formattedData = data?.map((item) => {
        return {
          geometry: JSON.parse(item?.warningModel?.geom),
          properties: item,
          type: 'Feature',
        };
      });
      // return (formattedData?.length ?? 0) > 0 ? formattedData : [mock_geojson];
      return formattedData;
    } catch (e) {
      return [];
    }
  }, [data]);

  const dustForecastLayer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'dust-forecast-geojson-layer',
      lineWidthUnits: 'pixels',
      pickable: true,
      stroked: true,
      getLineWidth: 3,
      lineWidthMinPixels: 3,
      // @ts-ignore
      data: dustGeojson || [],
      getLineColor: [127, 255, 13, 255],

      filled: true,
      getFillColor: () => [127, 255, 13, 102],
      updateTriggers: {
        onHover: [isSelectTheLastPointOfTimeline],
      },
      visible: isDustForecastLayerVisible,
      onHover: (info: any) => {
        // 只有选择时间轴上最新的时间的时候才触发
        if (info && info.object && isSelectTheLastPointOfTimeline) {
          setHoveredSandForecastData({
            position: [info.x, info.y],
            properties: info.object.properties,
          });
        } else {
          setHoveredSandForecastData(undefined);
        }
      },
    });
  }, [dustGeojson, isDustForecastLayerVisible, isSelectTheLastPointOfTimeline, setHoveredSandForecastData]);

  return dustForecastLayer;
}
