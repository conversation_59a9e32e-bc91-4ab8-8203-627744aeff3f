import { polygons } from '@turf/turf';
import { GeoJsonLayer } from 'deck.gl';
import { useMemo } from 'react';
// @ts-ignore
import useTextureToken from '@/hooks/dust/useTextureToken';
import { ParticleLayer } from '@/layers/ParticleLayer';
import { MaskExtension } from '@deck.gl/extensions';
import moment from 'moment';

type Params = {
  isWindLayerVisible: boolean;
  activeTimePoint: string | undefined;
};

export default function ({ isWindLayerVisible, activeTimePoint }: Params) {
  const agg = 'none';
  const token = useTextureToken();

  const windyLayer = useMemo(() => {
    if (!activeTimePoint || !isWindLayerVisible) {
      return [];
    }
    const config = {
      rotate: false,
      particle: {
        numParticles: 2000,
        maxAge: 10,
        speedFactor: 25,
        color: [255, 255, 255],
        width: 2,
        opacity: 0.6,
        animate: true,
      },
    };

    const image = `http://tile.openrj.cn/svc/tile/texture/windy?agg=${agg}&time=${moment(activeTimePoint).format('YYYY/MM/DD HH:00')}&token=${token}`;
    const imageUnscale = [-100, 100];
    const bounds = [60, 16, 144, 64];
    const maskGeojson = polygons([
      [
        [
          [71, 16],
          [71, 59],
          [140, 59],
          [140, 16],
          [71, 16],
        ],
      ],
    ]);
    return [
      new GeoJsonLayer({
        id: 'windy-mask-layer',
        data: maskGeojson,
        operation: 'mask',
      }),
      new ParticleLayer({
        id: 'particle-' + agg + '-' + moment(activeTimePoint).format('YYYY/MM/DD HH:00'),
        image,
        imageUnscale,
        colorRamp: {
          // 新版本 最小值使用 1700
          '1.000': 'rgb(127, 33, 63)',
          '0.857': 'rgb(213, 11, 85)',
          '0.714': 'rgb(253, 182 ,90)',
          '0.571': 'rgb(253, 240, 163)',
          '0.429': 'rgb(249, 252, 180)',
          '0.286': 'rgb(90, 220, 204)',
          '0.143': 'rgb(25,158,191)',
          '0.000': 'rgb(33,139, 168)',
        },
        min: 0,
        max: 10,
        bounds,
        // style properties
        numParticles: config.particle.numParticles,
        maxAge: config.particle.maxAge,
        speedFactor: config.particle.speedFactor,
        color: config.particle.color,
        width: config.particle.width,
        opacity: config.particle.opacity,
        animate: config.particle.animate,
        maskId: 'windy-mask-layer',
        extensions: [new MaskExtension()],
        visible: isWindLayerVisible,
        windyHeight: 10000,
        getPolygonOffset: () => [0, -1000], // layerIndex is per view
      }),
    ];
  }, [activeTimePoint, token, isWindLayerVisible]);

  return windyLayer;
}
