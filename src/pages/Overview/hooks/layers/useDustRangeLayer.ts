import { TranslatedPathLayer } from '@/layers/dust/TranslatedPathLayer';
import { request } from '@/utils';
import { GeoJsonLayer } from 'deck.gl';
import { Position } from 'deck.gl/typed';
import { useSetAtom } from 'jotai';
import { stringify } from 'qs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { hoveredSandRangeDataAtom } from '../../atoms';

type TDustRecordRegionInfoModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 沙尘事件Id 例：1 */
  eventId: number;
  /** 数据时间  */
  dataTime: string;
  /** 行政区编码 例：110000 */
  regionCode: number;
  /** 行政区名称 例：北京 */
  region: string;
  /** 沙尘影响面积 例：10 */
  area: number;
};

type TDustRecordInfoModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 沙尘事件ID 例：1 */
  eventID: number;
  /** 是否为起沙地，0：是，1：否 例：1 */
  sendSite: number;
  /** 沙团矢量  */
  geom: string;
  /** 沙团面积 例：10 */
  area: number;
  /** 沙团影响行政区信息  */
  regions: TDustRecordRegionInfoModel[];
  /**   */
  warningModel: TDustWarningModel;
};

type TDustWarningModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 角度信息  */
  direction: number;
  /** 影响行政区  */
  regions: TKeyValuePairModelIntegerString[];
  /**   */
  warnTime: string;
};

type TKeyValuePairModelIntegerString = {
  /** 名称  */
  key: number;
  /** 值 例：value */
  value: string;
};

/** 获取当前时间点的所有沙团信息 */
type TGetDustRecordInfosParams = {
  /** 行政区划代码  */
  regionCode?: number;
  /** 沙尘事件ID  */
  eventId?: number;
  /** 数据时间 yyyy/MM/dd HH:mm:ss  */
  dataTime: string;
};

/** 获取当前时间点的所有沙团信息 */
export const getDustRecordInfos = (params: TGetDustRecordInfosParams) => {
  return request(`/api/dust/record/infos?${stringify(params)}`) as Promise<TDustRecordInfoModel[]>;
};

type Params = {
  isDustRangeLayerVisible: boolean;
  activeTimePoint: string | undefined;
  regionCode: number | undefined;
  isSelectTheLastPointOfTimeline: boolean;
  timeList: string[];
  setRangeLoading: (v: boolean) => void;
};

export default function useDustRangeLayer({
  activeTimePoint,
  isDustRangeLayerVisible,
  regionCode,
  isSelectTheLastPointOfTimeline,
  timeList,
  setRangeLoading,
}: Params) {
  const [pointData, setPointData] = useState<Record<string, TDustRecordInfoModel[]>>({});
  const setHoveredSandRangeData = useSetAtom(hoveredSandRangeDataAtom);
  const { mutate } = useMutation((dataTime: string) =>
    getDustRecordInfos({
      dataTime,
      regionCode: regionCode,
    }),
  );
  const loadMore = useCallback(
    async (frame, loadList) => {
      if (loadList[frame]) {
        const d = await new Promise((resolve) => {
          mutate(loadList[frame], {
            onSuccess: (d) => {
              resolve(d);
            },
          });
        });
        // setPointData((prev) => ({ ...prev, [loadList[frame].date]: d }));
        const next = await loadMore(frame + 1, loadList);
        return {
          [loadList[frame]]: d,
          ...next,
        };
      } else {
        return {};
      }
    },
    [mutate],
  );

  useEffect(() => {
    if (!!regionCode && isDustRangeLayerVisible && activeTimePoint) {
      let hasPoint = false;
      for (let key in pointData) {
        if (activeTimePoint === key) {
          hasPoint = true;
          break;
        }
      }
      const frame = timeList.findIndex((t) => t === activeTimePoint);
      const loadList = timeList.concat().slice(frame, timeList.length).concat(timeList.concat().slice(0, frame));
      if (hasPoint) {
        const loadingPoint = loadList.find((t) => !pointData[t]);
        if (loadingPoint) {
          mutate(loadingPoint, {
            onSuccess: (d) => {
              setPointData((prev) => ({ ...prev, [loadingPoint]: d }));
            },
          });
        }
      } else {
        setRangeLoading(true);
        loadMore(0, loadList.length > 10 ? loadList.concat().slice(0, 10) : loadList).then((d) => {
          setRangeLoading(false);
          setPointData((prev) => ({ ...prev, ...d }));
        });
      }
    }
  }, [activeTimePoint, isDustRangeLayerVisible, loadMore, mutate, pointData, regionCode, setRangeLoading, timeList]);
  // const { data } = useQuery(
  //   ['/api/dust/record/infos', activeTimePoint, regionCode],
  //   () =>
  //     getDustRecordInfos({
  //       dataTime: activeTimePoint!,
  //       regionCode: regionCode,
  //     }),
  //   {
  //     enabled: !!activeTimePoint && !!regionCode,
  //     keepPreviousData: true,
  //   }
  // );
  const dustGeojson = useMemo(() => {
    const formattedData =
      activeTimePoint &&
      (pointData?.[activeTimePoint] ?? []).map((item) => {
        return {
          geometry: JSON.parse(item.geom),
          properties: item,
          type: 'Feature',
        };
      });
    return formattedData;
  }, [activeTimePoint, pointData]);

  // 使用PathLayer实现，需要对geojson数据进行简单格式化
  const pathData = useMemo(() => {
    return dustGeojson?.map((item) => {
      return {
        path: item.geometry.coordinates[0],
      };
    });
  }, [dustGeojson]);

  const dustRangeLayer = useMemo(() => {
    return new GeoJsonLayer({
      id: 'dust-range-geojson-layer',
      lineWidthUnits: 'pixels',
      pickable: true,
      stroked: true,
      getLineWidth: 2,
      lineWidthMinPixels: 2,
      // @ts-ignore
      data: dustGeojson || [],
      getLineColor: [255, 240, 13, 255],

      filled: isSelectTheLastPointOfTimeline,
      getFillColor: () => [0, 0, 0, 0],
      updateTriggers: {
        onHover: [isSelectTheLastPointOfTimeline],
        filled: [isSelectTheLastPointOfTimeline],
      },
      visible: isDustRangeLayerVisible,
      onHover: (info: any) => {
        // 只有选择时间轴上最新的时间的时候才触发
        if (info && info.object && isSelectTheLastPointOfTimeline) {
          setHoveredSandRangeData({
            position: [info.x, info.y],
            properties: info.object.properties,
          });
        } else {
          setHoveredSandRangeData(undefined);
        }
      },
    });
  }, [dustGeojson, isDustRangeLayerVisible, isSelectTheLastPointOfTimeline, setHoveredSandRangeData]);

  const dustRangeShadowLayer = useMemo(() => {
    return new TranslatedPathLayer<{
      path: Position[];
    }>({
      id: 'dust-range-geojson-shadow-layer',
      data: pathData,
      getPath: (d) => d.path,
      getWidth: 4,
      widthMinPixels: 4,
      getColor: [0, 0, 0],
      opacity: 0.3,
      jointRounded: true,
      offsetY: -0.01,
      visible: isDustRangeLayerVisible,
    });
  }, [isDustRangeLayerVisible, pathData]);

  return [dustRangeShadowLayer, dustRangeLayer];
}
