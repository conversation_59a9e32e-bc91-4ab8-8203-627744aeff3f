import { Dropdown, Menu } from 'antd';
import { Avatar as AvatarUi, StyledAntdMenu } from './ui';
import avatarImg from '@/assets/images/avatar.jpg';
import { useSpring } from 'react-spring';
import { useAtomValue } from 'jotai';
import { fullScreenAtom } from '@/atoms';
import { useState } from 'react';
import UpdatePasswordModal from '@/components/global/UpdatePasswordModal';
import { useLogOut } from '@/hooks';

const Avatar: React.FC = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [visible, setVisible] = useState(false);

  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    top: isFullScreen ? 0 : 40,
  });

  const logout = useLogOut();

  const menu = (
    <StyledAntdMenu
      style={{ width: 160 }}
      onClick={({ key }) => {
        if (key === '1') {
          setVisible(true);
        }
        if (key === '2') {
          logout();
        }
      }}
    >
      <Menu.Item key="1">修改密码</Menu.Item>
      <Menu.Item key="2" danger>
        退出登录
      </Menu.Item>
    </StyledAntdMenu>
  );

  return (
    <>
      <Dropdown
        overlay={menu}
        placement="bottomRight"
        trigger={['click']}
        overlayClassName="avatar-overlay"
      >
        <AvatarUi
          style={{ ...styles, pointerEvents: isFullScreen ? 'none' : 'auto' }}
        >
          <img src={avatarImg} alt="用户头像" />
        </AvatarUi>
      </Dropdown>
      <UpdatePasswordModal
        visible={visible}
        logout={logout}
        onCancel={() => setVisible(false)}
      />
    </>
  );
};

export default Avatar;
