import React from 'react';
import moment from 'moment';
import { Tooltip } from 'antd';
import { Dail, DayLabe<PERSON>, HourLabel } from './ui';

const TimelineDial: React.FC<{
  dialWidth: number;
  item: any;
  handleClick: () => void;
  shouldRenderDayLabel: boolean;
  shouldRenderHourLabel: boolean;
}> = ({
  dialWidth,
  item,
  handleClick,
  shouldRenderDayLabel,
  shouldRenderHourLabel,
}) => {
  return (
    <Tooltip
      title={`${item.dateTime}`}
      placement="top"
      mouseEnterDelay={0.05}
      mouseLeaveDelay={0}
      key={item.index}
    >
      <Dail
        style={{ flex: `0 0 ${dialWidth}px` }}
        data-index={item.index}
        shouldRenderDayLabel={shouldRenderDayLabel}
        shouldRenderHourLabel={shouldRenderHourLabel}
        onClick={handleClick}
      >
        {shouldRenderDayLabel && (
          <DayLabel>{moment(item.dateTime).format('DD日')}</DayLabel>
        )}
        {shouldRenderHourLabel && (
          <HourLabel>{moment(item.dateTime).format('HH')}时</HourLabel>
        )}
      </Dail>
    </Tooltip>
  );
};

export default TimelineDial;
