import { Center, ellipsisCss, Flex, FlexCol, HorCenter, scrollBarStyles, TriangleIcon } from '@/components/ui';
import { getColorFromTheme } from '@/components/ui/utils';
import { rankColors } from '@/utils';
import { Cascader, Menu } from 'antd';
import { animated } from 'react-spring';
import styled, { createGlobalStyle, css } from 'styled-components';

/**
 * 空气质量分级颜色
 */
// const areaQualityColors = [
//   '#7E0023',
//   '#940030',
//   '#99014D',
//   '#B5004A',
//   '#FF0000',
//   '#FF7E00',
//   '#FF9E02',
//   '#FEFF00',
//   '#D0FF00',
//   '#00B401',
// ];

/**
 * 主容器
 */
export const Main = styled.main`
  height: 100vh;
`;

/**
 * 基础面板
 */
export const Bcard = styled(animated.div)`
  background: ${getColorFromTheme('gray.900')};
  border-radius: 6px;
  position: absolute;
  z-index: 1000;
`;

/**
 * 头像
 */
export const Avatar = styled(Bcard)`
  width: 64px;
  height: 64px;
  border-radius: 32px;
  top: 40px;
  right: 40px;
  overflow: hidden;
  border: 2px solid #616473;
  cursor: pointer;

  img {
    display: block;
    width: 100%;
    height: auto;
  }
`;

/**
 * 搜索容器
 */
export const SearchContainer = styled(Bcard)`
  width: 480px;
  top: 40px;
  right: 124px;
  background: ${getColorFromTheme('gray.800')};
  color: white;
`;

/**
 * 放大、缩小等视图容器
 */
export const ZoomIcon = styled.img`
  width: 20px;
  height: 20px;
  margin-left: 8px;
  cursor: pointer;
`;

/**
 * 指南针容器
 */
export const CompassContainer = styled(Bcard)`
  right: 42px;
  bottom: 400px;
  width: 60px;
  height: 60px;
  background: transparent;
  cursor: pointer;

  img {
    width: 100%;
  }
`;

/**
 *
 */
export const IconBtn = styled(Center)<{
  active?: boolean;
}>`
  user-select: none;
  cursor: pointer;

  color: ${(props) => (props.active ? props.theme.colors.primary : getColorFromTheme('gray.500'))};

  &:hover {
    color: ${(props) => (props.active ? props.theme.colors.primary : getColorFromTheme('white'))};
  }

  .icomoon {
    font-size: 20px;

    &.icon-zoomin,
    &.icon-zoomout {
      font-size: 16px;
    }
  }
`;

/**
 * 首页按钮容器
 */
export const BaseContainer = styled(Bcard)`
  padding: 0 4px;
  border-radius: 4px;

  ${IconBtn} {
    width: 32px;
    height: 40px;

    &:last-child {
      border-top: 1px solid ${getColorFromTheme('gray.800')};
    }
  }
`;

/**
 * 视图缩放按钮容器
 */
export const ZoomContainer = styled(BaseContainer)`
  bottom: 164px;
  right: 52px;

  ${IconBtn} {
    &:nth-child(2) {
      border-top: 1px solid ${getColorFromTheme('gray.800')};
    }
  }
`;

export const ScreenContainer = styled(BaseContainer)`
  top: 144px;
  right: 52px;
`;

// 退出全屏按钮
export const QuitFullScreenBtn = styled(BaseContainer)`
  top: 40px;
  right: 40px;
  width: 140px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: ${getColorFromTheme('gray.400')};
  cursor: pointer;

  &:hover {
    color: white;
  }
`;

export const GridContainer = styled(BaseContainer)`
  bottom: 254px;
  right: 52px;
`;

/**
 * 测量距离、面积按钮容器
 */
export const MeasureContainer = styled(BaseContainer)`
  bottom: 304px;
  right: 52px;
`;

/**
 * 下方主控
 */
export const MCtrlContainer = styled(Bcard)`
  display: flex;
  align-items: center;
  left: 20px;
  right: 20px;
  bottom: 12px;
  height: 94px;
  padding-left: 18px;
  padding-right: 20px;
  color: #c1c1c4;

  .label {
    color: ${getColorFromTheme('gray.400')};
    font-size: 12px;
  }
`;

export const Label = styled.div`
  font-size: 12px;
  color: ${getColorFromTheme('gray.400')};
`;

export const LayerControl = styled.div`
  margin-left: 24px;
`;

/**
 * 图层控制按钮容器
 */
export const LyBtnContainer = styled(Flex)`
  margin-top: 8px;
`;

/**
 * 图层按钮
 */
export const LayerBtn = styled(Center)<{ active?: boolean }>`
  position: relative;
  width: 36px;
  height: 36px;
  margin-right: 8px;
  font-size: 20px;
  border-radius: 4px;
  color: ${(props) => {
    return props.active ? 'white' : props.theme.colors.gray['400'];
  }};
  background-color: ${(props) => {
    return props.active ? props.theme.colors.primary : props.theme.colors.gray['800'];
  }};
  cursor: pointer;

  &:last-child {
    margin-right: 0;
  }
`;

/**
 * 气象图层菜单容器
 */
export const MenuContainer = styled(animated.div)`
  position: absolute;
  bottom: 110px;
  left: 50%;
  font-size: 12px;
  border-radius: 5px;
  background: ${getColorFromTheme('gray.25262d')};
  transform: translateX(-50%);
  z-index: 1500;

  &::after {
    position: absolute;
    bottom: -8px;
    left: 50%;
    z-index: 1;
    display: block;
    width: 0;
    height: 0;
    border-top: 20px solid ${getColorFromTheme('gray.25262d')};
    border-right: 14px solid transparent;
    border-left: 14px solid transparent;
    transform: translateX(-50%);
    content: '';
  }

  ${Center} {
    padding: 10px 0;
    color: ${getColorFromTheme('gray.c1c1c4')};
    background: ${getColorFromTheme('gray.1c1d24')};
    border-radius: 5px 5px 0 0;

    .icomoon {
      margin-right: 4px;
    }
  }

  ul {
    padding: 0 8px;

    li {
      border-radius: 1px;
    }
  }
`;

export const LayerMenu = styled.div`
  padding: 0 8px;
`;

export const LayerMenuItem = styled.div<{ active?: boolean }>`
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 120px;
  height: 30px;
  margin: 8px 0;
  font-size: 12px;
  line-height: 20px;
  color: ${(props) => (props.active ? props.theme.colors.white : props.theme.colors.gray.c1c1c4)};
  background: ${(props) => (props.active ? props.theme.colors.primary : 'transparent')};
  border-radius: 3px;

  .label {
    flex: 0 0 64px;
    margin-right: 6px;
    color: white;
  }
`;

/**
 * 底部信息子项
 */
export const InfoItem = styled(Flex)`
  position: relative;
  align-items: center;
  height: 20px;
  line-height: 20px;
  color: ${getColorFromTheme('gray.400')};
  background: ${getColorFromTheme('gray.900')};
  border-radius: 3px;
  cursor: pointer;
  user-select: none;
`;

export const ItemHead = styled(Center)<{ size?: number }>`
  width: ${(props) => (props.size ? `${props.size}px` : null)};
  height: ${(props) => (props.size ? `${props.size}px` : null)};
  padding: ${(props) => (props.size ? null : '0 6px')};
  font-size: 12px;
  color: white;
  background: ${getColorFromTheme('primary')};
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;

  img {
    width: 12px;
    height: 12px;
  }
`;

export const ItemText = styled.div`
  flex: 1;
  padding: 0 10px;
  font-size: 12px;
`;

/**
 * PM2.5、AQI颜色色块容器
 */
export const RectContainer = styled(Flex)`
  padding: 0 4px;

  span {
    padding-right: 4px;
    font-size: 12px;
  }
`;

/**
 * 色块
 */
export const Rects = styled(Flex)`
  padding-top: 5px;

  div {
    width: 2px;
    height: 10px;
    margin-left: 2px;

    /* ${rankColors.map(
      (color, index) => css`
        &:nth-child(${index + 1}) {
          background: ${color};
        }
      `,
    )} */
  }
`;

/**
 * 底部详细数据
 */
export const Info = styled(Bcard)`
  display: flex;
  align-items: flex-end;
  left: 20px;
  right: 20px;
  bottom: 118px;
  background: transparent;
  z-index: 999;

  ${InfoItem} {
    &:last-child {
      margin-right: 0;
    }
  }
`;

/**
 * 比例尺
 */
export const Scale = styled(InfoItem)`
  position: absolute;
  bottom: 118px;
  right: 49px;
  margin-right: 0;

  justify-content: center;
  width: 100px;
  padding: 0 8px;

  div {
  }
`;

// @ts-ignore
export const StyledCascader = styled(Cascader)`
  width: 160px;
  flex-shrink: 0;
  color: white;

  .ant-cascader-menu-item {
    color: white !important;
  }

  .ant-cascader-menu-item-active:not(#index .ant-cascader-menu-item-disabled) {
    color: white !important;
  }

  &,
  .ant-cascader-input,
  .ant-cascader-picker-label {
    font-size: 16px;
  }
`;

/**
 * 时间控件容器
 */
export const PickerContainer = styled.div`
  flex-shrink: 0;
  height: 94px;
  margin-left: 20px;
  padding-left: 20px;
  padding-top: 14px;
  border-left: 1px solid ${getColorFromTheme('gray.31333d')};
`;

/**
 * 时间控件
 */
export const DatePickerContainer = styled(Center)`
  height: 36px;
  margin-top: 8px;
  padding-left: 8px;
  background: ${getColorFromTheme('gray.800')};
  border-radius: 4px;

  .icomoon {
    color: ${getColorFromTheme('gray.b5b5b8')};
  }
`;

export const TypeSelect = styled(FlexCol)`
  margin-left: 30px;
  margin-right: 14px;
  justify-content: center;
  align-items: center;

  h1 {
    margin: 0;
    font-size: 20px;
  }

  .icomoon {
    color: ${getColorFromTheme('gray.c1c1c4')};
    font-size: 4px;
    transform: scale(0.45);
    cursor: pointer;
  }
`;

export const TimelineControl = styled(Center)<{
  active?: boolean;
  disabled?: boolean;
}>`
  flex-shrink: 0;
  flex-direction: column;
  width: 36px;
  height: 54px;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;

  ${(props) =>
    props.disabled &&
    css`
      opacity: 0.7;
      pointer-events: none;
    `}

  color: ${(props) => (props.active ? props.theme.colors.white : props.theme.colors.gray['400'])};
  background: ${(props) => (props.active ? props.theme.colors.primary : props.theme.colors.gray['800'])};

  .icomoon {
    font-size: 16px;
  }

  p {
    margin: 4px 0 0 !important;
    font-size: 12px;
  }
`;

export const TimelineContainer = styled(Flex)`
  flex: 1;
  align-items: center;

  ${TimelineControl} {
    &:last-child {
    }
  }
`;

export const TimelineBodyWrapper = styled(animated.div)`
  flex: 1;
  padding-top: 5px;
  padding-left: 3px;
  margin-left: 10px;
  overflow-x: auto;
  border-radius: 4px;

  ${scrollBarStyles};
`;

export const TimelineBody = styled(Flex)`
  width: 0px;
  position: relative;
  height: 54px;
  align-items: flex-end;
  border-radius: 4px;
  background: ${getColorFromTheme('gray.800')};
`;

export const Nonius = styled.div`
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  width: 1px;
  background: ${getColorFromTheme('primary')};

  &::after {
    position: absolute;
    top: -5px;
    left: -2px;
    width: 5px;
    height: 5px;
    background: ${getColorFromTheme('primary')};
    border-radius: 3px;
    content: '';
  }
`;

export const Dail = styled.div<{
  percentage?: string;
  shouldRenderDayLabel: boolean;
  shouldRenderHourLabel: boolean;
}>`
  position: relative;
  flex: 0 0 10px;
  height: 54px;
  background: ${getColorFromTheme('gray.800')};

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &::after {
    position: absolute;
    top: ${(props) => (props.shouldRenderDayLabel ? '16px' : '22px')};
    right: 0;
    display: block;
    width: 1px;
    height: ${(props) => (props.shouldRenderDayLabel ? '14px' : '8px')};
    background: ${getColorFromTheme('gray.69696d')};
    content: '';
  }
`;

const TimelineLabel = styled.span`
  position: absolute;
  right: 0;
  z-index: 10;
  display: block;
  font-size: 14px;
  word-break: keep-all;
  color: ${getColorFromTheme('c1c1c4')};
  user-select: none;
`;

export const DayLabel = styled(TimelineLabel)`
  bottom: 24px;
  transform: translate(50%, 110%);
`;

export const HourLabel = styled(TimelineLabel)`
  top: 24px;
  transform: translate(50%, -110%);
`;
export const PollutionContainer = styled(Bcard)`
  display: flex;
  flex-direction: column;
  width: 480px;
  height: 500px;
  padding: 16px 20px;
  top: 114px;
  right: 124px;
  color: #c1c1c4;
  background: ${getColorFromTheme('gray.25262d')};
`;

export const ResultDesc = styled(HorCenter)`
  font-size: 14px;
  color: ${getColorFromTheme('gray.500')};

  span {
    padding: 0 4px;
    color: white;
  }

  .icomoon {
    font-size: 18px;
    cursor: pointer;
  }
`;

export const Index = styled.span`
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 14px;
  text-align: center;
  background: ${getColorFromTheme('primary')};
  color: white;
  border-radius: 2px;
`;

export const ContaminantContainer = styled(Bcard)`
  left: 100px;
  bottom: 154px;
  z-index: 5;
  width: 462px;
  padding: 0;
  background: ${getColorFromTheme('gray.25262d')};
  overflow: hidden;
  color: #c1c1c4;
`;

export const ContaminantTitle = styled(Center)`
  padding: 20px 20px 12px;
  justify-content: space-between;
  font-size: 14px;
  border-bottom: 1px solid ${getColorFromTheme('gray.1c1d24')};

  .icomoon {
    margin-right: 4px;
    font-size: 16px;

    &.icon-x {
      font-size: 12px;
      cursor: pointer;
    }
  }
`;

export const Switch = styled.div<{
  checked?: boolean;
  primaryBg?: string;
  shouldChangeBg?: boolean;
}>`
  position: relative;
  width: 20px;
  height: 8px;
  border-radius: 4px;

  background: ${({ checked, primaryBg, shouldChangeBg, theme }) => {
    if (checked) {
      return primaryBg && shouldChangeBg ? primaryBg : theme.colors.primary;
    } else {
      return theme.colors.gray['1c1d24'];
    }
  }};
  cursor: pointer;
  transition: background-color 0.2s;

  &::after {
    position: absolute;
    top: -1px;
    left: ${(props) => (props.checked ? '10px' : 0)};
    display: block;
    width: 10px;
    height: 10px;
    background: white;
    border-radius: 5px;
    transition: all 0.2s ease-in-out;
    content: '';
  }
`;

export const ContaminantContent = styled.div`
  background: ${getColorFromTheme('gray.1c1d24')};
`;

export const Accordion = styled.div``;

export const AccordionHead = styled(HorCenter)`
  padding: 6px 30px 6px 20px;
  font-size: 12px;

  ${HorCenter} {
    flex: 1;
    margin-right: 40px;
    color: ${getColorFromTheme('gray.c1c1c4')} !important;
    cursor: pointer;
    user-select: none;
  }

  ${TriangleIcon} {
    margin-right: 4px;
  }
`;

export const ItemsContainer = styled(Flex)`
  overflow-x: auto;
  ${scrollBarStyles};
  &::-webkit-scrollbar-thumb {
    height: 2px;
  }
  &::-webkit-scrollbar-thumb:window-inactive {
    width: 2px;
  }
`;

export const Item = styled.div<{ active?: boolean; bordered?: boolean; disabled?: boolean }>`
  font-size: 14px;
  color: ${(props) => (props.active ? props.theme.colors.primary : props.theme.colors.gray.c1c1c4)};
  cursor: pointer;
  flex-shrink: 0;
  user-select: none;

  ${(props) => {
    return props.bordered
      ? css`
          color: ${props.active ? props.theme.colors.white : props.theme.colors.gray.c1c1c4};
          padding-bottom: 4px;
          border-bottom: 2px solid ${props.active ? props.theme.colors.primary : 'tansparent'};
        `
      : null;
  }}

  ${(props) => {
    return props.disabled
      ? css`
          opacity: 0.5;
          cursor: not-allowed;
        `
      : null;
  }}
`;

export const AccordionBody = styled(animated.div)`
  background: ${getColorFromTheme('gray.1c1d24')};

  ${ItemsContainer} {
    padding: 10px 32px;

    ${Item} {
      margin-right: 14px;

      &:hover {
        color: ${getColorFromTheme('primary')};
      }
    }
  }
`;

export const DiaplayType = styled(Flex)`
  padding: 12px 30px;
  font-size: 12px;
  color: ${getColorFromTheme('gray.c1c1c4')};
  background: ${getColorFromTheme('gray.1c1d24')};
  cursor: pointer;

  span {
    flex: 1;
  }
`;

export const TypeItem = styled(HorCenter)<{ active?: boolean }>`
  margin-left: 20px;
  color: ${(props) => (props.active ? 'white' : props.theme.colors.gray.c1c1c4)};

  &:hover {
    color: white;
  }

  .icomoon {
    margin-right: 4px;
  }
`;

export const RemoteSensingContainer = styled.div`
  max-height: 220px;
  overflow-y: auto;

  ${scrollBarStyles}
`;

export const RemoteSensingList = styled.div`
  padding: 0 16px;
  border-top: 1px solid ${getColorFromTheme('gray.31333d')};
`;

export const StationList = styled(RemoteSensingList)``;

export const ProgressBar = styled.div<{
  percent: number;
}>`
  width: 60px;
  height: 6px;
  border-radius: 3px;
  background: ${getColorFromTheme('gray.31333d')};
  overflow: hidden;

  div {
    width: ${(props) => `${props.percent}%`};
    height: 6px;
    background: ${getColorFromTheme('primary')};
    border-radius: 3px;
  }

  & + span {
    font-size: 12px;
  }
`;

export const RemoteSensingInfo = styled.div`
  flex: 1;
  padding-left: 14px;

  h1 {
    margin: 0 0 8px 0;
  }

  ${Flex} {
    color: ${getColorFromTheme('gray.c1c1c4')};
    font-size: 14px;

    span {
      flex: 1;
    }

    ${HorCenter} {
      font-size: 14px;

      ${ProgressBar} {
        margin: 0 4px;
      }
    }

    .datetime,
    .qcf,
    .temperature {
      padding-right: 20px;
      color: #c1c1c4;
      font-size: 12px;

      span {
        padding-left: 10px;
        color: white;
      }
    }

    .temperature span {
      color: #f44336;
    }
  }
`;

export const RemoteSensing = styled(Flex)`
  padding: 12px 0;
  border-bottom: 1px solid ${getColorFromTheme('gray.31333d')};

  ${Index} {
    margin-top: 6px;
  }
`;

export const ChartContainer = styled.div`
  min-height: 180px;
  padding: 0 20px 20px;
`;

export const WarningContainer = styled(ContaminantContainer)`
  ${ItemsContainer} {
    border-bottom: 1px solid ${getColorFromTheme('gray.31333d')};
  }
`;
export const WarningTitle = styled(ContaminantTitle)`
  color: ${getColorFromTheme('gray.c1c1c4')};
`;
export const WarningContent = styled(ContaminantContent)``;

// 报警列表
export const WarningList = styled.div`
  max-height: 300px;
  padding: 0 10px;
  overflow-y: auto;

  ${scrollBarStyles};
`;

export const WarningListItem = styled.div<{
  active?: boolean;
}>`
  padding: 10px;
  border-bottom: 1px solid ${getColorFromTheme('gray.31333d')};
  color: white;
  border-radius: 4px;

  ${({ active }) => {
    return active
      ? css`
          background: #31333d;
        `
      : null;
  }}

  ${Index} {
    flex: 0 0 14px;
    margin-right: 10px;
  }

  span {
    flex: 1;
  }
`;
export const WnItemInfo = styled(HorCenter)`
  margin-top: 8px;
  padding-left: 8px;

  > span {
    flex: none;
    padding-left: 16px;
    color: #c1c1c4;

    span {
      color: white;
      font-size: 16px;
    }
  }
`;

export const WarningStatus = styled.div<{ status?: number }>`
  font-size: 12px;
  color: ${(props) => (props.status === 1 ? props.theme.colors.primary : props.theme.colors.danger)};
`;

export const AverageCycleLine = styled(HorCenter)`
  margin: 12px 0 0;
  padding: 4px 8px;
  justify-content: space-between;
  font-size: 12px;
  background: #31333d;
`;

export const WnPoSource = styled.div`
  padding-left: 16px;
  background: ${getColorFromTheme('gray.1c1d24')};

  h1 {
    padding: 8px 0;
    color: ${getColorFromTheme('c1c1c4')};
    font-size: 12px;
    border-bottom: 1px solid ${getColorFromTheme('gray.25262d')};
  }

  ul {
    margin-bottom: 0;

    li {
      display: flex;
      align-items: center;
      padding-bottom: 12px;
      color: white;
      font-size: 14px;
    }
  }
`;

export const WnCoordinate = styled(HorCenter)`
  padding: 12px;

  .icomoon {
    color: ${getColorFromTheme('gray.838485')};
  }
  span {
    color: ${getColorFromTheme('gray.c1c1c4')};
    line-height: 12px;

    &:nth-of-type(1) {
      margin-left: 4px;
    }
    &:nth-of-type(2) {
      margin-left: 10px;
      padding-left: 10px;
      border-left: 1px solid ${getColorFromTheme('gray.31333d')};
    }
  }
`;

// 黑色主题控件css
export const PageIndexStyle = createGlobalStyle`
.avatar-overlay {
border-top-color: #1f1f1f;
}
`;

export const MapContainer = styled.div`
  position: relative;
  overflow: hidden;
`;

export const OverlayContainer = styled.div`
  background: #2e2f36;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4);
`;

export const OverlayContent = styled.div`
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;

  ${scrollBarStyles};
`;
export const OverlayFooter = styled(Flex)`
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  justify-content: space-between;
  padding: 8px 12px;
`;

export const CheckboxContainer = styled.div`
  padding: 4px 0;
`;

export const LegendContainer = styled(animated.div)`
  position: fixed;
  top: 120px;
  bottom: 164px;
  right: 110px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  font-size: 12px;
  color: white;
`;

export const LegendPopover = styled(MenuContainer)<{
  width?: string;
}>`
  bottom: 32px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  align-items: center;

  ${Center} {
    flex-direction: column;
    min-width: ${({ width }) => width || '74px'};
    padding: 8px 12px;

    p {
      margin: 0;
      white-space: nowrap;
    }
  }
`;

export const LegendLump = styled.div`
  width: 10px;
  height: 24px;
  margin-right: 8px;
`;

export const Nums = styled(FlexCol)`
  justify-content: space-between;

  span:last-child {
    position: relative;
    top: 6px;
    line-height: 1;
  }

  .max {
  }
`;
export const LegendItems = styled.div`
  padding: 12px 16px 24px;

  & > ${Flex} {
    position: relative;
    align-items: flex-end;
    font-size: 12px;

    &:first-child {
      ${LegendLump} {
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
      }
    }

    &:last-child {
      ${LegendLump} {
        border-bottom-right-radius: 5px;
        border-bottom-left-radius: 5px;
      }
    }
  }
`;

export const StyledAntdMenu = styled(Menu)`
  &.ant-dropdown-menu {
    width: 160px;
    border-radius: 5px;
  }
`;

export const StationListItem = styled(HorCenter)`
  padding: 10px 0;
  border-bottom: 1px solid #333;

  .name {
    flex: 1;
    margin-right: 12px;
    color: #fff;

    ${ellipsisCss};
  }

  .value {
    color: #fff;
    font-size: 14px;

    span {
      padding-left: 6px;
      color: #fff;
    }
  }
`;

// 地图警报点hover容器
export const WnoContainer = styled(Bcard)`
  min-width: 300px;
  background: #25262d;
  transform: translate(-50%, -108%);
  color: #c1c1c4;

  &::after {
    position: absolute;
    bottom: -6px;
    left: 50%;
    display: block;
    width: 0;
    height: 0;
    border-top: 8px solid #25262d;
    border-right: 8px solid transparent;
    border-left: 8px solid transparent;
    transform: translateX(-50%);
    content: '';
  }
`;

export const StationOverlay = styled(WnoContainer)`
  transform: translate(-50%, -122%);
  width: 360px;
  max-width: 360px;

  .title {
    padding: 16px;
    color: #fff;
  }

  ${Flex} {
    flex-wrap: wrap;
    & > span {
      display: flex;
      justify-content: space-between;
      width: 120px;
      padding: 8px;
      border-top: 1px solid #31333d;
      border-left: 1px solid #31333d;

      span {
        color: ${getColorFromTheme('primary')};
      }
    }
  }
`;

export const DcolorLegendContainer = styled(Bcard)`
  bottom: 164px;
  right: 110px;
  width: 128px;
  padding-bottom: 4px;
  color: white;
  user-select: none;
  overflow: hidden;

  .title {
    height: 32px;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 32px;
    text-align: center;
    background: #25262d;
  }

  .item {
    margin-bottom: 8px;
    padding-left: 10px;

    .block {
      width: 10px;
      height: 10px;
      margin-right: 6px;
      border-radius: 2px;
    }
    .label {
      font-size: 12px;
    }
  }
`;
