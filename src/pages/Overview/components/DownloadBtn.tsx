import { useAtomValue, useSet<PERSON>tom } from 'jotai';
import { memo } from 'react';
import { colorLayerTypeAtom, openDownloadModalAtom } from '../atoms';

interface Props {
  onClick?: () => void;
}
function DownloadButton({ onClick }: Props) {
  const setOpen = useSetAtom(openDownloadModalAtom);
  const colorLayerType = useAtomValue(colorLayerTypeAtom);

  if (colorLayerType !== 'DCOLOR') {
    return null;
  }

  return (
    <>
      <div className="relative rounded-full cursor-pointer backdrop-filter-blur-card hover:bg-white/40 select-none">
        <svg xmlns="http://www.w3.org/2000/svg" width="124px" height="34px" viewBox="0 0 124 34" version="1.1">
          <defs>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1-btn">
              <stop stopColor="#FEAA38" offset="0%" />
              <stop stopColor="#FFCE00" stopOpacity="0.178649476" offset="100%" />
            </linearGradient>
          </defs>
          <rect
            fill="#101530"
            fillOpacity="0.5"
            stroke="url(#linearGradient-1-btn)"
            strokeWidth="1.5"
            id="矩形备份-71"
            x="0.75"
            y="0.75"
            width="122.5"
            height="32.5"
            rx="16.25"
          />
        </svg>
        <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center gap-[4px]" onClick={() => setOpen(true)}>
          {/* {loadingTile === undefined || loadingTile?.length > 0 ? (
          <LoadingOutlined className="text-[#FFAA17]" />
        ) : ( */}
          <iconpark-icon width="12" height="12" color="#FFAA17" name="xiazai1"></iconpark-icon>
          {/* )} */}
          <span className="text-[14px] text-[#FFAA17]" onClick={onClick}>
            下载沙尘动画
          </span>
        </div>
      </div>
    </>
  );
}

export default memo(DownloadButton);
