import { DatePicker } from 'antd';
import { PickerContainer, DatePickerContainer, Label } from './ui';
import moment from 'moment';
import { useAtom } from 'jotai';
import { datePickerValAtom, timelineStatusAtom } from '../atoms';
import { getDarkContainer } from '@/components/ui/utils';
import { dateFormatter } from '@/utils';
import { useResetAtom } from 'jotai/utils';
import { useState } from 'react';

const { RangePicker } = DatePicker;

const TimelineDatePicker: React.FC = () => {
  const [date, setDate] = useAtom(datePickerValAtom);
  const [selectDate, setSelectDate] = useState<any>([]);
  const [hackValue, setHackValue] = useState<any>();
  const resetTimelineStatus = useResetAtom(timelineStatusAtom);

  return (
    <PickerContainer>
      <Label>时间范围</Label>
      <DatePickerContainer>
        <i className="icomoon icon-calendar" />
        <RangePicker
          style={{ width: 220 }}
          bordered={false}
          suffixIcon={null}
          value={hackValue || [moment(date[0]), moment(date[1])]}
          // value={null}
          allowClear={false}
          placeholder={['开始日期', '结束日期']}
          className="dark-form-item"
          getPopupContainer={getDarkContainer}
          onOpenChange={(open) => {
            if (open) {
              setHackValue([]);
              setSelectDate([]);
            } else {
              setHackValue(undefined);
            }
          }}
          disabledDate={(current) => {
            return (
              current &&
              (current > moment().endOf('day') ||
                // Math.abs(current.diff(moment(), 'year')) > 0 ||
                current.isBefore(moment('2022-01-01')) ||
                (selectDate[0] && current.diff(selectDate[0], 'day') > 30) ||
                (selectDate[1] && current.diff(selectDate[1], 'day') < -30))
            );
          }}
          onCalendarChange={(dates) => {
            setSelectDate(dates);
          }}
          onChange={(val) => {
            if (val) {
              const [start, end] = val;

              resetTimelineStatus();
              setDate([
                start!.format(dateFormatter),
                end!.format(dateFormatter),
              ]);
            }
          }}
        />
      </DatePickerContainer>
    </PickerContainer>
  );
};

export default TimelineDatePicker;
