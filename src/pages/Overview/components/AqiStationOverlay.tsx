import { Flex } from '@/components/ui';
import { stationTypeOptions } from '@/utils';
import { useAtom } from 'jotai';
import React, { useCallback } from 'react';
import { nationStationPoTypeAtom } from '../atoms';
import type { AqiItem } from '../services';
import { StationOverlay } from './ui';
import Moment from 'moment';

interface Props {
  top: number;
  left: number;
  data: AqiItem;
}
const AqiStationOverlay: React.FC<Props> = ({ top, left, data }) => {
  const [nspo] = useAtom(nationStationPoTypeAtom);
  const formatVal = useCallback((val: number, type: string) => {
    if (val) {
      if (type === 'co') {
        return val.toFixed(1);
      }
      return Math.round(val);
    }

    return '-';
  }, []);
  return (
    <StationOverlay
      style={{
        left,
        top,
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        {data.region && data.placeName && (
          <div className="title">
            {data.type === 'nation' && data.region}
            {data.placeName}
            {/* {data.type === 'nation' ? '' : '省控站'} */}
          </div>
        )}
        {data.name && <div className="title">{data.name}</div>}
        {data.date && (
          <div className="title">
            {Moment(data.date).format('YYYY/MM/DD HH:mm')}
          </div>
        )}
      </div>
      <Flex>
        {stationTypeOptions.map((indicator) => {
          const highlight = data.type === 'nation' && nspo === indicator.value;
          const fontWeight = highlight ? 'bold' : 'normal';
          const fontSize = highlight ? 16 : 14;
          const color = highlight ? 'white' : '#c1c1c4';
          return (
            <span style={{ fontWeight, fontSize, color }} key={indicator.value}>
              {indicator.label}
              <span>
                {formatVal(Number(data[indicator.value]), indicator.value)}
              </span>
            </span>
          );
        })}
        <span />
        <span />
      </Flex>
    </StationOverlay>
  );
};

export default AqiStationOverlay;
