import { TimelineControl } from './ui';

const TimelinePlayBtn = ({
  isStarted,
  onClick,
  loading,
  children,
}: {
  isStarted: boolean;
  onClick: () => void;
  loading: boolean;
  children: React.ReactNode;
}) => {
  return (
    <TimelineControl active onClick={loading ? () => null : onClick}>
      {loading ? (
        children
      ) : (
        <>
          <i className={`icomoon ${isStarted ? 'icon-pause' : 'icon-play'}`} />
          <p>{isStarted ? '暂停' : '播放'}</p>
        </>
      )}
    </TimelineControl>
  );
};

export default TimelinePlayBtn;
