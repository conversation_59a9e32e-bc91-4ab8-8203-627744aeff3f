import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { useSpring } from 'react-spring';
import { viewStateAtom } from '../atoms';
import { fullScreenAtom } from '@/atoms';
import { CompassContainer } from './ui';

const Compass = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [viewState, setViewState] = useAtom(viewStateAtom);
  const angle = -Number(viewState ? viewState.bearing : 0);

  const style = useSpring({
    opacity: isFullScreen ? 0 : 1,
    right: isFullScreen ? 0 : 42,
  });

  return (
    viewState && (
      <CompassContainer
        style={{
          ...style,
          pointerEvents: isFullScreen ? 'none' : 'auto',
          transform: `rotate(${angle}deg)`,
        }}
        onClick={() => {
          if (viewState.bearing !== 0) {
            setViewState((prev) => ({
              ...prev,
              bearing: 0,
              transitionDuration: Math.abs(viewState.bearing || 0) * 10,
            }));
          }
        }}
      >
        <img src="/assets/images/compass.png" alt="" />
      </CompassContainer>
    )
  );
};

export default Compass;
