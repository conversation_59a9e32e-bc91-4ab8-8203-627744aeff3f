import GeoUtils from '@/utils/geo';
import { useAtomValue } from 'jotai';
import { coordinateAtom } from '../../atoms';
import { InfoItem, ItemHead, ItemText } from '../ui';

const HoverCoordinate: React.FC = () => {
  const coordinate = useAtomValue(coordinateAtom);
  const [lng = 0, lat = 0] = coordinate;

  return (
    <>
      {coordinate.length > 0 && (
        // min-w-200px 防止抖动
        <InfoItem className="min-w-[240px]">
          <ItemHead size={20}>
            <i className="icomoon icon-coordinate" />
          </ItemHead>
          <ItemText>
            {GeoUtils.format(lng)}
            &nbsp;&nbsp;
            {GeoUtils.format(lat)}
          </ItemText>
        </InfoItem>
      )}
    </>
  );
};

export default HoverCoordinate;
