import { Center, Flex } from '@/components/ui';
import { useOnClickOutside } from '@/hooks';
import { stationPollutionValuesAndColors } from '@/utils';
import { useAtomValue } from 'jotai';
import { memo, useMemo, useRef, useState } from 'react';
import { useSpring } from 'react-spring';
import { nationStationPoTypeAtom, pollutionLayersVisibleAtom } from '../../atoms';
import { InfoItem, ItemHead, LegendItems, LegendLump, LegendPopover, Nums, RectContainer, Rects } from '../ui';

const NationStationLegend = () => {
  const layersVisible = useAtomValue(pollutionLayersVisibleAtom);
  const nationStationType = useAtomValue(nationStationPoTypeAtom);
  const [visible, setVisible] = useState(false);
  const ref = useRef(null);

  const styles = useSpring({
    opacity: visible ? 1 : 0,
    bottom: visible ? 32 : 12,
    pointerEvents: visible ? 'auto' : 'none',
  });

  useOnClickOutside(ref, () => {
    setVisible(false);
  });

  const key = useMemo(
    () =>
      (nationStationType === 'o3' || nationStationType === 'co'
        ? `${nationStationType}-hour`
        : nationStationType) as keyof typeof stationPollutionValuesAndColors,
    [nationStationType],
  );

  const target = stationPollutionValuesAndColors[key];

  return layersVisible.nationStation ? (
    <InfoItem
      ref={ref}
      onClick={(e) => {
        e.stopPropagation();

        setVisible((prev) => !prev);
      }}
    >
      <LegendPopover style={styles}>
        <Center>
          <p>国控站</p>
          <p>{target.formula}</p>
          {target.unit && <p>{target.unit}</p>}
        </Center>
        <LegendItems>
          {target.values.map((item: any, index: number) => (
            <Flex key={item.color}>
              <LegendLump style={{ background: item.color }} />
              <Nums>
                <span className="max">{item.max && index === 0 ? item.max : ''}</span>
                <span>{item.min}</span>
              </Nums>
            </Flex>
          ))}
        </LegendItems>
      </LegendPopover>
      <ItemHead>国控站</ItemHead>
      <RectContainer>
        <span>{target.formula}</span>
        <Rects>
          {target.values.map((item: { color: string }) => (
            <div key={item.color} style={{ background: item.color }} />
          ))}
        </Rects>
      </RectContainer>
    </InfoItem>
  ) : null;
};

export default memo(NationStationLegend);
