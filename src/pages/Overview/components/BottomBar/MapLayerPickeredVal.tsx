import { useAtomValue } from 'jotai';
import { textureLayerPickerValueAtom } from '../../atoms';
import { InfoItem, ItemHead, ItemText } from '../ui';

const MapLayerPickeredVal = () => {
  const val = useAtomValue(textureLayerPickerValueAtom);
  return val ? (
    <InfoItem>
      <ItemHead size={20}>
        <i className="icomoon icon-cct" />
      </ItemHead>
      <ItemText>{val}</ItemText>
    </InfoItem>
  ) : null;
};

export default MapLayerPickeredVal;
