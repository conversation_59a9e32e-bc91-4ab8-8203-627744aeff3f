import { Center, Flex } from '@/components/ui';
import { useOnClickOutside } from '@/hooks';
import { weatherValuesAndColors } from '@/utils';
import { useAtomValue } from 'jotai';
import { memo, useRef, useState } from 'react';
import { useSpring } from 'react-spring';
import { fetchTextureWeatherTypeAtom } from '../../atoms';
import { InfoItem, ItemHead, LegendItems, LegendLump, LegendPopover, Nums, RectContainer, Rects } from '../ui';

const WeatherLegend = () => {
  const type = useAtomValue(fetchTextureWeatherTypeAtom) as keyof typeof weatherValuesAndColors;
  const [visible, setVisible] = useState(false);
  const ref = useRef(null);

  const styles = useSpring({
    opacity: visible ? 1 : 0,
    bottom: visible ? 32 : 12,
    pointerEvents: visible ? 'auto' : 'none',
  });

  useOnClickOutside(ref, () => {
    setVisible(false);
  });

  const target = weatherValuesAndColors[type];

  return type && target ? (
    <InfoItem
      ref={ref}
      onClick={(e) => {
        e.stopPropagation();

        setVisible((prev) => !prev);
      }}
    >
      <LegendPopover
        style={{
          ...styles,
          pointerEvents: visible ? 'auto' : 'none',
        }}
      >
        <Center>
          <p>气象</p>
          <p>{target.cn}</p>
          {target.unit && <p>{target.unit}</p>}
        </Center>
        <LegendItems>
          {target.values.map((item: { color: string; max?: number; min: number }, index: number) => (
            <Flex key={item.color}>
              <LegendLump style={{ background: item.color }} />
              <Nums>
                <span className="max">{item.max && index === 0 ? item.max : ''}</span>
                <span>{item.min}</span>
              </Nums>
            </Flex>
          ))}
        </LegendItems>
      </LegendPopover>
      <ItemHead>气象</ItemHead>
      <RectContainer>
        <span>{target.cn}</span>
        <Rects>
          {target.values.map((item: { color: string }) => (
            <div key={item.color} style={{ background: item.color }} />
          ))}
        </Rects>
      </RectContainer>
    </InfoItem>
  ) : null;
};

export default memo(WeatherLegend);
