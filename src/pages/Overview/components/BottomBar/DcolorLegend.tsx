import { fullScreenAtom } from '@/atoms';
import { HorCenter } from '@/components/ui';
import { useAtomValue } from 'jotai';
import { colorLayerTypeAtom } from '../../atoms';
import { dcolorLegends } from '../../utils';
import { DcolorLegendContainer } from '../ui';

const DcolorLegend = () => {
  const colorLayerType = useAtomValue(colorLayerTypeAtom);
  const isFullscreen = useAtomValue(fullScreenAtom);

  return colorLayerType === 'DCOLOR' && !isFullscreen ? (
    <DcolorLegendContainer>
      <div className="title">沙尘红外图</div>
      {dcolorLegends.map((item) => (
        <HorCenter key={item.id} className="item">
          <div
            className="block"
            style={{
              background: `${item.color ? item.color : item.from}`,
              backgroundImage: `linear-gradient` + `(135deg, ${item.from}, ${item.to})`,
            }}
          />
          <div className="label">{item.label}</div>
        </HorCenter>
      ))}
    </DcolorLegendContainer>
  ) : null;
};

export default DcolorLegend;
