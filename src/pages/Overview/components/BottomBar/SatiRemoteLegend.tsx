import { Center, Flex } from '@/components/ui';
import { useOnClickOutside } from '@/hooks';
import { remoteSensingValuesAndColors } from '@/utils';
import { useAtomValue, useSetAtom } from 'jotai';
import { memo, useMemo, useRef, useState } from 'react';
import { useSpring } from 'react-spring';
import { customLegendModalAtom, pollutionLayersVisibleAtom, remotePoTypeAtom } from '../../atoms';
import useGetLegend from '../../hooks/useGetLegend';
import CustomLegendModal from '../CustomLegendModal';
import { InfoItem, ItemHead, LegendItems, LegendLump, LegendPopover, Nums, RectContainer, Rects } from '../ui';

const SatiRemoteLegend = () => {
  const layersVisible = useAtomValue(pollutionLayersVisibleAtom);
  const setCustomLegendModal = useSetAtom(customLegendModalAtom);
  const poType = useAtomValue(remotePoTypeAtom) as keyof typeof remoteSensingValuesAndColors;
  const [visible, setVisible] = useState(false);
  const ref = useRef(null);

  const styles = useSpring({
    opacity: visible ? 1 : 0,
    bottom: visible ? 32 : 12,
    zIndex: visible ? 200 : -1,
    pointerEvents: visible ? 'auto' : 'none',
  });

  useOnClickOutside(ref, () => {
    setVisible(false);
  });

  const { legendInfo: target } = useGetLegend(poType, 'remote');

  const unit = useMemo(() => {
    if (poType === 'co') {
      return '1e19molec./c㎡';
    }
    if (poType === 'hcho') {
      return '1e16molec./c㎡';
    }
    return target?.unit;
  }, [poType, target?.unit]);

  return layersVisible.satiRemote && poType !== 'DCOLOR' ? (
    <>
      <div style={{ position: 'relative', color: '#c1c1c4' }} ref={ref} id='sati-remote-legend'>
        <LegendPopover style={{ ...styles }} width="84px">
          <Center>
            <p>卫星遥感</p>
            <p>{target?.formula}</p>
            {unit && <p>{unit}</p>}
          </Center>
          <LegendItems>
            {target.values.map((item: any, index: number) => (
              <Flex key={item.color}>
                <LegendLump style={{ background: item.color }} />
                <Nums>
                  <span className="max">{item.max && index === 0 ? item.max : ''}</span>
                  <span>{item.min}</span>
                </Nums>
              </Flex>
            ))}
          </LegendItems>
          <div
            style={{
              textAlign: 'center',
              marginBottom: '20px',
              cursor: 'pointer',
            }}
            onClick={(e) => {
              setCustomLegendModal({ pltType: poType, legendType: 'remote' });
            }}
          >
            <span>自定义</span>
          </div>
        </LegendPopover>
        <InfoItem
          onClick={(e) => {
            e.stopPropagation();
            setVisible((prev) => !prev);
          }}
        >
          <ItemHead>卫星遥感</ItemHead>
          <RectContainer>
            <span>{target.formula}</span>
            <Rects>
              {target.values.map((item: { color: string }) => (
                <div key={item.color} style={{ background: item.color }} />
              ))}
            </Rects>
          </RectContainer>
        </InfoItem>
      </div>
      <CustomLegendModal />
    </>
  ) : null;
};

export default memo(SatiRemoteLegend);
