import { fullScreenAtom } from '@/atoms';
import { Flex } from '@/components/ui';
import { useAtomValue } from 'jotai';
import { colorLayerTypeAtom, dustForecastLayerVisibleAtom, dustRangeLayerVisibleAtom } from '../../atoms';
import DownloadBtn from '../DownloadBtn';
import DownloadModal from '../DownloadModal';
import { Info, InfoItem, ItemText } from '../ui';
import DcolorLegend from './DcolorLegend';
import DustRangeLegend from './DustRangeLegend';
import ForecastLegend from './ForecastLegend';
import HoverCoordinate from './HoverCoordinate';
import MapLayerPickeredVal from './MapLayerPickeredVal';
import NationStationLegend from './NationStationLegend';
import RangeLegend from './RangeLegend';
import SatiRemoteLegend from './SatiRemoteLegend';
import WeatherLegend from './WeatherLegend';

const BottomBar: React.FC = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const dustForecastLayerVisible = useAtomValue(dustForecastLayerVisibleAtom);
  const dustRangeLayerVisible = useAtomValue(dustRangeLayerVisibleAtom);
  const colorLayerType = useAtomValue(colorLayerTypeAtom);

  return (
    <>
      <DownloadModal />
      <DcolorLegend />
      <Info
        style={{
          bottom: isFullScreen ? 12 : 118,
        }}
      >
        <DownloadBtn />
        <Flex className="flex-1 justify-end gap-3">
          <MapLayerPickeredVal />
          <WeatherLegend />
          <NationStationLegend />
          <SatiRemoteLegend />
          {dustForecastLayerVisible && <ForecastLegend />}
          {dustRangeLayerVisible && <DustRangeLegend />}
          {colorLayerType === 'DCOLOR' && <RangeLegend />}
          <HoverCoordinate />
          <InfoItem>
            <ItemText id="plotting-scale"></ItemText>
          </InfoItem>
        </Flex>
      </Info>
    </>
  );
};

export default BottomBar;
