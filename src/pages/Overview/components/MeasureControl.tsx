import { useSpring } from 'react-spring';
import { MeasureContainer, IconBtn } from './ui/index';
import { useAtomValue } from 'jotai';
import { measureDisStatusAtom, measureAreaStatusAtom } from '../atoms';
import { fullScreenAtom } from '@/atoms';
import React from 'react';
import { useAtom } from 'jotai';

const MeasureControl: React.FC = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [measuringDisStatus, setMeasuringDisStatus] =
    useAtom(measureDisStatusAtom);
  const [measuringAreaStatus, setMeasuringAreaStatus] = useAtom(
    measureAreaStatusAtom,
  );

  const style = useSpring({
    opacity: isFullScreen ? 0 : 1,
    right: isFullScreen ? 0 : 52,
  });

  return (
    <MeasureContainer
      style={{
        ...style,
        pointerEvents: isFullScreen ? 'none' : 'auto',
      }}
    >
      <IconBtn
        title="面积测量工具"
        active={measuringAreaStatus}
        onClick={() => {
          setMeasuringAreaStatus((prev) => {
            if (!prev) {
              setMeasuringDisStatus(false);
            }

            return !prev;
          });
        }}
      >
        <i className="icomoon icon-area" />
      </IconBtn>
      <IconBtn
        title="距离测量工具"
        active={measuringDisStatus}
        onClick={() => {
          setMeasuringDisStatus((prev) => {
            if (!prev) {
              setMeasuringAreaStatus(false);
            }
            return !prev;
          });
        }}
      >
        <i className="icomoon icon-ruler" />
      </IconBtn>
    </MeasureContainer>
  );
};

export default MeasureControl;
