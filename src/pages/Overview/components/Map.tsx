import { regionAtom } from '@/atoms';
import MapMarkerPortal from '@/components/global/MapMarkerPortal';
import { useMaskLayer, useQyGeo<PERSON>son, useWindowSize } from '@/hooks';
import { mapDataFormatMethods } from '@/utils';
import { MapView } from '@deck.gl/core';
import type Viewport from '@deck.gl/core/viewports/viewport';
import DeckGL from '@deck.gl/react';
import { readPixelsToArray } from '@luma.gl/core';
import { useAtom, useAtomValue, useSetAtom, useSetAtom as useUpdateAtom } from 'jotai';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { createPortal } from 'react-dom';
import {
  activeTimePointAtom,
  areaGroupsAtom,
  colorLayerTypeAtom,
  coordinateAtom,
  distanceGroupsAtom,
  dustForecastLayerVisibleAtom,
  dustRangeLayerVisibleAtom,
  dustTimePointsAtom,
  fetchTextureWeatherType<PERSON>tom,
  frameAtom,
  pollutionLayersVisibleAtom,
  remotePoTypeAtom,
  sandSourceLayerVisibleAtom,
  selectedSandSourceDataAtom,
  stationLayerPickValueAtom,
  textureLayerPickerValueAtom,
  timelineStatusAtom,
  viewStateAtom,
  weatherLayersStatusAtom,
} from '../atoms';
import { useFrontierTileLayerWithLabel, useMapLayers, useSetCalculatedViewState } from '../hooks';
import DisMarkerList from './DisMarkerList';
import MainControl from './MainControl';
import MeasuringAreaRect from './MeasuringAreaRect';
import { MapContainer } from './ui';

import useCloudLayer from '@/components/DustDetail/components/Map/hooks/layers/useCloudLayer';
import useDColorLayer from '@/components/DustDetail/components/Map/hooks/layers/useDColorLayer';
import useWindLayer from '@/components/DustDetail/components/Map/hooks/layers/useWindLayer';
import type { PickInfo } from 'deck.gl';
import { useResetAtom } from 'jotai/utils';
import { activeTimePointHourlyAtom, isSelectTheLastPointOfTimelineAtom, startPollAtom } from '../atoms/timeline';
import { useCloudDynamicTextureTileLayer } from '../hooks/layers/useCloudDynamicTextureTileLayer';
import useDustForecastLayer from '../hooks/layers/useDustForecastLayer';
import useDustRangeLayer from '../hooks/layers/useDustRangeLayer';
import { useDynamicTextureTileLayer } from '../hooks/layers/useDynamicTextureTileLayer';
import useSandSourceLayer from '../hooks/layers/useSandSourceLayer';
import { useTextureLayer } from '../hooks/layers/useTextureLayer';
import AqiStationOverlay from './AqiStationOverlay';
import SandForecastOverlay from './Overlay/SandForecastOverlay';
import SandRangeOverlay from './Overlay/SandRangeOverlay';
import SandSourceOverlay from './Overlay/SandSourceOverlay';

const Map: React.FC = () => {
  const [viewState, setViewState] = useAtom(viewStateAtom);
  const { height } = useWindowSize();
  const disGroups = useAtomValue(distanceGroupsAtom);
  const areaGroups = useAtomValue(areaGroupsAtom);
  const setCoordinate = useUpdateAtom(coordinateAtom);
  const setPickedVal = useUpdateAtom(textureLayerPickerValueAtom);
  const resetTimelineStatus = useResetAtom(timelineStatusAtom);
  const fetchTextureType = useAtomValue(remotePoTypeAtom);
  const fetchTextureWeatherType = useAtomValue(fetchTextureWeatherTypeAtom);
  const layerStatus = useAtomValue(pollutionLayersVisibleAtom);
  const [startPoll] = useAtom(startPollAtom);
  const stationLayerPickedValue = useAtomValue(stationLayerPickValueAtom);
  const region = useAtomValue(regionAtom);
  const textureLayer = useTextureLayer();
  const frame = useAtomValue(frameAtom);
  const dustRangeLayerVisible = useAtomValue(dustRangeLayerVisibleAtom);
  const dustForecastLayerVisible = useAtomValue(dustForecastLayerVisibleAtom);
  const activeTimePoint = useAtomValue(activeTimePointAtom);
  const isSelectTheLastPointOfTimeline = useAtomValue(isSelectTheLastPointOfTimelineAtom);
  const dustTimePoints = useAtomValue(dustTimePointsAtom);
  const sandSourceLayerVisible = useAtomValue(sandSourceLayerVisibleAtom);
  const mapContainerRef = useRef(null);
  const setSelectedSandSourceData = useSetAtom(selectedSandSourceDataAtom);
  const activeTimePointHourly = useAtomValue(activeTimePointHourlyAtom);
  const weatherLayersStatus = useAtomValue(weatherLayersStatusAtom);

  // 图层hooks
  const {
    tileLayer,
    geojsonLayer,
    labeledTileLayer,
    areaPointsLayer,
    distancePathLayer,
    distancePointsLayer,
    // frontierTileLayer,
    stationIconLayer,
    dMaskerLayer,
  } = useMapLayers();
  const colorLayerType = useAtomValue(colorLayerTypeAtom);
  const [stationIconBgLayer] = stationIconLayer;
  const frontierTileLayer = useFrontierTileLayerWithLabel(region === 0 && colorLayerType !== '', 0.4);
  const { dynamicTextureTileLayer } = useDynamicTextureTileLayer({
    timePoints: dustTimePoints,
    frame,
    visible: colorLayerType === 'DCOLOR',
  });
  const { cloudDynamicTextureTileLayer } = useCloudDynamicTextureTileLayer({
    timePoints: dustTimePoints,
    frame,
    visible: colorLayerType === 'TCOLOR',
  });

  const timeList = useMemo(() => dustTimePoints?.map((t) => t.label) || [], [dustTimePoints]);
  const setRangeLoading = useCallback(() => {}, []);

  const dustRangeLayer = useDustRangeLayer({
    isDustRangeLayerVisible: dustRangeLayerVisible,
    activeTimePoint,
    regionCode: region === 0 ? 100000 : region,
    isSelectTheLastPointOfTimeline,
    setRangeLoading,
    timeList,
  });

  const dustForecastLayer = useDustForecastLayer({
    isDustForecastLayerVisible: dustForecastLayerVisible,
    activeTimePoint,
    regionCode: region === 0 ? 100000 : region,
    isSelectTheLastPointOfTimeline,
  });

  const sandSourceLayer = useSandSourceLayer({ isSandSourceLayerVisible: sandSourceLayerVisible, setSelectedSandSourceData });

  const windLayer = useWindLayer({
    isWindLayerVisible: weatherLayersStatus.find((item) => item.label === '风向')?.visible || false,
    activeTimePoint: activeTimePointHourly,
  });
  const dColorLayer = useDColorLayer({
    isDColorLayerVisible: colorLayerType === 'DCOLOR' && !startPoll,
    activeTimePoint,
  });
  const cloudLayer = useCloudLayer({
    isCloudLayerVisible: colorLayerType === 'TCOLOR' && !startPoll,
    activeTimePoint,
  });

  // 重置时间轴状态
  useEffect(() => {
    document.oncontextmenu = () => {
      return false;
    };
    return () => {
      resetTimelineStatus();

      document.oncontextmenu = () => {
        return true;
      };
    };
  }, [resetTimelineStatus, setViewState]);

  // 重新计算viewState中的zoom和中心点
  const { geojson } = useSetCalculatedViewState();
  const { geojson: qyGeojson } = useQyGeojson();

  const maskLayer = useMaskLayer(region === 0 ? qyGeojson : geojson);

  return (
    <div className="relative h-full w-full">
      <MapContainer style={{ height }} id="map-container" ref={mapContainerRef}>
        <DeckGL
          // @ts-ignore
          // Deck={FpsThrottledDeck}
          useDevicePixels
          glOptions={{ preserveDrawingBuffer: true }}
          viewState={viewState}
          views={[new MapView({ repeat: true, orthographic: true })]}
          layers={
            [
              maskLayer,
              tileLayer,
              dynamicTextureTileLayer,
              cloudDynamicTextureTileLayer,
              dColorLayer,
              cloudLayer,
              textureLayer,
              dMaskerLayer,
              ...windLayer,
              frontierTileLayer,
              labeledTileLayer,
              geojsonLayer,
              dustRangeLayer,
              dustForecastLayer,
              sandSourceLayer,
              stationIconBgLayer,
              areaPointsLayer,
              distancePathLayer,
              distancePointsLayer,
            ] as any
          }
          controller={{
            scrollZoom: {
              speed: 0.0025,
              smooth: true,
            },
          }}
          onViewStateChange={({ viewState: newViewState }) => {
            setViewState(newViewState);
          }}
          onHover={(info: PickInfo<any>) => {
            if (info.coordinate) {
              setCoordinate(info.coordinate);

              if ((layerStatus.satiRemote || fetchTextureWeatherType) && textureLayer) {
                if (
                  info.bitmap &&
                  info.sourceLayer &&
                  info.sourceLayer.props?.image &&
                  info.sourceLayer.state?.layerType === 'texture-bitmap-layer'
                ) {
                  const { bitmap, sourceLayer } = info;
                  const pixelColor: number[] = readPixelsToArray(sourceLayer.props.image, {
                    sourceX: bitmap.pixel[0],
                    sourceY: bitmap.pixel[1],
                    sourceWidth: 1,
                    sourceHeight: 1,
                  });
                  const val = {
                    r: pixelColor[0],
                    g: pixelColor[1],
                    b: pixelColor[2],
                    a: pixelColor[3],
                  };

                  if (mapDataFormatMethods[fetchTextureWeatherType || fetchTextureType || 'pm25'] && (val.r !== 0 || val.g !== 0 || val.b !== 0)) {
                    const result = mapDataFormatMethods[fetchTextureWeatherType || fetchTextureType || 'pm25'](val);

                    setPickedVal(result);
                  } else {
                    setPickedVal(null);
                  }
                } else {
                  setPickedVal(null);
                }
              }
            }
          }}
        >
          {({ viewport }: { viewport: Viewport }) => {
            // 设置比例尺
            let val = Math.round(viewport.metersPerPixel * 100);
            val = val > 5000 ? `${Math.round(val / 1000)}千米` : `${val}米`;
            const stationOverlayPosition = stationLayerPickedValue
              ? viewport.project([stationLayerPickedValue?.lon, stationLayerPickedValue?.lat])
              : [0, 0];

            return (
              <>
                {/* 比例尺 */}
                {/* <PlottingScale val={val > 5000 ? `${Math.round(val / 1000)}千米` : `${val}米`} /> */}
                {val && createPortal(val, document.getElementById('plotting-scale') || document.body)}
                {/* 国控、省控point hover浮层 */}
                {stationLayerPickedValue && (
                  <AqiStationOverlay data={stationLayerPickedValue} left={stationOverlayPosition[0]} top={stationOverlayPosition[1]} />
                )}
                {/* 距离点距离标记，用于显示距离文字 */}
                {disGroups.map((group) => {
                  return (
                    <MapMarkerPortal key={group.id} groupId={group.id}>
                      <DisMarkerList groupId={group.id} points={group.points} viewport={viewport} />
                    </MapMarkerPortal>
                  );
                })}
                {/* 测量面积标记，用于显示面积文字 */}
                {areaGroups.map((group) => {
                  return (
                    <MapMarkerPortal key={group.id} groupId={group.id}>
                      <MeasuringAreaRect groupId={group.id} points={group.points} viewport={viewport} />
                    </MapMarkerPortal>
                  );
                })}
                <SandSourceOverlay viewport={viewport} mapContainer={mapContainerRef.current} />
              </>
            );
          }}
        </DeckGL>
      </MapContainer>
      <MainControl />
      <SandForecastOverlay />
      <SandRangeOverlay />
    </div>
  );
};

export default Map;
