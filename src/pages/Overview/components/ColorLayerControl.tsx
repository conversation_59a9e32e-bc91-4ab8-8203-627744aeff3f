import { fullScreenAtom } from '@/atoms';
import { Center } from '@/components/ui';
import { useOnClickOutside } from '@/hooks';
import { message } from 'antd';
import { useAtom, useAtomValue, useSetAtom, useSetAtom as useUpdateAtom } from 'jotai';
import { createRef, useEffect, useState } from 'react';
import { useSpring } from 'react-spring';
import {
  aggAtom,
  colorLayerTypeAtom,
  dustForecastLayerVisibleAtom,
  dustRangeLayerVisibleAtom,
  fetchTextureWeatherTypeAtom,
  isSelectTheLastPointOfTimelineAtom,
  pollutionPanelVisibleAtom,
  tileLayerVisibleAtom,
  weatherLayersStatusAtom,
} from '../atoms';
import { LayerBtn, LayerMenu, LayerMenuItem, MenuContainer, Switch } from './ui';

const opts: {
  label: string;
  value: 'TCOLOR' | 'DCOLOR' | 'DMASK' | 'RANGE' | 'FORECAST';
}[] = [
  {
    label: '卫星影像图',
    value: 'TCOLOR',
  },
  {
    label: '沙尘红外图',
    value: 'DCOLOR',
  },
  {
    label: '沙尘掩码图',
    value: 'DMASK',
  },
];
// 真彩假彩
const ColorLayerControl: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const isFullScreen = useAtomValue(fullScreenAtom);
  const setPoPanelVisible = useUpdateAtom(pollutionPanelVisibleAtom);
  const [tileLayerVisible, setTileLayerVisible] = useAtom(tileLayerVisibleAtom);

  const menuRef = createRef<HTMLDivElement>();
  const [colorLayerType, setColorLayerType] = useAtom(colorLayerTypeAtom);
  const [dustRangeLayerVisible, setDustRangeLayerVisible] = useAtom(dustRangeLayerVisibleAtom);
  const [dustForecastLayerVisible, setDustForecastLayerVisible] = useAtom(dustForecastLayerVisibleAtom);
  const setFetchTextureWeatherType = useSetAtom(fetchTextureWeatherTypeAtom);
  const setWeatherLayersStatus = useUpdateAtom(weatherLayersStatusAtom);
  const isSelectTheLastPointOfTimeline = useAtomValue(isSelectTheLastPointOfTimelineAtom);
  const agg = useAtomValue(aggAtom);

  const style = useSpring({
    opacity: visible && !isFullScreen ? 1 : 0,
    bottom: visible && !isFullScreen ? 90 : 80,
  });

  useOnClickOutside(menuRef, () => {
    setVisible(false);
  });

  const closeWeatherLayer = () => {
    setFetchTextureWeatherType(null);
    setWeatherLayersStatus((prevState) =>
      prevState.map((item) => {
        return item.label === '风向'
          ? item
          : {
              ...item,
              visible: false,
            };
      }),
    );
  };

  useEffect(() => {
    if (agg !== 'hourly') {
      setDustRangeLayerVisible(false);
      setDustForecastLayerVisible(false);
      setColorLayerType('');
    }
    // 仅在 agg 变化时执行
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [agg]);

  useEffect(() => {
    if (!isSelectTheLastPointOfTimeline) {
      setDustForecastLayerVisible(false);
    }
    // 仅在 isSelectTheLastPointOfTimeline 变化时执行
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSelectTheLastPointOfTimeline]);

  return (
    <LayerBtn
      title="影像图"
      active={colorLayerType !== '' || dustRangeLayerVisible || dustForecastLayerVisible}
      ref={menuRef}
      onClick={() => {
        setVisible(!visible);
        setPoPanelVisible(false);
      }}
    >
      <i className="icomoon icon-earth" />
      <MenuContainer
        style={{
          ...style,
          pointerEvents: visible && !isFullScreen ? 'auto' : 'none',
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Center>
          <i className="icomoon icon-earth !text-[16px]" />
          影像图
        </Center>
        <LayerMenu>
          {opts.map((item) => {
            return (
              <LayerMenuItem
                key={item.label}
                title={item.label}
                active={colorLayerType === item.value}
                onClick={(e) => {
                  if (agg !== 'hourly') {
                    message.error('该图层不支持当前颗粒度');
                    return;
                  }
                  e.stopPropagation();
                  if (colorLayerType === item.value) {
                    setColorLayerType('');
                  } else {
                    setColorLayerType(item.value);
                  }
                  closeWeatherLayer();
                }}
              >
                <div className="label">{item.label}</div>{' '}
                <Switch
                  checked={tileLayerVisible[item.value]}
                  primaryBg="rgba(255,255,255,.5)"
                  shouldChangeBg={item.value === colorLayerType}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (item.value === colorLayerType) {
                      setTileLayerVisible((prev) => ({
                        ...prev,
                        [item.value]: !prev[item.value],
                      }));
                    }
                  }}
                />
              </LayerMenuItem>
            );
          })}

          <LayerMenuItem
            key={'RANGE'}
            title={'沙尘范围'}
            active={dustRangeLayerVisible}
            onClick={(e) => {
              if (agg !== 'hourly') {
                message.error('该图层不支持当前颗粒度');
                return;
              }
              e.stopPropagation();
              setDustRangeLayerVisible(!dustRangeLayerVisible);
              closeWeatherLayer();
            }}
          >
            <div className="label">沙尘范围</div>
          </LayerMenuItem>

          <LayerMenuItem
            key={'FORECAST'}
            title={'沙尘预测'}
            active={dustForecastLayerVisible}
            onClick={(e) => {
              if (agg !== 'hourly') {
                message.error('该图层不支持当前颗粒度');
                return;
              }
              if (!isSelectTheLastPointOfTimeline) {
                message.error('请关闭其他污染物或气象，仅开启影像图图层，且选中最新的监测时间点后可开启');
                return;
              }
              e.stopPropagation();
              setDustForecastLayerVisible(!dustForecastLayerVisible);
              closeWeatherLayer();
            }}
          >
            <div className="label">沙尘预测</div>
          </LayerMenuItem>
        </LayerMenu>
      </MenuContainer>
    </LayerBtn>
  );
};

export default ColorLayerControl;
