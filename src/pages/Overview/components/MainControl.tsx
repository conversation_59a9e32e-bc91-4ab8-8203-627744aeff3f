import { useSpring } from 'react-spring';
import LayersControl from './LayersControl';
import { MCtrlContainer, StyledCascader } from './ui';
// import TimelineTypeSelect from './TimelineTypeSelect';
import { fullScreenAtom, regionAtom } from '@/atoms';
import { useAtomValue } from 'jotai';
import { aggAtom, timelineStatusAtom, timelineVisibleAtom } from '../atoms';
import MapTimeline from './MapTimeline';
// import { getDarkContainer } from '@/components/ui/utils';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import { CaretDownOutlined } from '@ant-design/icons';
import { ConfigProvider, theme } from 'antd';
import { useAtom } from 'jotai';
import { memo, useMemo } from 'react';
import { AggAndDatePicker } from './AggAndDatePicker';

const MainControl: React.FC = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [region, setRegion] = useAtom(regionAtom);
  const [timelineStatus, setTimelineStatus] = useAtom(timelineStatusAtom);
  const { options, cascaderValue, level } = useCascaderOptionsAndMatchValues(region);
  const agg = useAtomValue(aggAtom);

  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    bottom: isFullScreen ? 0 : 12,
  });

  const timelineVisible = useAtomValue(timelineVisibleAtom);
  const newOpts = useMemo(() => {
    if (level === 1) {
      return [
        ...options,
        {
          code: 0,
          name: '全域',
        },
      ];
    }
    return options;
  }, [level, options]);

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorText: '#c1c1c4',
          colorBgBase: '#25262D',
          colorIcon: '#ffffff',
          colorBgElevated: '#25262D',
          colorBgContainer: 'transparent',
          colorPrimary: '#286CFF',
          colorBorder: 'transparent',
          colorBgSpotlight: 'rgba(7, 7, 7, 1)',
        },
        components: {
          DatePicker: {
            activeShadow: 'none',
            cellActiveWithRangeBg: 'rgba(21, 106, 245, 0.3)',
            addonBg: '#c1c1c4',
            // 添加禁用状态的样式
            colorTextDisabled: 'rgba(255, 255, 255, 0.25)',
            colorBgContainerDisabled: 'rgba(255, 255, 255, 0.04)',
          },
        },
      }}
    >
      <MCtrlContainer
        style={{
          ...styles,
          pointerEvents: isFullScreen ? 'none' : 'auto',
          right: timelineVisible ? 20 : 'auto',
        }}
      >
        {/* @ts-ignore */}
        <StyledCascader
          allowClear={false}
          suffixIcon={<CaretDownOutlined />}
          className="dark-form-item"
          changeOnSelect
          // getPopupContainer={getDarkContainer}
          dropdownClassName="dark-cascader-dropdown"
          fieldNames={{
            label: 'name',
            value: 'code',
          }}
          placeholder="请选择"
          value={region === 0 ? [region] : cascaderValue}
          options={newOpts}
          bordered={false}
          onChange={(val) => {
            if (val && val.length > 0) {
              setRegion(val[val.length - 1] as number);
            }
            setTimelineStatus({ ...timelineStatus, isStarted: false });
          }}
        />
        <LayersControl />
        {timelineVisible && (
          <>
            <AggAndDatePicker />
            {agg !== 'custom' && <MapTimeline />}
          </>
        )}
      </MCtrlContainer>
    </ConfigProvider>
  );
};

export default memo(MainControl);
