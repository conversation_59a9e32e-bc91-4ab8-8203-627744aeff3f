import { fullScreenAtom } from '@/atoms';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useAtomValue } from 'jotai';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { aggAtom, isAggHalfHourAtom } from '../../atoms';
import ActiveButton from './ActiveButton';

/**
 * 生成 10 位的唯一 ID
 *
 * @returns 唯一 ID
 */
function genUniqueID(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let autoId = '';
  for (let i = 0; i < 10; i++) {
    autoId += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return autoId;
}

/**
 * 将一个元素在其父元素内水平居中
 *
 * @param container 父元素
 * @param innerItem 需要居中的元素
 */
export function scrollToHorizontalCenter(container: HTMLElement | null, innerItem: HTMLElement | null) {
  if (container && innerItem) {
    const containerWidth = container.offsetWidth;
    const innerItemWidth = innerItem.offsetWidth;
    const scrollLeft = innerItem.offsetLeft - (containerWidth - innerItemWidth) / 2;

    container.scrollTo({
      left: scrollLeft,
      behavior: 'smooth',
    });
  }
}

export type TimelineListItem = {
  id: number | string;
  label: string;
  detailLabel: string;
  value: string;
  haveDust?: boolean;
  isStartPoint?: boolean;
};

type Props<T> = {
  /**
   * 当前项是否被选中
   */
  isActive?: boolean;
  /**
   * 当前项数据
   */
  data: T;
  /**
   * 监听点击事件
   *
   * @param data
   * @param event
   * @returns
   */
  handleClick: (data: T, event: React.MouseEvent) => void;
  containerRef: React.RefObject<HTMLDivElement>;
  setOutlineWidth: (width: number) => void;
};

function Item<T extends TimelineListItem>({ isActive = false, data, handleClick, containerRef, setOutlineWidth }: Props<T>) {
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const [isItemVisible, setIsItemVisible] = useState(true);
  const isHourData = useMemo(() => dayjs(data.value).minute() === 0, [data.value]);
  const agg = useAtomValue(aggAtom);
  const [flagKey, setFlagKey] = useState(0);
  const isHalfHour = useAtomValue(isAggHalfHourAtom);

  useEffect(() => setFlagKey((prev) => prev + 1), [isHalfHour]);

  const iconStyle = twMerge(
    'block rounded-full box-content',
    isHourData ? 'w-[10px] h-[10px]' : 'w-[6px] h-[6px]',
    data.haveDust ? 'bg-[#FFB600]' : 'bg-[#C1C1C4]',
    data.isStartPoint && 'border-2 border-white',
    isActive && 'border-solid border-[2px] bottom-[1px]',
  );

  const itemRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);

  const domId = useMemo(() => genUniqueID(), []);
  const currentDayjs = useMemo(() => dayjs(data?.value), [data?.value]);
  const [showFlag, setShowFlag] = useState(true);
  const isFullScreen = useAtomValue(fullScreenAtom);

  // 根据 agg , 判断是否满足如下情况：
  // 1. 如果 agg 为 hourly, 择判断 data.value 是否为当天 0 点，如果是，则返回 14日这种类型
  // 2. 如果 agg 为 daily, 则判断 data.value 是否为当月 1 号，如果是，则返回 1月这种类型
  // 3. 如果 agg 为 monthly, 则判断 data.value 是否为当年 1 月 1 号，如果是，则返回 2025年这种类型
  // 4. 如果 agg 为 custom, 则判断 data.value 是否为当月 1 号，如果是，则返回 1月这种类型
  // 否则 返回 null
  const detailLabel = useMemo(() => {
    if (agg === 'hourly' && currentDayjs.hour() === 0 && currentDayjs.minute() === 0) {
      return currentDayjs.format('DD日');
    }
    if ((agg === 'daily' || agg === 'custom') && currentDayjs.date() === 1) {
      return currentDayjs.format('M月');
    }
    if (agg === 'monthly' && currentDayjs.month() === 0 && currentDayjs.date() === 1) {
      return currentDayjs.format('YYYY年');
    }
    return null;
  }, [agg, currentDayjs]);

  // fix: 解决全屏下沙尘旗帜不消失的问题
  useEffect(() => {
    if (isFullScreen) {
      setShowFlag(false);
    } else {
      setTimeout(() => {
        setShowFlag(!isFullScreen);
      }, 500);
    }
  }, [isFullScreen]);

  useEffect(() => {
    if (isActive) {
      const outerContainer = containerRef.current;
      const clickedItem = itemRef.current;

      // 自动滚动到父元素中间
      scrollToHorizontalCenter(outerContainer, clickedItem);
      if (clickedItem) {
        setOutlineWidth(clickedItem.offsetLeft + clickedItem.offsetWidth / 2);
      }
    }
  }, [containerRef, isActive, setOutlineWidth]);

  // 监听 container 的 宽度变化，如果变化，重新执行滚动到中间的逻辑
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        scrollToHorizontalCenter(entry.target as HTMLElement, itemRef.current);
      });
    });
    observer.observe(containerRef.current!);
    return () => observer.disconnect();
  }, [containerRef]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 元素进入视口
            setIsItemVisible(true);
          } else {
            // 元素不在视口中
            setIsItemVisible(false);
          }
        });
      },
      {
        root: containerRef.current!,
      },
    );
    observer.observe(iconRef.current!);
    return () => observer.disconnect();
    // mounted 执行，不需要依赖参数
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const tooltipTitle = useMemo(() => {
    switch (agg) {
      case 'hourly':
        const minute = currentDayjs.minute();
        if (minute === 0) {
          return currentDayjs.format('MM月DD日-HH时');
        }
        return currentDayjs.format('MM月DD日-HH时mm分');
      case 'daily':
      case 'custom':
        return currentDayjs.format('MM月DD日');
      case 'monthly':
        return currentDayjs.format('YYYY年MM月');
      case 'yearly':
        return currentDayjs.format('YYYY年');
      default:
        return currentDayjs.format('YYYY年MM月DD日 HH时mm分');
    }
  }, [agg, currentDayjs]);

  return (
    <div
      ref={itemRef}
      id={domId}
      className="flex flex-col items-center justify-center min-w-[16px] h-full mx-[1px] relative flex-shrink-0 cursor-pointer scroll-snap-align-center select-none"
      onClick={(e) => {
        handleClick(data, e);
      }}
      onMouseLeave={() => setIsTooltipOpen(false)}
      onMouseEnter={() => setIsTooltipOpen(true)}
    >
      {showFlag && !!data?.isStartPoint && (
        <Tooltip
          placement="top"
          arrow={false}
          key={flagKey}
          styles={{
            body: {
              padding: 0,
              minWidth: 0,
              background: 'transparent',
            },
            root: {
              transform: 'translateY(15px)',
            },
          }}
          title={
            <div className="relative w-[2px] h-[34px] bg-[#ff0000] rounded-t-[2px]">
              <div
                className="absolute top-[1px] left-0 text-[12px] text-white bg-[#ff0000] whitespace-nowrap px-[4px] pr-[10px]"
                style={{
                  clipPath: 'polygon(100% 0%, 80% 50%, 100% 100%, 0% 100%, 0% 0%)',
                }}
              >
                起沙
              </div>
            </div>
          }
          open={isItemVisible}
          onOpenChange={() => false}
        >
          <div className="absolute top-1/2 left-1/2 h-[0px] w-[0px]"></div>
        </Tooltip>
      )}
      <Tooltip
        styles={{
          body: {
            paddingTop: '2px',
            paddingBottom: '2px',
          },
        }}
        placement="top"
        destroyOnHidden
        title={
          <div className="flex flex-col justify-center">
            {/* <div className="text-[12px] leading-[17px] text-center">{currentDayjs.format('MM-DD')}</div>
            <div className="text-[14px] leading-[20px] text-center">{currentDayjs.format('HH:mm')}</div> */}
            {tooltipTitle.split('-').map((line, index) => (
              <div key={line} className="text-[12px] leading-[17px] text-center">
                {line}
              </div>
            ))}
          </div>
        }
        open={(isItemVisible && isTooltipOpen) || (isActive && isItemVisible && showFlag)}
        onOpenChange={(val) => setIsTooltipOpen(val)}
      >
        <div className="absolute top-1/2 left-1/2 h-[0px] w-[0px]"></div>
      </Tooltip>

      <div className={twMerge(isActive && 'flex items-center justify-center shrink-0')} ref={iconRef}>
        {isActive ? (
          <ActiveButton haveDust={!!data?.haveDust}>
            <span className={iconStyle}></span>
          </ActiveButton>
        ) : (
          <span className={iconStyle}></span>
        )}
      </div>

      {detailLabel && (
        <>
          {/* 竖向的 1px 细线，并居中 */}
          <div className="absolute top-[1px] left-1/2 h-[14px] w-[1px] bg-[#69696D] -translate-x-1/2"></div>
          <div className="absolute text-[12px] text-[#C1C1C4] whitespace-nowrap bottom-[1px]">{detailLabel}</div>
        </>
      )}
    </div>
  );
}

export default memo(Item);
