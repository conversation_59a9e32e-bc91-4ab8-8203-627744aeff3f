import React, { FC, memo, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import Item, { type TimelineListItem } from './TimelineItem';

interface Props {
  /**
   * 列表数据
   */
  options: TimelineListItem[];

  /**
   * 数据变动的回调函数
   *
   * @param data
   * @param event
   * @returns
   */
  onChange: (data: TimelineListItem, event: React.MouseEvent) => void;

  /**
   * 当前选中的数据
   */
  value?: TimelineListItem;
}

const BasicTimeline: FC<Props> = ({ options, onChange, value }) => {
  const scrollContainer = useRef<HTMLDivElement>(null);
  const [, setOutlineWidth] = useState(0);

  return (
    <div
      className={twMerge(
        'flex grow h-full px-[10px] overflow-x-auto justify-between relative horizontal-scroll-bar',
        options.length === 1 && 'justify-center',
      )}
      ref={scrollContainer}
    >
      {options.map((item) => {
        return (
          <Item
            key={item.id}
            data={item}
            isActive={item.value === value?.value}
            handleClick={(data, e) => onChange(data, e)}
            containerRef={scrollContainer}
            setOutlineWidth={(width) => setOutlineWidth(width)}
          />
        );
      })}
    </div>
  );
};

export default memo(BasicTimeline);
