import { frameAtom, isSelectedImageLayerAtom, loadingCloudTileAtom, loadingDColorTileAtom, startPollAtom } from '@/pages/Overview/atoms';
import { LoadingOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { twJoin, twMerge } from 'tailwind-merge';
import { tweenFrameCount } from '../../configs';
import TimelinePlayBtn from '../TimelinePlayBtn';
import BasicTimeline from './BasicTimeline';
import { type TimelineListItem } from './TimelineItem';

const actionIconStyle = twJoin(
  'flex items-center justify-center w-[26px] h-[26px] border-[1px] border-white  bg-[#000000]/20 rounded-full flex-shrink-0 grow-0 cursor-pointer select-none',
);

const findIndex = (list: TimelineListItem[], id: number | undefined | string) => {
  return list.findIndex((v) => v.id === id);
};

type Props = {
  /**
   * 当前选中的时间点
   *
   * 初始时如果传undefined，则默认选中列表最后一个
   */
  activeItem?: TimelineListItem;
  /**
   * 设置当前选中的时间点
   */
  setActiveItem: React.Dispatch<React.SetStateAction<TimelineListItem | undefined>>;
  /**
   * 需要展示的时间点列表
   */
  list: TimelineListItem[];
};

function Timeline({ list, activeItem, setActiveItem }: Props) {
  const [startPoll, setStartPoll] = useAtom(startPollAtom);
  const [frame, setFrame] = useAtom(frameAtom);
  const loadingDColorTile = useAtomValue(loadingDColorTileAtom);
  const loadingCloudTile = useAtomValue(loadingCloudTileAtom);
  const isSelectedImageLayer = useAtomValue(isSelectedImageLayerAtom);

  const isLoading = useMemo(() => {
    return loadingCloudTile || loadingDColorTile;
  }, [loadingCloudTile, loadingDColorTile]);

  useEffect(() => {
    if (isLoading) {
      setStartPoll(false);
    }
  }, [isLoading, setStartPoll]);

  const totalFrame = useMemo(() => {
    return (list.length - 1) * (tweenFrameCount + 1);
  }, [list.length]);

  const getActiveTimePointPercentage = useCallback(
    (activeTimelineItem: TimelineListItem) => {
      if (activeTimelineItem) {
        const index = list.findIndex((item) => item.value === activeTimelineItem.value);
        return index / (list.length - 1);
      }
      return 0;
    },
    [list],
  );

  const setActiveItemAndFrame = useCallback(
    (data: TimelineListItem) => {
      setActiveItem(data);
      const percentage = getActiveTimePointPercentage(data);
      const currentFrame = Math.round(totalFrame * percentage);
      setFrame(currentFrame);
    },
    [setActiveItem, getActiveTimePointPercentage, totalFrame, setFrame],
  );

  const handleActiveItemChange = (data: TimelineListItem) => {
    // 点击时停止轮播
    setStartPoll(false);
    setActiveItemAndFrame(data);
  };

  const poll = useCallback(() => {
    const index = findIndex(list, activeItem?.id);
    if (index > list.length - 1) {
      setActiveItem(list[0]);
    } else {
      setActiveItem(list[index]);
    }
  }, [activeItem?.id, list, setActiveItem]);

  const handlePollStateChange = useCallback(() => {
    if (!startPoll) {
      if (activeItem) {
        const currentIndex = findIndex(list, activeItem.id);
        const percentage = currentIndex / (list.length - 1);
        setFrame(Math.round(totalFrame * percentage));

        if (currentIndex === list.length - 1) {
          setFrame(0);
        }
      } else {
        poll();
        setFrame(0);
      }
    }
    setStartPoll((prev) => !prev);
  }, [startPoll, setStartPoll, poll, setFrame, activeItem, list, totalFrame]);

  useEffect(() => {
    // 初始化时间轴
    if (!activeItem && Array.isArray(list) && list.length > 0) {
      setActiveItemAndFrame(list[list.length - 1]);
    }
    setFrame(0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [list]);

  // 轮播控制
  useEffect(() => {
    let intervalId: any;

    if (startPoll) {
      intervalId = setInterval(() => {
        // 两个时间点之间的总帧数
        const frameInTwoPoints = tweenFrameCount + 1;
        if (frame % frameInTwoPoints === 0) {
          const percentage = frame / totalFrame;
          const index = Math.round((list.length - 1) * percentage);
          setActiveItem(list[index]);
        }

        setFrame((prev) => {
          if (prev >= totalFrame) {
            setStartPoll(false); // 到达最后一个点位时停止轮播
            return prev;
          }
          return prev + 1;
        });
      }, 100);
    } else {
      clearInterval(intervalId);
    }
    return () => {
      clearInterval(intervalId);
    };
  }, [frame, list, setActiveItem, setFrame, startPoll, totalFrame, setStartPoll]);

  return (
    <div className="relative z-[1] flex items-center gap-[8px] overflow-hidden grow">
      <TimelinePlayBtn isStarted={startPoll} loading={isLoading && isSelectedImageLayer} onClick={handlePollStateChange}>
        <div
          className={twMerge(actionIconStyle, 'bg-white !text-black')}
          onClick={() => {
            message.warning('请等待资源加载完毕');
          }}
          title={'资源加载中'}
        >
          <LoadingOutlined />
        </div>
      </TimelinePlayBtn>
      <div className="flex h-[54px] bg-[#25262D] items-center justify-between px-[6px] grow gap-[10px] overflow-hidden">
        {/* 基础时间轴模块 */}
        <BasicTimeline value={activeItem} options={list} onChange={handleActiveItemChange} />
      </div>
    </div>
  );
}

export default memo(Timeline);
