import { FlyToInterpolator } from 'deck.gl';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { calculatedViewStateAtom, viewStateAtom } from '../atoms';
import { fullScreenAtom } from '@/atoms';
import { useCallback } from 'react';
import { useSpring } from 'react-spring';
import { ZoomContainer, IconBtn } from './ui/index';

const ZoomControl: React.FC = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const calculatedViewState = useAtomValue(calculatedViewStateAtom);

  const style = useSpring({
    opacity: isFullScreen ? 0 : 1,
    right: isFullScreen ? 0 : 52,
  });
  const setViewState = useUpdateAtom(viewStateAtom);

  const zoomIn = useCallback(() => {
    setViewState((prev) => ({
      ...prev,
      zoom: (prev.zoom || 6) + 0.4,
      transitionDuration: 500,
    }));
  }, [setViewState]);
  const zoomOut = useCallback(() => {
    setViewState((prev) => ({
      ...prev,
      zoom: (prev.zoom || 6) - 0.4,
      transitionDuration: 500,
    }));
  }, [setViewState]);

  return (
    <ZoomContainer
      style={{ ...style, pointerEvents: isFullScreen ? 'none' : 'auto' }}
    >
      <IconBtn
        title="复原"
        onClick={() => {
          const newViewState = calculatedViewState || {
            latitude: 23.81024,
            longitude: 108.800204,
            zoom: 6,
            maxZoom: 20,
            minZoom: 4,
            pitch: 0,
            bearing: 0,
          };
          setViewState({
            ...newViewState,
            transitionInterpolator: new FlyToInterpolator(),
            transitionDuration: 1000,
          });
        }}
      >
        <i className="icomoon icon-location" />
      </IconBtn>
      <IconBtn title="放大" onClick={zoomIn}>
        <i className="icomoon icon-zoomin" />
      </IconBtn>
      <IconBtn title="缩小" onClick={zoomOut}>
        <i className="icomoon icon-zoomout" />
      </IconBtn>
    </ZoomContainer>
  );
};

export default ZoomControl;
