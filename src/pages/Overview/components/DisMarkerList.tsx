import { DisMarker } from '@/components/ui';
import type Viewport from '@deck.gl/core/viewports/viewport';
import { useAtom } from 'jotai';
import type { DistanceGroup, DistancePoint } from '../atoms';
import { distanceGroupsAtom } from '../atoms';

interface Props {
  groupId: string;
  points: DistancePoint[];
  viewport: Viewport;
}

const DisMarkerList: React.FC<Props> = ({ groupId, points, viewport }) => {
  const [disGroups, setDisGroups] = useAtom(distanceGroupsAtom);

  return (
    <>
      {points.map((point, index) => {
        const [left, top] = viewport.project(point.coordinate);
        return (
          <DisMarker
            key={point.id}
            style={{ left: left + 4, top }}
            onClick={() => {
              if (index === 0) {
                setDisGroups((prev: DistanceGroup[]) =>
                  prev.filter((group) => group.id !== groupId),
                );
                document.getElementById(groupId)?.remove();
              } else {
                const find = disGroups.find((group) => group.id === groupId);

                // 如果只剩一个起点，直接删除此组
                if (find && find.points.length === 2) {
                  setDisGroups((prev) =>
                    prev.filter((group) => group.id !== groupId),
                  );
                  document.getElementById(groupId)?.remove();
                } else {
                  setDisGroups((prev) =>
                    prev.map((group) =>
                      group.id === groupId
                        ? {
                            ...group,
                            points: group.points.filter(
                              (p) => p.id !== point.id,
                            ),
                          }
                        : group,
                    ),
                  );
                }
              }
            }}
          >
            {index === 0 ? (
              <>
                <span>起点 </span> <i className="icomoon icon-trash" />
              </>
            ) : (
              <>
                {' '}
                {point.distance.toFixed(2)}千米
                <i className="icomoon icon-close" />
              </>
            )}
          </DisMarker>
        );
      })}
    </>
  );
};

export default DisMarkerList;
