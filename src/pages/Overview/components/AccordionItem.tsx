import { <PERSON><PERSON><PERSON><PERSON>, TriangleIcon } from '@/components/ui';
import { AccordionHead, Switch, AccordionBody } from './ui';
import { useSpring } from 'react-spring';

type Props = {
  open?: boolean;
  switchChecked?: boolean;
  title: string;
  height?: number;
  onSwitchClick: () => void;
  onClick: () => void;
};

const AccordionItem: React.FC<Props> = ({
  open,
  title,
  height,
  children,
  switchChecked,
  onSwitchClick,
  onClick,
}) => {
  const styles = useSpring({
    a: open ? height || 360 : 0,
    config: {
      duration: 200,
    },
  });

  return (
    <>
      <AccordionHead>
        <HorCenter onClick={onClick}>
          <TriangleIcon
            type="down"
            size={4}
            borderColor="#c1c1c4"
            style={{
              transform: `rotate(${open ? '180deg' : '0'})`,
            }}
          />
          {title}
        </HorCenter>
        <Switch checked={switchChecked} onClick={onSwitchClick} />
      </AccordionHead>
      <AccordionBody
        style={{
          ...styles,
          height: styles.a,
          overflow: 'hidden',
        }}
      >
        {children}
      </AccordionBody>
    </>
  );
};

export default AccordionItem;
