import { fullScreenAtom } from '@/atoms';
import { Center } from '@/components/ui';
import { useOnClickOutside } from '@/hooks';
import { useAtom, useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { createRef, useState } from 'react';
import { useSpring } from 'react-spring';
import { pollutionPanelVisibleAtom, sandSourceLayerVisibleAtom } from '../atoms';
import { LayerBtn, LayerMenu, LayerMenuItem, MenuContainer } from './ui';

// 真彩假彩
const MapVectorControl: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const isFullScreen = useAtomValue(fullScreenAtom);
  const setPoPanelVisible = useUpdateAtom(pollutionPanelVisibleAtom);

  const menuRef = createRef<HTMLDivElement>();
  const [sandSourceLayerVisible, setSandSourceLayerVisible] = useAtom(sandSourceLayerVisibleAtom);

  const style = useSpring({
    opacity: visible && !isFullScreen ? 1 : 0,
    bottom: visible && !isFullScreen ? 90 : 80,
  });

  useOnClickOutside(menuRef, () => {
    setVisible(false);
  });

  return (
    <LayerBtn
      title="地图矢量"
      active={sandSourceLayerVisible}
      ref={menuRef}
      onClick={() => {
        setVisible(!visible);
        setPoPanelVisible(false);
      }}
    >
      <iconpark-icon name="shachenjiance" color={sandSourceLayerVisible ? '#ffffff' : '#B9B9BC'}></iconpark-icon>
      <MenuContainer
        style={{
          ...style,
          pointerEvents: visible && !isFullScreen ? 'auto' : 'none',
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Center>
          <iconpark-icon size="16" name="shachenjiance" color="#B9B9BC"></iconpark-icon>
          <span className="ml-[4px]">地图矢量</span>
        </Center>
        <LayerMenu>
          <LayerMenuItem
            style={{ width: 80 }}
            title={'沙源地'}
            active={sandSourceLayerVisible}
            onClick={(e) => {
              e.stopPropagation();
              setSandSourceLayerVisible(!sandSourceLayerVisible);
            }}
          >
            <div className="label">沙源地</div>
          </LayerMenuItem>
        </LayerMenu>
      </MenuContainer>
    </LayerBtn>
  );
};

export default MapVectorControl;
