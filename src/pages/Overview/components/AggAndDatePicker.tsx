import { CaretDownOutlined } from '@ant-design/icons';
import { Select } from 'antd';
import { useAtom, useSetAtom } from 'jotai';
import { aggAtom, weatherLayersStatusAtom } from '../atoms';
import { Agg } from '../types';
import RangePicker from './RangePicker';
import { Label, PickerContainer } from './ui';

const option: { label: string; value: Agg }[] = [
  {
    label: '时',
    value: 'hourly',
  },
  {
    label: '日',
    value: 'daily',
  },
  {
    label: '月',
    value: 'monthly',
  },
  {
    label: '年',
    value: 'yearly',
  },
  {
    label: '自定义',
    value: 'custom',
  },
];

export const AggAndDatePicker = () => {
  const [agg, setAgg] = useAtom(aggAtom);
  const setWeatherLayersStatus = useSetAtom(weatherLayersStatusAtom);

  return (
    <PickerContainer>
      <Label>时间筛选</Label>
      <div className="flex items-center mt-[12px] gap-[4px]">
        <Select
          className="dark-form-item w-[90px]"
          options={option}
          value={agg}
          onChange={(ag) => {
            if (ag !== 'hourly') {
              setWeatherLayersStatus((prev) => {
                return prev.map((v) => {
                  if (v.label === '风向') {
                    return {
                      ...v,
                      visible: false,
                    };
                  }

                  return v;
                });
              });
            }

            setAgg(ag);
          }}
          suffixIcon={
            <CaretDownOutlined
              style={{
                pointerEvents: 'none',
              }}
            />
          }
          popupMatchSelectWidth={90}
        />
        <RangePicker />
      </div>
    </PickerContainer>
  );
};
