import { Hor<PERSON>enter } from '@/components/ui';
import { array_, number_ } from '@/utils/format';
import { legendSection } from '@/utils/legendSection';
import { InputNumber, message, Modal, Slider } from 'antd';
import { useAtom } from 'jotai';
import { first, last } from 'lodash';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { customLegendModalAtom } from '../../atoms';
import useGetLegend, { DIFF_LAYER_PREFIX, DIFF_PERSANGE_LAYER_PREFIX, getMinMAxByLegned } from '../../hooks/useGetLegend';
import './index.less';

const CustomLegendModal = () => {
  const [customLegendModal, setCustomLegendModal] = useAtom(customLegendModalAtom);
  const open = useMemo(() => !!customLegendModal, [customLegendModal]);
  const pltType = useMemo(() => customLegendModal?.pltType, [customLegendModal?.pltType]);
  const legendType = useMemo(() => customLegendModal?.legendType as string, [customLegendModal?.legendType]);
  const { legendInfo, handleSetLegend, handleDelLegend, defaultLegend } = useGetLegend(pltType, legendType);
  const sliderRef = useRef(null);
  const isDiffLayer = new RegExp(`^${DIFF_LAYER_PREFIX}`).test(pltType);
  const isDiffPercentageLayer = new RegExp(`^${DIFF_PERSANGE_LAYER_PREFIX}`).test(pltType);

  let { max: defaultMax, min: defaultMin } = useMemo(() => {
    return (
      legendSection[legendType as 'remote' | 'station']?.[pltType] || {
        max: 1,
        min: 0,
      }
    );
  }, [legendType, pltType]);

  const { min: defaultMinByLegend, max: defaultMaxByLegend } = getMinMAxByLegned(defaultLegend);
  const defaultAbsMax = Math.max(Math.abs(defaultMinByLegend), Math.abs(defaultMaxByLegend));
  if (isDiffLayer) {
    defaultMin = -defaultAbsMax ?? -100;
    defaultMax = defaultAbsMax ?? 100;
  }

  if (isDiffPercentageLayer) {
    defaultMin = -100;
    defaultMax = 100;
  }

  // 解决 sliderMarker 重叠问题
  useEffect(() => {
    try {
      const sliderDom = document.querySelector('.custom-slider');
      if (sliderDom) {
        const sliderMarks = sliderDom?.querySelectorAll('.ant-slider-mark-text');
        const sliderMarksCounts = sliderMarks.length;
        let count = 1;

        while (count < sliderMarksCounts) {
          const currentEl = sliderMarks[count] as HTMLDivElement;
          const preEl = sliderMarks[count - 1] as HTMLDivElement;
          const nextEl = sliderMarks[count + 1] as HTMLDivElement;

          // 判断最后一个与前一个是否重叠，如果重叠，隐藏前一个
          if (count === sliderMarksCounts - 1) {
            if (currentEl.offsetLeft - preEl.offsetLeft < preEl.clientWidth) {
              preEl.style.display = 'none';
            }
            break;
          }

          // 判断当前元素是否重叠，如果重叠，隐藏当前元素
          if (nextEl.offsetLeft - preEl.offsetLeft < currentEl.clientWidth + preEl.clientWidth) {
            currentEl.style.display = 'none';
          }

          count++;
        }
      }
    } catch (e) {
      console.error(e);
    }
  }, []);

  const numbers = useMemo(() => {
    return array_(legendInfo?.values).filter(({ min, max }) => min !== undefined || max !== undefined);
  }, [legendInfo?.values]);

  const min = useMemo(() => number_(last(numbers)?.max), [numbers]);
  const max = useMemo(() => number_(first(numbers)?.min), [numbers]);

  const id = useMemo(() => legendInfo?.id, [legendInfo?.id]);

  const [miniMum, setminiMum] = useState(min);
  const [maxiMum, setmaxiMum] = useState(max);

  const formatter = (value: number | undefined) => (value === miniMum ? `Min:${miniMum}` : `Max:${maxiMum}`);

  const handleCancel = useCallback(() => {
    setCustomLegendModal(undefined);
  }, [setCustomLegendModal]);

  const handleOK = useCallback(() => {
    if (number_(maxiMum) <= number_(miniMum)) return message.error('最大值必须大于最小值');
    handleSetLegend.mutate({
      max: maxiMum,
      min: miniMum,
      pollutionType: String(pltType).toUpperCase(),
      type: legendType === 'remote' ? 1 : 2,
    });
    setCustomLegendModal(undefined);
  }, [handleSetLegend, legendType, maxiMum, miniMum, pltType, setCustomLegendModal]);

  return (
    <Modal okText="提交" cancelText="以后再说" width={684} visible={open} onOk={handleOK} onCancel={handleCancel} className="custom-legend">
      <div>
        <div className="sub-title ">
          <i className="icon " />
          <span className="name ">自定义色带阈值</span>
        </div>
        <div className="main-container">
          <div className="top">
            <HorCenter className="title">浓度阈值</HorCenter>
            <Slider
              className="custom-slider"
              ref={sliderRef}
              tooltip={{ formatter }}
              dots={false}
              marks={{ [defaultMin]: defaultMin, [defaultMax]: defaultMax }}
              min={defaultMin}
              max={defaultMax}
              step={0.01}
              range={{ draggableTrack: true }}
              value={[miniMum as number, maxiMum as number]}
              onChange={(value: number[]) => {
                setminiMum(value[0]);
                setmaxiMum(value[1]);
              }}
            />
          </div>
          <div className="middle">
            <div>
              <div>最小值</div>
              <InputNumber
                controls={false}
                max={defaultMax}
                min={defaultMin}
                className="input-number"
                value={miniMum}
                onChange={(e) => setminiMum(e as number)}
              />
            </div>
            <span style={{ marginTop: 20 }}>-</span>
            <div>
              <div>最大值</div>
              <InputNumber
                controls={false}
                max={defaultMax}
                min={defaultMin}
                className="input-number"
                value={maxiMum}
                onChange={(e) => setmaxiMum(e as number)}
              />
            </div>
            <div style={{ marginTop: 20 }}>
              <div
                className="setting-default"
                onClick={() => {
                  if (id !== undefined) {
                    handleDelLegend.mutate(id);
                    handleCancel();
                  } else {
                    message.warning('当前已是默认值');
                  }
                  // setminiMum(defaultMin);
                  // setmaxiMum(defaultMax);
                }}
              >
                恢复默认值
              </div>
            </div>
          </div>
          <div className="bottom">
            <div>1.可通过滑动输入条或输入框设置最小和最大值；</div>
            <div>2.最大值不可小于最小值；</div>
            <div>3.点击【恢复默认值】可将最小值和最大值恢复致默认值；</div>
            <div>4.点击【提交】系统对最小和最大值按照色带分级进行平分，并重新渲染对应的图层数据；</div>
            <div>5.点击【以后再说】关闭弹窗，值域不变；</div>
            <div>6.自定义色带只对操作账号生效。</div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default memo(CustomLegendModal);
