import { fullScreenAtom } from '@/atoms';
import { useAtomValue } from 'jotai';
import { Scale } from './ui';

const PlottingScale: React.FC<{
  val: string;
}> = ({ val }) => {
  const isFullScreen = useAtomValue(fullScreenAtom);

  return (
    <>
      {val && (
        <Scale
          style={{
            bottom: isFullScreen ? 12 : 118,
          }}
        >
          {val}
        </Scale>
      )}
    </>
  );
};

export default PlottingScale;
