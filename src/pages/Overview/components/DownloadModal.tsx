import { regionAtom } from '@/atoms';
import DownloadAnimation from '@/components/DownloadAnimation';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import { WebMercatorViewport } from 'deck.gl/typed';
import { useAtom, useAtomValue } from 'jotai';
import { openDownloadModalAtom, viewStateAtom } from '../atoms';
import useTimeDustPoints from '../hooks/useDustTimePoints';

const DownloadModal = () => {
  const timePoints = useTimeDustPoints();
  const [open, setOpen] = useAtom(openDownloadModalAtom);
  const region = useAtomValue(regionAtom);
  const { regionMap } = useCascaderOptionsAndMatchValues(region);
  const viewState = useAtomValue(viewStateAtom);
  const viewport = new WebMercatorViewport(viewState);

  if (region === undefined) {
    return null;
  }

  return (
    <DownloadAnimation
      regionCode={[region]}
      timePoints={timePoints.map((t) => ({
        time: t.label,
        haveDust: !!t.haveDust,
      }))}
      dustName={regionMap.get(region)}
      open={!!open}
      onCancle={() => setOpen(false)}
      bounds={viewport.getBounds()}
    />
  );
};
export default DownloadModal;
