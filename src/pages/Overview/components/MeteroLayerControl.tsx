import { fullScreenAtom } from '@/atoms';
import { Center } from '@/components/ui';
import { useOnClickOutside } from '@/hooks';
import { message } from 'antd';
import { useAtom, useAtomValue, useSetAtom, useSetAtom as useUpdateAtom } from 'jotai';
import { createRef, useEffect, useState } from 'react';
import { useSpring } from 'react-spring';
import {
  aggAtom,
  alertPanelVisibleAtom,
  colorLayerTypeAtom,
  dustForecastLayerVisibleAtom,
  dustRangeLayerVisibleAtom,
  fetchTextureWeatherTypeAtom,
  pollutionLayersVisibleAtom,
  pollutionPanelVisibleAtom,
  showWindMapAtom,
  timelineStatusAtom,
  timeTypeAtom,
  weatherLayersStatusAtom,
} from '../atoms';
import { LayerBtn, LayerMenu, LayerMenuItem, MenuContainer } from './ui';

// 气象图层控制组件
const MeteroLayerControl: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [weatherLayersStatus, setWeatherLayersStatus] = useAtom(weatherLayersStatusAtom);
  const timeType = useAtomValue(timeTypeAtom);
  const setPollutionLayersVisible = useUpdateAtom(pollutionLayersVisibleAtom);
  const setPollutionPanelVisible = useUpdateAtom(pollutionPanelVisibleAtom);
  const setAlertPanelVisible = useUpdateAtom(alertPanelVisibleAtom);
  const setFetchTextureWeatherType = useUpdateAtom(fetchTextureWeatherTypeAtom);
  const setShowWindMap = useUpdateAtom(showWindMapAtom);
  const setColorLayerType = useSetAtom(colorLayerTypeAtom);
  const setDustRangeLayerVisible = useSetAtom(dustRangeLayerVisibleAtom);
  const dustForecastLayerVisible = useSetAtom(dustForecastLayerVisibleAtom);
  const agg = useAtomValue(aggAtom);

  const [timelineStatus, setTimelineStatus] = useAtom(timelineStatusAtom);
  const menuRef = createRef<HTMLDivElement>();

  const style = useSpring({
    opacity: visible && !isFullScreen ? 1 : 0,
    bottom: visible && !isFullScreen ? 90 : 80,
  });

  useOnClickOutside(menuRef, () => {
    setVisible(false);
  });

  useEffect(() => {
    if (timeType === 'date') {
      setWeatherLayersStatus((prev) =>
        prev.map((item) => ({
          ...item,
          visible: false,
        })),
      );

      setFetchTextureWeatherType(null);
    }
  }, [setFetchTextureWeatherType, setWeatherLayersStatus, timeType]);

  const hideColorLayers = () => {
    setColorLayerType('');
    setDustRangeLayerVisible(false);
    dustForecastLayerVisible(false);
  };

  return (
    <LayerBtn
      title="气象图层"
      active={weatherLayersStatus.some((item) => item.visible)}
      ref={menuRef}
      onClick={() => {
        setPollutionPanelVisible(false);
        setAlertPanelVisible(false);
        setVisible(!visible);
      }}
    >
      <i className="icomoon icon-layer-2 !text-[16px]" />
      <MenuContainer
        style={{
          ...style,
          pointerEvents: visible && !isFullScreen ? 'auto' : 'none',
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Center>
          <i className="icomoon icon-layer-2" />
          气象
        </Center>
        <LayerMenu>
          {weatherLayersStatus.map((item) => (
            <LayerMenuItem
              key={item.label}
              active={item.visible}
              style={{ width: 80 }}
              onClick={(e) => {
                e.stopPropagation();
                if (item.label === '风向' && agg !== 'hourly') {
                  message.error('该图层不支持当前颗粒度');
                  return;
                }

                setTimelineStatus({ ...timelineStatus, isStarted: false });
                const isClickingWindMenuItem = item.label === '风向';

                if (item.label !== '风向' && item.value) {
                  if (!item.visible) {
                    setFetchTextureWeatherType(item.value);
                    setPollutionLayersVisible((prev) => ({
                      ...prev,
                      satiRemote: false,
                    }));
                    hideColorLayers();
                  } else {
                    setFetchTextureWeatherType(null);
                  }
                }

                setWeatherLayersStatus((prevState) =>
                  prevState.map((stateItem) => {
                    if (isClickingWindMenuItem) {
                      if (stateItem.label === item.label) {
                        setShowWindMap(!stateItem.visible);
                        return {
                          ...stateItem,
                          visible: !stateItem.visible,
                        };
                      }

                      return stateItem;
                    }

                    if (item.label === stateItem.label) {
                      return {
                        ...stateItem,
                        visible: !stateItem.visible,
                      };
                    }

                    if (stateItem.label === '风向') {
                      return stateItem;
                    }
                    return {
                      ...stateItem,
                      visible: false,
                    };
                  }),
                );
              }}
            >
              {item.label}
            </LayerMenuItem>
          ))}
        </LayerMenu>
      </MenuContainer>
    </LayerBtn>
  );
};

export default MeteroLayerControl;
