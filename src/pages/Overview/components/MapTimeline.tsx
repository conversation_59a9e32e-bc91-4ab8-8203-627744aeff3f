import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { useAtom, useAtomValue } from 'jotai';
import { memo, useEffect, useState } from 'react';
import { activeTimelineItemAtom, aggAtom, datePickerValAtom } from '../atoms';
import useDustTimePoints from '../hooks/useDustTimePoints';
import Timeline from './Timeline';
import { TimelineListItem } from './Timeline/TimelineItem';

dayjs.extend(isSameOrBefore);

const MapTimeline: React.FC = () => {
  const [activeTimelineItem, setActiveTimelineItem] = useAtom(activeTimelineItemAtom);
  const agg = useAtomValue(aggAtom);
  const [timelineList, setTimelineList] = useState<TimelineListItem[]>([]);
  const datePickerVal = useAtomValue(datePickerValAtom);
  const dustTimePoints = useDustTimePoints();

  useEffect(() => {
    if (['daily', 'custom', 'monthly', 'yearly'].includes(agg)) {
      const [startDate, endDate] = datePickerVal || [];

      // 根据 dateType 和 datePickerVal 生成 timelineList
      const list: TimelineListItem[] = [];
      const start = dayjs(startDate);
      const end = dayjs(endDate);

      let current = start;
      while (current.isSameOrBefore(end)) {
        list.push({
          id: current.format('YYYY/MM/DD HH:mm:ss') + '-0-false',
          label: current.format('YYYY/MM/DD HH:mm:ss'),
          detailLabel: current.format('YYYY/MM/DD HH:mm:ss'),
          value: current.format('YYYY/MM/DD HH:mm:ss'),
          haveDust: false,
          isStartPoint: false,
        });

        // 根据不同的聚合类型增加不同的时间间隔
        if (agg === 'daily') {
          current = current.add(1, 'day');
        } else if (agg === 'monthly') {
          current = current.add(1, 'month');
        } else if (agg === 'yearly') {
          current = current.add(1, 'year');
        } else {
          // custom 类型，默认按天
          current = current.add(1, 'day');
        }
      }

      setTimelineList(list);
      setActiveTimelineItem(list[list.length - 1]);
    } else {
      setTimelineList(dustTimePoints);
      setActiveTimelineItem(dustTimePoints[dustTimePoints.length - 1]);
    }
  }, [agg, datePickerVal, dustTimePoints, setActiveTimelineItem]);

  return (
    <Timeline
      activeItem={activeTimelineItem}
      list={timelineList}
      setActiveItem={(item) => {
        if (item) {
          setActiveTimelineItem(item);
        }
      }}
    />
  );
};

export default memo(MapTimeline);
