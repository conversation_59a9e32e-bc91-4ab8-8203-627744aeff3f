import React from 'react';
import turfBboxPolygon from '@turf/bbox-polygon';
import turfArea from '@turf/area';
import type { AreaPoint } from '../atoms';
import { areaGroupsAtom } from '../atoms';
import { useSetAtom as useUpdateAtom } from 'jotai';
import type Viewport from '@deck.gl/core/viewports/viewport';
import { AreaRect } from '@/components/ui';

interface Props {
  groupId: string;
  points: AreaPoint[];
  viewport: Viewport;
}
const MeasuringAreaRect: React.FC<Props> = ({ groupId, points, viewport }) => {
  const setAreaGroups = useUpdateAtom(areaGroupsAtom);

  if (points.length === 2) {
    const [tl, br] = points;

    const bbox = [...tl.coordinate, ...br.coordinate];
    //  @ts-ignore
    const poly = turfBboxPolygon(bbox);
    const areaVal = turfArea(poly);

    const [tlLeft, tlTop] = viewport.project(tl.coordinate);
    const [brLeft, brTop] = viewport.project(br.coordinate);

    return (
      <AreaRect
        style={{
          top: Math.min(tlTop, brTop),
          left: Math.min(tlLeft, brLeft),
          width: Math.abs(tlLeft - brLeft),
          height: Math.abs(tlTop - brTop),
        }}
      >
        <div
          className="content"
          onClick={() => {
            setAreaGroups((prev) =>
              prev.filter((group) => group.id !== groupId),
            );
          }}
        >
          {(areaVal / 1000000).toFixed(2)}平方千米
          <i className="icomoon icon-trash" />
        </div>
      </AreaRect>
    );
  }

  return null;
};

export default MeasuringAreaRect;
