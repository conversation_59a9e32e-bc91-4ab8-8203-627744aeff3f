import { satRemoteOptions } from '@/utils';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { aggAtom, remotePoTypeAtom } from '../atoms';
import { Item } from './ui';

export default function PollutionSelect() {
  const [fetchTextureType, setFetchTextureType] = useAtom(remotePoTypeAtom);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const agg = useAtomValue(aggAtom);

  const checkScrollability = useCallback(() => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
    }
  }, []);

  const handleScroll = useCallback(() => {
    checkScrollability();
  }, [checkScrollability]);

  const scrollLeft = useCallback(() => {
    if (scrollContainerRef.current && canScrollLeft) {
      scrollContainerRef.current.scrollBy({
        left: -100,
        behavior: 'smooth',
      });
    }
  }, [canScrollLeft]);

  const scrollRight = useCallback(() => {
    if (scrollContainerRef.current && canScrollRight) {
      scrollContainerRef.current.scrollBy({
        left: 100,
        behavior: 'smooth',
      });
    }
  }, [canScrollRight]);

  // 初始化和内容变化时检查滚动状态
  useEffect(() => {
    checkScrollability();
  }, [checkScrollability]);

  // 当颗粒度变化时，如果当前图层不支持该颗粒度，则切换为 pm10
  useEffect(() => {
    if (fetchTextureType && satRemoteOptions.find((item) => item.value === fetchTextureType)?.disabledAggs?.includes(agg)) {
      setFetchTextureType('pm10');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [agg]);

  return (
    <div className="flex items-center justify-center relative overflow-hidden px-[10px]">
      {/* 左侧按钮 */}
      <div
        className={twMerge(
          'absolute flex items-center justify-center top-[2px] left-[10px] w-5 h-5 cursor-pointer',
          !canScrollLeft && 'opacity-50 cursor-not-allowed',
        )}
        onClick={scrollLeft}
      >
        <LeftOutlined className={twMerge('text-[#C1C1C4] text-[12px]')} />
      </div>

      <div className="mx-5 flex items-center gap-3 overflow-x-auto horizontal-scroll-bar" ref={scrollContainerRef} onScroll={handleScroll}>
        {satRemoteOptions.map((item) => {
          const isDisabled = item.disabledAggs?.includes(agg);
          return (
            <Item
              key={item.value}
              bordered
              active={item.value === fetchTextureType}
              disabled={isDisabled}
              onClick={() => {
                if (isDisabled) {
                  message.warning('该图层不支持当前选中颗粒度');
                  return;
                }
                if (fetchTextureType === item.value) return;
                setFetchTextureType(item.value);
              }}
            >
              <div className="text-[14px] text-center">{item.title}</div>
              <div className="text-[10px] leading-[14px] text-center h-[14px]">{item.subTitle}</div>
            </Item>
          );
        })}
      </div>

      {/* 右侧按钮 */}
      <div
        className={twMerge(
          'absolute flex items-center justify-center right-[10px] top-[2px] w-5 h-5  cursor-pointer',
          !canScrollRight && 'opacity-50 cursor-not-allowed',
        )}
        onClick={scrollRight}
      >
        <RightOutlined className={twMerge('text-[#C1C1C4] text-[12px]')} />
      </div>
    </div>
  );
}
