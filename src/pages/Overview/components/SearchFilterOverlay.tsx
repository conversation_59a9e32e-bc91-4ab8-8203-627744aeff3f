import React from 'react';
import { Checkbox } from 'antd';
import {
  CheckboxContainer,
  OverlayContainer,
  OverlayContent,
  OverlayFooter,
} from './ui';
import { SmStyledButton, Spacer } from '@/components/ui';
import type { CheckboxValueType } from 'antd/lib/checkbox/Group';

const SearchFilterOverlay: React.FC<{
  value: CheckboxValueType[] | number[];
  options?: {
    label: string;
    value: string | number;
  }[];
  handleCheckboxChange?: (val: CheckboxValueType[]) => void;
  handleSubmit: () => void;
  handleCancel: () => void;
  content?: React.ReactNode;
}> = ({
  value,
  options,
  content,
  handleCheckboxChange,
  handleSubmit,
  handleCancel,
}) => {
  return (
    <OverlayContainer>
      <OverlayContent>
        {content || (
          <Checkbox.Group
            value={value}
            onChange={(val) => {
              if (handleCheckboxChange) {
                handleCheckboxChange(val);
              }
            }}
          >
            {(options ?? []).map((opt) => (
              <CheckboxContainer key={opt.value}>
                <Checkbox value={opt.value}>{opt.label}</Checkbox>
              </CheckboxContainer>
            ))}
          </Checkbox.Group>
        )}
      </OverlayContent>
      <OverlayFooter>
        {value.length > 0 && (
          <SmStyledButton onClick={handleCancel}>重置</SmStyledButton>
        )}
        <Spacer />
        <SmStyledButton variant="primary" onClick={handleSubmit}>
          确定
        </SmStyledButton>
      </OverlayFooter>
    </OverlayContainer>
  );
};

export default SearchFilterOverlay;
