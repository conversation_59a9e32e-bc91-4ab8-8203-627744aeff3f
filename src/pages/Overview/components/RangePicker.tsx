import { datetimeFormatter } from '@/utils';
import { DatePicker, DatePickerProps } from 'antd';
import locale from 'antd/es/date-picker/locale/zh_CN';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useAtom, useAtomValue } from 'jotai';
import { useEffect, useMemo } from 'react';
import { aggAtom, datePickerValAtom, isAggHalfHourAtom, isSelectedImageLayerAtom } from '../atoms';
import { Agg, AGG_TO_DATE_TYPE } from '../types';

type RangeValue = [Dayjs | null, Dayjs | null] | null;

type RangePickerConfig = {
  dateType: AGG_TO_DATE_TYPE;
  format: string;
  defaultRange: [string, string];
};

const rangePickerConfig: Record<Agg, RangePickerConfig> = {
  hourly: {
    dateType: AGG_TO_DATE_TYPE.hourly,
    format: 'YYYY-MM-DD HH时',
    // 近12个小时
    defaultRange: [dayjs().subtract(11, 'hour').startOf('hour').format(datetimeFormatter), dayjs().endOf('hour').format(datetimeFormatter)],
  },
  daily: {
    dateType: AGG_TO_DATE_TYPE.daily,
    format: 'YYYY-MM-DD',
    // 近7天(包括当天)
    defaultRange: [dayjs().subtract(6, 'day').startOf('day').format(datetimeFormatter), dayjs().endOf('day').format(datetimeFormatter)],
  },
  monthly: {
    dateType: AGG_TO_DATE_TYPE.monthly,
    format: 'YYYY-MM',
    // 近6个月(包括当月)
    defaultRange: [dayjs().subtract(5, 'month').startOf('month').format(datetimeFormatter), dayjs().endOf('day').format(datetimeFormatter)],
  },
  yearly: {
    dateType: AGG_TO_DATE_TYPE.yearly,
    format: 'YYYY',
    // 上一个完整年度
    defaultRange: [
      dayjs().subtract(1, 'year').startOf('year').format(datetimeFormatter),
      dayjs().subtract(1, 'year').endOf('year').format(datetimeFormatter),
    ],
  },
  custom: {
    dateType: AGG_TO_DATE_TYPE.custom,
    format: 'YYYY-MM-DD',
    // 近7天(包括当天)
    defaultRange: [dayjs().subtract(6, 'day').startOf('day').format('YYYY/MM/DD'), dayjs().endOf('day').format('YYYY/MM/DD')],
  },
};

const halfHourRange = 2 * 24 - 1;
const hourRange = 7 * 24 - 1;

const RangePicker = () => {
  const agg = useAtomValue(aggAtom);
  const [dateRange, setDateRange] = useAtom(datePickerValAtom);
  const isSelectedImageLayer = useAtomValue(isSelectedImageLayerAtom);

  const dateType = rangePickerConfig[agg].dateType;
  const isAggHalfHour = useAtomValue(isAggHalfHourAtom);

  const maxSpan = useMemo<Record<Agg, { unit: dayjs.ManipulateType; value: number }>>(() => {
    return {
      hourly: { unit: 'hour', value: isAggHalfHour ? halfHourRange : hourRange }, // 7天换算成小时
      daily: { unit: 'day', value: 29 },
      // 12个月
      monthly: { unit: 'month', value: 11 },
      // 8年
      yearly: { unit: 'year', value: 7 },
      custom: { unit: 'day', value: 29 },
    };
  }, [isAggHalfHour]);

  const disableDate: DatePickerProps['disabledDate'] = (current, { from, type }) => {
    // 禁用未来日期
    if (current.isAfter(dayjs().endOf('day'))) return true;

    const { unit, value } = maxSpan[agg];

    if (from) {
      // 根据时间单位计算最大范围
      const minDate = from.subtract(value, unit);
      const maxDate = from.add(value, unit);

      switch (type) {
        case 'year':
          return current.year() < minDate.year() || current.year() > maxDate.year();

        case 'month':
          const getYearMonth = (date: Dayjs) => date.year() * 12 + date.month();
          return getYearMonth(current) < getYearMonth(minDate) || getYearMonth(current) > getYearMonth(maxDate);

        case 'date':
        default:
          // 对于 hourly 模式，需要使用 hour 作为比较单位
          // if (agg === 'hourly') {
          //   // 获取当前日期的小时
          //   const currentDateTime = current.hour(current.hour());
          //   // 获取选中日期的小时
          //   const fromDateTime = from.hour(from.hour());

          //   // 计算小时差
          //   const hoursDiff = Math.abs(currentDateTime.diff(fromDateTime, 'hour'));
          //   const maxHours = isAggHalfHour ? halfHourRange : hourRange;

          //   // 如果小时差超过最大小时数，直接禁用
          //   if (hoursDiff > maxHours) {
          //     return true;
          //   }

          //   // 如果是最大小时数的边界，需要检查方向
          //   if (hoursDiff === maxHours) {
          //     // 如果当前时间在选择时间之前，禁用
          //     if (currentDateTime.isBefore(fromDateTime)) {
          //       return true;
          //     }
          //   }

          //   return false;
          // }

          const daysDiff = current.diff(from, 'day');
          const fromHour = from.hour();
          // 为适配先选end的情况，判断是否需要多选一天
          // 如果是hourly聚合，且fromHour小于23，且daysDiff小于0，则需要多选一天
          // 例如：2025-07-01 00：00 ~ 2025-07-07 23:00 为 7 * 24小时
          // 2025-07-01 02:00 ~ 2025-07-08 01:00 为 7 * 24小时
          const needOneMoreDay = agg === 'hourly' && fromHour < 23 && daysDiff < 0;

          return current.isBefore(minDate.subtract(needOneMoreDay ? 1 : 0, 'day'), 'hour') || current.isAfter(maxDate, 'hour');
      }
    }

    return false;
  };

  // 根据 agg，初始化 datePickerValueAtom 的默认值
  useEffect(() => {
    const defaultRange = rangePickerConfig[agg].defaultRange;
    setDateRange(defaultRange);
  }, [agg, setDateRange]);

  // 半小时颗粒度，最大可选范围是 2 * 24 小时，所以需要重置
  useEffect(() => {
    if (isAggHalfHour) {
      const [start, end] = dateRange || [];
      if (start && end) {
        const diff = dayjs(end).diff(dayjs(start), 'hour');
        if (diff > halfHourRange) {
          const defaultRange = rangePickerConfig['hourly'].defaultRange;
          setDateRange(defaultRange);
        }
      }
    }
  }, [dateRange, isAggHalfHour, setDateRange]);

  const disabledTime: DatePickerProps['disabledTime'] = (current, range, { from }) => {
    // 仅在hourly聚合时生效
    if (agg !== 'hourly' || !from || !current) return {};

    const allHours = Array.from({ length: 24 }, (_, i) => i);
    const daysDiff = current.diff(from, 'day');
    const daysDiffAbs = Math.abs(current.diff(from, 'day'));
    const maxDays = isAggHalfHour ? 2 : 7; // 半小时模式为2天，其他为7天

    // 如果超过最大范围，禁用所有时间
    if (daysDiffAbs > maxDays) {
      return { disabledHours: () => allHours };
    }

    // 如果在最大范围内，禁用当前小时之前的时间
    const fromHour = from.hour();
    const disabledHours = () => {
      if (daysDiffAbs >= maxDays - 1) {
        if (fromHour === 0) {
          return [];
        }
        if (daysDiff < 0) {
          if (fromHour === 23) {
            return [];
          }

          return allHours.slice(0, fromHour + 1); // 如果当前时间在选择时间之前，禁用从0到fromHour的小时
        }
        return allHours.slice(fromHour);
      }
      return [];
    };
    return {
      disabledHours,
    };
  };

  return (
    <div className="flex items-center">
      <i className="icomoon icon-calendar" />
      <DatePicker.RangePicker
        suffixIcon={null}
        className="dark-form-item"
        locale={locale}
        bordered={false}
        allowClear={false}
        style={{ fontSize: 16 }}
        picker={dateType.toLowerCase() as 'date' | 'month' | 'year'}
        format={rangePickerConfig[agg].format}
        showTime={agg === 'hourly' ? { format: 'HH', hideDisabledOptions: false } : false}
        placement="topLeft"
        disabledDate={disableDate}
        disabledTime={disabledTime}
        placeholder={['开始日期', '结束日期']}
        value={dateRange ? ([dayjs(dateRange?.[0]), dayjs(dateRange?.[1])] as any) : undefined}
        onChange={(dates) => {
          if (dates?.length === 2 && dates[0] && dates[1]) {
            const [sd, ed] = dates;
            const unitTypeMapping = {
              hourly: 'hour',
              daily: 'day',
              monthly: 'month',
              yearly: 'year',
              custom: 'day',
            };
            const unitType = (unitTypeMapping[agg] || 'day') as dayjs.OpUnitType;
            setDateRange([sd.startOf(unitType).format(datetimeFormatter), ed.endOf(unitType).format(datetimeFormatter)]);
          }
        }}
      />
    </div>
  );
};

export default RangePicker;
