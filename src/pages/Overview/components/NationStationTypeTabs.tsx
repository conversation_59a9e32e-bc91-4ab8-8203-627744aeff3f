import { stationTypeOptions } from '@/utils';
import { useAtom } from 'jotai';
import { memo } from 'react';
import { nationStationPoTypeAtom } from '../atoms';
import { Item, ItemsContainer } from './ui';

const NationStationTypeTabs = () => {
  const [nationStationType, setNationStationType] = useAtom(
    nationStationPoTypeAtom,
  );
  return (
    <ItemsContainer>
      {stationTypeOptions.map((item) => (
        <Item
          key={item.value}
          active={nationStationType === item.value}
          onClick={() => setNationStationType(item.value)}
        >
          {item.label}
        </Item>
      ))}
    </ItemsContainer>
  );
};

export default memo(NationStationTypeTabs);
