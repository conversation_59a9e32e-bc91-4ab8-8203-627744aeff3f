import { twMerge } from 'tailwind-merge';

interface Props {
  label: string;
  disabled?: boolean;
  onClick?: () => void;
}
const Capsule = ({ label, onClick, disabled }: Props) => {
  return (
    <div
      className={twMerge(
        'h-7 mb-1 px-4 inline-flex items-center rounded-full max-w-[168px] truncate text-sm cursor-pointer hover:!bg-[#286CFF]/10 select-none',
        disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-[#286CFF]/10',
      )}
      title={label}
      onClick={onClick}
      style={{
        background: 'linear-gradient( 180deg, rgba(81, 165, 255, .2) 0%, rgba(40, 108, 255, .2) 100%)',
      }}
    >
      {label}
    </div>
  );
};

export default Capsule;
