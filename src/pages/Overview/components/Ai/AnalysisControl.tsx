import { fullScreenAtom, regionAtom, userInfoAtom } from '@/atoms';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import dayjs from 'dayjs';
import { useAtomValue } from 'jotai';
import { useCallback, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { a, useSpring } from 'react-spring';
import { getAnalysisReportCreate, GetAnalysisReportCreateParams } from '../../services';
import AnalysisPrompt from './AnalysisPrompt';
import Capsule from './Capsule';

const formatter = 'YYYY/MM/DD HH:mm:ss';
const getDateParams = (type: 1 | 2) => {
  // 1. 近24小时沙尘分析报告 2. 昨日颗粒物分析报告
  // 返回开始和结束日期
  const now = new Date();
  if (type === 1) {
    // 近24小时
    return {
      startDate: dayjs(now).subtract(24, 'hour').startOf('hour').format(formatter),
      endDate: dayjs(now).endOf('hour').format(formatter),
    };
  }

  // 昨日
  return {
    startDate: dayjs().subtract(1, 'day').startOf('day').format(formatter),
    endDate: dayjs().subtract(1, 'day').endOf('day').format(formatter),
  };
};

const AnalysisControl = () => {
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [visible, setVisible] = useState(false);
  const userInfo = useAtomValue(userInfoAtom);
  const { regionMap } = useCascaderOptionsAndMatchValues(userInfo?.regionCode);
  const region = useAtomValue(regionAtom);
  const name = useMemo(() => (region === 0 ? '全域' : regionMap.get(region) ?? ''), [region, regionMap]);

  const styles = useSpring({
    opacity: isFullScreen ? 0 : 1,
    top: isFullScreen ? 0 : 40,
  });
  const mutation = useMutation((p: GetAnalysisReportCreateParams) => {
    return getAnalysisReportCreate(p);
  });
  const getRegionCode = useCallback(
    (type: 1 | 2) => {
      if (type === 1) {
        if (region === 0 || region === 150000) {
          return 100000;
        }

        return region;
      }

      if (region === 0) {
        return 150000;
      }

      return region ?? 150000;
    },
    [region],
  );

  const mutate = useCallback(
    (type: 1 | 2) => {
      const { startDate, endDate } = getDateParams(type);
      const regionCode = getRegionCode(type);
      setVisible(true);
      mutation.mutate(
        {
          regionCode: Number(regionCode),
          reportType: type,
          startDate,
          endDate,
        },
        {
          onSuccess() {
            setTimeout(() => {
              setVisible(false);
            }, 1500);
          },
        },
      );
    },
    [getRegionCode, mutation],
  );

  return (
    <>
      {/* AI分析中提示 */}
      <AnalysisPrompt visible={visible} />
      <a.div
        className="w-[230px] h-[122px] absolute right-[124px] z-10"
        style={{
          ...styles,
          background: 'url("/assets/images/ai-bg.png") no-repeat center center',
          backgroundSize: 'cover',
        }}
      >
        <div
          className="w-[120px] h-7 leading-[24px] ml-[96px] mt-2 pl-[10px] text-sm border-[2px] border-primary rounded-full outline-none truncate"
          title={name}
        >
          {name}
        </div>

        <div className="mt-[10px] px-4">
          {['近24小时沙尘分析报告', '昨日颗粒物分析报告'].map((label, index) => {
            const disabled = region === undefined || mutation.isLoading;
            return (
              <Capsule
                key={label}
                label={label}
                disabled={disabled}
                onClick={() => {
                  if (!disabled) {
                    mutate((index + 1) as 1 | 2);
                  }
                }}
              />
            );
          })}
        </div>
      </a.div>
    </>
  );
};

export default AnalysisControl;
