import { a, useTransition } from 'react-spring';

interface Props {
  visible?: boolean;
}

const AnalysisPrompt = ({ visible }: Props) => {
  const transition = useTransition(visible, {
    from: { opacity: 0, y: -40, x: '-50%' },
    enter: { opacity: 1, y: 0, x: '-50%' },
    leave: { opacity: 0, y: -40, x: '-50%' },
  });
  return transition((style, item) =>
    item ? (
      <a.div
        className="fixed top-10 left-1/2 z-50 px-4 py-4 bg-[#1c1d24]/80 text-white pointer-events-none rounded-[12px] backdrop-filter-blur-card"
        style={{
          ...style,
        }}
      >
        <div className="text-center">
          <h3 className="text-base">AI正在分析中</h3>
          <p className="mt-1 text-xs opacity-80">稍后可以在分析报告模块中查看报告内容</p>
        </div>
      </a.div>
    ) : null,
  );
};

export default AnalysisPrompt;
