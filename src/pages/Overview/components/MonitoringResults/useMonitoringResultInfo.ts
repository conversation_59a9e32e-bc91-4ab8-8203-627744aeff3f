// import { getDustRecordRegionInfos } from '@/api-v2';
import { regionAtom } from '@/atoms';
import { getDustRecordRegionInfos } from '@/components/DustDetail/services';
import { ensureArray } from '@/utils/type';
import dayjs from 'dayjs';
import { useAtomValue } from 'jotai';
import { isNil } from 'lodash';
import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { activeTimePointAtom } from '../../atoms/timeline';

type TDustRecordRegionInfoModel = {
  /** 沙团ID 例：202405211030_10 */
  dustId: string;
  /** 沙尘事件Id 例：1 */
  eventId: number;
  /** 数据时间  */
  dataTime: string;
  /** 行政区编码 例：110000 */
  regionCode: number;
  /** 行政区名称 例：北京 */
  region: string;
  /** 沙尘影响面积 例：10 */
  area: number;
};

type TDustRecordRegionModel = {
  /** 数据时间  */
  dataTime: string;
  /** 沙尘开始时间  */
  startTime: string;
  /** 影响面积集合  */
  infoModels: TDustRecordRegionInfoModel[];
};

// const data: TDustRecordRegionModel = {
//   dataTime: '2024-05-21T10:30:00Z',
//   startTime: '2024-05-21T10:00:00Z',
//   infoModels: [],
// };
export function useMonitoringResultInfo(agg: string) {
  const activeTimePoint = useAtomValue(activeTimePointAtom);
  const region = useAtomValue(regionAtom);

  const { data, isLoading } = useQuery(
    ['/api/dust/record/region/infos', activeTimePoint, region],
    // 兼容全域请求中国的 0 -> 100000
    () => {
      return getDustRecordRegionInfos({ dataTime: activeTimePoint!, regionCode: region === 0 ? 100000 : region! });
    },
    {
      enabled: Boolean(activeTimePoint && agg === 'hourly') && isNil(region),
    },
  );

  const activeTime = useMemo(() => {
    return dayjs(activeTimePoint).format('HH:mm');
  }, [activeTimePoint]);

  const activeDate = useMemo(() => {
    return dayjs(activeTimePoint).format('YYYY-MM-DD');
  }, [activeTimePoint]);

  const formattedData = useMemo(() => {
    let infoModels = ensureArray(data?.infoModels).map((item) => {
      return {
        ...item,
        intArea: Number(item.area).toFixed(1),
      };
    });
    if (data?.infoModel) {
      infoModels = [
        {
          ...data.infoModel,
          intArea: Number(data.infoModel.area).toFixed(1),
        },
        ...infoModels,
      ];
    }
    return {
      ...data,
      infoModels,
    };
  }, [data]);

  return { data: formattedData, activeTime, activeDate, isLoading };
}
