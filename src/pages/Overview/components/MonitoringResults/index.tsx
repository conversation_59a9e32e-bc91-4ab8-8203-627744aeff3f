import { fullScreenAtom } from '@/atoms';
import { formatAreaNumber, formatAreaUnit } from '@/utils/formatArea';
import { Spin } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { a, useSpring } from 'react-spring';
import { aggAtom, colorLayerTypeAtom, dustForecastLayerVisibleAtom, dustRangeLayerVisibleAtom } from '../../atoms';
import CardContainer from '../CardContainer';
import { useMonitoringResultInfo } from './useMonitoringResultInfo';

export default function MonitoringResults() {
  const [agg] = useAtom(aggAtom);
  const { data, activeDate, activeTime, isLoading } = useMonitoringResultInfo(agg);
  const isFullscreen = useAtomValue(fullScreenAtom);
  const colorLayerType = useAtomValue(colorLayerTypeAtom);
  const dustRangeLayerVisible = useAtomValue(dustRangeLayerVisibleAtom);
  const dustForecastLayerVisible = useAtomValue(dustForecastLayerVisibleAtom);

  const visible = useMemo(() => {
    const isLayerVisible = colorLayerType || dustRangeLayerVisible || dustForecastLayerVisible;
    return agg === 'hourly' && isLayerVisible;
  }, [agg, colorLayerType, dustForecastLayerVisible, dustRangeLayerVisible]);

  const style = useSpring({
    opacity: isFullscreen ? 0 : 1,
    x: isFullscreen ? -80 : 0,
  });

  if (!visible) {
    return null;
  }

  return (
    <a.div
      className="absolute top-5 left-5"
      style={{
        ...style,
        pointerEvents: isFullscreen ? 'none' : 'auto',
      }}
    >
      <CardContainer>
        <div className="px-[10px] py-[16px] text-white text-[14px]">
          <div className="flex items-center px-[9px]">
            <iconpark-icon className="mr-[6px]" width="26" height="26" name="shijian-d51nc9em"></iconpark-icon>
            <div className="align-bottom pl-1.5">
              <span className="text-[30px] leading-[30px] mr-[9px]">{activeTime}</span>
              <span className="text-[14px] leading-[16px]">{activeDate}</span>
            </div>
          </div>
          <div className="h-0 border-t-[1px] mt-[6px] mb-[10px] border-t-white/20"></div>
          <div className="text-[#FFBA17] px-[9px]">监测结果</div>
          <div className="divide-y divide-dashed divide-white/20">
            {isLoading ? (
              <Spin
                size="small"
                className="monitor-spin"
                style={{
                  color: 'white',
                  marginLeft: 9,
                  marginTop: 9,
                }}
              />
            ) : (
              <>
                {data.infoModels.length > 0 ? (
                  data.infoModels.map((item, index) => {
                    return (
                      <div key={index} className="flex items-end justify-between pt-[2px] pb-[7px] px-[9px] gap-[13px]">
                        <div className="flex items-center">
                          <iconpark-icon className="mr-[3px]" color="#FFBA17" name="dili-6of0o4f2"></iconpark-icon>
                          <p className="ml-1 text-[14px] leading-[20px] shrink-0">{item.region}</p>
                        </div>
                        <div className="flex items-end">
                          <div className="text-[26px] leading-[26px] mr-[3px] shrink-0">{formatAreaNumber(Number(item.intArea))}</div>
                          <div className="text-[12px] leading-[14px] shrink-0">{formatAreaUnit(Number(item.intArea))} k㎡</div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <span className="pl-[9px]">-</span>
                )}
              </>
            )}
          </div>
          {/*
          <div className="flex items-center bg-[#FFB600]/40 rounded-full h-[20px] text-[12px] px-[10px] mt-[3px]">
            沙尘开始时间：{data?.startTime || '-'}
          </div> */}
        </div>
      </CardContainer>
    </a.div>
  );
}
