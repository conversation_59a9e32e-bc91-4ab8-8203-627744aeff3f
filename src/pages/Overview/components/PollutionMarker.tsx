import React from 'react';
import type { Viewport } from 'deck.gl';
import type { Pollution } from '@/types';
import { MapLocMarker } from '@/components/ui';

const PollutionMarker: React.FC<{
  pollutions: Pollution[];
  viewport: Viewport;
  poId: number | undefined;
  handleClick: (id: number) => void;
}> = ({ pollutions, viewport, poId, handleClick }) => {
  return (
    <>
      {pollutions.map((item, index) => {
        const [left, top] = viewport.project([item.lon, item.lat]);
        const isHighLevel = item.level === 99;
        const isCurrent = poId === item.id;

        return (
          <MapLocMarker
            title={item.name}
            key={item.id}
            active={isHighLevel}
            style={{
              left,
              top,
              zIndex: isCurrent ? 20 : 10,
              transform: isCurrent
                ? 'scale(1.3) translate(-50%, -100%)'
                : 'translate(-50%, -100%) scale(1)',
              transformOrigin: 'bottom center',
              transition: 'transform .3s',
            }}
            onClick={() => handleClick(item.id)}
          >
            {index + 1}
          </MapLocMarker>
        );
      })}
    </>
  );
};

export default PollutionMarker;
