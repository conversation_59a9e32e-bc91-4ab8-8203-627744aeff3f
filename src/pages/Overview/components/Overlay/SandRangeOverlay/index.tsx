import { hoveredSandRangeDataAtom } from '@/pages/Overview/atoms';
import { useAtomValue } from 'jotai';
import { memo, useMemo } from 'react';
import usePredictionData from './usePredictionData';

function SandRangeOverlay() {
  const hoveredSandRangeData = useAtomValue(hoveredSandRangeDataAtom);
  const { directionText, regionsText } = usePredictionData();
  const show = useMemo(() => {
    return !!hoveredSandRangeData && (regionsText || directionText);
  }, [directionText, hoveredSandRangeData, regionsText]);
  return (
    <>
      {show && (
        <div
          className="w-[223px] px-[16px] py-[18px] text-white text-[14px] bg-[#101530]/60 backdrop-filter-blur-card z-[200] rounded-[14px]"
          style={{
            position: 'absolute',
            top: `${hoveredSandRangeData?.position[1]}px`,
            left: `${hoveredSandRangeData?.position[0]}px`,
          }}
        >
          <div className="flex items-center justify-between px-[4px]">
            <p className="text-[16px] text-[#FFBA17] font-bold">预测结果</p>
            <p className="text-[12px] ">仅供参考</p>
          </div>

          <div className="flex items-center justify-between mt-[9px] pb-[7px] border-b-[1px] border-dashed border-white/20 px-[4px]">
            <p>传播方向</p>
            <p className="font-bold">{directionText}</p>
          </div>

          <div className="mt-[7px] px-[4px] gap-[8px]">
            <div className="">受影响省份</div>
            <div className="font-bold ">{regionsText}</div>
          </div>
        </div>
      )}
    </>
  );
}

export default memo(SandRangeOverlay);
