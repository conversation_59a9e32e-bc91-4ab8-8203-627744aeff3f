import { selectedSandSourceDataAtom } from '@/pages/Overview/atoms';
import { request } from '@/utils/request';
import { ensureArray, ensureNumber } from '@/utils/type';
import { Viewport } from 'deck.gl/typed';
import { useAtom } from 'jotai';
import { stringify } from 'qs';
import { useMemo } from 'react';
import { createPortal } from 'react-dom';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router';
import DustEventListItem from './DustEventListItem';

export type SandSourceOverlayProps = {
  viewport: Viewport;
  mapContainer: HTMLDivElement | null;
};

type TGetDustEventPageParams = {
  /** 开始时间 yyyy/MM/dd HH:mm:ss  */
  startTime?: string;
  /** 开始时间 yyyy/MM/dd HH:mm:ss  */
  endTime?: string;
  /** 行政区编码  */
  regionCode?: number;
  /** 起沙地编码  */
  sendSiteRegionCode?: number;
  /** 沙源地编码  */
  sourceCode?: number;
  /** 排序规则  */
  sort?: number;
  /** 页码  */
  pageNum?: number;
  /** 条数  */
  pageSize?: number;
};

/** 获取沙尘事件次数统计-沙源地 */
type TGetDustEventStatsCountSourceParams = {
  /** 沙源地编码  */
  sourceCode?: number;
};

/** 获取沙尘事件列表 */
export const getDustEventPage = (params: TGetDustEventPageParams) => {
  return request(`/api/dust/event/page?${stringify(params)}`) as Promise<any>;
};

/** 获取沙尘事件次数统计-沙源地 */
export const getDustEventStatsCountSource = (params: TGetDustEventStatsCountSourceParams) => {
  return request(`/api/dust/event/stats/count/source?${stringify(params)}`) as Promise<any>;
};

function getAliasRegionName(name: string) {
  const threeCharacters = ['内蒙古', '黑龙江'];
  if (threeCharacters.find((item) => name.includes(item))) {
    return name.slice(0, 3);
  }
  return name.slice(0, 2);
}

export default function SandSourceOverlay({ viewport, mapContainer }: SandSourceOverlayProps) {
  const [selectedSandSourceData, setSelectedSandSourceData] = useAtom(selectedSandSourceDataAtom);
  const { name, area, code, regions } = selectedSandSourceData?.properties || {};
  const navigate = useNavigate();

  const shortRegions = useMemo(() => {
    return ensureArray(regions)
      .map((regionName) => getAliasRegionName(regionName as string))
      .join('、');
  }, [regions]);

  const formattedArea = useMemo(() => {
    return (Number(area) / 10000).toFixed(2);
  }, [area]);

  const { data: frequencyStatistics } = useQuery(
    ['/api/dust/event/stats/count/source', code],
    () => getDustEventStatsCountSource({ sourceCode: code }),
    {
      enabled: !!code,
    },
  );

  const { data: dustEvent } = useQuery(
    ['/api/dust/event/page', code],
    () => getDustEventPage({ sourceCode: code, pageSize: 2, regionCode: 150000, pageNum: 0 }),
    {
      enabled: !!code,
    },
  );

  const dustEventList = useMemo(() => {
    return ensureArray(dustEvent?.content);
  }, [dustEvent?.content]);

  const [left, top] = useMemo(() => {
    const coordinate = selectedSandSourceData?.coordinate;
    if (Array.isArray(coordinate)) {
      return viewport.project([ensureNumber(coordinate[0]), ensureNumber(coordinate[1])]);
    }
    return [9999, 9999];
  }, [selectedSandSourceData?.coordinate, viewport]);

  // 使用 createPortal 是为了防止点击穿透
  return createPortal(
    <div
      className="absolute z-[20] min-w-[324px] px-[12px] pt-[20px] pb-[13px] bg-[#101530]/60 rounded-[14px] text-white backdrop-filter-blur-card"
      style={{
        top: top,
        left: left,
      }}
    >
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div
            className="flex items-center justify-center w-[27px] h-[27px] mr-[8px] rounded-[6px]"
            style={{
              background: 'linear-gradient(to right, #fae763, #FFAE00)',
            }}
          >
            <iconpark-icon width="24" height="24" color="#ffffff" name="shayuandi"></iconpark-icon>
          </div>
          <span>{name}</span>
        </div>
      </div>
      <div className="flex items-center justify-between mt-[10px]">
        <div className="flex items-center">
          <iconpark-icon name="dingwei-d5njj7hd"></iconpark-icon>
          <div className="ml-[4px] mr-[10px]">{shortRegions}</div>
        </div>
        <div>{formattedArea} 万KM²</div>
      </div>

      {/* 数量统计 */}
      <div className="flex items-center gap-[20px] bg-[#FFFCE724] rounded-[8px] mt-[10px] p-[12px]">
        <div className="flex items-center gap-[6px]">
          <div className="text-[42px] text-[#FF8200] leading-[48px]">{frequencyStatistics?.totalCount}</div>
          <div className="flex flex-col justify-center">
            <p className="text-[12px] font-white">总计</p>
            <p className="text-[12px] font-white">次沙尘</p>
          </div>
        </div>
        <div className="w-[1px] h-[39px] bg-[#D7D1CC]"></div>
        <div className="flex items-center flex-1 justify-between">
          <div className="flex-1">
            <div className="text-[24px] text-[#FFC935] leading-28 text-center">{frequencyStatistics?.monthCount}</div>
            <div className="text-[14px] leading-[20px] text-white text-center">本月</div>
          </div>
          <div className="flex-1">
            <div className="text-[24px] text-[#FFC935] leading-28 text-center">{frequencyStatistics?.quarterCount}</div>
            <div className="text-[14px] leading-[20px] text-white text-center">本季</div>
          </div>
          <div className="flex-1">
            <div className="text-[24px] text-[#FFC935] leading-28 text-center">{frequencyStatistics?.yearCount}</div>
            <div className="text-[14px] leading-[20px] text-white text-center">本年</div>
          </div>
        </div>
      </div>

      {/* 事件列表 */}
      <div className="flex justify-between text-[14px] leading-[20px] mt-[10px]">
        <p>沙尘事件</p>
        <div
          onClick={() => {
            navigate(`/dust-event?sourceCode=${code}`);
            setSelectedSandSourceData(undefined);
          }}
          className="flex items-center gap-[4px] cursor-pointer select-none"
        >
          查看更多
          <div className="flex items-center justify-center w-[14px] h-[14px] rounded-full border-[1px] border-[#F5A012]">
            <iconpark-icon color="#F5A012" name="youqiehuan"></iconpark-icon>
          </div>
        </div>
      </div>
      <div className="mt-[6px] flex flex-col gap-[6px]">
        {dustEventList.map((item) => (
          <DustEventListItem key={item.code} content={item} />
        ))}
      </div>

      {/* 关闭按钮 */}
      <div
        className="absolute top-[-6px] right-[-6px] flex items-center justify-center w-[22px] h-[22px] rounded-full bg-[#566E84] cursor-pointer select-none"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          setSelectedSandSourceData(undefined);
        }}
      >
        <iconpark-icon width="10" height="10" color="#ffffff" name="guanbi"></iconpark-icon>
      </div>
    </div>,
    mapContainer || document.body,
  );
}
