import { useSpring } from 'react-spring';
import { fullScreenAtom } from '@/atoms';
import { ScreenContainer, IconBtn, QuitFullScreenBtn } from './ui';
import { useAtom } from 'jotai';
import { useHandleKeyDown } from '../hooks';

const ScreenControl: React.FC = () => {
  const [isFullScreen, setFullScreen] = useAtom(fullScreenAtom);

  useHandleKeyDown((e) => {
    if (e.keyCode === 27 && isFullScreen) {
      setFullScreen(false);
    }
  });

  const quitBtnStyle = useSpring({
    opacity: isFullScreen ? 1 : 0,
    right: isFullScreen ? 40 : 0,
  });

  const style = useSpring({
    opacity: isFullScreen ? 0 : 1,
    right: isFullScreen ? 0 : 52,
  });

  return (
    <>
      <QuitFullScreenBtn
        title="退出演示模式"
        style={{
          ...quitBtnStyle,
          pointerEvents: isFullScreen ? 'auto' : 'none',
        }}
        onClick={() => setFullScreen(false)}
      >
        退出演示模式
      </QuitFullScreenBtn>
      <ScreenContainer
        style={{
          ...style,
          pointerEvents: isFullScreen ? 'none' : 'auto',
        }}
      >
        <IconBtn title="演示模式" onClick={() => setFullScreen(true)}>
          <i className="icomoon icon-full-screen" />
        </IconBtn>
      </ScreenContainer>
    </>
  );
};

export default ScreenControl;
