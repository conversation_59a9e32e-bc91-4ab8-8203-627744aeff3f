import { useAtomValue, useSet<PERSON>tom as useUpdateAtom } from 'jotai';
import { alertPanelVisibleAtom, pollutionLayersVisibleAtom, pollutionPanelVisibleAtom } from '../atoms';
import ColorLayerControl from './ColorLayerControl';
import MapLayerControl from './MapLayerControl';
import MapVectorControl from './MapVectorControl';
import MeteroLayerControl from './MeteroLayerControl';
import { LayerBtn, LayerControl, LyBtnContainer } from './ui';

const LayersControl: React.FC = () => {
  const setContaminantVisible = useUpdateAtom(pollutionPanelVisibleAtom);
  const setWarningPanelVisible = useUpdateAtom(alertPanelVisibleAtom);
  const contaminantLayersVisible = useAtomValue(pollutionLayersVisibleAtom);

  return (
    <LayerControl>
      <div className="label">图层控制</div>
      <LyBtnContainer>
        <MapLayerControl />
        <MeteroLayerControl />
        <LayerBtn
          title="污染物图层"
          active={contaminantLayersVisible.satiRemote || contaminantLayersVisible.nationStation}
          onClick={() => {
            setContaminantVisible((prev) => {
              if (!prev) {
                setWarningPanelVisible(false);
              }
              return !prev;
            });
          }}
        >
          <i className="icomoon icon-layer-3" />
        </LayerBtn>
        <ColorLayerControl />
        <MapVectorControl />
      </LyBtnContainer>
    </LayerControl>
  );
};

export default LayersControl;
