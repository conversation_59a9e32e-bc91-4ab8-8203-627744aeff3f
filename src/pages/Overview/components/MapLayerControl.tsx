import { createRef, useState } from 'react';
import { useAtom } from 'jotai';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useSpring } from 'react-spring';
import { LayerBtn, LayerMenu, LayerMenuItem, MenuContainer } from './ui';
import { Center } from '@/components/ui';
import {
  mapLayerStatusAtom,
  pollutionPanelVisibleAtom,
  mapTypeAtom,
} from '../atoms';
import { fullScreenAtom } from '@/atoms';
import { useOnClickOutside } from '@/hooks';

// 地图图层控制
const MapLayerControl: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [mapLayerStatus, setMapLayerStatus] = useAtom(mapLayerStatusAtom);
  const setPollutionPanelVisible = useUpdateAtom(pollutionPanelVisibleAtom);
  const setMapType = useUpdateAtom(mapTypeAtom);
  const menuRef = createRef<HTMLDivElement>();

  const style = useSpring({
    opacity: visible && !isFullScreen ? 1 : 0,
    bottom: visible && !isFullScreen ? 90 : 80,
  });

  useOnClickOutside(menuRef, () => {
    setVisible(false);
  });

  return (
    <LayerBtn
      title="地理图层"
      active={mapLayerStatus.some((item) => item.visible)}
      ref={menuRef}
      onClick={() => {
        setPollutionPanelVisible(false);
        setVisible(!visible);
      }}
    >
      <i className="icomoon icon-layer-1" />
      <MenuContainer
        style={{
          ...style,
          pointerEvents: visible && !isFullScreen ? 'auto' : 'none',
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Center>
          <i className="icomoon icon-layer-1" />
          地理
        </Center>
        <LayerMenu>
          {mapLayerStatus.map((item) => (
            <LayerMenuItem
              key={item.label}
              active={item.visible}
              onClick={(e) => {
                e.stopPropagation();
                mapLayerStatus.forEach((st) => {
                  // eslint-disable-next-line no-param-reassign
                  st.visible = st.label === item.label;
                });
                setMapLayerStatus([...mapLayerStatus]);
                setMapType(item.type);
              }}
            >
              {item.label}
            </LayerMenuItem>
          ))}
        </LayerMenu>
      </MenuContainer>
    </LayerBtn>
  );
};

export default MapLayerControl;
