/* eslint-disable no-restricted-globals */
import { fullScreenAtom, regionAtom, userInfoAtom } from '@/atoms';
import { Block, Center, Flex, HorCenter } from '@/components/ui';
import { getLevelByRegionCode, remoteSensingValuesAndColors } from '@/utils';
import type { BidirectionalBarConfig } from '@ant-design/charts';
import { BidirectionalBar } from '@ant-design/charts';
import { Empty, message, Spin } from 'antd';
import { useAtom, useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import moment from 'moment';
import React, { useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import { useTransition } from 'react-spring';
import {
  activeTimelineItemAtom,
  aggAtom,
  datePickerValAtom,
  displayTypeAtom,
  fetchTextureWeatherTypeAtom,
  nationStationPoTypeAtom,
  pollutionAccordionStatusAtom,
  pollutionLayersVisibleAtom,
  pollutionPanelVisibleAtom,
  remotePoTypeAtom,
  stationDataAtom,
  weatherLayersStatusAtom,
} from '../atoms';
import { getEvaluationSatelliteCustomList } from '../services';
import { Agg } from '../types';
import AccordionItem from './AccordionItem';
import NationStationTypeTabs from './NationStationTypeTabs';
import PollutionSelect from './PollutionSelect';
import {
  Accordion,
  ChartContainer,
  ContaminantContainer,
  ContaminantTitle,
  DiaplayType,
  Index,
  ProgressBar,
  RemoteSensing,
  RemoteSensingContainer,
  RemoteSensingInfo,
  RemoteSensingList,
  StationList,
  StationListItem,
  TypeItem,
} from './ui';

const sectionIconLayerAggs: Agg[] = ['hourly', 'daily'];

const PollutionPanel: React.FC = () => {
  const [visible, setVisible] = useAtom(pollutionPanelVisibleAtom);
  const userInfo = useAtomValue(userInfoAtom);
  const isFullScreen = useAtomValue(fullScreenAtom);
  const [displayType, setDisplayType] = useAtom(displayTypeAtom);
  const [current, setCurrent] = useAtom(pollutionAccordionStatusAtom);
  const [layerStatus, setLayerStatus] = useAtom(pollutionLayersVisibleAtom);
  const fetchTextureType = useAtomValue(remotePoTypeAtom);
  const setWeatherLayersStatus = useUpdateAtom(weatherLayersStatusAtom);
  const setFetchTextureWeatherType = useUpdateAtom(fetchTextureWeatherTypeAtom);
  const regionCode = useAtomValue(regionAtom);
  const stationData = useAtomValue(stationDataAtom);
  const nationType = useAtomValue(nationStationPoTypeAtom);
  const level = getLevelByRegionCode(regionCode);
  const activeTimelineItem = useAtomValue(activeTimelineItemAtom);
  const [dateRange] = useAtom(datePickerValAtom);
  const agg = useAtomValue(aggAtom);

  const transitions = useTransition(visible && !isFullScreen, {
    from: {
      opacity: 0,
      bottom: 134,
    },
    enter: {
      opacity: 1,
      bottom: 154,
    },
    leave: {
      opacity: 0,
      bottom: 134,
    },
  });

  interface Item {
    region: string;
    regionCode: number;
    value: number;
    cloudCover: number;
  }

  const dateParams = useMemo(() => {
    const formatter = 'YYYY/MM/DD HH:mm:ss';
    switch (agg) {
      case 'hourly':
        return {
          startDate: moment(activeTimelineItem?.value).startOf('hour').format(formatter),
          endDate: moment(activeTimelineItem?.value).endOf('hour').format(formatter),
        };
      case 'daily':
        return {
          startDate: moment(activeTimelineItem?.value).startOf('day').format(formatter),
          endDate: moment(activeTimelineItem?.value).endOf('day').format(formatter),
        };
      case 'monthly':
        return {
          startDate: moment(activeTimelineItem?.value).startOf('month').format(formatter),
          endDate: moment(activeTimelineItem?.value).endOf('month').format(formatter),
        };
      case 'yearly':
        return {
          startDate: moment(activeTimelineItem?.value).startOf('year').format(formatter),
          endDate: moment(activeTimelineItem?.value).endOf('year').format(formatter),
        };
      default:
        return dateRange
          ? {
              startDate: dateRange?.[0] ? moment(dateRange[0]).startOf('day').format(formatter) : '',
              endDate: dateRange?.[1] ? moment(dateRange[1]).endOf('day').format(formatter) : '',
            }
          : {
              startDate: '',
              endDate: '',
            };
    }
  }, [activeTimelineItem?.value, agg, dateRange]);

  const { data, isLoading } = useQuery<Item[]>(
    ['eva-satellite-list', regionCode, dateParams, fetchTextureType],
    () =>
      getEvaluationSatelliteCustomList({
        regionCode: Number(regionCode === 0 ? userInfo?.regionCode : regionCode),
        ...(dateParams || {}),
        type: String(fetchTextureType),
        agg: agg,
      }),
    {
      enabled: Boolean(typeof regionCode !== 'undefined' && fetchTextureType && Boolean(activeTimelineItem?.value) && dateRange),
    },
  );

  const chartData = useMemo(() => {
    if (data) {
      return data.map((item) => ({
        有效率: 1 - (item.cloudCover || 0),
        浓度值: item.value * 100,
        label: item.region,
      }));
    }
    return [];
  }, [data]);

  const config: BidirectionalBarConfig = {
    data: chartData,
    height: 200,
    xField: 'label',
    color: ['#286cff', '#ffc625'],
    xAxis: {
      position: 'bottom',
      label: {
        style: {
          fontSize: 12,
          fill: '#C1C1C4',
        },
      },
    },
    yAxis: {
      有效率: {
        label: {
          style: {
            fill: '#c1c1c4',
          },
        },
        grid: {
          line: {
            style: {
              stroke: '#31333d',
            },
          },
        },
        line: {
          style: {
            stroke: '#31333d',
          },
        },
      },
      浓度值: {
        label: {
          formatter(value) {
            if (Number.isNaN(value)) return 'N/A';
            if (fetchTextureType === 'dmask') {
              return Number(value) / 100;
            } else {
              return remoteSensingValuesAndColors[fetchTextureType]?.roundFunc(Number(value) / 100);
            }
          },
          style: {
            fill: '#c1c1c4',
          },
        },
        grid: {
          line: {
            style: {
              stroke: '#31333d',
            },
          },
        },
        line: {
          style: {
            stroke: '#31333D',
          },
        },
      },
    },
    yField: ['有效率', '浓度值'],
    tooltip: {
      shared: true,
      showMarkers: false,
      domStyles: {
        'g2-tooltip': {
          boxShadow: 'none',
        },
      },
      customContent(title, d) {
        if (d.length === 0) {
          return '';
        }
        let value = d[1].value / 100;
        const method = remoteSensingValuesAndColors[fetchTextureType]?.roundFunc;
        if (method) {
          value = method(value);
        } else {
          value = value;
        }

        return `
        <div class="g2-tooltip-title">${title}</div>
        <ul class="g2-tooltip-list">
        <li class="g2-tooltip-list-item" data-index="">
          <span class="g2-tooltip-marker"
           style="background: rgb(40, 108, 255); width: 8px; height: 8px;
            border-radius: 50%; display: inline-block; margin-right: 8px;"
            ></span>
          <span class="g2-tooltip-name">有效率</span>:
          <span class="g2-tooltip-value" style="display: inline-block;
          float: right;
           margin-left: 30px;">
          ${Math.trunc(d[0].value * 100)}%</span>
        </li>
        <li class="g2-tooltip-list-item" data-index=""
        style="list-style-type: none; padding: 0px;
        margin: 12px 0px;">
          <span class="g2-tooltip-marker"
          style="background: rgb(255, 198, 37); width: 8px; height: 8px;
          border-radius: 50%; display: inline-block; margin-right: 8px;"></span>
          <span class="g2-tooltip-name">浓度值</span>:
          <span class="g2-tooltip-value" style="display: inline-block;
           float: right; margin-left: 30px;">${value}</span>
        </li></ul>`;
      },
    },
    legend: {
      layout: 'horizontal',
      position: 'top',
      itemWidth: 120,
      label: {
        style: {
          fill: 'red',
        },
      },
    },
  };

  const cannotOpenNationStation = useMemo(() => {
    return !sectionIconLayerAggs.includes(agg);
  }, [agg]);

  useEffect(() => {
    setDisplayType('list');
  }, [level, setDisplayType]);

  // 国控站点数据仅支持时、日
  useEffect(() => {
    if (cannotOpenNationStation) {
      setLayerStatus((prev) => {
        return {
          ...prev,
          nationStation: false,
        };
      });
      setCurrent((prev) => (prev === 0 ? 0 : null));
    }
  }, [cannotOpenNationStation, setCurrent, setDisplayType, setLayerStatus]);

  return transitions(
    (styles, show) =>
      show && (
        <ContaminantContainer style={styles}>
          <ContaminantTitle>
            <div>
              <i className="icomoon icon-layer-3" />
              污染物
            </div>
            <i className="icomoon icon-x" onClick={() => setVisible(false)} />
          </ContaminantTitle>
          <Accordion>
            <AccordionItem
              title="卫星遥感"
              height={310}
              open={current === 0}
              switchChecked={layerStatus.satiRemote}
              onSwitchClick={() => {
                setLayerStatus((prev) => {
                  if (!prev.satiRemote) {
                    setFetchTextureWeatherType(null);
                    setWeatherLayersStatus((prevState) =>
                      prevState.map((item) => {
                        return item.label === '风向'
                          ? item
                          : {
                              ...item,
                              visible: false,
                            };
                      }),
                    );
                  }
                  return {
                    ...prev,
                    satiRemote: !prev.satiRemote,
                  };
                });
              }}
              onClick={() => setCurrent((prev) => (prev === 0 ? null : 0))}
            >
              <PollutionSelect />
              {data && data.length === 0 && !isLoading && (
                <Block padding="40px 20px">
                  <Empty description={<span className="text-white/60">暂无数据</span>} />
                </Block>
              )}
              {data && data.length > 0 && (
                <>
                  <DiaplayType>
                    <span>污染物详情</span>
                    <TypeItem active={displayType === 'list'} onClick={() => setDisplayType('list')}>
                      <i className="icomoon icon-list" /> 列表
                    </TypeItem>
                    {level === 1 && (
                      <TypeItem active={displayType === 'chart'} onClick={() => setDisplayType('chart')}>
                        <i className="icomoon icon-chart" /> 图表
                      </TypeItem>
                    )}
                  </DiaplayType>
                  <RemoteSensingContainer>
                    {displayType === 'list' && (
                      <RemoteSensingList>
                        {isLoading && (
                          <Center>
                            <Block padding="20px 0">
                              <Spin />
                            </Block>
                          </Center>
                        )}
                        {!isLoading &&
                          (data || [])
                            .sort((a, b) => b.value - a.value)
                            .map((item, index) => {
                              let value: string | number = item.value;
                              if (isNaN(value)) {
                                value = 'N/A';
                              }
                              value = remoteSensingValuesAndColors[fetchTextureType!]?.roundFunc(value);
                              return (
                                <RemoteSensing key={item.regionCode}>
                                  <Index
                                    style={{
                                      width: 16,
                                      height: 16,
                                      lineHeight: '16px',
                                    }}
                                  >
                                    {index + 1}
                                  </Index>
                                  <RemoteSensingInfo>
                                    <h1>{item.region || '未知行政区域'}</h1>
                                    <Flex>
                                      <span>
                                        浓度: {value} {isNaN(item.value) ? '' : remoteSensingValuesAndColors[fetchTextureType].unit}
                                      </span>
                                      <HorCenter style={{ width: 160 }}>
                                        <span>有效率</span>
                                        <ProgressBar percent={+((1 - (item.cloudCover || 0)) * 100).toFixed(0)}>
                                          <div />
                                        </ProgressBar>
                                        <span>{((1 - (item.cloudCover || 0)) * 100).toFixed(0)}%</span>
                                      </HorCenter>
                                    </Flex>
                                  </RemoteSensingInfo>
                                </RemoteSensing>
                              );
                            })}
                      </RemoteSensingList>
                    )}
                    {/* 对称条形图 */}
                    {displayType === 'chart' && (
                      <ChartContainer>
                        <BidirectionalBar {...config} />
                      </ChartContainer>
                    )}
                  </RemoteSensingContainer>
                </>
              )}
            </AccordionItem>
            <AccordionItem
              title="国控站"
              height={262}
              open={current === 1}
              switchChecked={layerStatus.nationStation}
              onSwitchClick={() => {
                if (cannotOpenNationStation) {
                  message.error('该图层不支持当前选中颗粒度');
                  return;
                }
                setLayerStatus((prev) => {
                  if (!prev.satiRemote) {
                  }
                  return {
                    ...prev,
                    nationStation: !prev.nationStation,
                  };
                });
              }}
              onClick={() => {
                if (cannotOpenNationStation) {
                  message.error('该图层不支持当前选中颗粒度');
                  return;
                }
                setCurrent((prev) => (prev === 1 ? null : 1));
              }}
            >
              <NationStationTypeTabs />
              <RemoteSensingContainer>
                <StationList>
                  {stationData.length === 0 && !isLoading && (
                    <Block padding="40px 20px">
                      <Empty description={<span className="text-white/60">暂无数据</span>} />
                    </Block>
                  )}
                  {stationData
                    .filter((item) => item.type === 'nation')
                    .map((item, index) => {
                      const formatVal = (val: number, type: string) => {
                        if (type === 'so2' || type === 'co') {
                          return val.toFixed(1);
                        }
                        return Math.round(val);
                      };
                      let unit = 'μg/m³';
                      if (nationType === 'co') {
                        unit = 'mg/m³';
                      }
                      if (nationType === 'aqi') {
                        unit = '';
                      }
                      return (
                        <StationListItem key={item.placeName}>
                          <Index
                            style={{
                              width: 16,
                              height: 16,
                              lineHeight: '16px',
                            }}
                          >
                            {index + 1}
                          </Index>
                          <div className="name">{item.placeName}</div>
                          <div className="value" style={{ color: '#fff' }}>
                            {formatVal(Number(item[nationType]), nationType)}
                            <span style={{ color: '#999' }}>{unit}</span>
                          </div>
                        </StationListItem>
                      );
                    })}
                </StationList>
              </RemoteSensingContainer>
            </AccordionItem>
          </Accordion>
        </ContaminantContainer>
      ),
  );
};

export default PollutionPanel;
