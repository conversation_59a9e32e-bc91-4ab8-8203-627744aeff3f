#index {
  color: #c1c1c4;

  .avatar-overlay {
    .ant-dropdown-arrow {
      right: 28px;
    }
  }
  // 日期范围选择器
  .ant-picker {
    color: rgba(255, 255, 255, 0.85);

    border: 1px solid #434343;
  }
  .ant-picker:hover,
  .ant-picker-focused {
    border-color: #165996;
  }
  .ant-picker-focused {
    border-color: #286cff;

    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
  }
  .ant-picker.ant-picker-disabled {
    background: rgba(255, 255, 255, 0.08);
    border-color: #434343;
    cursor: not-allowed;
  }
  .ant-picker.ant-picker-disabled .ant-picker-suffix {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker.ant-picker-borderless {
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }

  .ant-picker-input > input {
    color: rgba(255, 255, 255, 0.85);

    background: transparent;
    background-color: transparent;
    background-image: none;
    border: 1px solid #434343;
    border: 0;
  }
  .ant-picker-input > input::-moz-placeholder {
    opacity: 1;
  }
  .ant-picker-input > input:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-input > input::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }

  .ant-picker-input > input:hover {
    border-color: #165996;
    border-right-width: 1px !important;
  }
  .ant-picker-input > input:focus,
  .ant-picker-input > input-focused {
    border-color: #286cff;
    border-right-width: 1px !important;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
  }
  .ant-picker-input > input-disabled {
    color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.08);
    cursor: not-allowed;
    opacity: 1;
  }
  .ant-picker-input > input-disabled:hover {
    border-color: #434343;
    border-right-width: 1px !important;
  }
  .ant-picker-input > input[disabled] {
    color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.08);
    opacity: 1;
  }
  .ant-picker-input > input[disabled]:hover {
    border-color: #434343;
    border-right-width: 1px !important;
  }
  .ant-picker-input > input-borderless,
  .ant-picker-input > input-borderless:hover,
  .ant-picker-input > input-borderless:focus,
  .ant-picker-input > input-borderless-focused,
  .ant-picker-input > input-borderless-disabled,
  .ant-picker-input > input-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none;
  }

  .ant-picker-input > input:focus {
    box-shadow: none;
  }
  .ant-picker-input > input[disabled] {
    background: transparent;
  }
  .ant-picker-input:hover .ant-picker-clear {
    opacity: 1;
  }
  .ant-picker-input-placeholder > input {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-suffix {
    align-self: center;
    margin-left: 4px;
    color: rgba(255, 255, 255, 0.3);
    line-height: 1;
    pointer-events: none;
  }
  .ant-picker-suffix > * {
    vertical-align: top;
  }
  .ant-picker-clear {
    color: rgba(255, 255, 255, 0.3);
    background: #141414;
  }
  .ant-picker-clear:hover {
    color: rgba(255, 255, 255, 0.45);
  }
  .ant-picker-separator {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-focused .ant-picker-separator {
    color: rgba(255, 255, 255, 0.45);
  }
  .ant-picker-range:hover .ant-picker-clear {
    opacity: 1;
  }
  .ant-picker-range .ant-picker-active-bar {
    bottom: -1px;
    height: 2px;
    margin-left: 11px;
    background: #286cff;
    opacity: 0;
    transition: all 0.3s ease-out;
    pointer-events: none;
  }
  .ant-picker-range.ant-picker-focused .ant-picker-active-bar {
    opacity: 1;
  }

  .ant-picker-dropdown {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-picker-ranges .ant-picker-preset > .ant-tag-blue {
    color: #286cff;
    background: #111b26;
    border-color: #153450;
  }
  .ant-picker-range-arrow {
    box-shadow: 2px -2px 6px rgba(0, 0, 0, 0.06);
    transition: left 0.3s ease-out;
  }
  .ant-picker-range-arrow::after {
    border: 5px solid #303030;
    border-color: #1f1f1f #1f1f1f transparent transparent;
  }
  .ant-picker-panel-container {
    background: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48),
      0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }

  .ant-picker-panel-container .ant-picker-panel {
    background: transparent;
  }
  .ant-picker-panel-container .ant-picker-panel-focused {
    border-color: #303030;
  }
  .ant-picker-panel {
    background: #1f1f1f;
    border: 1px solid #303030;
  }
  .ant-picker-panel-focused {
    border-color: #286cff;
  }
  .ant-picker-header {
    color: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid #303030;
  }

  .ant-picker-header button {
    color: rgba(255, 255, 255, 0.3);
    background: transparent;
  }
  .ant-picker-header > button:hover {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-picker-header-view button:hover {
    color: #286cff;
  }

  .ant-picker-content th {
    color: rgba(255, 255, 255, 0.85);
  }
  .ant-picker-cell {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-cell-in-view {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end)
    .ant-picker-cell-inner {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-cell-in-view.ant-picker-cell-today
    .ant-picker-cell-inner::before {
    border: 1px solid #286cff;
  }

  .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
    background: #343c50;
  }
  .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
    color: #fff;
    background: #286cff;
  }
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
    background: #343c50;
  }
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range)::after {
    border-top: 1px dashed #2277c7;
    border-bottom: 1px dashed #2277c7;
  }
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end::before,
  .ant-picker-panel
    > :not(.ant-picker-date-panel)
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before,
  .ant-picker-panel
    > :not(.ant-picker-date-panel)
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before {
    background: #262e42;
  }
  .ant-picker-date-panel
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start
    .ant-picker-cell-inner::after,
  .ant-picker-date-panel
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end
    .ant-picker-cell-inner::after {
    background: #262e42;
  }

  tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:first-child::after,
  tr
    > .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after,
  .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range)::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after {
    border-left: 1px dashed #2277c7;
  }
  tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child::after,
  tr
    > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after,
  .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {
    border-right: 1px dashed #2277c7;
  }
  .ant-picker-cell-disabled {
    pointer-events: none;
  }
  .ant-picker-cell-disabled .ant-picker-cell-inner {
    color: rgba(255, 255, 255, 0.3);
    background: transparent;
  }
  .ant-picker-cell-disabled::before {
    background: #303030;
  }
  .ant-picker-cell-disabled.ant-picker-cell-today
    .ant-picker-cell-inner::before {
    border-color: rgba(255, 255, 255, 0.3);
  }
  .ant-picker-decade-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
  .ant-picker-year-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
  .ant-picker-quarter-panel .ant-picker-cell-disabled .ant-picker-cell-inner,
  .ant-picker-month-panel .ant-picker-cell-disabled .ant-picker-cell-inner {
    background: #303030;
  }

  .ant-picker-footer {
    border-bottom: 1px solid transparent;
  }
  .ant-picker-panel .ant-picker-footer {
    border-top: 1px solid #303030;
  }
  .ant-picker-footer-extra {
    padding: 0 12px;
    line-height: 38px;
    text-align: left;
  }
  .ant-picker-footer-extra:not(:last-child) {
    border-bottom: 1px solid #303030;
  }

  .ant-picker-today-btn {
    color: #286cff;
  }
  .ant-picker-today-btn:hover {
    color: #165996;
  }
  .ant-picker-today-btn:active {
    color: #388ed3;
  }
  .ant-picker-today-btn.ant-picker-today-btn-disabled {
    color: rgba(255, 255, 255, 0.3);
  }

  .ant-picker-year-panel .ant-picker-cell-range-hover-start::after,
  .ant-picker-quarter-panel .ant-picker-cell-range-hover-start::after,
  .ant-picker-month-panel .ant-picker-cell-range-hover-start::after {
    border-left: 1px dashed #2277c7;
  }
  .ant-picker-panel-rtl
    .ant-picker-year-panel
    .ant-picker-cell-range-hover-start::after,
  .ant-picker-panel-rtl
    .ant-picker-quarter-panel
    .ant-picker-cell-range-hover-start::after,
  .ant-picker-panel-rtl
    .ant-picker-month-panel
    .ant-picker-cell-range-hover-start::after {
    border-right: 1px dashed #2277c7;
  }
  .ant-picker-panel-rtl
    .ant-picker-year-panel
    .ant-picker-cell-range-hover-end::after,
  .ant-picker-panel-rtl
    .ant-picker-quarter-panel
    .ant-picker-cell-range-hover-end::after,
  .ant-picker-panel-rtl
    .ant-picker-month-panel
    .ant-picker-cell-range-hover-end::after {
    border-left: 1px dashed #2277c7;
  }

  .ant-picker-week-panel .ant-picker-cell:hover .ant-picker-cell-inner,
  .ant-picker-week-panel .ant-picker-cell-selected .ant-picker-cell-inner,
  .ant-picker-week-panel .ant-picker-cell .ant-picker-cell-inner {
    background: transparent !important;
  }

  .ant-picker-week-panel-row:hover td {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-week-panel-row-selected td,
  .ant-picker-week-panel-row-selected:hover td {
    background: #286cff;
  }
  .ant-picker-week-panel-row-selected td.ant-picker-cell-week,
  .ant-picker-week-panel-row-selected:hover td.ant-picker-cell-week {
    color: rgba(255, 255, 255, 0.5);
  }
  .ant-picker-week-panel-row-selected
    td.ant-picker-cell-today
    .ant-picker-cell-inner::before,
  .ant-picker-week-panel-row-selected:hover
    td.ant-picker-cell-today
    .ant-picker-cell-inner::before {
    border-color: #fff;
  }
  .ant-picker-week-panel-row-selected td .ant-picker-cell-inner,
  .ant-picker-week-panel-row-selected:hover td .ant-picker-cell-inner {
    color: #fff;
  }

  .ant-picker-datetime-panel .ant-picker-time-panel {
    border-left: 1px solid #303030;
  }
  .ant-picker-datetime-panel .ant-picker-date-panel,
  .ant-picker-datetime-panel .ant-picker-time-panel {
    transition: opacity 0.3s;
  }
  .ant-picker-datetime-panel-active .ant-picker-date-panel,
  .ant-picker-datetime-panel-active .ant-picker-time-panel {
    opacity: 0.3;
  }
  .ant-picker-datetime-panel-active .ant-picker-date-panel-active,
  .ant-picker-datetime-panel-active .ant-picker-time-panel-active {
    opacity: 1;
  }

  .ant-picker-datetime-panel .ant-picker-time-panel-column::after {
    height: 198px;
  }
  .ant-picker-time-panel-column:not(:first-child) {
    border-left: 1px solid #303030;
  }
  .ant-picker-time-panel-column-active {
    background: rgba(17, 27, 38, 0.2);
  }

  .ant-picker-time-panel-column
    > li.ant-picker-time-panel-cell
    .ant-picker-time-panel-cell-inner {
    color: rgba(255, 255, 255, 0.85);
    transition: background 0.3s;
  }
  .ant-picker-time-panel-column
    > li.ant-picker-time-panel-cell
    .ant-picker-time-panel-cell-inner:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-picker-time-panel-column
    > li.ant-picker-time-panel-cell-selected
    .ant-picker-time-panel-cell-inner {
    background: #111b26;
  }
  .ant-picker-time-panel-column
    > li.ant-picker-time-panel-cell-disabled
    .ant-picker-time-panel-cell-inner {
    color: rgba(255, 255, 255, 0.3);
    background: transparent;
  }
  .ant-picker-panel-rtl
    tr
    > .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):first-child::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range)::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after {
    border-right: 1px dashed #2277c7;
  }
  .ant-picker-panel-rtl
    tr
    > .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):last-child::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {
    border-right: none;
    border-left: 1px dashed #2277c7;
  }
  .ant-picker-panel-rtl
    tr
    > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after,
  .ant-picker-panel-rtl
    tr
    > .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover)::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-end.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover)::after,
  .ant-picker-panel-rtl
    .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-start.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover)::after,
  .ant-picker-panel-rtl
    tr
    > .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-start:last-child::after,
  .ant-picker-panel-rtl
    tr
    > .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-end:first-child::after {
    border-right: 1px dashed #2277c7;
    border-left: 1px dashed #2277c7;
  }
  // 级联选择
  .ant-cascader {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.85) !important;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
  }
  .ant-cascader-input.ant-input {
    position: static;
    width: 100%;
    padding-right: 24px;
    background-color: transparent !important;
    cursor: pointer;
  }
  .ant-cascader-picker-show-search .ant-cascader-input.ant-input {
    position: relative;
  }
  .ant-cascader-picker {
    color: rgba(255, 255, 255, 0.85) !important;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    background-color: transparent;
    border-radius: 2px;
    outline: 0;
    cursor: pointer;
    transition: color 0.3s;
    font-feature-settings: 'tnum';
  }
  .ant-cascader-picker-with-value .ant-cascader-picker-label {
    color: transparent;
  }
  .ant-cascader-picker-disabled {
    color: rgba(255, 255, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.08) !important;
  }
  .ant-cascader-picker-disabled .ant-cascader-input {
    cursor: not-allowed;
  }
  .ant-cascader-picker:focus .ant-cascader-input {
    border-color: #286cff;
    border-right-width: 1px !important;
    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
  }
  .ant-cascader-picker-borderless .ant-cascader-input {
    border-color: transparent !important;
    box-shadow: none !important;
  }
  .ant-cascader-picker-show-search.ant-cascader-picker-focused {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-cascader-picker-clear {
    color: rgba(255, 255, 255, 0.3);
    background: #141414;
    opacity: 0;
    transition: color 0.3s ease, opacity 0.15s ease;
  }
  .ant-cascader-picker-clear:hover {
    color: rgba(255, 255, 255, 0.45);
  }
  .ant-cascader-picker:hover .ant-cascader-picker-clear {
    opacity: 1;
  }
  .ant-cascader-picker-arrow {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-cascader-picker-label:hover
    + .ant-cascader-input:not(.ant-cascader-picker-disabled
      .ant-cascader-picker-label:hover
      + .ant-cascader-input) {
    border-color: #165996;
    border-right-width: 1px !important;
  }
  .ant-cascader-menus {
    background: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48),
      0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  .ant-cascader-menu {
    border-right: 1px solid #303030;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .ant-cascader-menu:last-child {
    border-right-color: transparent;
  }
  .ant-cascader-menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  .ant-cascader-menu-item-disabled {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-cascader-menu-item-disabled:hover {
    background: transparent;
  }
  .ant-cascader-menu-empty .ant-cascader-menu-item {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    background-color: #111b26;
  }
  .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,
  .ant-cascader-menu-item-loading-icon {
    color: rgba(255, 255, 255, 0.45) !important;
  }
  .ant-cascader-menu-item-disabled.ant-cascader-menu-item-expand
    .ant-cascader-menu-item-expand-icon,
  .ant-cascader-menu-item-disabled.ant-cascader-menu-item-loading-icon {
    color: rgba(255, 255, 255, 0.3);
  }
  .ant-cascader-menu-item .ant-cascader-menu-item-keyword {
    color: #a61d24;
  }

  .ant-cascader-menu-rtl .ant-cascader-menu {
    border-left: 1px solid #303030;
  }
}
