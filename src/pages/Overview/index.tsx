import HelmetTitle from '@/components/global/HealmetTitle';
import { ConfigProvider } from 'antd';
import AnalysisControl from './components/Ai/AnalysisControl';
import Avatar from './components/Avatar';
import BottomBar from './components/BottomBar';
import Compass from './components/Compass';
import Map from './components/Map';
import MeasureControl from './components/MeasureControl';
import MonitoringResults from './components/MonitoringResults';
import PollutionPanel from './components/PollutionPanel';
import ScreenControl from './components/ScreenControl';
import ZoomControl from './components/ZoomControl';

const Index: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorText: '#c1c1c4', // 设置主字体颜色
          colorBgBase: '#1c1d24',
          colorPrimary: '#286cff',
        },
      }}
    >
      <HelmetTitle title="大气一张图" />
      {/* <PageIndexStyle /> */}
      <Map />
      <Avatar />
      <ZoomControl />
      <ScreenControl />
      <MeasureControl />
      <BottomBar />
      <Compass />
      <PollutionPanel />
      {/* 左上角监测结果 */}
      <MonitoringResults />
      {/* AI分析*/}
      <AnalysisControl />
    </ConfigProvider>
  );
};

export default Index;
