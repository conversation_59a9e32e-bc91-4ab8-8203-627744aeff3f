import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { PageMain } from '@/components/ui';
import { PageTabItem, PageTabs } from './components/ui';
import DataTable from './components/DataTable';

const ReceiveMonitoring = () => {
  return (
    <PageMain>
      <HelmetTitle title="数据接收管理" />
      <PageHead title="数据管理">
        <span>系统管理</span>
        <i className="icomoon icon-next" />
        <span className="text-primary">接收监控</span>
      </PageHead>
      <PageTabs>
        <PageTabItem to="/receive-monitoring" active={1}>
          接收监控
        </PageTabItem>
        <PageTabItem to="/receiving-log">接收日志</PageTabItem>
        <PageTabItem to="/data-download">数据下载</PageTabItem>
      </PageTabs>
      <DataTable />
    </PageMain>
  );
};

export default ReceiveMonitoring;
