import { Table } from 'antd';
import { useQuery } from 'react-query';
import { columns } from '../tableCols';
import { fetchRealtimeLogList } from '../services';
import type { LogItem } from '../type';

const DataTable = () => {
  const { data, isLoading } = useQuery<LogItem[]>(
    ['realtime-list'],
    fetchRealtimeLogList,
  );

  return (
    <div style={{ margin: '20px 0' }}>
      <Table
        rowKey="id"
        loading={isLoading}
        dataSource={data || []}
        columns={columns}
        pagination={false}
      />
    </div>
  );
};

export default DataTable;
