import { Hor<PERSON>enter, Flex, ellipsisCss } from '@/components/ui';
import { Row } from 'antd';
import styled, { css } from 'styled-components';
import { Link } from 'react-router-dom';
import { getColorFromTheme } from '@/components/ui/utils';

export const FilterContainer = styled(Row)`
  margin: 30px 0 20px;
`;

export const ReceiveProgress = styled(HorCenter)<{
  percentage: number;
}>`
  margin-left: 20px;

  .bar-wrapper {
    width: 60px;
    height: 4px;
    overflow: hidden;
    background: #e8e8e8;
    border-radius: 2px;

    .bar {
      width: ${({ percentage }) => `${percentage}%`};
      height: 4px;
      background: ${({ theme }) => theme.colors.primary};
      border-radius: 2px;
    }
  }
  .num {
    padding-left: 4px;
    font-size: 12px;
  }
`;

export const FailedMessage = styled.div`
  width: 240px;
  padding-left: 8px;
  color: #999;
  margin-left: 8px;
  border-left: 1px solid #ccc;
  line-height: 1;
  cursor: default;

  ${ellipsisCss};
`;

export const PageTabs = styled(Flex)`
  margin-top: 40px;
  border-bottom: 1px solid #e6e6e6;
`;

export const PageTabItem = styled(Link)<{
  active?: number;
}>`
  position: relative;
  display: block;
  margin-right: 30px;
  padding: 0 20px 6px;
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  border-bottom: 3px solid transparent;

  ${({ active }) =>
    active &&
    css`
      pointer-events: none;
    `}

  ${(props) => {
    return props.active
      ? css`
          color: ${getColorFromTheme('primary')};
          border-color: ${getColorFromTheme('primary')};
        `
      : null;
  }}
`;
