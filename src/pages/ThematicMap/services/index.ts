import type { FetchParams } from '@/pages/ThematicMapList/atoms';
import { request } from '@/utils';
import type { SaveTmParams } from '../types';
import { stringify } from 'qs';
import omitBy from 'lodash/omitBy';

export const getTmList = (params: FetchParams) => {
  const newParams = omitBy(params, (param) => !param);
  return request(`/api/tm/list?${stringify(newParams)}`);
};

export const getTmDetails = (id: number | string) =>
  request(`/api/tm?id=${id}`);

export const createTm = (params: Omit<SaveTmParams, 'id'>) =>
  request(`/api/tm/create`, {
    method: 'POST',
    body: JSON.stringify(params),
  });

export const updateTm = (params: SaveTmParams) =>
  request(`/api/tm/update`, {
    method: 'POST',
    body: JSON.stringify(params),
  });

export const deleteTm = (id: string | number) =>
  request(`/api/tm/delete?id=${id}`, {
    method: 'POST',
  });

export const getTmMenu = () => request(`/api/tm/menu`);

export const renameTm = (params: { id: number; name: string }) =>
  request(`/api/tm/rename`, {
    method: 'POST',
    body: JSON.stringify(params),
  });
// 一会
export const getUserList = (regionCode: number) =>
  request(`/api/user/list?regionCode=${regionCode}&page=0&size=100`);

export const getTmUserList = (regionCode: number) =>
  request(`/api/tm/list/user?regionCode=${regionCode}`);

export interface FetchTextureListParams {
  endDate: string;
  startDate: string;
  type: string;
  agg: string;
}

export const fetchTextureList = (params: FetchTextureListParams) =>
  request(`/api/texture/meta?${stringify(params)}`);

export const getCustomizeRegions = () => request(`/api/region/customize/list`);

export const uploadImgWithCompression = (formData: FormData) => {
  return request(`/api/file/img/lossy/compression`, {
    method: 'POST',
    body: formData,
  });
};
