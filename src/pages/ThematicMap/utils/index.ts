import type { RGBColor } from 'react-color';
import type {
  CircleLayerProps,
  RectangleLayerProps,
  ScaleLayerProps,
} from '../types';

export const whiteRgba = { r: 255, g: 255, b: 255, a: 1 };
export const blackRgba = { r: 0, g: 0, b: 0, a: 1 };
export const redRgba = { r: 255, g: 0, b: 0, a: 1 };

export const rectangleLayerDefaultConfig: RectangleLayerProps['props'] = {
  top: 0,
  left: 0,
  backgroundColor: whiteRgba,
  borderStyle: 'solid',
  opacity: 100,
  borderWidth: 1,
  borderColor: whiteRgba,
  width: 200,
  height: 200,
};
export const circleLayerDefaultConfig: CircleLayerProps['props'] = {
  top: 0,
  left: 0,
  backgroundColor: { r: 255, g: 255, b: 255, a: 1 },
  borderStyle: 'solid',
  opacity: 100,
  borderWidth: 1,
  borderColor: redRgba,
  radius: 20,
  unit: 'km',
  lon: 0,
  lat: 0,
  mode: 'coordinate',
  as: 'circle',
};

export const textLayerDefaultConfig = {
  top: 20,
  left: 20,
  height: 16 * 1.5,
  fontSize: 16,
  fontFamily: 'Microsoft Yahei',
  italic: false,
  fontWeight: 400,
  color: blackRgba,
};

export const compassLayerDefaultConfig = {
  top: 20,
  left: 20,
  width: 90,
  height: 90,
  style: 'simple',
};

export const transformRgbColorToString = (rgbColor?: RGBColor) =>
  rgbColor
    ? `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, ${rgbColor.a})`
    : '';

export const scaleLayerDefaultConfig: ScaleLayerProps['props'] = {
  borderWidth: 4,
  color: { r: 0, g: 0, b: 0, a: 1 },
  borderStyle: 'solid',
  fontSize: 14,
  translate: [0, 0],
  top: 0,
  left: 0,
  type: 'horizontal',
  style: 'bar',
};

export const dcolorLegends = [
  {
    id: 1,
    color: '#7e0023',
    label: '冰云 厚云 高层云',
  },
  {
    id: 2,
    color: '#000',
    label: '薄卷云 凝结尾流',
  },
  {
    id: 3,
    color: '#99703C',
    label: '厚云 中层云',
  },

  {
    id: 4,
    from: '#EA6CC3',
    to: '#E23A39',
    label: '沙尘',
  },
  {
    id: 5,
    color: '#BAAF3B',
    label: '冷气团 低层云',
  },
  {
    id: 6,
    color: '#AA64D4',
    label: '热气团 低层云',
  },
  {
    id: 7,
    color: '#3F7754',
    label: '薄云 中层云',
  },
];

export const coders = {
  pm25: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  pm10: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  no2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  o3: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  so2: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 1000) / 255),
      Math.floor((v * 1000) % 255),
      0,
      0,
    ],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 1000,
  },
  aod: {
    format: 'float',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 1000) / 255),
      Math.floor((v * 1000) % 255),
      0,
      0,
    ],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 1000,
  },
  co: {
    format: 'double',
    bands: 1,
    encoder: (v: number) => [
      Math.floor((v * 10000) / 255),
      Math.floor((v * 10000) % 255),
      0,
      0,
    ],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 10000,
  },
  DCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
    decoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
  },
  TCOLOR: {
    format: 'byte',
    bands: 4,
    encoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
    decoder: ([r, g, b, a]: [number, number, number, number]) => [r, g, b, a],
  },
};

export const isSpecialArea = (code: string) => code.substring(2, 4) === '99';

export const dataUrlToFile = (dataUrl: string, type: string) => {
  const binary = atob(dataUrl.split(',')[1]);
  const array = [];
  for (let i = 0; i < binary.length; i++) {
    array.push(binary.charCodeAt(i));
  }
  return new Blob([new Uint8Array(array)], { type: type });
};
