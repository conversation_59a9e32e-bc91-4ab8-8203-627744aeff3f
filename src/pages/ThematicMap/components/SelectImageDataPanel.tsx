import { StyledButton } from '@/components/ui';
import { message, Checkbox } from 'antd';
import { useAtomValue } from 'jotai';
import { useAtomCallback } from 'jotai/utils';
import React, { useState, useMemo } from 'react';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { layerAtomFamily, layerIdsAtom } from '../atoms';
import type { LayerProps } from '../types';
import {
  DataContainer,
  DataSelectPanel,
  ImageDataItem,
  SelectDataModalBody,
  SelectDataModalFooter,
  SelectDataModalTitle,
} from './ui';

export interface SelectedItem {
  id: string;
  dataUrl: string;
  layerType: string;
  groupName: string;
  displayName: string;
  internalName: string;
  legendType: string;
}

interface Props {
  visible?: boolean;
  onOk: (vals: SelectedItem[]) => void;
  onCancel: () => void;
}

const config = [
  {
    dataUrl: '',
    displayName: '沙尘红外图',
    groupName: '影像图层',
    id: '0',
    internalName: 'DCOLOR',
    layerType: 'colormap',
    legendType: 'colormap',
  },
  {
    dataUrl: '',
    displayName: '卫星影像图',
    groupName: '影像图层',
    id: '1',
    internalName: 'TCOLOR',
    layerType: 'colormap',
    legendType: 'colormap',
  },
  {
    dataUrl: '',
    displayName: '沙尘掩码',
    groupName: '影像图层',
    id: '2',
    internalName: 'DMASK',
    layerType: 'heatmap',
    legendType: 'heatmap',
  },
];

const SelectImageDataPanel: React.FC<Props> = ({ visible, onOk, onCancel }) => {
  const [selected, setSelected] = useState<number[]>([]);
  const layerIds = useAtomValue(layerIdsAtom);
  const [layers, setLayers] = useState<LayerProps[]>([]);

  const getDataLayers = useAtomCallback(
    useCallback(
      (get) => {
        return layerIds
          .map((id) => {
            return get(layerAtomFamily({ id }));
          })
          .filter(
            (layer) => layer.type === 'heatmap' || layer.type === 'colormap',
          );
      },
      [layerIds],
    ),
  );

  useEffect(() => {
    const lys = getDataLayers();
    setLayers(lys);
  }, [getDataLayers]);

  const layerNames = useMemo(() => {
    return layers.map((item) => item.name);
  }, [layers]);

  const handleOnOk = useCallback(() => {
    if (selected.length === 0) {
      message.error('请选择数据源');
    } else {
      onOk(selected.map((item) => config[item]));
    }
  }, [onOk, selected]);

  useEffect(() => {
    return () => {
      setSelected([]);
    };
  }, [visible]);

  const disabled = useMemo(
    () => layers.some((layer) => layer.type === 'colormap'),
    [layers],
  );

  const handleCheckboxChange = useCallback(
    (index: number) => {
      if (selected.includes(index)) {
        setSelected([]);
      } else {
        setSelected([index]);
      }
    },
    [selected],
  );

  return visible ? (
    <DataSelectPanel>
      <SelectDataModalBody>
        <SelectDataModalTitle>新增影像</SelectDataModalTitle>
        <DataContainer>
          <ImageDataItem>
            <Checkbox
              checked={
                selected.includes(0) ||
                layerNames.includes('影像图层-沙尘红外图')
              }
              disabled={disabled}
              onChange={() => handleCheckboxChange(0)}
            >
              <div className="label">沙尘红外图</div>
            </Checkbox>
          </ImageDataItem>
          <ImageDataItem>
            <Checkbox
              checked={
                selected.includes(1) ||
                layerNames.includes('影像图层-卫星影像图')
              }
              disabled={disabled}
              onChange={() => handleCheckboxChange(1)}
            >
              <div className="label">卫星影像图</div>
            </Checkbox>
          </ImageDataItem>
          <ImageDataItem>
            <Checkbox
              checked={
                selected.includes(2) ||
                layerNames.includes('影像图层-沙尘掩码')
              }
              disabled={disabled}
              onChange={() => handleCheckboxChange(2)}
            >
              <div className="label">沙尘掩码</div>
            </Checkbox>
          </ImageDataItem>
        </DataContainer>
        <SelectDataModalFooter>
          <StyledButton onClick={onCancel}>取消</StyledButton>
          <StyledButton variant="primary" onClick={handleOnOk}>
            确定
          </StyledButton>
        </SelectDataModalFooter>
      </SelectDataModalBody>
    </DataSelectPanel>
  ) : null;
};

export default SelectImageDataPanel;
