import { useAtom } from 'jotai';
import { memo } from 'react';
import { canvasSizeAtom } from '../atoms';
import { useReCalcLayerPos } from '../hooks';
import CanvasSizeControl from './CanvasSizeControl';

const CanvasSizeControlContainer = () => {
  const [canvasSize, setCanvasSize] = useAtom(canvasSizeAtom);
  const reCalcLayerPos = useReCalcLayerPos();

  return (
    <CanvasSizeControl
      width={canvasSize.width}
      height={canvasSize.height}
      resetSize={() => {
        const container = document.getElementById('map-container');
        setCanvasSize((prev) => {
          reCalcLayerPos({
            oldSize: {
              width: prev.width,
              height: prev.height,
            },
            newSize: {
              width: (container?.offsetWidth || 0) - 4,
              height: (container?.offsetHeight || 0) - 4,
            },
          });
          return {
            width: (container?.offsetWidth || 0) - 4,
            height: (container?.offsetHeight || 0) - 4,
          };
        });
      }}
      setWidth={(val) => {
        // if (+val > 1330 || +val < 808) {
        //   message.error('画布最大宽度为1330px，最大宽度为1330px');
        // } else {
        setCanvasSize((prev) => {
          reCalcLayerPos({
            oldSize: {
              width: prev.width,
              height: prev.height,
            },
            newSize: {
              width: +val,
              height: prev.height,
            },
          });

          return {
            ...prev,
            width: +val,
          };
        });
        // }
      }}
      setHeight={(val) => {
        // if (+val > 950 || +val < 592) {
        //   message.error('画布最大高度为950px，最小高度为592px');
        // }
        setCanvasSize((prev) => {
          reCalcLayerPos({
            oldSize: {
              width: prev.width,
              height: prev.height,
            },
            newSize: {
              width: prev.width,
              height: +val,
            },
          });

          return {
            ...prev,
            height: +val,
          };
        });
      }}
    />
  );
};

export default memo(CanvasSizeControlContainer);
