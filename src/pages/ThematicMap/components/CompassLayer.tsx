import { useRouter } from '@/hooks';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import React, { useCallback } from 'react';
import { useMemo } from 'react';
import {
  currentLayerIdAtom,
  layerAtomFamily,
  layerIdsAtom,
  mapTypeAtom,
  viewStateAtom,
} from '../atoms';
import Moveable from 'react-moveable';
import { useAtom } from 'jotai';
import type { CompassLayerProps } from '../types';

const CompassLayer = () => {
  const { query } = useRouter();
  const [layerIds] = useAtom(layerIdsAtom);
  const compassId = useMemo(() => {
    const find = layerIds.find((layerId) => layerId.includes('compass'));
    return find ? find : '';
  }, [layerIds]);
  const [compassLayer, setCompassLayer] = useAtom(
    layerAtomFamily({ id: compassId }),
  );
  const mapType = useAtomValue(mapTypeAtom);
  const setViewState = useUpdateAtom(viewStateAtom);
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const { id = '0' } = compassLayer;
  const compassImg = useMemo(() => {
    if (mapType === 'vec') {
      return `/assets/images/compass-in-tm.png`;
    }
    return `/assets/images/compass-white-big.png`;
  }, [mapType]);
  const isCurrent = currentLayerId === id;

  const updateLayerProps = useCallback(
    (newVal: any) => {
      setCompassLayer((prev: CompassLayerProps) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      }));
    },
    [setCompassLayer],
  );

  return compassLayer.type ? (
    <>
      <Moveable
        key={id}
        target={
          isCurrent && query.type !== 'view' && compassLayer.visible
            ? (document.querySelector(`#${compassLayer.id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        resizable={currentLayerId === id}
        keepRatio={true}
        draggable={currentLayerId === id}
        throttleDrag={0}
        throttleResize={0}
        onDrag={({ top, left }) => {
          updateLayerProps({
            top,
            left,
          });
        }}
        onResize={({ width, target, delta, direction }) => {
          const top = (target as HTMLDivElement).offsetTop;
          const left = (target as HTMLDivElement).offsetLeft;
          const newTop = direction[1] < 0 ? top - delta[1] : top;
          const newLeft = direction[0] < 0 ? left - delta[0] : left;

          updateLayerProps({
            top: newTop,
            left: newLeft,
            width,
            height: width,
          });
        }}
      />

      <div
        id={compassLayer.id}
        onClick={() => {
          if (query.type === 'view') return;
          setCurrentLayerId(id);
        }}
        onWheel={(e) => {
          setViewState((prev) => {
            let zoom = (prev.zoom || 0) + (e.deltaY > 0 ? -0.5 : 0.5);
            zoom = zoom < 3 ? 3 : zoom;
            zoom = zoom > 18 ? 18 : zoom;

            return {
              ...prev,
              minZoom: 3,
              zoom,
              transitionDuration: 0,
            };
          });
        }}
        style={{
          position: 'absolute',
          top: compassLayer.props.top,
          left: compassLayer.props.left,
          zIndex: 9999,
          width: compassLayer.props.width,
          height: compassLayer.props.height,
          display: compassLayer.visible ? 'block' : 'none',
        }}
      >
        <img
          src={compassImg}
          alt=""
          style={{ width: '100%', userSelect: 'none', pointerEvents: 'none' }}
        />
      </div>
    </>
  ) : null;
};

export default CompassLayer;
