import { Flex } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { useRouter } from '@/hooks';
import { fetchTextureTimePoints } from '@/services/global';
import { dateFormatter } from '@/utils';
import { DatePicker, Select } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { useMemo, useEffect } from 'react';
import { useQuery } from 'react-query';
import { currentLayerIdAtom, layerAtomFamily } from '../atoms';
import { useUpdateFamilyChildProps } from '../hooks';
import { ConfigLabel } from './ui';
import dayjs from 'dayjs';

const ColorMapLayerDateTime = () => {
  const { query } = useRouter();
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const update = useUpdateFamilyChildProps();
  const [current, setCurrent] = useAtom(
    layerAtomFamily({
      id: currentLayerId,
    }),
  );
  const { startDate, endDate, time } = current.props;

  const { data } = useQuery(
    ['meta-in-control', startDate],
    () =>
      fetchTextureTimePoints({
        agg: 'none',
        startDate,
        endDate,
        type: current.props.dataType,
      }),
    {
      staleTime: 5 * 1000 * 60,
    },
  );

  const options = useMemo(() => {
    return (data || []).map((item: any) => ({
      label: dayjs(item.timePoints).format('HH:mm'),
      value: dayjs(item.timePoints).format('HH:mm'),
    }));
  }, [data]);

  useEffect(() => {
    if (
      options.length > 0 &&
      !query.type &&
      !options.find((item: { value: string }) => item.value === time)
    ) {
      setCurrent((prev: any) => {
        return {
          ...prev,
          props: {
            ...prev.props,
            time: options[0].value,
          },
        };
      });
    }
  }, [options, query.type, setCurrent, time]);

  return (
    <>
      <ConfigLabel>数据范围</ConfigLabel>
      <Flex>
        <DatePicker
          className="dark-form-item flex-1"
          getPopupContainer={getDarkContainer}
          value={dayjs(current.props.startDate)}
          disabledDate={(curDate) => {
            return curDate && curDate.isAfter(dayjs().endOf('day'));
          }}
          onChange={(val) => {
            if (val) {
              update({
                id: String(currentLayerId),
                newVal: {
                  startDate: val.format(dateFormatter),
                  endDate: `${val.format(dateFormatter)} 23:59`,
                },
              });
            }
          }}
        />
        <Select
          className="flex-1 dark-form-item"
          getPopupContainer={getDarkContainer}
          onChange={(val) =>
            update({
              id: String(currentLayerId),
              newVal: {
                time: val,
              },
            })
          }
          style={{ marginLeft: 20 }}
          options={options}
          value={time}
        />
      </Flex>
    </>
  );
};

export default ColorMapLayerDateTime;
