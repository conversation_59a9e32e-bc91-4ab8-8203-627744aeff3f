import { userInfoAtom } from '@/atoms';
import { useCascaderOptionsAndMatchValues } from '@/hooks';
import { Cascader } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { memo, useEffect, useMemo } from 'react';
import { useQuery } from 'react-query';
import {
  isCascaderChangeAtom,
  regionCodeAtom,
  shouldShowAllRegionAtom,
} from '../atoms';
import { getCustomizeRegions } from '../services';
import type { IRegion } from '../types';
import { isSpecialArea } from '../utils';

const RegionCascader = () => {
  const [regionCode, setRegionCode] = useAtom(regionCodeAtom);
  const userInfo = useAtomValue(userInfoAtom);
  const setIsCascaderChange = useUpdateAtom(isCascaderChangeAtom);
  const { options, cascaderValue } = useCascaderOptionsAndMatchValues(
    Number(regionCode || userInfo?.regionCode),
  );
  const [shouldShowAllRegion, setShouldShowAllRegion] = useAtom(
    shouldShowAllRegionAtom,
  );


  const value = useMemo(() => {
    if (isSpecialArea(`${regionCode}`)) {
      return [Number(regionCode)];
    }
    return cascaderValue;
  }, [cascaderValue, regionCode]);

  const { data } = useQuery<IRegion[]>(
    ['customize-regions'],
    getCustomizeRegions,
  );

  useEffect(() => {
    return () => {
      setIsCascaderChange(false);
    };
  }, [setIsCascaderChange, setShouldShowAllRegion]);

  return (
    <>
      <Cascader
        style={{ marginBottom: 8,color: '#fff' }}
        className="dark-form-item w-full text-[#fff]"
        allowClear={false}
        changeOnSelect
        value={shouldShowAllRegion ? [0] : value}
        fieldNames={{
          label: 'name',
          value: 'code',
        }}
        options={
          [
            ...options,
            {
              name: '全域',
              code: 0,
            },
            ...(data || []),
          ] as any
        }
        dropdownClassName="dark-cascader-dropdown"
        bordered={false}
        onChange={(val: any) => {
          if (val && val.length > 0) {
            setIsCascaderChange(true);
            const code = +val[val.length - 1];
            setShouldShowAllRegion(code === 0);
            if (code !== 0) {
              setRegionCode(code);
            }
          }
        }}
      />
    </>
  );
};

export default memo(RegionCascader);
