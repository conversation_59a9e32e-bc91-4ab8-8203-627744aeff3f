import { memo } from 'react';
import type { ChangeEvent } from 'react';
import React, { useCallback, useRef } from 'react';

import { ToolItem, ToolItemBox } from './ui';
import ToolBoxDragItem from './ToolBoxDragItem';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useMutation } from 'react-query';
import { uploadFile } from '@/services/global';
import { currentLayerIdAtom, layerAtomFamily, layerIdsAtom } from '../atoms';
import type { ImageLayerProps } from '../types';
import { useAtom } from 'jotai';
import { useSetAtom as useUpdateAtom } from 'jotai';
import { message } from 'antd';

const ToolBox: React.FC<{
  dataPanelVisible: boolean;
  onDataLayerBtnClick: () => void;
  imagePanelVisible: boolean;
  onImageLayerBtnClick: () => void;
}> = ({
  dataPanelVisible,
  onDataLayerBtnClick,
  imagePanelVisible,
  onImageLayerBtnClick,
}) => {
  const [layerIds, setLayerIds] = useAtom(layerIdsAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const uploadMutation = useMutation((file: FormData) => uploadFile(file));
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleClick = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  const handleUpload = useCallback(
    (e: ChangeEvent) => {
      const input = e.target as HTMLInputElement;
      const { files } = input;
      if (files && files[0]) {
        if (files[0].size > 1024 * 1024 * 10) {
          message.error('图片最大限制为10M，请重新上传');
          return;
        }
        const formData = new FormData();
        formData.append('file', files[0]);
        uploadMutation.mutate(formData, {
          onSuccess(d) {
            const id = `image-layer-${new Date().getTime()}`;
            const lens = layerIds.filter((layerId) =>
              layerId.includes('image'),
            ).length;
            const newImageLayer: ImageLayerProps = {
              id,
              type: 'image',
              name: `图片图层${lens + 1}`,
              visible: true,
              props: {
                width: 200,
                left: 20,
                top: 20,
                url: d.url,
              },
            };

            layerAtomFamily(newImageLayer);
            setLayerIds((prev) => [id, ...prev]);
            setCurrentLayerId(id);
          },
        });
      }
    },
    [layerIds, setCurrentLayerId, setLayerIds, uploadMutation],
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <ToolItemBox>
        <ToolItem active={dataPanelVisible} onClick={onDataLayerBtnClick}>
          <i className="icomoon icon-data-layer" />
          <div className="label">数据</div>
        </ToolItem>
        <ToolItem active={imagePanelVisible} onClick={onImageLayerBtnClick}>
          <i className="icomoon icon-image-layer" />
          <div className="label">影像</div>
        </ToolItem>
        <ToolBoxDragItem
          label="文本"
          iconClass="icon-layer-title"
          type="new-text"
        />
        <ToolItem onClick={handleClick}>
          <i className="icomoon icon-pic" />
          <div className="label">图片</div>
        </ToolItem>
        <input
          ref={fileInputRef}
          type="file"
          name=""
          accept="image/jpg, image/png, image/jpeg"
          id="image-uploader"
          style={{ display: 'none' }}
          onChange={handleUpload}
        />
        <ToolBoxDragItem
          label="色块"
          iconClass="icon-rect"
          type="new-rectangle"
        />
        <ToolBoxDragItem
          label="范围"
          iconClass="icon-circle"
          type="new-circle"
        />
        <ToolBoxDragItem label="箭头" iconClass="icon-arrow" type="new-arrow" />
      </ToolItemBox>
    </DndProvider>
  );
};

export default memo(ToolBox);
