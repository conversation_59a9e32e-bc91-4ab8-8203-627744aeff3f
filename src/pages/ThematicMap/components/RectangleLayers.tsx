import { use<PERSON>tom } from 'jotai';
import React, { useCallback } from 'react';
import {
  currentLayerIdAtom,
  layerAtomFamily,
  layerIdsAtom,
  viewStateAtom,
} from '../atoms';
import Moveable from 'react-moveable';
import { useRouter } from '@/hooks';
import { transformRgbColorToString } from '../utils';
import { useLayerCanDrop } from '../hooks';
import { useSetAtom as useUpdateAtom } from 'jotai';

const RectangleLayerItem: React.FC<{
  id: string;
  layerIndex: number;
  isCurrent: boolean;
  setCurrentLayerId: any;
}> = ({ isCurrent, id, setCurrentLayerId, layerIndex }) => {
  const { query } = useRouter();
  const [current, setCurrent] = useAtom(layerAtomFamily({ id }));
  const { visible, props } = current;
  const setViewState = useUpdateAtom(viewStateAtom);
  const drop = useLayerCanDrop();

  const setRectProps = useCallback(
    (newVal: Record<string, any>) => {
      setCurrent((prev: any) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      }));
    },
    [setCurrent],
  );

  return (
    <React.Fragment key={id}>
      <Moveable
        target={
          isCurrent && query.type !== 'view' && visible
            ? (document.querySelector(`#${id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        draggable={isCurrent}
        resizable={isCurrent}
        throttleDrag={1}
        throttleResize={1}
        onDrag={({ left, top }) => {
          setRectProps({
            left,
            top,
          });
        }}
        onResize={({ target, width, height, delta, direction }) => {
          const top = (target as HTMLDivElement).offsetTop;
          const left = (target as HTMLDivElement).offsetLeft;
          const newTop = direction[1] < 0 ? top - delta[1] : top;
          const newLeft = direction[0] < 0 ? left - delta[0] : left;

          setRectProps({
            top: newTop,
            left: newLeft,
            width,
            height,
          });
        }}
      />
      <div
        id={id}
        ref={drop}
        onClick={() => {
          if (query.type === 'view') return;
          setCurrentLayerId(id);
        }}
        onWheel={(e) => {
          setViewState((prev) => {
            let zoom = (prev.zoom || 0) + (e.deltaY > 0 ? -0.5 : 0.5);
            zoom = zoom < 3 ? 3 : zoom;
            zoom = zoom > 18 ? 18 : zoom;

            return {
              ...prev,
              minZoom: 3,
              zoom,
              transitionDuration: 0,
            };
          });
        }}
        className="target"
        style={{
          position: 'absolute',
          top: props.top,
          left: props.left,
          width: props.width,
          height: props.height,
          zIndex: 1000 - layerIndex,
          background: transformRgbColorToString(props.backgroundColor),
          borderWidth: props.borderWidth,
          borderStyle: props.borderStyle,
          borderColor: transformRgbColorToString(props.borderColor),
          opacity: props.opacity / 100,
          display: visible ? 'block' : 'none',
        }}
      />
    </React.Fragment>
  );
};

const RectangleLayers = () => {
  const [layerIds] = useAtom(layerIdsAtom);
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const rectLayerIds = layerIds.filter((layerId) =>
    layerId.includes('rectangle'),
  );

  return (
    <>
      {rectLayerIds.map((layerId) => {
        const index = layerIds.findIndex((lyId) => lyId === layerId);

        return (
          <RectangleLayerItem
            key={layerId}
            id={layerId}
            layerIndex={index}
            isCurrent={currentLayerId === layerId}
            setCurrentLayerId={setCurrentLayerId}
          />
        );
      })}
    </>
  );
};

export default RectangleLayers;
