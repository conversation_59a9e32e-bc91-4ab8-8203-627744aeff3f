import {
  Center,
  ellipsis<PERSON>s,
  Flex,
  FlexCol,
  Hor<PERSON>enter,
  scroll<PERSON><PERSON><PERSON>ty<PERSON>,
  StyledButton,
} from '@/components/ui';
import { getColorFromTheme } from '@/components/ui/utils';
import { Bcard } from '@/components/ui';
import styled, { createGlobalStyle, css } from 'styled-components';

export const TmPageMain = styled(FlexCol)`
  flex: 1;
  padding: 30px 30px 0 30px;
  align-items: stretch;
`;

export const Container = styled(Flex)`
  flex: 1;
  padding: 16px 0 24px;
  align-items: stretch;
`;

export const ImgContainer = styled.div`
  flex: 1;
  overflow: auto;
  img {
    display: block;
    width: 100%;
  }
`;

export const MapContainer = styled.div`
  position: relative;
  flex: 1;
  /* border-radius: 10px; */
  overflow: hidden;
  /* border: 1px solid #ddd; */
  background: white;
  overflow: auto;

  /* 拖拽库 */
  .moveable-line {
    background: #286cff !important;
  }
  .moveable-control {
    background: #286cff !important;
  }
  .moveable-line.moveable-rotation-line .moveable-control {
    background: #fff !important;
    border-color: #286cff !important;
  }
  .moveable-rotation .moveable-rotation-control {
    background: #fff !important;
    border-color: #286cff !important;
  }
  .moveable-control.moveable-origin {
    background: #fff !important;
  }
`;

export const Right = styled(FlexCol)`
  position: relative;
  width: 440px;
  align-items: stretch;
  margin-left: 12px;
`;

const baseCardCss = css`
  color: white;
  background: #25262d;
  border-radius: 8px;
  overflow: hidden;
`;

export const Rt = styled(Flex)`
  column-gap: 10px;
  margin-bottom: 10px;
`;

export const RtBtnGroup = styled(HorCenter)`
  flex: 1;
  height: 42px;
  padding: 10px;
  ${baseCardCss};

  .icomoon {
    margin-right: 4px;
  }

  span {
    flex: 1;
    color: white;
    line-height: 14px;
    cursor: pointer;

    &:first-child {
      border-right: 1px solid #838485;
    }
  }
`;

export const SingleBtn = styled(Center)`
  width: 100%;
  height: 42px;
  line-height: 42px;
  cursor: pointer;

  .icomoon {
    margin-right: 6px;
  }

  ${baseCardCss};
`;

export const RightInner = styled(Flex)`
  position: relative;
  flex: 1;
  overflow-x: visible;
  overflow-y: hidden;

  ${baseCardCss};
`;
export const ToolItemBox = styled(FlexCol)`
  width: 42px;
  padding-top: 26px;
  align-items: center;
  border-right: 1px solid #000;
`;
export const ToolItem = styled.div<{
  active?: boolean;
}>`
  position: relative;
  width: 32px;
  height: 52px;
  padding-top: 6px;
  margin-bottom: 24px;
  font-size: 12px;
  text-align: center;
  border-radius: 4px;
  color: #999;
  background: ${({ active }) => {
    return active ? '#1C1D24' : 'transparent';
  }};
  cursor: pointer;
  user-select: none;

  &:hover {
    color: white;
    background: #286cff;
  }

  &::after {
    position: absolute;
    bottom: -12px;
    left: 50%;
    width: 16px;
    height: 1px;
    margin-left: -8px;
    background: #1c1d24;
    content: '';
  }

  .icomoon {
    color: white;
    font-size: 16px;
  }

  .label {
  }
`;

export const PropPanel = styled(FlexCol)`
  flex: 1;
  align-items: stretch;
  overflow: hidden;
`;

export const ScrollContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  ${scrollBarStyles};
`;

export const PanelTitle = styled(Flex)`
  line-height: 16px;
  padding: 12px 0;
  font-size: 18px;
  font-weight: 700;

  .line {
    width: 3px;
    height: 16px;
    margin-right: 7px;
    background: #286cff;
  }
`;

export const DarkContainer = styled.div`
  padding: 12px 20px;
  background: #1c1d24;

  .dark-form-item.ant-input-number {
    border-color: transparent;
  }

  .dark-form-item.ant-input,
  .dark-form-item .ant-input-number-input,
  .dark-form-item .ant-input,
  .dark-form-item.ant-cascader-picker,
  .dark-form-item.ant-select:not(.ant-select-customize-input)
    .ant-select-selector,
  .dark-form-item.ant-picker {
    background: #25262d !important;
    color:#fff !important;
  }
`;

export const SubTitle = styled(HorCenter)`
  font-size: 12px;
  padding: 10px 32px 10px 10px;
  cursor: pointer;

  .icomoon {
    color: #c1c1c4;
  }
`;

export const Label = styled.div`
  margin-bottom: 4px;
  padding-left: 12px;
  font-size: 12px;
  color: #999;
`;

export const LayersContainer = styled(DarkContainer)`
  border-top: 1px solid #25262d;
  flex: 1;
  overflow-y: auto;

  ${scrollBarStyles};

  ${Label} {
    color: white;
  }
`;

export const LayerItem = styled(HorCenter) <{
  active?: boolean;
}>`
  padding: 12px;
  border-radius: 4px;
  overflow: hidden;

  .icomoon.edit-icon,
  .icon-handle {
    cursor: pointer;

    &:hover {
      color: white;
    }
  }

  ${({ active, theme }) => {
    return active
      ? css`
          color: white;
          background: ${theme.colors.primary};
        `
      : css`
          color: #838485;
          border-bottom: 1px solid #25262d;

          .icon-layer-title {
            color: ${theme.colors.primary};
          }
        `;
  }}

  .icon-handle {
    margin-right: 16px;
  }

  .layer-icon {
    margin-right: 8px;
    font-size: 14px;
  }

  .hidden {
    visibility: hidden;
  }

  .label {
    flex: 1;
    color: white;
    line-height: 1;
    cursor: pointer;
    ${ellipsisCss};
  }

  .edit-icon {
    margin-left: 12px;
    font-size: 14px;
  }
`;

export const LayersActions = styled(Flex)`
  padding: 12px 0;

  ${Center} {
    flex: 1;
    justify-content: center;
    line-height: 1;
    cursor: pointer;

    &:not(last-child) {
      border-right: 1px solid #838485;
    }

    .icomoon {
      margin-right: 4px;
    }
  }
`;

export const TextConfigRow = styled(HorCenter)`
  column-gap: 12px;
  margin-bottom: 12px;
`;

export const ColorPickerInner = styled.div<{
  background: string | undefined;
}>`
  flex: 1;
  width: 200px;
  height: 32px;
  background: ${({ background }) => background};
  border-radius: 4px;
  border: 4px solid #25262d;
`;

export const SelectDataModalBody = styled.div`
  padding: 30px 30px 0;
`;
export const SelectDataModalTitle = styled.div`
  padding-left: 10px;
  font-size: 18px;
  line-height: 1;
  border-left: 3px solid ${getColorFromTheme('primary')};
  color: white;
`;

export const SelectDataModalFooter = styled(Flex)`
  justify-content: flex-end;
  padding: 20px 0;

  ${StyledButton} {
    margin-left: 20px;
  }
`;

export const DataContainer = styled.div`
  max-height: 320px;
  overflow-y: auto;
  margin-top: 20px;
  padding: 0 12px 20px;

  ${scrollBarStyles};

  .title {
  }

  .item {
  }

  /* .dark-checkbox {
    .ant-checkbox-inner {
      background: transparent;
      border-color: #838485;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #286cff;
      border-color: #286cff;
      &::after {
        border-color: white;
      }
    }

    .ant-checkbox-checked::after {
      border-color: white;
    }
  } */
  .ant-checkbox:not(.ant-checkbox-checked) .ant-checkbox-inner {
    background-color: #fff;
    border: 1px solid #fff;
  }
  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #fff;
    border: 1px solid #fff;
  }
  
  .ant-checkbox-checked .ant-checkbox .ant-checkbox-inner{
    background-color: #286cff !important;
    border: 1px solid #286cff !important;
  }
`;

export const CollapseTitle = styled.div`
  flex: 1;
  font-size: 14px;

  .ant-checkbox-inner {
    background: transparent;
  }
`;

// export const DataItem = styled.div<{
//   active?: boolean;
// }>`
//   position: relative;
//   margin-right: 24px;
//   margin-bottom: 16px;
//   padding-left: 22px;
//   line-height: 14px;
//   color: white;
//   cursor: pointer;

//   &::before {
//     position: absolute;
//     top: 50%;
//     left: 0;
//     width: 14px;
//     height: 14px;
//     margin-top: -7px;
//     border-radius: 2px;
//     content: '';

//     ${({ active, theme }) => {
//       return active
//         ? css`
//             background: ${theme.colors.primary};
//           `
//         : css`
//             border: 1px solid #838485;
//           `;
//     }}
//   }
// `;

export const ScaleContainer = styled(HorCenter) <{
  color?: string;
  borderWidth: number;
  borderStyle: 'solid' | 'dotted' | 'dashed';
  fontSize: number;
}>`
  position: absolute;
  top: 10;
  left: 10;
  z-index: 10;
  cursor: pointer;
  user-select: none;

  .bar {
    position: relative;
    width: 100px;
    color: ${({ color }) => color};
    font-weight: 700;
    font-size: ${({ fontSize }) => `${fontSize}px`};
    text-align: center;
    border-bottom: ${({ color, borderWidth, borderStyle }) =>
    `${borderWidth}px ${borderStyle} ${color} `};

    &::before,
    &::after {
      position: absolute;
      bottom: 0;
      width: 2px;
      height: ${({ borderWidth }) => `${borderWidth * 2}px`};
      background: ${({ color }) => color};
      content: '';
    }

    &::after {
      right: 0;
    }
    &::before {
      left: 0;
    }
  }
  .unit {
    padding-left: 4px;
  }
`;

export const NameModalTitle = styled(Flex)`
  margin-bottom: 16px;
  font-size: 16px;
  color: white;
`;

export const NameModalFooter = styled(Flex)`
  padding: 20px 30px;
  justify-content: flex-end;

  ${StyledButton} {
    margin-left: 20px;
  }
`;

export const DetailsViewLine = styled(HorCenter)`
  padding: 8px 0 8px 12px;

  .label {
    color: #999;
    font-size: 12px;
  }
  .value {
    padding-left: 30px;
    color: white;
    font-size: 14px;
  }
`;

export const LegendWrapper = styled(Flex)`
  position: absolute;
  z-index: 20;
`;
export const LegendType = styled.div`
  padding-right: 4px;
  line-height: 24px;
  font-size: 14px;
  text-align: right;

  p {
    margin: 0;
    line-height: 1;

    & + p {
      margin-top: 4px;
      font-size: 12px;
    }
  }
`;

export const LegendItem = styled(Flex)`
  width: 40px;
  flex-wrap: wrap;

  .lump {
    width: 100%;
  }
  .min {
    min-width: 10px;
    font-size: 12px;
  }
  .max {
    flex: 1;
    font-size: 12px;
    text-align: right;
    transform: translateX(50%);
  }
`;

export const DataSelectPanel = styled(Bcard)`
  width: 334px;
  left: -346px;
  top: 0;
  background: #25262d;
  border-radius: 8px;
  z-index: 10010;

  .ant-collapse {
    color: white;
    background: transparent;
    border: none;

    & > .ant-collapse-item {
      border-color: #1c1d24;
    }

    .ant-collapse-header {
      padding-left: 0 !important;
      color: white;
    }
    .ant-collapse-content {
      color: white;
      background: transparent;
      border-top-color: #1c1d24;
      .ant-collapse-content-box {
        padding: 0;
      }
    }
  }
`;

export const DataItemsContainer = styled.div`
  background: #1c1d24;
  border-radius: 4px;
  padding: 12px 24px;
  .ant-checkbox:not(.ant-checkbox-checked) .ant-checkbox-inner {
    background-color: #fff;
    border: 1px solid #fff;
  }
  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #fff;
    border: 1px solid #fff;
  }
  
  .ant-checkbox-checked .ant-checkbox .ant-checkbox-inner{
    background-color: #286cff !important;
    border: 1px solid #286cff !important;
  }

 
`;
export const DataItem = styled(HorCenter)`
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    margin-left: 8px;
    color: white;
  }
`;

export const ImageDataItem = styled(DataItem)`
  margin-bottom: 0;
  padding: 12px 0;
  border-bottom: 1px solid #1c1d24;
`;

export const SliderWrapper = styled(HorCenter)`
  .ant-slider-rail,
  .ant-slider:hover .ant-slider-rail {
    background: #2c2d33;
  }
  .ant-slider-track,
  .ant-slider:hover .ant-slider-track {
    background: #286cff;
  }

  .ant-slider-handle {
    background: #838485;
    border-color: #838485;
  }
`;

export const OpacityValWrapper = styled(HorCenter)`
  width: 80px;
  height: 28px;
  padding: 0 12px;
  background: #25262d;
  color: #999999;

  .label {
    font-size: 12px;
  }

  input {
    width: 40px;
    height: 28px;
    margin-right: 12px;
    background: transparent;
    border: none;
    outline: none;
  }
`;

export const ConfigLabel = styled.div<{
  marginTop?: string;
}>`
  margin-top: ${({ marginTop }) => marginTop || 0};
  margin-bottom: 4px;
  padding-left: 12px;
  color: #999;
  font-size: 12px;
`;

export const ShapeConfItemContainer = styled(HorCenter)`
  margin-top: 12px;
  column-gap: 12px;

  .item {
    flex: 1;
  }
`;
export const ColorConfigRect = styled.div`
  height: 28px;
  border-radius: 4px;
  background: #286cff;
`;
export const SolidConfigBox = styled(HorCenter)`
  justify-content: space-between;
  height: 28px;
  padding: 0 12px;
  border-radius: 4px;
  color: #838485;
  background: #25262d;

  .line {
    width: 36px;
    height: 0;
    margin-top: -1px;
    border-bottom: 2px solid #888888;
  }
`;
export const SolidOption = styled.div<{
  borderStyle?: 'solid' | 'dotted' | 'dashed';
}>`
  width: 80px;
  height: 40px;
  padding: 8px 24px;

  .line {
    margin-top: 11px;
    border-bottom: ${({ borderStyle }) => `2px ${borderStyle || 'solid'}`};
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

export const Divider = styled.div`
  height: 1px;
  background: #25262d;
`;

export const LegendBox = styled(HorCenter)`
  padding: 10px;
  background: #23242d;

  .colors {
    flex: 1;
    margin-right: 8px;

    .item {
      flex: 1;
      height: 8px;
    }
  }
`;

export const DescInInitModal = styled.div`
  margin: 12px 0;
  color: #999;
  font-size: 12px;
`;

export const GlobalStyle = createGlobalStyle`
  .ant-popover-arrow {
    color: transparent !important;
  }
`;

export const SizeInput = styled.input``;

export const SizeControlItem = styled(HorCenter)`
  flex: 1;
  width: 124px;
  height: 32px;
  margin-right: 12px;
  padding: 0 12px;
  border-radius: 4px;
  background: #25262d;

  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .input-wrapper {
    flex: 1;
  }

  ${SizeInput} {
    width: 100%;
    height: 32px;
    margin: 0;
    background: transparent;
    border: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .label {
    margin-left: 4px;
    color: #999;
    font-size: 12px;
  }
`;

export const DcolorLegendItem = styled(HorCenter)`
  min-width: 140px;
  .block {
    width: 10px;
    height: 10px;
    margin-right: 6px;
    border-radius: 2px;
  }
  .label {
    color: white;
    font-size: 12px;
  }
`;

export const LegendText = styled(HorCenter)`
  justify-content: space-between;
  margin-top: 6px;
  background: #25262d;
  border-radius: 4px;
  padding: 6px 12px;
  color: white;

  .block {
    width: 56px;
    height: 16px;
    background: white;
    border-radius: 2px;
  }

  .label {
    font-size: 12px;
    line-height: 16px;
  }
`;

export const PageLoading = styled.div`
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
`;
export const ErrorTips = styled.div`
  margin-top: 8px;
  font-size: 12px;
  color: #ff0000;
`;
