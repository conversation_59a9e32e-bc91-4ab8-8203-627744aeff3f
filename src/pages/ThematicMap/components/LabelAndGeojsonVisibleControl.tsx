import { Ho<PERSON><PERSON><PERSON>, Spacer } from '@/components/ui';
import React, { memo } from 'react';

const LabelAndGeojsonVisibleControl: React.FC<{
  geojsonVisible: boolean;
  tileLabelVisible: boolean;
  setGeojsonVisible: (v: boolean) => void;
  setTileLabelVisible: (v: boolean) => void;
}> = ({
  geojsonVisible,
  setGeojsonVisible,
  tileLabelVisible,
  setTileLabelVisible,
}) => {
  return (
    <div
      style={{
        padding: '12px',
        color: '#c1c1c4',
      }}
    >
      <HorCenter style={{ marginBottom: 12 }}>
        <Spacer>省市行政区域边界可见性设置</Spacer>
        <i
          className={`icomoon icon-${geojsonVisible ? 'visible' : 'hidden'}`}
          onClick={() => setGeojsonVisible(!geojsonVisible)}
        />
      </HorCenter>
      <HorCenter>
        <Spacer>城市名称可见性设置</Spacer>
        <i
          className={`icomoon icon-${tileLabelVisible ? 'visible' : 'hidden'}`}
          onClick={() => setTileLabelVisible(!tileLabelVisible)}
        />
      </HorCenter>
    </div>
  );
};

export default memo(LabelAndGeojsonVisibleControl);
