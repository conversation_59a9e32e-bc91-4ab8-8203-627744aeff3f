import { <PERSON><PERSON><PERSON><PERSON>, StyledButton } from '@/components/ui';
import React, { useState } from 'react';
import { ConfigLabel, ErrorTips, SizeControlItem, SizeInput } from './ui';

interface Props {
  width: string | number;
  height: string | number;
  setWidth: (width: string) => void;
  setHeight: (height: string) => void;
  resetSize: () => void;
}
const CanvasSizeControl: React.FC<Props> = ({
  width,
  height,
  setWidth,
  setHeight,
  resetSize,
}) => {
  const [errorVisible, setErrorVisible] = useState(false);
  return (
    <div>
      <ConfigLabel>画布尺寸</ConfigLabel>
      <HorCenter>
        <SizeControlItem>
          <div className="input-wrapper">
            <SizeInput
              placeholder="宽度"
              type="number"
              value={width}
              max={1330}
              min={950}
              onChange={(e) => {
                setWidth(e.target.value);
              }}
            />
          </div>
          <div className="label">W</div>
        </SizeControlItem>
        <SizeControlItem>
          <div className="input-wrapper">
            <SizeInput
              placeholder="高度"
              type="number"
              value={height}
              max={808}
              min={592}
              onChange={(e) => {
                setHeight(e.target.value);
              }}
            />
          </div>
          <div className="label">H</div>
        </SizeControlItem>
        <StyledButton onClick={resetSize}>重置</StyledButton>
      </HorCenter>
    </div>
  );
};

export default CanvasSizeControl;
