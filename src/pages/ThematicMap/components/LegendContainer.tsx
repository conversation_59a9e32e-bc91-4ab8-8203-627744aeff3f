import { useAtom } from 'jotai';
import React, { useCallback, useMemo, memo } from 'react';
import { currentLayerIdAtom, layerLegendsAtom } from '../atoms';
import HeatmapLegend from './HeatmapLegend';
import Moveable from 'react-moveable';
import { useRouter } from '@/hooks';
import { remoteSensingValuesAndColors } from '@/utils';
import { DcolorLegendItem } from './ui';
import { transformRgbColorToString } from '../utils';

const LegendContainer = () => {
  const { query } = useRouter();
  const [layerLegends, setLayerLegend] = useAtom(layerLegendsAtom);
  const [currentId, setCurrentId] = useAtom(currentLayerIdAtom);

  const dcolorLayerLegends = useMemo(
    () => layerLegends.filter((legend) => legend.dataType === 'DCOLOR'),
    [layerLegends],
  );

  const heatmapLayerLegends = useMemo(
    () => layerLegends.filter((legend) => legend.layerType === 'heatmap'),
    [layerLegends],
  );

  const setProps = useCallback(
    (newVal: Record<string, number[]>) => {
      setLayerLegend((prev) =>
        prev.map((item) =>
          item.id === currentId
            ? {
              ...item,
              props: {
                ...item.props,
                ...newVal,
              },
            }
            : item,
        ),
      );
    },
    [currentId, setLayerLegend],
  );

  return (
    <div
      style={{
        pointerEvents: 'auto',
      }}
    >
      {heatmapLayerLegends.map((legend) => {
        const isCurrent = currentId === legend.id;
        const lowerType = legend.dataType.toLowerCase();

        if (!remoteSensingValuesAndColors[lowerType]) {
          return null;
        }
        
        return (
          <React.Fragment key={legend.id}>
            <Moveable
              target={
                isCurrent && query.type !== 'view' && legend.visible
                  ? (document.querySelector(`#${legend.id}`) as HTMLDivElement)
                  : null
              }
              container={document.querySelector('#dom') as HTMLDivElement}
              zoom={1}
              origin={true}
              edge={false}
              keepRatio
              draggable={isCurrent}
              scalable={isCurrent}
              throttleDrag={0}
              throttleResize={0}
              onScale={({ scale }) => {
                setProps({
                  scale,
                });
              }}
              onDrag={({ translate }) => {
                setProps({
                  translate,
                });
              }}
            />
            <HeatmapLegend
              key={legend.id}
              top={legend.props.top - 50}
              left={legend.props.left}
              type={legend.dataType}
              id={legend.id}
              visible={legend.visible}
              color={transformRgbColorToString(legend.props.textColor)}
              scale={legend.props.scale}
              translate={legend.props.translate}
              onClick={() => setCurrentId(legend.id)}
            />
          </React.Fragment>
        );
      })}

      {dcolorLayerLegends.map((legend) => {
        const isCurrent = legend.id === currentId;
        const { scale, translate } = legend.props;
        return (
          <React.Fragment key={legend.id}>
            <Moveable
              target={
                isCurrent && query.type !== 'view' && legend.visible
                  ? (document.querySelector(`#${legend.id}`) as HTMLDivElement)
                  : null
              }
              container={document.querySelector('#dom') as HTMLDivElement}
              zoom={1}
              origin={true}
              edge={false}
              keepRatio
              draggable={isCurrent}
              scalable={isCurrent}
              throttleDrag={0}
              throttleResize={0}
              onScale={({ scale: sc }) => {
                setProps({
                  scale: sc,
                });
              }}
              onDrag={({ translate: tslate }) => {
                setProps({
                  translate: tslate,
                });
              }}
            />
            <DcolorLegendItem
              id={legend.id}
              style={{
                position: 'absolute',
                top: legend.props.top - 50,
                left: legend.props.left,
                bottom: 500,
                zIndex: 1001,
                display: legend.visible ? 'flex' : 'none',
                // eslint-disable-next-line max-len
                transform: `translate(${translate[0]}px, ${translate[1]}px) scale(${scale[0]}, ${scale[1]})`,
                userSelect: 'none',
              }}
              onClick={() => {
                setCurrentId(legend.id);
              }}
            >
              <div
                className="block"
                style={{
                  background: legend.props.color,
                  backgroundImage:
                    `linear-gradient` +
                    `(135deg, ${legend.props.from}, ${legend.props.to})`,
                }}
              />
              <span
                className="label"
                style={{
                  color: transformRgbColorToString(legend.props.textColor),
                }}
              >
                {legend.props.label}
              </span>
            </DcolorLegendItem>
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default memo(LegendContainer);
