import React from 'react';
import type { Viewport } from 'deck.gl';
import { MapView } from 'deck.gl';
import DeckGL from '@deck.gl/react';
import {
  useGeoJSONLayer,
  useMapEffect,
  useMapTextureLayers,
  useTileLayer,
  useTileLayerWithLabel,
  useIboTileLayer,
} from '../hooks';
import { useAtom } from 'jotai';
import {
  canvasSizeAtom,
  currentLayerIdAtom,
  scaleValueAtom,
  shouldShowAllRegionAtom,
  viewportAtom,
  viewStateAtom,
} from '../atoms';
import { useRef } from 'react';
import type { ContextProviderValue } from '@deck.gl/core/lib/deck';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useMemo } from 'react';
import { MapContainer } from './ui';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useMaskLayer, useQyG<PERSON><PERSON><PERSON>, useRouter } from '@/hooks';
import DropContainer from './DropContainer';
import LegendContainer from './LegendContainer';
import FpsThrottledDeck from './FpsThrottledDeck';
import LayersInMap from './LayersInMap';

const Map = () => {
  const { query } = useRouter();
  const deckRef = useRef<DeckGL<ContextProviderValue> | null>(null);
  const mapContainer = useRef<HTMLDivElement | null>(null);
  const setScaleValue = useUpdateAtom(scaleValueAtom);
  const [, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const { tileLayer } = useTileLayer();
  const { labeledTileLayer } = useTileLayerWithLabel();
  const [viewState, setViewState] = useAtom(viewStateAtom);
  const setViewport = useUpdateAtom(viewportAtom);
  const mapLayers = useMapTextureLayers();
  const canvasSize = useAtomValue(canvasSizeAtom);
  const shouldShowAllRegion = useAtomValue(shouldShowAllRegionAtom);

  const { geojson: qyGeojson } = useQyGeojson();
  const geojsonData = useMapEffect();
  const geojsonLayer = useGeoJSONLayer(geojsonData);
  const iboLayer = useIboTileLayer();
  const maskLayer = useMaskLayer(shouldShowAllRegion ? qyGeojson : geojsonData);

  const canCtrlMap = useMemo(() => query.type !== 'view', [query.type]);

  const canvasPos = useMemo(() => {
    if (mapContainer.current) {
      const { offsetHeight, offsetWidth } = mapContainer.current;

      const left = offsetWidth - canvasSize.width;
      const top = offsetHeight - canvasSize.height;

      return {
        left,
        top,
      };
    }
  }, [canvasSize.height, canvasSize.width]);

  return (
    <MapContainer id="map-container" ref={mapContainer}>
      <DndProvider backend={HTML5Backend}>
        <div
          id="canvas"
          style={{
            position: 'absolute',
            top: (canvasPos?.top || 0) / 2,
            left: (canvasPos?.left || 0) / 2,
            width: canvasSize.width,
            height: canvasSize.height,
            zIndex: 20,
          }}
        >
          <div
            id="dom"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: canvasSize.width,
              height: canvasSize.height,
              zIndex: 21,
              pointerEvents: 'none',
              overflow: 'hidden',
            }}
          >
            <LayersInMap />
            <LegendContainer />
          </div>
          <DeckGL
            ref={deckRef}
            views={[new MapView({ repeat: true, orthographic: true })]}
            // @ts-ignore
            Deck={FpsThrottledDeck}
            viewState={viewState}
            glOptions={{ preserveDrawingBuffer: true }}
            onViewStateChange={({ viewState: newViewState }) => {
              setCurrentLayerId(null);
              setViewState((prev) => ({
                ...prev,
                ...newViewState,
              }));
            }}
            controller={
              canCtrlMap
                ? {
                    scrollZoom: {
                      speed: 0.0025,
                      smooth: true,
                    },
                    dragPan: true,
                    dragRotate: false,
                    doubleClickZoom: true,
                    inertia: false,
                  }
                : null
            }
            layers={[
              maskLayer,
              tileLayer,
              ...(mapLayers as any[]).reverse(),
              iboLayer,
              geojsonLayer,
              labeledTileLayer,
            ]}
          >
            {({ viewport }: { viewport: Viewport }) => {
              const val1 = Math.round(viewport.metersPerPixel * 50);
              const val2 = Math.round(viewport.metersPerPixel * 100);
              const val3 = Math.round(viewport.metersPerPixel * 200);
              setViewport(viewport);

              const formatVal = (val: number) =>
                val > 5000 ? `${Math.round(val / 1000)}千米` : `${val}米`;

              setScaleValue([
                formatVal(val1),
                formatVal(val2),
                formatVal(val3),
              ]);
              return (
                <>
                  <DropContainer />
                </>
              );
            }}
          </DeckGL>
        </div>
      </DndProvider>
    </MapContainer>
  );
};

export default Map;
