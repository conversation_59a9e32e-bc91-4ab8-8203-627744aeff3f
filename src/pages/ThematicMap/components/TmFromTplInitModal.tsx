import DarkModal from '@/components/global/DarkModal';
import { Block, Flex, StyledButton } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { ModalTitle } from '@/pages/ThematicMapTpl/components/ui';
import type { DateType } from '@/pages/ThematicMapTpl/types';
import { getDateByType } from '@/pages/ThematicMapTpl/utils';
import { dateFormatter } from '@/utils';
import { DatePicker, Form, Select } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { DescInInitModal, NameModalFooter } from './ui';
import dayjs from 'dayjs';

interface Props {
  visible: boolean;
  cycleType?: DateType;
  onOk: ({
    type,
    startDate,
    endDate,
  }: {
    type: DateType;
    startDate: string;
    endDate: string;
  }) => void;
  onCancel: () => void;
}

const TmFromTplInitModal: React.FC<Props> = ({
  visible,
  cycleType = 'daily',
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [type, setType] = useState(cycleType);
  const { startDate } = getDateByType(type);

  useEffect(()=>{
  },[cycleType, type])

  useEffect(() => {
    setType(cycleType);
  }, [cycleType]);
  

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        type: type,
        date: dayjs(startDate),
      });
    }

    return () => {
      form.resetFields();
    };
  }, [form, startDate, type, visible]);

  const handleOk = useCallback(() => {
    form.validateFields().then((values) => {
      const momentType = type === 'daily' ? 'day' : type.replace('ly', '');

      onOk({
        type,
        startDate: values.date.startOf(momentType).format(dateFormatter),
        endDate: values.date.endOf(momentType).format(dateFormatter),
      });
    });
  }, [form, onOk, type]);

  return (
    <DarkModal zIndex={1000} open={visible} onCancel={onCancel}>
      <Block padding="30px 30px 0">
        <ModalTitle>设置数据范围</ModalTitle>
        <DescInInitModal>
          系统根据模板类型已经为您设置了数据范围，如需修改可点击
          下方的筛选框进行修改，修改后系统将统一替换
        </DescInInitModal>
        <Form form={form}>
          <Flex>
            <Form.Item noStyle name="type">
              <Select
                style={{ width: 140, marginRight: 12 }}
                className="dark-form-item"
                getPopupContainer={getDarkContainer}
                value={type}
                onChange={(val) => setType(val)}
                options={[
                  {
                    label: '日均值',
                    value: 'daily',
                  },
                  {
                    label: '周均值',
                    value: 'weekly',
                  },
                  {
                    label: '月均值',
                    value: 'monthly',
                  },
                  {
                    label: '季度均值',
                    value: 'quarterly',
                  },
                  {
                    label: '年均值',
                    value: 'yearly',
                  },
                ]}
              />
            </Form.Item>
            <Form.Item style={{ flex: 1 }} name="date" noStyle>
              {false ? (
                <DatePicker.RangePicker
                  allowClear={false}
                  className="flex-1 dark-form-item"
                  getPopupContainer={getDarkContainer}
                  disabledDate={(cur) => cur && cur > dayjs()}
                />
              ) : (
                <DatePicker
                  allowClear={false}
                  className="w-full dark-form-item"
                  getPopupContainer={getDarkContainer}
                  disabledDate={(cur) => cur && cur > dayjs()}
                  // @ts-ignore
                  picker={type === 'daily' ? 'date' : type.replace('ly', '')}
                  value={dayjs(startDate)}
                />
              )}
            </Form.Item>
          </Flex>
        </Form>
      </Block>
      <NameModalFooter>
        <StyledButton onClick={onCancel}>取消</StyledButton>
        <StyledButton variant="primary" onClick={handleOk}>
          确定
        </StyledButton>
      </NameModalFooter>
    </DarkModal>
  );
};

export default TmFromTplInitModal;
