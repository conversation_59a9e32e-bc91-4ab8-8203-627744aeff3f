import { useSet<PERSON><PERSON> as useUpdate<PERSON>tom } from 'jotai';
import React, { useCallback, useMemo } from 'react';
import {
  currentLayerIdAtom,
  layerAtomFamily,
  layerCtrlVisibleAtom,
  layerIdsAtom,
  layerLegendsAtom,
} from '../atoms';
import { LayerItem } from './ui';
import cls from 'classnames';
import { Modal } from 'antd';
import { useAtom } from 'jotai';
import type { DropTargetMonitor, XYCoord } from 'react-dnd';
import { useDrag, useDrop } from 'react-dnd';
import { useRef, useEffect } from 'react';
import TextLayerControl from './TextLayerControl';
import DataLayerControl from './DataLayerControl';
import RectLayerControl from './RectLayerControl';
import CircleLayerControl from './CircleLayerControl';
import ImageLayerControl from './ImageLayerControl';
import ScaleLayerControl from './ScaleLayerControl';
import { useFormatDisplayName } from '@/utils';

interface Props {
  id: string;
  index: number;
  currentId: string | null;
  typeInUrlQuery: string;
  ctrlVisible: boolean;
}


// export const formatNameLayer = (displayName: string) => {
//   const find = Object.keys(globalPoMapping).find((item) =>
//     displayName.toLowerCase().includes(item),
//   );
//   // if (find) {
//   //   return displayName
//   //     .toLowerCase()
//   //     .replace(new RegExp(find, 'g'), globalPoMapping[find]);
//   // }
//   if (find) {
//     const names = displayName.split('_')
//     return names.length > 1 ?
//       `${names[0].toLowerCase()
//         .replace(new RegExp(find, 'g'), globalPoMapping[find])}_${names[1]}`
//       : displayName
//         .toLowerCase()
//         .replace(new RegExp(find, 'g'), globalPoMapping[find]);
//   }
//   return displayName;
// };

const MapLayerItemInPanel: React.FC<Props> = ({
  id,
  currentId,
  index,
  ctrlVisible,
  typeInUrlQuery,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [layer, setLayer] = useAtom(layerAtomFamily({ id }));
  const { name, type, visible, props } = layer;
  const setLayerIds = useUpdateAtom(layerIdsAtom);
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);
  const isTextTitle = useMemo(() => {
    return layer.type === 'text' && 'isTitle' in props && props.isTitle;
  }, [layer.type, props]);
  const setCtrlVisible = useUpdateAtom(layerCtrlVisibleAtom);

  const toggleLayerVisible = useCallback(() => {
    setLayer((prev: any) => {
      setLayerLegends((prevState) =>
        prevState.map((item) =>
          item.id === id || item.id.includes(id)
            ? {
              ...item,
              visible: !prev.visible,
            }
            : item,
        ),
      );
      return {
        ...prev,
        visible: !prev.visible,
      };
    });
  }, [id, setLayer, setLayerLegends]);

  const removeLayer = useCallback(() => {
    Modal.confirm({
      title: '是否删除此图层？',
      zIndex: 10001,
      onOk() {
        setLayerIds((prev) => {
          const result = prev.filter((layerId) => layerId !== id);
          setCurrentLayerId(result[0]);
          return result;
        });

        layerAtomFamily.remove({ id });
        setLayerLegends((prev) =>
          prev.filter((item) => item.id !== id && !item.id.includes(id)),
        );
      },
    });
  }, [setLayerIds, setLayerLegends, setCurrentLayerId, id]);

  const [{ handlerId }, drop] = useDrop({
    accept: 'layer-item-in-panel',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: any, monitor: DropTargetMonitor) {
      setCtrlVisible(false);
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      setLayerIds((prev) => {
        const clone = [...prev];
        const layerItem = clone.splice(dragIndex, 1);
        clone.splice(hoverIndex, 0, layerItem[0]);

        return clone;
      });

      // eslint-disable-next-line no-param-reassign
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: 'layer-item-in-panel',
    item: () => ({ id, index }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag() {
      return typeInUrlQuery !== 'view';
    },
  });

  const opacity = isDragging ? 0 : 1;

  drag(drop(ref));

  useEffect(() => {
    setCtrlVisible(!isDragging);
  }, [isDragging, setCtrlVisible, drag]);

  const { formatDisplayName } = useFormatDisplayName()


  return (
    <>
      <LayerItem
        ref={ref}
        active={currentLayerId === id && typeInUrlQuery !== 'view'}
        style={{ opacity }}
      >
        {typeInUrlQuery !== 'view' && (
          <i
            className={cls(['icomoon icon-handle'])}
            data-handler-id={handlerId}
          />
        )}
        <i
          onClick={() => {
            if (typeInUrlQuery !== 'view') {
              setCurrentLayerId(id);
            }
          }}
          className={cls(['icomoon', 'layer-icon'], {
            'icon-data-layer': type === 'heatmap' || type === 'colormap',
            'icon-text-layer': type === 'text',
            'icon-compass': type === 'compass',
            'icon-ruler': type === 'scale',
            'icon-rect': type === 'rectangle',
            'icon-circle': type === 'circle',
            'icon-pic': type === 'image',
            'icon-arrow': type === 'arrow',
          })}
        />
        <div
          className="label"
          title={name}
          onClick={() => {
            if (typeInUrlQuery !== 'view') {
              setCurrentLayerId(id);
            }
          }}
        >
          {
            type === 'heatmap' ? formatDisplayName(name) : name
          }
          {/* {name} */}
        </div>
        {typeInUrlQuery !== 'view' && (
          <>
            <i
              className={cls([
                'icomoon  edit-icon',
                {
                  'icon-layer-title': isTextTitle,
                  hidden: type !== 'text',
                },
              ])}
            />
            <i
              className={cls([
                'icomoon edit-icon',
                {
                  'icon-trash-line': type !== 'compass' && type !== 'scale',
                },
              ])}
              onClick={removeLayer}
            />
            <i
              className={cls([
                'icomoon',
                'edit-icon',
                {
                  'icon-visible': visible,
                  'icon-hidden': !visible,
                },
              ])}
              onClick={toggleLayerVisible}
            />
          </>
        )}
      </LayerItem>
      {ctrlVisible && typeInUrlQuery !== 'view' && (
        <>
          {layer.type === 'text' && currentId === layer.id ? (
            <TextLayerControl />
          ) : null}
          {(layer?.type === 'heatmap' || layer?.type === 'colormap') &&
            currentId === layer.id && <DataLayerControl />}
          {(layer?.type === 'rectangle' || layer?.type === 'arrow') &&
            currentId === layer.id && <RectLayerControl />}
          {layer?.type === 'circle' && currentId === layer.id && (
            <CircleLayerControl />
          )}
          {layer?.type === 'image' && currentId === layer.id && (
            <ImageLayerControl />
          )}
          {layer?.type === 'scale' && currentId === layer.id && (
            <ScaleLayerControl />
          )}
        </>
      )}
    </>
  );
};

export default MapLayerItemInPanel;
