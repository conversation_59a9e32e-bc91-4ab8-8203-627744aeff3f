import { getDarkContainer } from '@/components/ui/utils';
import { Input, InputNumber, Popover, Select } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import React, { useCallback } from 'react';
import { SketchPicker } from 'react-color';
import {
  currentLayerIdAtom,
  currentTmNameAtom,
  layerAtomFamily,
} from '../atoms';
import { ColorPickerInner, DarkContainer, TextConfigRow } from './ui';
import { memo } from 'react';
import { transformRgbColorToString } from '../utils';

const TextLayerControl = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);
  const [current, setCurrent] = useAtom(
    layerAtomFamily({ id: currentLayerId }),
  );

  const updateCurrent = useCallback(
    (newVal: any) => {
      setCurrent((prev: any) => ({
        ...prev,
        ...newVal,
      }));
    },
    [setCurrent],
  );

  const updateCurrentProps = useCallback(
    (newVal: any) => {
      setCurrent((prev: any) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      }));
    },
    [setCurrent],
  );

  return (
    <DarkContainer
      style={{
        padding: '12px 0',
      }}
    >
      <TextConfigRow>
        <Input.TextArea
          className="dark-form-item w-full"
          value={current?.name}
          onChange={(e) => {
            updateCurrent({ name: e.target.value });
            if (current?.props.isTitle) {
              setCurrentTmName(e.target.value);
            }
          }}
        />
      </TextConfigRow>
      <TextConfigRow>
        <Select
          className="dark-form-item w-full"
          getPopupContainer={getDarkContainer}
          value={current?.props.fontFamily}
          onChange={(val) => updateCurrentProps({ fontFamily: val })}
          options={[
            {
              label: '微软雅黑',
              value: 'Microsoft Yahei',
            },
            {
              label: '宋体',
              value: 'Simsun',
            },
          ]}
        />
      </TextConfigRow>
      <TextConfigRow>
        <Select
          className="dark-form-item"
          getPopupContainer={getDarkContainer}
          style={{ width: 174 }}
          onChange={(val) => updateCurrentProps({ fontWeight: val })}
          value={current?.props.fontWeight}
          options={[
            {
              label: '常规体',
              value: 400,
            },
            {
              label: '粗体',
              value: 700,
            },
          ]}
        />
        <InputNumber
          size="small"
          value={current?.props.fontSize}
          className="dark-form-item"
          style={{ width: 80 }}
          min={12}
          max={40}
          onChange={(val) => updateCurrentProps({ fontSize: val })}
        />
        <Popover
          content={
            <SketchPicker
              color={current?.props.color}
              onChangeComplete={(colors) => {
                updateCurrentProps({ color: colors.rgb });
              }}
            />
          }
          placement="rightTop"
          getPopupContainer={getDarkContainer}
          trigger={['click']}
        >
          <ColorPickerInner
            background={transformRgbColorToString(current?.props.color)}
          />
        </Popover>
      </TextConfigRow>
      {/* <TextConfigRow>
        <Checkbox
          style={{ color: 'white' }}
          checked={current?.props.isTitle}
          onChange={(e) => {
            const {
              target: { checked },
            } = e;
            if (checked) {
              setCurrentTmName(current?.name || '');
              updateCurrentProps({
                isTitle: true,
              });
            }
            updateCurrentProps({ isTitle: checked });
          }}
        >
          设为专题图名称
        </Checkbox>
      </TextConfigRow> */}
    </DarkContainer>
  );
};

export default memo(TextLayerControl);
