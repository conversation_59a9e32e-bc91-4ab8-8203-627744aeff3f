import { useAtom } from 'jotai';
import type { Viewport } from 'deck.gl';
import React, { useMemo, useCallback } from 'react';
import {
  currentLayerIdAtom,
  layerAtomFamily,
  layerIdsAtom,
  viewportAtom,
  viewStateAtom,
} from '../atoms';
import Moveable from 'react-moveable';
import { useRouter } from '@/hooks';
import { transformRgbColorToString } from '../utils';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useLayerCanDrop } from '../hooks';
import type { CircleLayerProps } from '../types';

const CircleLayerItem: React.FC<{
  id: string;
  isCurrent: boolean;
  viewport: Viewport;
  setCurrentLayerId: (id: string) => void;
  index: number;
}> = ({ id, isCurrent, viewport, setCurrentLayerId, index }) => {
  const { query } = useRouter();
  const [current, setCurrent]: [CircleLayerProps, any] = useAtom(
    layerAtomFamily({ id }),
  );
  const setViewState = useUpdateAtom(viewStateAtom);
  const drop = useLayerCanDrop();
  const { props, visible } = current;
  const { unit, radius } = props;
  const halfWidth =
    (radius * (unit === 'km' ? 1000 : 1)) / viewport.metersPerPixel;
  const [x, y] = viewport ? viewport.project([props.lon, props.lat]) : [0, 0];
  const eleLeft = x - halfWidth;
  const eleTop = y - halfWidth;
  const setProps = useCallback(
    (newVal: Record<string, any>) => {
      setCurrent((prev: CircleLayerProps) => ({
        ...prev,
        props: {
          ...prev.props,
          ...newVal,
        },
      }));
    },
    [setCurrent],
  );

  return (
    <React.Fragment key={id}>
      <Moveable
        style={{
          top: eleTop,
          left: eleLeft,
          width: halfWidth * 2,
          height: halfWidth * 2,
        }}
        target={
          isCurrent && query.type !== 'view' && visible
            ? (document.querySelector(`#${id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        draggable={isCurrent}
        resizable={isCurrent}
        throttleDrag={0}
        throttleResize={2}
        onDrag={({ left, top, target }) => {
          const width = (target as HTMLElement).offsetWidth;
          const [lon, lat] = viewport.unproject([
            left + width / 2,
            top + width / 2,
          ]);
          setProps({
            lon,
            lat,
          });
        }}
        onResize={({ width }) => {
          const newRadius =
            Math.round(width * viewport.metersPerPixel) /
            (unit === 'km' ? 1000 : 1);

          setProps({
            radius: newRadius / 2,
          });
        }}
      />
      <div
        id={id}
        ref={drop}
        onWheel={(e) => {
          setViewState((prev) => {
            let zoom = (prev.zoom || 3) + (e.deltaY > 0 ? -0.5 : 0.5);
            zoom = zoom < 3 ? 3 : zoom;
            zoom = zoom > 18 ? 18 : zoom;

            return {
              ...prev,
              minZoom: 3,
              zoom,
              transitionDuration: 0,
            };
          });
        }}
        onClick={() => {
          if (query.type === 'view') return;
          setCurrentLayerId(id);
        }}
        style={{
          position: 'absolute',
          top: eleTop,
          left: eleLeft,
          width: halfWidth * 2,
          height: halfWidth * 2,
          zIndex: 1000 - index,
          background: transformRgbColorToString(props.backgroundColor),
          borderWidth: props.borderWidth,
          borderStyle: props.borderStyle,
          borderColor: transformRgbColorToString(props.borderColor),
          opacity: props.opacity / 100,
          borderRadius: props.as === 'rectangle' ? 0 : 9999,
          display: visible ? 'block' : 'none',
        }}
      />
    </React.Fragment>
  );
};

const CircleLayers = () => {
  const [layerIds] = useAtom(layerIdsAtom);
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const viewport = useAtomValue(viewportAtom);

  const circleLayerIds = useMemo(
    () => layerIds.filter((layerId) => layerId.includes('circle')),
    [layerIds],
  );

  return (
    <>
      {circleLayerIds.map((layerId) => {
        try {
          const index = layerIds.findIndex((lyId) => lyId === layerId);
          const isCurrent = currentLayerId === layerId;
          return (
            <CircleLayerItem
              id={layerId}
              key={layerId}
              index={index}
              viewport={viewport}
              isCurrent={isCurrent}
              setCurrentLayerId={setCurrentLayerId}
            />
          );
        } catch (error) {
          return null;
        }
      })}
    </>
  );
};

export default CircleLayers;
