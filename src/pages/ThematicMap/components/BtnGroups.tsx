import { useRouter } from '@/hooks';
import React, { memo } from 'react';

// import type { SelectedItem } from './SelectDataModal';
import { Rt, SingleBtn } from './ui';

const BtnGroups = () => {
  const { push } = useRouter();
  return (
    <Rt>
      <SingleBtn
        onClick={() => {
          push('/thematic-map-list');
        }}
      >
        <i className="icomoon icon-folder" /> 打开专题图列表
      </SingleBtn>
    </Rt>
  );
};

export default memo(BtnGroups);
