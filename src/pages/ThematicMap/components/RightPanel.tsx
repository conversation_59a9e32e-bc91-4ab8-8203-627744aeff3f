import { GlobalStyle, Spacer } from '@/components/ui';
import { useRouter, useWindowSize } from '@/hooks';
import { useAtom } from 'jotai';
import { useSetAtom as useUpdateAtom } from 'jotai';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { useMemo } from 'react';
import {
  geojsonVisibleAtom,
  tileLabelVisibleAtom,
  tmDetailsAtom,
} from '../atoms';
import {
  accordionStatusAtom,
  canvasSizeAtom,
  currentLayerIdAtom,
  currentTmNameAtom,
  layerIdsAtom,
  tileLayerOpacityAtom,
} from '../atoms';
import MapLayersInPanel from './MapLayersInPanel';
import MapTypeSelect from './MapTypeSelect';
import RegionCascader from './RegionCascader';
import {
  DarkContainer,
  DetailsViewLine,
  PanelTitle,
  PropPanel,
  Right,
  RightInner,
  ScrollContainer,
  SubTitle,
} from './ui';
import NameTmModal from './NameTmModal';
import { useState } from 'react';
import { useQuery } from 'react-query';
import type { TmItem } from '../types';
import { getTmDetails } from '../services';
import OpacitySlider from './OpacitySlider';
import ToolBox from './ToolBox';
import type { SelectedItem } from './SelectDataPanel';
import DataSelectPanel from './SelectDataPanel';
import SelectImageDataPanel from './SelectImageDataPanel';
import {
  useClearLayers,
  useCreateLayers,
  useCreateWithTpl,
  useEditEffect,
  useExportTmToImage,
  usePageUnmount,
  useResetLayerDateProps,
  useSaveTm,
} from '../hooks';
import RightPanelActions from './RightPanelActions';
import { getTplDetails } from '@/pages/ThematicMapTpl/services';
import type { TplItem } from '@/pages/ThematicMapTpl/types';
import TmFromTplInitModal from './TmFromTplInitModal';
import LabelAndGeojsonVisibleControl from './LabelAndGeojsonVisibleControl';
import CanvasSizeControlContainer from './CanvasSizeControlContainer';

const RightPanel = () => {
  const { query, push, history } = useRouter();
  const [visible, setVisible] = useState(false);
  const [initModalVisible, setInitModalVisible] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);
  const [imagePanelVisible, setImagePanelVisible] = useState(false);
  const { height } = useWindowSize();
  const [currentTmName] = useAtom(currentTmNameAtom);
  const [actionType, setActionType] = useState<'save' | 'export'>('save');
  const [accordionStatus, setAccordionStatus] = useAtom(accordionStatusAtom);
  const [layerIds] = useAtom(layerIdsAtom);
  const setCanvasSize = useUpdateAtom(canvasSizeAtom);
  const [tileLayerOpacity, setTileLayerOpacity] = useAtom(tileLayerOpacityAtom);
  const createDataLayers = useCreateLayers();
  const clearLayers = useClearLayers();
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const exportTm = useExportTmToImage();
  const [geojsonVisible, setGeojsonVisible] = useAtom(geojsonVisibleAtom);
  const [tileLabelVisible, setTileLabelVisible] = useAtom(tileLabelVisibleAtom);
  const setTmDetails = useUpdateAtom(tmDetailsAtom);
  const beforeSave = useCallback(() => {
    setVisible(true);
  }, []);
  const saveTm = useSaveTm();
  const { data: details } = useQuery<TmItem>(
    ['tm-map-detail', query.id, query.type],
    () => getTmDetails(query.id),
    {
      enabled: !!query.id,
      staleTime: 0,
      cacheTime: 0,
    },
  );
  useEffect(() => {
    if (details) {
      setTmDetails(details);
    }
  }, [details, setTmDetails]);
  const { data: tplDetails } = useQuery<TplItem>(
    ['tpl-details', query.tplId],
    () => getTplDetails(query.tplId),
    {
      enabled: Boolean(query.tplId),
      staleTime: 0,
      cacheTime: 0,
    },
  );

  const parsedDetailsContent = useEditEffect(details);
  useCreateWithTpl(tplDetails);

  useEffect(() => {
    if (!query.tplId && !query.id) {
      const node = document.getElementById('map-container');

      setCanvasSize({
        width: (node?.offsetWidth || 0) - 4,
        height: (node?.offsetHeight || 0) - 4,
      });
    }
  }, [query.id, query.tplId, setCanvasSize]);

  useEffect(() => {
    if (query.tplId && tplDetails) {
      setInitModalVisible(true);
    }
  }, [query.tplId, tplDetails]);

  // 组件卸载时，复原状态
  usePageUnmount();


  const isViewPage = useMemo(() => query.type === 'view', [query.type]);
  const detailsMapType = useMemo(() => {
    if (parsedDetailsContent) {
      return parsedDetailsContent.mapType;
    }

    return '';
  }, [parsedDetailsContent]);

  const createLayers = useCallback(
    (values: SelectedItem[]) => {
      createDataLayers(values, () => {
        setPanelVisible(false);
      });
    },
    [createDataLayers],
  );

  const resetLayerDateProps = useResetLayerDateProps();

  useEffect(() => {
    return () => {
      setTmDetails(null);
    };
  }, [setTmDetails]);


  return (
    <Right style={{ height: height - 120 }}>
      <GlobalStyle />
      <DataSelectPanel
        visible={panelVisible}
        onOk={createLayers}
        onCancel={() => setPanelVisible(false)}
      />
      <SelectImageDataPanel
        visible={imagePanelVisible}
        onOk={(values) => {
          createLayers(values);
          setImagePanelVisible(false);
        }}
        onCancel={() => setImagePanelVisible(false)}
      />
      <RightInner>
        {query.type !== 'view' && (
          <ToolBox
            onDataLayerBtnClick={() => {
              setPanelVisible((prev) => {
                if (!prev) {
                  setImagePanelVisible(false);
                }
                return !prev;
              });
            }}
            dataPanelVisible={panelVisible}
            imagePanelVisible={imagePanelVisible}
            onImageLayerBtnClick={() => {
              setImagePanelVisible((prev) => {
                if (!prev) {
                  setPanelVisible(false);
                }
                return !prev;
              });
            }}
          />
        )}
        <PropPanel>
          <ScrollContainer>
            <SubTitle
              onClick={() =>
                setAccordionStatus((prev) => ({
                  ...prev,
                  range: !prev.range,
                }))
              }
            >
              <PanelTitle>
                <div className="line" />
                <span>范围设置</span>
              </PanelTitle>
              <Spacer />
              <i
                className="icomoon icon-arrow-down"
                style={{
                  transition: 'all .2s',
                  transform: `rotate(${accordionStatus.range ? '0' : '180deg'
                    })`,
                }}
              />
            </SubTitle>
            {accordionStatus.range && (
              <DarkContainer>
                {isViewPage ? (
                  <>
                    <DetailsViewLine>
                      <div className="label">行政区域</div>
                      <div className="value">{parsedDetailsContent.shouldShowAllRegion ? "全域" : details?.region}</div>
                    </DetailsViewLine>
                  </>
                ) : (
                  <>
                    <RegionCascader />
                  </>
                )}
              </DarkContainer>
            )}
            <SubTitle
              onClick={() =>
                setAccordionStatus((prev) => ({
                  ...prev,
                  display: !prev.display,
                }))
              }
            >
              <PanelTitle>
                <div className="line" />
                <span>显示设置</span>
              </PanelTitle>
              <Spacer />
              <i
                className="icomoon icon-arrow-down"
                style={{
                  transition: 'all .2s',
                  transform: `rotate(${accordionStatus.display ? '0' : '180deg'
                    })`,
                }}
              />
            </SubTitle>
            {accordionStatus.display && (
              <>
                <DarkContainer>
                  {isViewPage ? (
                    <>
                      <DetailsViewLine>
                        <div className="label">地图模式</div>
                        <div className="value">
                          {!detailsMapType && '无'}
                          {detailsMapType === 'img' && '影像底图'}
                          {detailsMapType === 'vec' && '标准底图'}
                          {detailsMapType === 'ter' && '地形底图'}
                        </div>
                      </DetailsViewLine>
                      <DetailsViewLine>
                        <div className="label">画布尺寸</div>
                        <div className="value">
                          {parsedDetailsContent.width}px *{' '}
                          {parsedDetailsContent.height}px
                        </div>
                      </DetailsViewLine>
                    </>
                  ) : (
                    <>
                      <MapTypeSelect />
                      <LabelAndGeojsonVisibleControl
                        geojsonVisible={geojsonVisible}
                        setGeojsonVisible={(bool) => {
                          setGeojsonVisible(bool);
                        }}
                        tileLabelVisible={tileLabelVisible}
                        setTileLabelVisible={(bool) => {
                          setTileLabelVisible(bool);
                        }}
                      />
                      <OpacitySlider
                        value={tileLayerOpacity}
                        setValue={(val) => setTileLayerOpacity(val)}
                      />
                      <CanvasSizeControlContainer />
                    </>
                  )}
                </DarkContainer>
                <MapLayersInPanel />
              </>
            )}
          </ScrollContainer>
          <RightPanelActions
            handleSave={() => {
              setActionType('save');
              if (query.type === 'edit' && details?.name) {
                saveTm({
                  name: details.name,
                  callback: () => {
                    setTimeout(() => {
                      push('/thematic-map-list');
                    }, 1200);
                  },
                });
                return;
              }
              beforeSave();
            }}
            handleExport={() => {
              setActionType('export');
              if (
                (query.type === 'edit' || query.type === 'view') &&
                details?.name
              ) {
                exportTm(`${details.name}.jpg`);
                return;
              }
              beforeSave();
            }}
            handleClear={clearLayers}
            handleBack={() => {
              // history.goBack()
              history.push('/thematic-map-list');
            }
            }
          />
        </PropPanel>
      </RightInner>
      <NameTmModal
        name={currentTmName}
        visible={visible}
        onOk={(name) => {
          if (query.type === 'view') {
            exportTm(`${name}.jpg`);
            setVisible(false);
            return;
          }
          setVisible(false);
          saveTm({
            name,
            callback: () => {
              if (actionType === 'save') {
                setTimeout(() => {
                  push('/thematic-map-list');
                }, 1200);
              }
              if (actionType === 'export') {
                exportTm(`${name}.jpg`);
              }
            },
          });
        }}
        onCancel={() => setVisible(false)}
      />
      <TmFromTplInitModal
        visible={initModalVisible}
        cycleType={tplDetails?.cycleType}
        onOk={({ type, startDate, endDate }) => {
          resetLayerDateProps({
            type,
            startDate,
            endDate,
          });
          setCurrentLayerId(layerIds[0]);
          setInitModalVisible(false);
        }}
        onCancel={() => setInitModalVisible(false)}
      />
    </Right>
  );
};

export default RightPanel;
