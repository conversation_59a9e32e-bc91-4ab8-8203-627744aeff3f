import { useAtom } from 'jotai';
import React, { useMemo, useCallback } from 'react';
import Moveable from 'react-moveable';
import {
  currentLayerIdAtom,
  layerAtomFamily,
  layerIdsAtom,
  viewStateAtom,
} from '../atoms';
import { useRouter } from '@/hooks';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useLayerCanDrop, useUpdateFamilyChildProps } from '../hooks';

export const handleUrl = (url: string) => {
  const urlObj = new URL(url);
  const pathname = urlObj.pathname;
  const search = urlObj.search;
  const hash = urlObj.hash;
  // 重新构建不包含域名和端口的新URL
  const newUrl = `${pathname}${search}${hash}`;
  return newUrl;
}


const ImageLayerItem: React.FC<{
  isCurrent: boolean;
  id: string;
  index: number;
}> = ({ isCurrent, id, index }) => {
  const { query } = useRouter();
  const [layer] = useAtom(layerAtomFamily({ id }));
  const { props } = layer;
  const [, setCurrentId] = useAtom(currentLayerIdAtom);
  const setViewState = useUpdateAtom(viewStateAtom);
  const drop = useLayerCanDrop();
  const update = useUpdateFamilyChildProps();
  const token = localStorage.getItem('__NMENG_TOKEN__')

  const updateLayerProps = useCallback(
    (newVal: any) => {
      update({
        id,
        newVal,
      });
    },
    [id, update],
  );

  const imageUrl = useMemo(() => {
    console.log(props.url, props.url?.split('/api')[1]);
    return handleUrl(props.url)+`&token=${token}`;
  }, [props.url, token]);


  return (
    <React.Fragment key={id}>
      <Moveable
        key={id}
        target={
          isCurrent && query.type !== 'view' && layer.visible
            ? (document.querySelector(`#${id}`) as HTMLDivElement)
            : null
        }
        container={document.querySelector('#dom') as HTMLDivElement}
        zoom={1}
        origin={true}
        edge={false}
        resizable={isCurrent}
        draggable={isCurrent}
        throttleDrag={0}
        throttleResize={0}
        onDrag={({ left, top }) => {
          updateLayerProps({
            top,
            left,
          });
        }}
        onResize={({ width, target, delta, direction }) => {
          const top = (target as HTMLDivElement).offsetTop;
          const left = (target as HTMLDivElement).offsetLeft;
          const newTop = direction[1] < 0 ? top - delta[1] : top;
          const newLeft = direction[0] < 0 ? left - delta[0] : left;

          updateLayerProps({
            top: newTop,
            left: newLeft,
            width,
          });
        }}
      />
      <div
        id={id}
        ref={drop}
        style={{
          position: 'absolute',
          top: props.top,
          left: props.left,
          width: props.width,
          zIndex: 1000 - index,
          display: layer.visible ? 'block' : 'none',
        }}
        onClick={() => {
          if (query.type === 'view') return;
          setCurrentId(layer.id);
        }}
        onWheel={(e) => {
          setViewState((prev) => {
            let zoom = (prev.zoom || 0) + (e.deltaY > 0 ? -0.5 : 0.5);
            zoom = zoom < 3 ? 3 : zoom;
            zoom = zoom > 18 ? 18 : zoom;

            return {
              ...prev,
              minZoom: 3,
              zoom,
              transitionDuration: 0,
            };
          });
        }}
      >
        <img src={imageUrl} alt="" width="100%" />
      </div>
    </React.Fragment>
  );
};

const ImageLayers = () => {
  const layerIds = useAtomValue(layerIdsAtom);
  const [currentId] = useAtom(currentLayerIdAtom);

  const imageLayerIds = useMemo(() => {
    return layerIds.filter((layerId) => layerId.includes('image'));
  }, [layerIds]);

  return (
    <>
      {imageLayerIds.map((layerId) => {
        const isCurrent = layerId === currentId;
        const index = layerIds.findIndex((lyId) => layerId === lyId);
        return (
          <ImageLayerItem
            id={layerId}
            key={layerId}
            isCurrent={isCurrent}
            index={index}
          />
        );
      })}
    </>
  );
};

export default ImageLayers;
