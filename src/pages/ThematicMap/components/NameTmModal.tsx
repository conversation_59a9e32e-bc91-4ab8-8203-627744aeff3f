import DarkModal from '@/components/global/DarkModal';
import { Block, StyledButton } from '@/components/ui';
import formRules from '@/utils/formRules';
import { Form, Input } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { NameModalFooter, NameModalTitle } from './ui';

interface Props {
  visible: boolean;
  name?: string;
  onOk: (name: string) => void;
  onCancel: () => void;
}

const NameTmModal: React.FC<Props> = ({ visible, name, onOk, onCancel }) => {
  const [value, setValue] = useState('');
  const [form] = Form.useForm();

  useEffect(() => {
    if (name && visible) {
      form.setFieldsValue({
        name,
      });
    }

    return () => {
      form.resetFields();
    };
  }, [form, name, visible]);

  const handleOk = useCallback(() => {
    form.validateFields().then((values) => {
      onOk(values.name);
    });
  }, [form, onOk]);

  return (
    <DarkModal zIndex={10000} visible={visible} onCancel={onCancel}>
      <Block padding="30px 30px 0">
        <NameModalTitle>设置专题图名称</NameModalTitle>
        <Form form={form}>
          <Form.Item
            name="name"
            rules={[
              ...formRules.required('请输入专题图名称'),
              {
                type: 'string',
                max: 30,
                message: '专题图名称限制在30字符以内',
              },
            ]}
          >
            <Input
              className="dark-form-item"
              placeholder="请输入专题图名称"
              value={value}
              onChange={(e) => setValue(e.target.value)}
            />
          </Form.Item>
        </Form>
      </Block>
      <NameModalFooter>
        <StyledButton onClick={onCancel}>取消</StyledButton>
        <StyledButton variant="primary" onClick={handleOk}>
          确定
        </StyledButton>
      </NameModalFooter>
    </DarkModal>
  );
};

export default NameTmModal;
