import { getDarkContainer } from '@/components/ui/utils';
import { CaretDownOutlined } from '@ant-design/icons';
import { Popover } from 'antd';
import React, { useState } from 'react';
import { createGlobalStyle } from 'styled-components';
import { SolidConfigBox, SolidOption } from './ui';

export type LineStyle = 'solid' | 'dashed' | 'dotted';
const SuppleStyle = createGlobalStyle`
  .ant-popover-arrow {
    border-color: #1f1f1f;
  }
`;

const LineStyleChooser: React.FC<{
  style: LineStyle;
  handleItemClick: (style: LineStyle) => void;
}> = ({ style, handleItemClick }) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <SuppleStyle />
      <Popover
        visible={visible}
        trigger={['click']}
        onVisibleChange={(status) => {
          setVisible(status);
        }}
        content={
          <div>
            <SolidOption
              onClick={() => {
                handleItemClick('solid');
                setVisible(false);
              }}
            >
              <div className="line" />
            </SolidOption>
            <SolidOption
              borderStyle="dotted"
              onClick={() => {
                handleItemClick('dotted');
                setVisible(false);
              }}
            >
              <div className="line" />
            </SolidOption>
            <SolidOption
              borderStyle="dashed"
              onClick={() => {
                handleItemClick('dashed');
                setVisible(false);
              }}
            >
              <div className="line" />
            </SolidOption>
          </div>
        }
        placement="leftTop"
        getPopupContainer={getDarkContainer}
      >
        <SolidConfigBox>
          <div className="line" style={{ borderBottomStyle: style }} />
          <CaretDownOutlined />
        </SolidConfigBox>
      </Popover>
    </>
  );
};

export default LineStyleChooser;
