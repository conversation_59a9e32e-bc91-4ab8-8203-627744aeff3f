import React, { memo } from 'react';
import { useLayerCanDrop } from '../hooks';

const DropContainer: React.FC = ({ children }) => {
  const drop = useLayerCanDrop();

  return (
    <div
      ref={drop}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        zIndex: 20,
        overflow: 'hidden',
      }}
    >
      {children}
    </div>
  );
};

export default memo(DropContainer);
