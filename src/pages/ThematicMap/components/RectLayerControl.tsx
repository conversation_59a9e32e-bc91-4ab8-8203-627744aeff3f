import { Block } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { InputNumber, Popover, Checkbox } from 'antd';
import { useAtomValue } from 'jotai';
import React from 'react';
import { SketchPicker } from 'react-color';
import { currentLayerIdAtom, layerAtomFamily } from '../atoms';
import { useUpdateFamilyChildProps } from '../hooks';
import type { RectangleLayerProps } from '../types';
import { transformRgbColorToString } from '../utils';
import LineStyleChooser from './LineStyleChooser';
import OpacitySlider from './OpacitySlider';
import {
  ColorConfigRect,
  ConfigLabel,
  DarkContainer,
  ShapeConfItemContainer,
} from './ui';

const RectLayerControl = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom) as string;
  const current = useAtomValue(
    layerAtomFamily({
      id: currentLayerId,
    }),
  ) as RectangleLayerProps;
  const update = useUpdateFamilyChildProps();

  return (
    <DarkContainer
      style={{
        padding: '12px 0',
      }}
    >
      <ShapeConfItemContainer>
        <div className="item">
          <ConfigLabel>填充</ConfigLabel>
          <Popover
            content={
              <SketchPicker
                color={current.props.backgroundColor}
                onChange={(val) => {
                  update({
                    id: currentLayerId,
                    newVal: { backgroundColor: val.rgb },
                  });
                }}
              />
            }
            placement="rightTop"
            getPopupContainer={getDarkContainer}
            trigger={['click']}
          >
            <ColorConfigRect
              style={{
                backgroundColor: transformRgbColorToString(
                  current.props.backgroundColor,
                ),
              }}
            />
          </Popover>
        </div>
        <div className="item">
          <ConfigLabel>描边</ConfigLabel>
          <Popover
            content={
              <SketchPicker
                color={current.props.borderColor}
                onChange={(val) => {
                  update({
                    id: currentLayerId,
                    newVal: { borderColor: val.rgb },
                  });
                }}
              />
            }
            placement="rightTop"
            getPopupContainer={getDarkContainer}
            trigger={['click']}
          >
            <ColorConfigRect
              style={{
                border: `2px solid ${transformRgbColorToString(
                  current.props.borderColor,
                )}`,
                background: 'transparent',
              }}
            />
          </Popover>
        </div>
        <div className="item">
          <ConfigLabel>线宽</ConfigLabel>
          <InputNumber
            size="small"
            className="dark-form-item"
            style={{ width: 80 }}
            min={0}
            max={20}
            defaultValue={1}
            value={current.props.borderWidth}
            onChange={(val) =>
              update({
                id: currentLayerId,
                newVal: {
                  borderWidth: val,
                },
              })
            }
          />
        </div>
        <div className="item">
          <ConfigLabel>类型</ConfigLabel>
          <LineStyleChooser
            style={current.props.borderStyle}
            handleItemClick={(style) => {
              update({
                id: currentLayerId,
                newVal: {
                  borderStyle: style,
                },
              });
            }}
          />
        </div>
      </ShapeConfItemContainer>
      <OpacitySlider
        value={current.props.opacity}
        setValue={(val) =>
          update({
            id: currentLayerId,
            newVal: {
              opacity: val,
            },
          })
        }
      />
      <Block padding="12px 0 0 6px">
        <Checkbox
          style={{ color: 'white' }}
          checked={current.props.backgroundColor.a === 0}
          onChange={(e) => {
            const { checked } = e.target;
            const prevAlpha = current.props.backgroundColor.a || 255;

            update({
              id: currentLayerId,
              newVal: {
                backgroundColor: {
                  ...current.props.backgroundColor,
                  a: checked ? 0 : prevAlpha,
                },
              },
            });
          }}
        >
          无填充
        </Checkbox>
      </Block>
    </DarkContainer>
  );
};

export default RectLayerControl;
