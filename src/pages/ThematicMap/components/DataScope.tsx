import { Flex } from '@/components/ui';
import { getDarkContainer } from '@/components/ui/utils';
import { dateFormatter } from '@/utils';
import { DatePicker, Select } from 'antd';
import { useAtom } from 'jotai';
import { useAtomValue } from 'jotai';
import { currentLayerIdAtom, layerAtomFamily } from '../atoms';
import { useUpdateFamilyChildProps } from '../hooks';
import { ConfigLabel } from './ui';
import dayjs from 'dayjs';

const DataScope = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom) as string;

  const [current] = useAtom(layerAtomFamily({ id: currentLayerId }));
  const update = useUpdateFamilyChildProps();

  return (
    <>
      <ConfigLabel>数据范围</ConfigLabel>
      <Flex>
        <Select
          style={{ width: 110, marginRight: 12 }}
          className="dark-form-item"
          getPopupContainer={getDarkContainer}
          value={current?.props.dateType}
          onChange={(val) => {
            const isNotDaily = val !== 'daily';
            if (val === 'none') {
              update({
                id: currentLayerId,
                newVal: {
                  dateType: val,
                  startDate: dayjs()
                    .subtract(1, 'd')
                    .startOf('d')
                    .format(dateFormatter),
                  endDate: dayjs()
                    .subtract(1, 'd')
                    .endOf('d')
                    .format(dateFormatter),
                },
              });
            } else {
              update({
                id: currentLayerId,
                newVal: isNotDaily
                  ? {
                      dateType: val,
                      startDate: dayjs()
                        .startOf(val.replace('ly', '') )
                        .format(dateFormatter),
                      endDate: dayjs()
                        .endOf(val.replace('ly', ''))
                        .format(dateFormatter),
                    }
                  : {
                      dateType: val,
                      startDate: dayjs().format(dateFormatter),
                      endDate: dayjs().format(dateFormatter),
                    },
              });
            }
          }}
          options={[
            {
              label: '日均值',
              value: 'daily',
            },
            {
              label: '周均值',
              value: 'weekly',
            },
            {
              label: '月均值',
              value: 'monthly',
            },
            {
              label: '季度均值',
              value: 'quarterly',
            },
            {
              label: '年均值',
              value: 'yearly',
            },
            {
              label: '自定义均值',
              value: 'none',
            },
          ]}
        />

        {current.props.dateType === 'none' ? (
          <DatePicker.RangePicker
            allowClear={false}
            className="flex-1 dark-form-item"
            getPopupContainer={getDarkContainer}
            value={[
              dayjs(current?.props.startDate),
              dayjs(current?.props.endDate),
            ]}
            disabledDate={(cur) => cur && cur > dayjs().endOf('day')}
            onChange={(val) => {
              if (val && val.length === 2) {
                update({
                  id: currentLayerId,
                  newVal: {
                    startDate: dayjs(val[0]).format(dateFormatter),
                    endDate: dayjs(val[1]).format(dateFormatter),
                  },
                });
              }
            }}
          />
        ) : (
          <DatePicker
            allowClear={false}
            className="flex-1 dark-form-item"
            getPopupContainer={getDarkContainer}
            picker={
              current?.props.dateType === 'daily'
                ? 'date'
                : (current?.props.dateType.replace('ly', '') as any)
            }
            value={dayjs(current?.props.startDate)}
            disabledDate={(cur) => cur && cur > dayjs()}
            onChange={(val) => {
              let startDate: string;
              let endDate: string;
              type ScopeType = 'date' | 'week' | 'month' | 'quarter' | 'year';
              if (current?.props.dateType === 'daily') {
                startDate = dayjs(val).format(dateFormatter);
                endDate = dayjs(val).format(dateFormatter);
              } else {
                const type = current?.props.dateType.replace('ly', '');
                startDate = dayjs(val)
                  .startOf(type as ScopeType)
                  .format(dateFormatter);
                endDate = dayjs(val)
                  .endOf(type as ScopeType)
                  .format(dateFormatter);
              }

              update({
                id: currentLayerId,
                newVal: {
                  startDate,
                  endDate,
                },
              });
            }}
          />
        )}
      </Flex>
    </>
  );
};

export default DataScope;
