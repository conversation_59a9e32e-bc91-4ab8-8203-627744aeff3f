import HelmetTitle from '@/components/global/HealmetTitle';
import PageHead from '@/components/global/PageHead';
import { useRouter, useWindowSize } from '@/hooks';
import { ConfigProvider, Spin } from 'antd';
import { useAtomValue } from 'jotai';
import { useMemo, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { isSavingAtom, tmDetailsAtom } from './atoms';
import Map from './components/Map';
import RightPanel from './components/RightPanel';
import { Container, ImgContainer, MapContainer, PageLoading, TmPageMain } from './components/ui';
import './style.less';

const ThematicMap = () => {
  const { query } = useRouter();
  const isSaving = useAtomValue(isSavingAtom);
  const tmDetails = useAtomValue(tmDetailsAtom);
  const token = localStorage.getItem('__NMENG_TOKEN__')
  const imgRef = useRef<HTMLImageElement>(null);


  const imgSize = useMemo(() => {
    const content = tmDetails?.content;
    if (content) {
      const canvasSize = JSON.parse(content);
      return {
        width: canvasSize.width,
        height: canvasSize.height,
      };
    }

    return {
      width: 0,
      height: 0,
    };
  }, [tmDetails?.content]);

  const text = useMemo(() => {
    if (query.type === 'view') {
      return '专题图详情';
    } else if (query.type === 'edit') {
      return '编辑专题图';
    } else if (query.type === 'copy') {
      return '复制专题图';
    }
    return '创建专题图';
  }, [query.type]);

  const { height } = useWindowSize();

  return (
    <ConfigProvider
      theme={{
        token: {
          colorText: '#c1c1c4', // 设置主字体颜色
          colorBgBase: '#1c1d24',
          colorPrimary: '#286cff',
        },
      }}
    >
      <TmPageMain>
        {isSaving && (
          <PageLoading>
            {' '}
            <Spin />
          </PageLoading>
        )}
        <HelmetTitle title="专题图" />
        <PageHead title="专题图">
          <Link to="/thematic-map-list">专题图列表</Link>
          <i className="icomoon icon-next" />
          <span className="text-primary">{text}</span>
        </PageHead>
        <Container className=" bg-white rounded" >
          {tmDetails?.imageUrl && query.type === 'view' ? (
            <MapContainer className='flex items-center justify-center'
              style={{
                height: height - 120,
                overflow: 'auto',
              }}
            >
              <div
              >
                <ImgContainer
                  id="canvas"
                  // style={imgSize.height >= 787 ? {
                  //   // 添加缩放适配
                  //   // transform: `scale(${Math.min(
                  //   //   (height - 40) / (imgSize.height),
                  //   //   (window.innerWidth - 0) / (imgSize.width)
                  //   // )})`,
                  //   // transformOrigin: 'center center',
                  // } : {
                  // }}
                  style={{
                    width: imgSize.width >= 1328 ? '1328px' : imgSize.width,
                    height: imgSize.height >= 791 ? '791px' : imgSize.height,
                  }}
                >
                  <img
                    id="canvas"
                    ref={imgRef}
                    src={tmDetails.imageUrl.trim() + `&token=${token}`}
                    width={imgSize.width}
                    height={imgSize.height}
                    className="block"
                    style={{
                      width: imgSize.width >= 1328 ? '100%' : imgSize.width,
                      height: imgSize.height >= 791 ? '100%' : imgSize.height,
                    }}
                  />
                </ImgContainer>
              </div>
            </MapContainer>
          ) : (
            <Map />
          )}
          <RightPanel />
        </Container>
      </TmPageMain>
    </ConfigProvider>
  );
};

export default ThematicMap;
