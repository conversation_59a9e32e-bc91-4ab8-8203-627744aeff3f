import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import { atom } from 'jotai';
import { atomFamily } from 'jotai/utils';
import type { LayerProps, Legend, TmItem } from '../types';

export const viewStateAtom = atom<ViewStateProps>({
  latitude: 39.915156,
  longitude: 116.400819,
  zoom: 6,
  maxZoom: 18,
  minZoom: 3,
  pitch: 0,
  bearing: 0,
});
export const viewportAtom = atom<any>({});
export const regionCodeAtom = atom<number | null>(null);
export const tileLayerOpacityAtom = atom<number>(100);
export const tmapNameAtom = atom<undefined | string>(undefined);
export const dateTypeMapping = {
  date: 'daily',
  week: 'weekly',
  month: 'monthly',
  quarter: 'quarterly',
  year: 'yearly',
  custom: 'custom',
};

export const dataScopeAtom = atom<{
  type: keyof typeof dateTypeMapping;
}>({
  type: 'date',
});
export const mapTypeAtom = atom<'img' | 'vec' | 'ter'>('img');
export const layersAtom = atom<LayerProps[]>([]);
export const currentLayerIdAtom = atom<string | null>(null);
export const needDisableContainerEventAtom = atom(false);
export const scaleValueAtom = atom<string[] | number[]>([0, 0, 0]);
export const currentTmIdAtom = atom<number | null>(null);
export const currentTmNameAtom = atom('');
export const accordionStatusAtom = atom({
  range: true,
  display: true,
  data: true,
});
export const layerLegendsAtom = atom<Legend[]>([]);
export const layerCtrlVisibleAtom = atom(true);
export const isCascaderChangeAtom = atom(false);
export const layerAtomFamily = atomFamily(
  (params: any) => atom(params),
  (a: any, b: any) => a.id === b.id,
);
export const layerIdsAtom = atom<string[]>([]);
// 是否显示全域
export const shouldShowAllRegionAtom = atom(false);
// 记录已经调用过setTileUrl的图层，防止重复执行
// 将请求参数stringify然后进行比较
export const renderedLayersAtom = atom<string[]>([]);

export interface CanvasSize {
  width: number;
  height: number;
}
export const canvasSizeAtom = atom({
  width: 0,
  height: 0,
});

// 省市行政区边界可见性
export const geojsonVisibleAtom = atom(true);
export const tileLabelVisibleAtom = atom(true);
export const isSavingAtom = atom(false);

// 专题图详情
export const tmDetailsAtom = atom<null | TmItem>(null);
