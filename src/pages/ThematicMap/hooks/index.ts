import { Tile<PERSON>ayer, GeoJsonLayer, WebMercatorViewport } from 'deck.gl';
import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  createColorImageTileLayer,
  dateFormatter,
  downloadUseLink,
  getLevelByRegionCode,
  getNewViewState,
  getTextureUrl,
  getTileUrl,
  wmts,
} from '@/utils';
import { useAtomValue, useSetAtom as useUpdateAtom } from 'jotai';
import { useAtomCallback } from 'jotai/utils';
import { Modal, message } from 'antd';
import type { CanvasSize } from '../atoms';
import { isCascaderChangeAtom, isSavingAtom, tmDetailsAtom } from '../atoms';
import {
  canvasSizeAtom,
  currentLayerIdAtom,
  currentTmIdAtom,
  currentTmNameAtom,
  dataScopeAtom,
  dateTypeMapping,
  layerAtomFamily,
  layerIdsAtom,
  layerLegendsAtom,
  layersAtom,
  mapTypeAtom,
  regionCodeAtom,
  // renderedLayersAtom,
  shouldShowAllRegionAtom,
  tileLayerOpacityAtom,
  viewStateAtom,
  geojsonVisibleAtom,
  tileLabelVisibleAtom,
} from '../atoms';
import { baseGeojsonLayerConfig, request } from '@/utils';
import { useMutation, useQueries, useQuery, useQueryClient } from 'react-query';
import type {
  HeatmapLayerProps,
  LayerProps,
  Legend,
  SaveTmParams,
  TmItem,
} from '../types';
import { stringify } from 'qs';
import {
  fetchGeojsonByRegionCode,
  fetchGeojsonIncludeChild,
} from '@/services/global';
// eslint-disable-next-line max-len
import type { RGBColor } from 'react-color';
import { useAtom } from 'jotai';
import { useDrop } from 'react-dnd';
import {
  circleLayerDefaultConfig,
  compassLayerDefaultConfig,
  dataUrlToFile,
  dcolorLegends,
  rectangleLayerDefaultConfig,
  scaleLayerDefaultConfig,
  textLayerDefaultConfig,
} from '../utils';
import type { SelectedItem } from '../components/SelectDataPanel';
import dayjs from 'moment';
import uniqueId from 'lodash/uniqueId';
import { createTm, updateTm, uploadImgWithCompression } from '../services';
import { useCreateTextureLayer, useRouter } from '@/hooks';
import type { TplItem } from '@/pages/ThematicMapTpl/types';
import { getDateByType } from '@/pages/ThematicMapTpl/utils';
import { userInfoAtom } from '@/atoms';
import center from '@turf/center';
import html2canvas from 'html2canvas';
import { useTextureToken } from '@/pages/Overview/hooks';
import { useFrontierTileLayerWithLabel } from '@/pages/Overview/hooks';
import { handleUrl } from '../components/ImageLayers';
import { colorRamps, decoder } from '@/utils/dataDown';

export const useTileLayerWithLabel = () => {
  const mapType = useAtomValue(mapTypeAtom);
  const visible = useAtomValue(tileLabelVisibleAtom);

  const labelType = useMemo(() => {
    if (mapType === 'img') return 'cia';
    if (mapType === 'ter') return 'cta';
    return 'cva';
  }, [mapType]);

  const labeledTileLayer = useMemo(() => {
    return new TileLayer({
      ...tileLayerBaseConfig,
      id: 'tianditu-with-label',
      data: mapType ? wmts(labelType) : wmts('cva'),
      pickable: false,
      visible,
    });
  }, [labelType, mapType, visible]);

  return { labeledTileLayer };
};

export const useTileLayer = () => {
  const mapType = useAtomValue(mapTypeAtom);
  const opacity = useAtomValue(tileLayerOpacityAtom);
  const tileLayer = useMemo(
    () => [
      new TileLayer({
        ...tileLayerBaseConfig,
        id: 'img-tileLayer',
        opacity: opacity / 100,
        data: wmts('img'),
        visible: mapType === 'img',
      }),
      new TileLayer({
        ...tileLayerBaseConfig,
        id: 'vec-tileLayer',
        opacity: opacity / 100,
        data: wmts('vec'),
        visible: mapType === 'vec',
      }),
      new TileLayer({
        ...tileLayerBaseConfig,
        id: 'ter-tileLayer',
        opacity: opacity / 100,
        data: wmts('ter'),
        visible: mapType === 'ter',
      }),
    ]
    ,
    [mapType, opacity],
  );

  return { tileLayer };
};

export const useGeoJSONLayer = (d: any) => {
  const mapType = useAtomValue(mapTypeAtom);
  const geojsonVisible = useAtomValue(geojsonVisibleAtom);

  const layer = useMemo(
    () =>
      new GeoJsonLayer({
        ...baseGeojsonLayerConfig,
        getLineColor: mapType === 'img' ? [255, 255, 255] : [99, 99, 99],
        filled: false,
        getFillColor: () => [40, 108, 255, 120],
        data: d,
        visible: geojsonVisible,
      }),
    [d, geojsonVisible, mapType],
  );

  return layer;
};

export const useMapTextureLayers = () => {
  const [layerIds] = useAtom(layerIdsAtom);
  const regionCode = useAtomValue(regionCodeAtom);
  const [dataLayers, setDataLayers] = useState<HeatmapLayerProps[]>([]);
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const currentDataLayer = useAtomValue(
    layerAtomFamily({ id: currentLayerId }),
  );
  const legends = useAtomValue(layerLegendsAtom);
  const token = useTextureToken();
  const { createTextureLayer } = useCreateTextureLayer();

  const getDataLayers = useAtomCallback(
    useCallback(
      (get) => {
        return layerIds
          .filter(
            (layerId) =>
              layerId.includes('heatmap') || layerId.includes('colormap'),
          )
          .map((id) => {
            return get(layerAtomFamily({ id }));
          });
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [layerIds, currentDataLayer, legends],
    ),
  );

  useEffect(() => {
    const lys = getDataLayers();
    // @ts-ignore
    setDataLayers(lys);
  }, [getDataLayers, currentDataLayer, currentLayerId, currentDataLayer.props]);

  const queriesResult = useQueries(
    dataLayers.map((layer: HeatmapLayerProps) => ({
      queryKey: [
        layer.id,
        regionCode,
        layer?.props?.dataType,
        layer?.props?.dateType,
        layer?.props?.startDate,
        layer?.props?.endDate,
      ],
      enabled: Boolean(layer.props),
      staleTime: 0,
      queryFn: () => {
        const {
          type,
          props: { dateType, dataType },
        } = layer;
        let agg = dateType;

        if (type === 'colormap' || dataType === 'DMASK' || dataType === 'DCOLOR') {
          agg = 'none';
        }
        if (
          type !== 'colormap' &&
          dataType !== 'DMASK' &&
          dateType === 'none' &&
          dataType !== 'DCOLOR'
        ) {
          agg = 'daily';
        }

        return request(
          `${layer.props.fetchUrl}?${stringify({
            agg,
            startDate: layer.props.startDate,
            endDate: layer.props.endDate,
            regionCode,
            type: layer.props.dataType,
          })}`,
        );
      },
    })),
  );

  const mapLayers = useMemo(() => {
    return dataLayers.map((layer, index) => {
      const currentData = queriesResult[index].data || [];


      if (layer.type === 'heatmap' && layer.props.dataType === 'DMASK') {
        const dataType = layer.props.dataType.toLowerCase() as any;
        if (!currentData[0]?.timePoints) return null;

        const { time } = layer.props;
        const find = currentData.find((item: any) => {
          return dayjs(item.timePoints).format('HH:mm') === time;
        });

        const timeParam = find?.timePoints;
        const url = getTileUrl(
          {
            agg: 'none',
            type: layer.props.dataType.toUpperCase(),
            time: timeParam,
            token: token || '',
          },
          true,
        );

        const textureLayer = createTextureLayer({
          id: layer.id,
          dataUrl: url,
          decoder: decoder[dataType],
          colorRamp: colorRamps[dataType],
          visible: layer.visible && currentData && currentData.length > 0,
        });
        return textureLayer;
        // eslint-disable-next-line no-else-return
      }
      if (layer.type === 'heatmap') {
        const dataType = layer.props.dataType.toLowerCase() as any;
        if (!currentData[0]?.timePoints) return null;

        const {
          props: { dateType, startDate, endDate },
        } = layer;
        let agg = dateType;
        let isCustom = false


        if (dateType === 'none') {
          agg = 'daily';
          isCustom = true;
        }
        if (layer.props.dataType === 'DCOLOR') {
          agg = 'none';
        }
        const url = isCustom ?
          getTextureUrl(
            {
              startDate: startDate || '',
              endDate: endDate || '',
              type: layer.props.dataType,
              tileToken: token || '',
            },
            true,
          ) :
          getTileUrl(
            {
              agg,
              type: layer.props.dataType.toUpperCase(),
              time: currentData[0].timePoints,
              token: token || '',
            },
            true,
          );

        const textureLayer = createTextureLayer({
          id: layer.id,
          dataUrl: url,
          decoder: decoder[dataType],
          colorRamp: colorRamps[dataType],
          visible: layer.visible && currentData && currentData.length > 0,
          isBitmapLayer: layer.props.dataType === 'DCOLOR'
        });
        return textureLayer;
        // eslint-disable-next-line no-else-return
      }
      if (layer.type === 'colormap') {
        let timeParam;
        if (currentData && currentData.length > 0) {
          const { time } = layer.props;
          const find = currentData.find((item: any) => {
            return dayjs(item.timePoints).format('HH:mm') === time;
          });

          timeParam = find?.timePoints;
        }

        if (!timeParam) return null;
        const tileLayer = createColorImageTileLayer(
          getTileUrl(
            {
              time: timeParam,
              agg: 'none',
              token: token || '',
              type: layer.props.dataType.toUpperCase(),
            },
            true,
          ),
          layer.visible && currentData && currentData.length > 0,
          'color-image-tile-layer',
        );

        return tileLayer;
      }
    });
  }, [createTextureLayer, dataLayers, queriesResult, token]);

  return mapLayers;
};

export const useMapContainerRect = (container: HTMLDivElement | null) => {
  const [pos, setPos] = useState({
    top: 20,
    left: 20,
  });

  useEffect(() => {
    if (container) {
      const left =
        container.getBoundingClientRect().left +
        document.documentElement.scrollLeft;
      const top =
        container.getBoundingClientRect().top +
        document.documentElement.scrollTop;

      setPos({
        left,
        top,
      });
    }
  }, [container]);

  return pos;
};

/**
 * 根据当前图层类型设置control标题
 * @returns string
 */
export const useCurrentLayerConfigName = () => {
  const currentLayerId = useAtomValue(currentLayerIdAtom);
  const layers = useAtomValue(layersAtom);

  const currentLayer = useMemo(() => {
    return layers.find((item) => item.id === currentLayerId);
  }, [currentLayerId, layers]);

  const name = useMemo(() => {
    if (currentLayer) {
      switch (currentLayer.type) {
        case 'arrow':
          return '箭头';
        case 'circle':
          return '圆形';
        case 'heatmap':
        case 'scatter-point':
          return '数据';
        case 'text':
          return '文本';
        case 'image':
          return '图片';
        case 'rectangle':
          return '矩形';
        default:
          return '';
      }
    }

    return '';
  }, [currentLayer]);

  return name;
};

export const useUpdateCurrentLayer = <T extends LayerProps>() => {
  const setLayers = useUpdateAtom(layersAtom);
  const currentId = useAtomValue(currentLayerIdAtom);

  const updateLayerProps = useCallback(
    (newVal: Record<string, number | string | RGBColor | boolean>) => {
      setLayers((prev) =>
        prev.map((item) =>
          item.id === currentId
            ? ({
              ...item,
              props: {
                ...item.props,
                ...newVal,
              },
            } as T)
            : item,
        ),
      );
    },
    [currentId, setLayers],
  );

  const updateLayerBaseProps = useCallback(
    (newVal: Record<string, string | number>) => {
      setLayers((prev) =>
        prev.map((item) =>
          item.id === currentId
            ? ({
              ...item,
              ...newVal,
            } as T)
            : item,
        ),
      );
    },
    [currentId, setLayers],
  );

  return {
    updateLayerProps,
    updateLayerBaseProps,
  };
};

export const useUpdateFamilyChildProps = () => {
  const update = useAtomCallback(
    useCallback(
      (get, set, args: { id: string; newVal: Record<string, any> }) => {
        const { id, newVal } = args;
        const prev = get(layerAtomFamily({ id }));
        set(layerAtomFamily({ id }), {
          ...prev,
          props: {
            ...prev.props,
            ...newVal,
          },
        });
      },
      [],
    ),
  );

  return update;
};

export const useLayerCanDrop = () => {
  const [layerIds, setLayerIds] = useAtom(layerIdsAtom);
  const viewState = useAtomValue(viewStateAtom);
  const mapType = useAtomValue(mapTypeAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const rect = useMapContainerRect(
    document.getElementById('map-container') as HTMLDivElement | null,
  );

  const viewport = useMemo(() => {
    return new WebMercatorViewport(viewState);
  }, [viewState]);

  const [, drop] = useDrop(
    () => ({
      accept: ['dragItem', 'toolBoxItem'],
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        isOverCurrent: monitor.isOver({ shallow: true }),
      }),
      drop(
        item: { id: string; top: number; left: number; type: string },
        monitor,
      ) {
        const clientOffset = monitor.getClientOffset();
        let id: any;
        let newLayer: any;
        if (item.type === 'new-text') {
          const lens = layerIds.filter((layer) =>
            layer.includes('text'),
          ).length;
          id = `text-layer-${new Date().getTime()}`;
          const name = `未命名文字图层${lens + 1}`;
          newLayer = {
            id,
            type: 'text',
            name,
            visible: true,
            props: {
              ...textLayerDefaultConfig,
              width: name.length * 16,
              color:
                mapType === 'img'
                  ? { r: 255, g: 255, b: 255, a: 1 }
                  : { r: 0, g: 0, b: 0, a: 1 },
              left: (clientOffset?.x || 0) - rect.left,
              top: (clientOffset?.y || 0) - rect.top,
            },
          };
        } else if (item.type === 'new-rectangle') {
          id = `rectangle-layer-${new Date().getTime()}`;
          const lens = layerIds.filter((layer) =>
            layer.includes('rectangle'),
          ).length;
          newLayer = {
            id,
            type: 'rectangle',
            name: `色块图层${lens + 1}`,
            visible: true,
            props: {
              ...rectangleLayerDefaultConfig,
              left: (clientOffset?.x || 0) - rect.left - 100,
              top: (clientOffset?.y || 0) - rect.top - 100,
            },
          };
        } else if (item.type === 'new-circle') {
          id = `circle-layer-${new Date().getTime()}`;
          const lens = layerIds.filter((layer) =>
            layer.includes('circle'),
          ).length;

          const [lon, lat] = viewport.unproject([
            (clientOffset?.x || 0) - rect.left,
            (clientOffset?.y || 0) - rect.top,
          ]);
          newLayer = {
            id,
            type: 'circle',
            name: `范围图层${lens + 1}`,
            visible: true,
            props: {
              ...circleLayerDefaultConfig,
              lon,
              lat,
            },
          };
        } else if (item.type === 'new-arrow') {
          id = `arrow-layer-${new Date().getTime()}`;
          const lens = layerIds.filter((layer) =>
            layer.includes('arrow'),
          ).length;
          newLayer = {
            id,
            type: 'arrow',
            name: `箭头图层${lens + 1}`,
            visible: true,
            props: {
              ...rectangleLayerDefaultConfig,
              width: 48,
              height: 48,
              left: (clientOffset?.x || 0) - rect.left,
              top: (clientOffset?.y || 0) - rect.top,
              translate: [-24, -24],
              scale: [1, 1],
              rotate: 0,
            },
          };
        }

        layerAtomFamily(newLayer);
        setLayerIds((prev) => [id, ...prev]);
        setCurrentLayerId(id);

        return undefined;
      },
    }),
    [layerIds, rect, viewport],
  );

  return drop;
};

export const useCreateLayers = () => {
  const regionCode = useAtomValue(regionCodeAtom);
  const [, setLayerIds] = useAtom(layerIdsAtom);
  const setLegends = useUpdateAtom(layerLegendsAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);

  const createDataLayers = useCallback(
    (values: SelectedItem[], callback: () => void) => {
      const newLayers = values.map((item) => {
        const id = `${item.layerType
          }-layer-${uniqueId()}-${new Date().getTime()}`;
        const name = `${item.groupName}-${item.displayName}`;
        const type = item.layerType;
        const fetchUrl = '/api/texture/time/point';
        // 沙尘掩码 获取时间需要修改结束时间
        const dateParmas =
          type === 'heatmap'
            ? {
              startDate: dayjs()
                .subtract(1, 'd')
                .format(
                  item?.internalName === 'DMASK'
                    ? 'YYYY/MM/DD HH:00'
                    : dateFormatter,
                ),
              endDate: dayjs()
                .subtract(1, 'd')
                .format(
                  item?.internalName === 'DMASK'
                    ? 'YYYY/MM/DD 23:59'
                    : dateFormatter,
                ),
            }
            : {
              startDate: dayjs().subtract(1, 'd').format(dateFormatter),
              endDate: dayjs().subtract(1, 'd').format(dateFormatter),
              time: null,
            };

        return {
          id,
          name,
          type,
          visible: true,
          props: {
            dataType: item.internalName,
            fetchUrl,
            region: regionCode,
            dateType: item?.internalName === 'DMASK' ? 'none' : 'daily',
            ...dateParmas,
          },
        } as HeatmapLayerProps;
      });
      const container = document.getElementById('canvas');
      const newLegends: any[] = newLayers.map((item) => {
        const { id, props, type } = item;
        if (props.dataType === 'DCOLOR') {
          return dcolorLegends.map((legendItem, index) => {
            return {
              id: id + '-' + legendItem.id,
              props: {
                label: legendItem.label,
                color: legendItem.color,
                from: legendItem.from,
                textColor: { r: 0, g: 0, b: 0, a: 1 },
                to: legendItem.to,
                left: container!.clientWidth - 180,
                top: container!.clientHeight - (8 - index) * 24,
                scale: [1, 1],
                translate: [0, 0],
              },
              layerType: type,
              dataType: props.dataType,
              visible: true,
              // 区分图例颜色获取对象
              render: type === 'heatmap' || type === 'colormap',
            };
          });
        }
        return {
          id,
          props: {
            left: 20,
            top: container!.clientHeight - 60,
            scale: [1, 1],
            translate: [0, 0],
            textColor: { r: 0, g: 0, b: 0, a: 1 },
          },
          layerType: item.type as 'heatmap',
          dataType: props.dataType,
          visible: true,
          // 区分图例颜色获取对象
          render: item.type === 'heatmap',
        };
      });

      setLegends((prev) => [
        ...prev,
        ...newLegends.reduce((prevState, cur) => {
          return Array.isArray(cur)
            ? [...prevState, ...cur]
            : [...prevState, cur];
        }, [] as Legend[]),
      ]);
      newLayers.forEach((layer) => {
        layerAtomFamily(layer);
      });
      setLayerIds((prev) => [...newLayers.map((layer) => layer.id), ...prev]);

      setTimeout(() => {
        setCurrentLayerId(newLayers[0].id);
      }, 200);

      if (callback && typeof callback === 'function') {
        callback();
      }
    },
    [regionCode, setCurrentLayerId, setLayerIds, setLegends],
  );

  return createDataLayers;
};

export const useClearLayers = () => {
  const setLayerIds = useUpdateAtom(layerIdsAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const setLegends = useUpdateAtom(layerLegendsAtom);

  const clearLayers = useCallback(() => {
    Modal.confirm({
      title: '确认清空所有图层吗？',
      onOk() {
        setLayerIds((prev) => {
          const result = prev.filter(
            (id) => id.includes('scale') || id.includes('compass'),
          );
          setCurrentLayerId(result[0]);
          return result;
        });

        setLegends([]);
      },
    });
  }, [setCurrentLayerId, setLayerIds, setLegends]);

  return clearLayers;
};

export const useExportTmToImage = () => {
  const [currentLayerId, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const { query } = useRouter();
  const tmDetails = useAtomValue(tmDetailsAtom);
  const token = localStorage.getItem('__NMENG_TOKEN__')
  const downloadImage = useCallback(
    (url: string, name: string, currentId: string) => {
      const linkEl = document.createElement('a');
      linkEl.href = url;
      linkEl.setAttribute('download', name);

      document.body.appendChild(linkEl);
      linkEl.click();
      document.body.removeChild(linkEl);

      setCurrentLayerId(currentId);
    },
    [setCurrentLayerId],
  );

  const exportTm = useCallback(
    (name: string) => {
      const currentId = currentLayerId;
      setCurrentLayerId(null);
      if (query.type === "view" && tmDetails?.imageUrl) {
        let url = tmDetails?.imageUrl.trim() + `&token=${token}`;
        fetch(handleUrl(url))
          .then(res => res.blob())
          .then(blob => {
            const url = URL.createObjectURL(blob);
            const linkEl = document.createElement('a');
            linkEl.href = url;
            linkEl.setAttribute('download', name);
            document.body.appendChild(linkEl);
            linkEl.click();
            document.body.removeChild(linkEl);
            URL.revokeObjectURL(url);
            setCurrentLayerId(currentId);
          });
      } else {
        setTimeout(() => {
          html2canvas(document.getElementById('canvas') as any, {
            useCORS: true,
          }).then((canvas) => {
            const url = canvas.toDataURL('image/jpeg');
            downloadImage(url, name, String(currentId));
          });
        }, 500);
      }

    },
    [currentLayerId, downloadImage, query.type, setCurrentLayerId, tmDetails?.imageUrl, token],
  );

  return exportTm;
};

export const useSaveTm = () => {
  const [, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const { query } = useRouter();
  const regionCode = useAtomValue(regionCodeAtom);
  const layerLegends = useAtomValue(layerLegendsAtom);
  const dataScope = useAtomValue(dataScopeAtom);
  const tileLayerOpacity = useAtomValue(tileLayerOpacityAtom);
  const mapType = useAtomValue(mapTypeAtom);
  const [currentTmId] = useAtom(currentTmIdAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);
  const layerIds = useAtomValue(layerIdsAtom);
  const viewState = useAtomValue(viewStateAtom);
  const shouldShowAllRegion = useAtomValue(shouldShowAllRegionAtom);
  const canvasSize = useAtomValue(canvasSizeAtom);
  const tileLabelVisible = useAtomValue(tileLabelVisibleAtom);
  const geojsonVisible = useAtomValue(geojsonVisibleAtom);
  const queryClient = useQueryClient();
  const uploadMutation = useMutation((fd: FormData) =>
    uploadImgWithCompression(fd),
  );
  const setIsSaving = useUpdateAtom(isSavingAtom);

  const createMutation = useMutation((params: Omit<SaveTmParams, 'id'>) =>
    createTm(params),
  );
  const updateMutation = useMutation((params: SaveTmParams) =>
    updateTm(params),
  );
  const save = useAtomCallback(
    useCallback(
      (get, set, args: { name: string; callback?: () => void }) => {
        // 保存专题图
        setCurrentLayerId(null);
        setIsSaving(true);

        setTimeout(() => {
          html2canvas(document.getElementById('canvas')!, {
            scale: 0.9,
          }).then((canvas) => {
            const url = canvas.toDataURL();
            const blob = dataUrlToFile(url, 'image/jpeg');

            const formData = new FormData();
            const file = new File([blob], new Date().getTime() + '.jpg');
            formData.append('file', file);

            uploadMutation.mutate(formData, {
              onSuccess(res) {
                const { id } = res;
                const { type: dateType } = dataScope;
                const { name, callback } = args;
                const { zoom, longitude, latitude } = viewState;
                const layers = layerIds.map((layerId) => {
                  return get(layerAtomFamily({ id: layerId }));
                });

                const submitData = {
                  name,
                  regionCode,
                  imageId: id,
                  agg: dateTypeMapping[dateType],
                  templateId: query.tplId ? query.tplId : undefined,
                  content: JSON.stringify({
                    width: canvasSize.width,
                    height: canvasSize.height,
                    tileLabelVisible,
                    geojsonVisible,
                    shouldShowAllRegion,
                    legends: layerLegends,
                    layers,
                    opacity: tileLayerOpacity,
                    regionCode,
                    type: dateType,
                    mapType,
                    viewState: {
                      zoom,
                      longitude,
                      latitude,
                    },
                  }),
                } as any;
                if (currentTmId) {
                  updateMutation.mutate(
                    { ...submitData, id: query.id },
                    {
                      onSuccess() {
                        message.success('操作成功');
                        setIsSaving(false);
                        setCurrentTmName(name);
                        queryClient.invalidateQueries([
                          'tm-map-detail',
                          query.id,
                          query.type,
                        ]);

                        if (typeof callback === 'function') {
                          callback();
                        }
                      },
                    },
                  );
                } else {
                  createMutation.mutate(submitData, {
                    onSuccess() {
                      message.success('操作成功');
                      setIsSaving(false);
                      setCurrentTmName(name);

                      if (typeof callback === 'function') {
                        callback();
                      }
                    },
                  });
                }
              },
            });
          });
        }, 200);
      },
      [
        canvasSize.height,
        canvasSize.width,
        createMutation,
        currentTmId,
        dataScope,
        geojsonVisible,
        layerIds,
        layerLegends,
        mapType,
        query.id,
        query.tplId,
        query.type,
        queryClient,
        regionCode,
        setCurrentLayerId,
        setCurrentTmName,
        setIsSaving,
        shouldShowAllRegion,
        tileLabelVisible,
        tileLayerOpacity,
        updateMutation,
        uploadMutation,
        viewState,
      ],
    ),
  );

  return save;
};

export const usePageUnmount = () => {
  const { query } = useRouter();
  const setLayerIds = useUpdateAtom(layerIdsAtom);
  const setMapType = useUpdateAtom(mapTypeAtom);
  const setCurrentTmId = useUpdateAtom(currentTmIdAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const setGeojsonVisible = useUpdateAtom(geojsonVisibleAtom);
  const setTileLabelVisible = useUpdateAtom(tileLabelVisibleAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);

  useEffect(() => {
    if (!query.id || !query.type) {
      setLayerIds((prev) => {
        prev.forEach((layerId) => {
          layerAtomFamily.remove({ id: layerId });
        });
        return [];
      });

      setCurrentLayerId(null);
      setCurrentTmName('');
      setCurrentTmId(null);
      setLayerLegends([]);
      setMapType('img');
      setTileLabelVisible(true);
      setGeojsonVisible(true);
    }
  }, [
    query.id,
    query.type,
    setCurrentLayerId,
    setCurrentTmId,
    setCurrentTmName,
    setGeojsonVisible,
    setLayerIds,
    setLayerLegends,
    setMapType,
    setTileLabelVisible,
  ]);

  useEffect(() => {
    return () => {
      setLayerIds((prev) => {
        prev.forEach((layerId) => {
          layerAtomFamily.remove({ id: layerId });
        });
        return [];
      });

      setCurrentLayerId(null);
      setCurrentTmName('');
      setCurrentTmId(null);
      setLayerLegends([]);
      setMapType('img');
      setTileLabelVisible(true);
      setGeojsonVisible(true);
    };
  }, [
    setCurrentLayerId,
    setCurrentTmId,
    setCurrentTmName,
    setGeojsonVisible,
    setLayerIds,
    setLayerLegends,
    setMapType,
    setTileLabelVisible,
  ]);
};

export const useEditEffect = (details: TmItem | undefined) => {
  const { query } = useRouter();
  const [parsedDetailsContent, setParsedDetailsContent] = useState<any>({});
  const setRegionCode = useUpdateAtom(regionCodeAtom);
  const setLayerIds = useUpdateAtom(layerIdsAtom);
  const setDataScope = useUpdateAtom(dataScopeAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const setLayers = useUpdateAtom(layersAtom);
  const setMapType = useUpdateAtom(mapTypeAtom);
  const setCurrentTmId = useUpdateAtom(currentTmIdAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);
  const setTileLayerOpacity = useUpdateAtom(tileLayerOpacityAtom);
  const setViewState = useUpdateAtom(viewStateAtom);
  const setShouldShowAllRegion = useUpdateAtom(shouldShowAllRegionAtom);
  const setCanvasSize = useUpdateAtom(canvasSizeAtom);
  const setTileLabelVisible = useUpdateAtom(tileLabelVisibleAtom);
  const setGeojsonVisible = useUpdateAtom(geojsonVisibleAtom);
  const setAtom = useAtomCallback(
    useCallback((get, set, obj: any) => {
      const { id, layer } = obj;
      set(layerAtomFamily({ id }), layer);
    }, []),
  );

  useEffect(() => {
    if (details && query.id) {
      const { content, createType, fromDate, toDate } = details;
      const parsedContent = JSON.parse(content);
      setParsedDetailsContent(parsedContent);
      if (query.type !== 'copy') {
        setCurrentTmId(details.id);
      }
      setRegionCode(parsedContent.regionCode);
      setCanvasSize({
        width: parsedContent.width,
        height: parsedContent.height,
      });
      setTileLayerOpacity(parsedContent.opacity);
      setShouldShowAllRegion(parsedContent.shouldShowAllRegion);
      setTileLabelVisible(parsedContent.tileLabelVisible);
      setGeojsonVisible(parsedContent.geojsonVisible);

      setDataScope({
        type: parsedContent.type,
      });
      if (createType === 1) {
        parsedContent.layers
          .map((layer: HeatmapLayerProps) =>
            layer.type === 'heatmap' || layer.type === 'colormap'
              ? {
                ...layer,
                props: {
                  ...layer.props,
                  startDate: fromDate,
                  endDate: toDate,
                },
              }
              : layer,
          )
          .forEach((layer: any) => {
            setAtom({
              id: layer.id,
              layer,
            });
          });
      } else {
        parsedContent.layers.forEach((layer: any) => {
          setAtom({
            id: layer.id,
            layer,
          });
        });
      }
      setLayerIds(parsedContent.layers.map((layer: any) => layer.id));
      setMapType(parsedContent.mapType);
      setCurrentTmName(details.name);
      setLayerLegends(parsedContent.legends || []);
      setViewState((prev) => ({
        ...prev,
        ...parsedContent.viewState,
      }));
    }
  }, [
    details,
    query.id,
    query.type,
    setAtom,
    setCanvasSize,
    setCurrentLayerId,
    setCurrentTmId,
    setCurrentTmName,
    setDataScope,
    setGeojsonVisible,
    setLayerIds,
    setLayerLegends,
    setLayers,
    setMapType,
    setRegionCode,
    setShouldShowAllRegion,
    setTileLabelVisible,
    setTileLayerOpacity,
    setViewState,
  ]);

  useEffect(() => {
    return () => {
      setCanvasSize({
        width: 0,
        height: 0,
      });
    };
  }, [setCanvasSize]);

  return parsedDetailsContent;
};

export const useCreateWithTpl = (tplDetails?: TplItem) => {
  const { query } = useRouter();
  const setRegionCode = useUpdateAtom(regionCodeAtom);
  const setLayerIds = useUpdateAtom(layerIdsAtom);
  const setDataScope = useUpdateAtom(dataScopeAtom);
  const setCurrentLayerId = useUpdateAtom(currentLayerIdAtom);
  const setMapType = useUpdateAtom(mapTypeAtom);
  const setCurrentTmName = useUpdateAtom(currentTmNameAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);
  const setTileLayerOpacity = useUpdateAtom(tileLayerOpacityAtom);
  const setViewState = useUpdateAtom(viewStateAtom);
  const setCanvasSize = useUpdateAtom(canvasSizeAtom);
  const setTileLabelVisible = useUpdateAtom(tileLabelVisibleAtom);
  const setGeojsonVisible = useUpdateAtom(geojsonVisibleAtom);

  useEffect(() => {
    if (tplDetails && query.tplId) {
      const { content, cycleType } = tplDetails;
      const { startDate, endDate } = getDateByType(cycleType);
      const parsedContent = JSON.parse(content);
      setRegionCode(parsedContent.regionCode);
      setTileLayerOpacity(parsedContent.opacity);
      setDataScope({
        type: parsedContent.type,
      });
      setCanvasSize({
        width: parsedContent.width,
        height: parsedContent.height,
      });
      setTileLabelVisible(parsedContent.tileLabelVisible);
      setGeojsonVisible(parsedContent.geojsonVisible);
      parsedContent.layers
        .map((layer: any) => {
          const result =
            layer.type === 'heatmap' || layer.type === 'colormap'
              ? {
                ...layer,
                id: `${layer.id}`,
                props: {
                  ...layer.props,
                  startDate,
                  endDate,
                },
              }
              : layer;
          return result;
        })
        .forEach((layer: any) => {
          layerAtomFamily(layer);
          setLayerIds((prev) => [...prev, layer.id]);
        });
      setMapType(parsedContent.mapType);
      setCurrentTmName(tplDetails.name);
      setCurrentLayerId(parsedContent.layers[0].id);
      setLayerLegends(parsedContent.legends || []);
      setViewState((prev) => ({
        ...prev,
        ...parsedContent.viewState,
      }));
    }
  }, [
    tplDetails,
    query.tplId,
    setCurrentLayerId,
    setCurrentTmName,
    setDataScope,
    setLayerIds,
    setLayerLegends,
    setMapType,
    setRegionCode,
    setTileLayerOpacity,
    setViewState,
    setCanvasSize,
    setTileLabelVisible,
    setGeojsonVisible,
  ]);
};

export const useReCalcLayerPos = () => {
  const [layerIds] = useAtom(layerIdsAtom);
  const setLayerLegends = useUpdateAtom(layerLegendsAtom);

  const reCalcLayerPos = useAtomCallback(
    useCallback(
      (
        get,
        set,
        args: {
          oldSize: CanvasSize;
          newSize: CanvasSize;
        },
      ) => {
        const { oldSize, newSize } = args;
        const wRatio = newSize.width / oldSize.width;
        const hRatio = newSize.height / oldSize.height;
        setLayerLegends((prev) =>
          prev.map((legend) => {
            return legend.dataType === 'DCOLOR'
              ? {
                ...legend,
                props: {
                  ...legend.props,
                  top: legend.props.top * hRatio,
                  left: newSize.width - 160,
                  translate: [
                    legend.props.translate[0] * wRatio,
                    legend.props.translate[1] * hRatio,
                  ],
                },
              }
              : {
                ...legend,
                props: {
                  ...legend.props,
                  top: legend.props.top * hRatio,
                  left: legend.props.left * wRatio,
                  translate: [
                    legend.props.translate[0] * wRatio,
                    legend.props.translate[1] * hRatio,
                  ],
                },
              };
          }),
        );

        layerIds
          .filter(
            (layerId) =>
              !layerId.includes('colormap') && !layerId.includes('heatmap'),
          )
          .forEach((layerId) => {
            const atom = layerAtomFamily({ id: layerId });
            const layer = get(atom);

            if (layer.type === 'compass') {
              set(atom, {
                ...layer,
                props: {
                  ...layer.props,
                  left: newSize.width - layer.props.width - 20,
                  top: layer.props.top * hRatio,
                },
              });
            } else {
              set(atom, {
                ...layer,
                props: {
                  ...layer.props,
                  left: layer.props.left * wRatio,
                  top: layer.props.top * hRatio,
                },
              });
            }
          });
      },
      [layerIds, setLayerLegends],
    ),
  );

  return reCalcLayerPos;
};

export const useResetLayerDateProps = () => {
  const [layerIds] = useAtom(layerIdsAtom);

  const resetLayerDateProps = useAtomCallback(
    useCallback(
      (get, set, args: any) => {
        const { type, startDate, endDate } = args;
        const needResetIds = layerIds.filter(
          (layerId) =>
            layerId.includes('heatmap') || layerId.includes('colormap'),
        );

        if (needResetIds.length > 0) {
          needResetIds.forEach((id) => {
            const prev = get(layerAtomFamily({ id }));
            set(layerAtomFamily({ id }), {
              ...prev,
              dateType: type,
              props: {
                ...prev.props,
                dateType: type,
                startDate,
                endDate,
              },
            });
          });
        }
      },
      [layerIds],
    ),
  );

  return resetLayerDateProps;
};

export const useMapEffect = () => {
  const [geojsonData, setGeojsonData] = useState([]);
  const shouldShowAllRegion = useAtomValue(shouldShowAllRegionAtom);
  const { query } = useRouter();
  const userInfo = useAtomValue(userInfoAtom);
  const mapContainer = document.getElementById('map-container');
  const [layers, setLayers] = useAtom(layersAtom);
  const [, setCurrentLayerId] = useAtom(currentLayerIdAtom);
  const [, setViewState] = useAtom(viewStateAtom);
  const [regionCode, setRegionCode] = useAtom(regionCodeAtom);
  const [layerIds, setLayerIds] = useAtom(layerIdsAtom);
  const isCascaderChange = useAtomValue(isCascaderChangeAtom);
  const canvasSize = useAtomValue(canvasSizeAtom);
  const level = useMemo(
    () => (shouldShowAllRegion ? 1 : getLevelByRegionCode(String(regionCode))),
    [regionCode, shouldShowAllRegion],
  );
  const queryKeys = useMemo(
    () => ['user-region-geojson', regionCode, shouldShowAllRegion],
    [regionCode, shouldShowAllRegion],
  );

  useEffect(() => {
    if (query.id || query.tplId) {
      return;
    }

    if (mapContainer) {
      const { clientWidth, clientHeight } = mapContainer;
      const compassLayerId = `compass-layer-${new Date().getTime()}`;
      const scaleLayerId = `scale-layer-${new Date().getTime()}`;

      const find = layerIds.find(
        (layerId) =>
          layerId.includes('compass-layer') || layerId.includes('scale-layer'),
      );
      if (!find) {
        layerAtomFamily({
          id: compassLayerId,
          type: 'compass',
          name: '指南针',
          visible: true,
          props: {
            ...compassLayerDefaultConfig,
            left: clientWidth - 120,
          },
        });
        layerAtomFamily({
          id: scaleLayerId,
          type: 'scale',
          name: '比例尺',
          visible: true,
          props: {
            ...scaleLayerDefaultConfig,
            top: clientHeight - 60,
            left: 40,
          },
        });
        setLayerIds((prev) => [...prev, compassLayerId, scaleLayerId]);
      }
    }
  }, [
    layers.length,
    setLayers,
    query,
    mapContainer,
    setCurrentLayerId,
    setLayerIds,
    layerIds,
  ]);

  const { data } = useQuery(
    queryKeys,
    () =>
      fetchGeojsonIncludeChild({
        code: Number(shouldShowAllRegion ? 150000 : regionCode),
        level,
      }),
    {
      enabled: !!regionCode && level === 1,
      staleTime: 0,
    },
  );

  useEffect(() => {
    setGeojsonData(data || []);
  }, [data]);

  const { data: levelGeojson } = useQuery(
    queryKeys,
    () => fetchGeojsonByRegionCode(Number(regionCode === 100000 ? 150000 : regionCode)),
    {
      enabled: !!regionCode,
      staleTime: 0,
    },
  );

  useEffect(() => {
    if (userInfo?.regionCode && !query.id) {
      setRegionCode(userInfo.regionCode);
    }
  }, [query.id, setRegionCode, userInfo?.regionCode]);

  const isNotLevel1 = useMemo(() => level === 3 || level === 2, [level]);

  useEffect(() => {
    if (
      (!data && !levelGeojson) ||
      (query.id && !isCascaderChange) ||
      (query.tplId && !isCascaderChange) ||
      canvasSize.width < 400
    ) {
      return;
    }
    const newViewState = getNewViewState(
      isNotLevel1 ? levelGeojson : data,
      {},
      Number(canvasSize.width),
      Number(canvasSize.height),
      60,
    );
    const {
      geometry: { coordinates },
    } = center(isNotLevel1 ? levelGeojson : data);

    setViewState((prev) => ({
      ...prev,
      ...newViewState,
      longitude: coordinates[0],
      latitude: coordinates[1],
      transitionDuration: 800,
    }));
    // 不能将viewState作为依赖项，会导致无限重复渲染
  }, [
    data,
    setViewState,
    levelGeojson,
    regionCode,
    query.id,
    query.tplId,
    isCascaderChange,
    isNotLevel1,
    canvasSize.width,
    canvasSize.height,
  ]);

  return geojsonData;
};

export const useIboTileLayer = () => {
  const layerIds = useAtomValue(layerIdsAtom);
  const includeColorImageLayers = useMemo(() => {
    return layerIds.some((item) => item.includes('colormap-layer'));
  }, [layerIds]);
  const needShowAllRegion = useAtomValue(shouldShowAllRegionAtom);

  const layer = useFrontierTileLayerWithLabel(
    needShowAllRegion && includeColorImageLayers,
    0.4,
  );

  return layer;
};
