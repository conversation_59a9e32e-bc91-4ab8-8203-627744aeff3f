import { message } from 'antd';
import { encrypt } from './encrypt';

export const request = async (
  fetchUrl: string,
  fetchConfig?: RequestInit & { headers?: null },
  isNeedEncrypt = false,
) => {
  const exportUrls: string[] = [
    '/api/texture/agg',
    '/api/dust/stats/export/send/site',
    '/api/dust/stats/export/region/count',
    '/api/dust/stats/export/event/duration',
    '/api/dust/stats/export/event/region/area',
    '/api/file?type=temporary'
  ];

  const doNotNeedTokenUrls = ['/api/user/login'];
  // const exportUrls = [
  //   '/api/alert/export',
  //   '/api/sop/export',
  //   '/api/alert/export/count/stat',
  //   '/api/alert/export/level/stat',
  // ];
  const isExporting =
    fetchUrl.includes('export') || fetchUrl === '/api/sop/template' ||
    exportUrls.includes(fetchUrl) ||
    exportUrls.find((item) => fetchUrl.includes(item));

  const url = fetchUrl.includes('http')
    ? fetchUrl
    : process.env.API_PREFIX + fetchUrl;
  const config = fetchConfig || {};
  const token = localStorage.getItem('__NMENG_TOKEN__') || undefined;

  if (!doNotNeedTokenUrls.includes(fetchUrl) && !token) {
    const { href } = window.location;
    window.location.href = `/#/login?from=${encodeURIComponent(href)}`;

    return false;
  }

  let cfg = {
    ...config,
    headers: {
      ...(doNotNeedTokenUrls.includes(fetchUrl) ? {} : { token }),
    },
  };

  if (config.headers) {
    const { headers = {}, ...rest } = config;

    cfg = {
      ...rest,
      headers: {
        ...(headers || {
          'Content-Type': 'application/json',
        }),
        ...(doNotNeedTokenUrls.includes(fetchUrl) ? {} : { token }),
      },
    };
  }

  // 加密操作
  if (isNeedEncrypt && cfg.body) {
    const body = encrypt(JSON.parse(cfg.body as string));
    cfg = { ...cfg, body: JSON.stringify(body) };
  }

  try {
    const response = await fetch(url, cfg);
    if (isExporting) {
      try {
        const responseClone = response.clone();
        const json = await responseClone.json();
        if (json.code === 60002) {
          message.destroy();
          message.error(json.message);
        }
      } catch (error) {
        return await response.blob();
      }
    }

    const result = await response.json();

    if (result.code !== 0) {
      if (result.code === 2 || result.code === -1) {
        const { href } = window.location;
        window.location.href = `/#/login?from=${encodeURIComponent(href)}`;
        return false;
      }
      throw new Error(result.message);
    }

    return result.data;
  } catch (error: any) {
    if (error.message === 'Failed to fetch') {
      message.destroy();
      message.error('网络请求错误');
    } else {
      message.error(error.message);
    }
    throw new Error(error.message);
  }
};
