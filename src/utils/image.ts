let canvas: HTMLCanvasElement | null = null;
let canvasCtx: CanvasRenderingContext2D | null = null;

export async function loadImageData(url: string): Promise<ImageData> {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = url;
    img.setAttribute('crossOrigin', 'anonymous');
    img.onload = () => {
      resolve(getImageData(img));
    };
  });
}

function getImageData(img: any): ImageData {
  if (!canvas) {
    canvas = document.createElement('canvas');
    canvasCtx = canvas.getContext('2d');
  }
  if (!canvasCtx) {
    throw new Error('failed to create canvas');
  }
  const { width, height } = img;
  canvas.width = width;
  canvas.height = height;
  canvasCtx.drawImage(img, 0, 0);
  const data = canvasCtx.getImageData(0, 0, width, height);
  canvasCtx.clearRect(0, 0, width, height);
  return data;
}
export function getTranslateImageData(url: string) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = url;
    img.setAttribute('crossOrigin', 'anonymous');
    img.onload = () => {
      resolve(img);
    };
    img.onerror = () => {
      const placeholderImage = new Image();
      placeholderImage.src = '/assets/images/tile.webp';
      placeholderImage.onload = () => {
        resolve(placeholderImage);
      };
    };
  });
}
