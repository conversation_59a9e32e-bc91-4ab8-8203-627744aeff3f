import { loadImageData } from '@/utils/image';
import { Transform } from '@luma.gl/engine';
import { Buffer, Texture2D } from '@luma.gl/webgl';
import { project32 } from 'deck.gl';

const MIN_LNG = 60;
const MAX_LAT = 64;
const TILE_WIDTH = 4;
const TILE_HEIGHT = 4;
export const TILE_PIXELS = 400;
export const DEGREE_UNIT = 0.01;
const canvas = document.createElement('canvas');
const gl = canvas.getContext('webgl2');

/**
 * 根据指定的渲染范围计算出需要的瓦片的信息
 */
export function calculateTextureTileInfo(
  minLng: number,
  maxLng: number,
  minLat: number,
  maxLat: number,
) {
  const x1 = Math.floor((minLng - MIN_LNG) / TILE_WIDTH);
  const x2 = Math.floor((maxLng - MIN_LNG) / TILE_WIDTH);
  const y1 = Math.floor((MAX_LAT - maxLat) / TILE_HEIGHT);
  const y2 = Math.floor((MAX_LAT - minLat) / TILE_HEIGHT);

  const left = x1 * TILE_PIXELS * DEGREE_UNIT + MIN_LNG;
  const right = x2 * TILE_PIXELS * DEGREE_UNIT + MIN_LNG + TILE_WIDTH;
  const top = MAX_LAT - y1 * TILE_PIXELS * DEGREE_UNIT;
  const bottom = MAX_LAT - y2 * TILE_PIXELS * DEGREE_UNIT - TILE_HEIGHT;

  const tiles = [];
  // tile 按 Z 型排列
  for (let y = y1; y <= y2; y += 1) {
    for (let x = x1; x <= x2; x += 1) {
      tiles.push([x, y]);
    }
  }

  const width = (x2 - x1 + 1) * TILE_PIXELS;
  const height = (y2 - y1 + 1) * TILE_PIXELS;
  const rows = y2 - y1 + 1;
  const cols = x2 - x1 + 1;

  return { tiles, left, right, bottom, top, width, height, rows, cols };
}

export type PixelEncoder = (value: number) => [number, number, number, number];
export type PixelDecoder = (pixel: [number, number, number, number]) => number;

/**
 * 对传入的瓦片进行平均聚合
 * TODO: 使用web worker在其他线程来完成聚合
 */
export async function aggregate(
  urls: string[],
  encoder: PixelEncoder,
  decoder: PixelDecoder,
): Promise<ImageData> {
  return new Promise((resolve) => {
    const values = new DataView(new ArrayBuffer(TILE_PIXELS * TILE_PIXELS * 8));
    const counts = new DataView(new ArrayBuffer(TILE_PIXELS * TILE_PIXELS));
    const promises: Promise<ImageData>[] = [];

    // 记录每个像素的有效值之和以及有效值数量
    urls.forEach((url) => promises.push(loadImageData(url)));
    Promise.all(promises).then((imgs: ImageData[]) => {
      imgs.forEach((img: ImageData) => {
        for (let x = 0; x < TILE_PIXELS; x += 1) {
          for (let y = 0; y < TILE_PIXELS; y += 1) {
            const pixelNumber = y * TILE_PIXELS + x;
            const idx = pixelNumber * 4;
            const r = img.data[idx];
            const g = img.data[idx + 1];
            const b = img.data[idx + 2];
            const a = img.data[idx + 3];

            const value = decoder([r, g, b, a]);
            if (value > 0) {
              counts.setUint8(pixelNumber, counts.getUint8(pixelNumber) + 1);
              const offset = pixelNumber * 8;
              values.setFloat64(offset, values.getFloat64(offset) + value);
            }
          }
        }
      });

      // 计算有效均值、编码、写入图片缓冲区
      const buffer = new ArrayBuffer(TILE_PIXELS * TILE_PIXELS * 4);
      const data = new DataView(buffer);
      for (let x = 0; x < TILE_PIXELS; x += 1) {
        for (let y = 0; y < TILE_PIXELS; y += 1) {
          const pixelNumber = y * TILE_PIXELS + x;
          const count = counts.getUint8(pixelNumber);
          if (count > 0) {
            const value = values.getFloat64(pixelNumber * 8);
            const avg = value / count;
            const [r, g, b, a] = encoder(avg);
            const idx = pixelNumber * 4;
            data.setUint8(idx, r);
            data.setUint8(idx + 1, g);
            data.setUint8(idx + 2, b);
            data.setUint8(idx + 3, a);
          }
        }
      }
      const arr = new Uint8ClampedArray(buffer);
      resolve(new ImageData(arr, TILE_PIXELS, TILE_PIXELS));
    });
  });
}

const bandsMap = {
  1: { type: 'float', defaultValue: '0.0' },
  2: { type: 'vec2', defaultValue: 'vec2(0.0, 0.0)' },
  3: { type: 'vec3', defaultValue: 'vec3(0.0, 0.0, 0.0)' },
  4: { type: 'vec4', defaultValue: 'vec4(0.0, 0.0, 0.0, 0.0)' },
};
// 使用gpu聚合数据解决聚合时带有a通道的数据自左乘问题
export async function aggregateTileWithAlpha(
  urls: string[],
  encoder: string,
  decoder: string,
  bands: 1 | 2 | 3 | 4,
): Promise<ImageData> {
  if (!gl) throw new Error('webgl not supported');
  // 创建位标
  const invalueX = new Buffer(gl, {
    data: new Float32Array(TILE_PIXELS * TILE_PIXELS).map(
      (_, index) => index % TILE_PIXELS,
    ),
  });
  const invalueY = new Buffer(gl, {
    data: new Float32Array(TILE_PIXELS * TILE_PIXELS).map((_, index) =>
      Math.floor(index / TILE_PIXELS),
    ),
  });
  // 初始化数据
  const r = new Buffer(gl, {
    byteLength: TILE_PIXELS * TILE_PIXELS * 4,
  });
  const g = new Buffer(gl, {
    byteLength: TILE_PIXELS * TILE_PIXELS * 4,
  });
  const b = new Buffer(gl, {
    byteLength: TILE_PIXELS * TILE_PIXELS * 4,
  });
  const a = new Buffer(gl, {
    byteLength: TILE_PIXELS * TILE_PIXELS * 4,
  });
  // 创建transform
  const transform = new Transform(gl, {
    vs: `
        ${urls.map((_, i) => `uniform sampler2D bitmapTexture${i};`).join('\n')}
        attribute float invalueX;
        attribute float invalueY;
        varying float r;
        varying float g;
        varying float b;
        varying float a;
        ${decoder}
        ${encoder}
        void main()
        {
          ${bandsMap[bands].type} value = ${bandsMap[bands].defaultValue};
          float valueOpt = 0.0;
          ${bandsMap[bands].type} positionValue = ${
      bandsMap[bands].defaultValue
    };
          vec4 color;
          ${urls
            .map(
              (
                _,
                i,
              ) => `color = texture2D(bitmapTexture${i}, vec2(invalueX / ${TILE_PIXELS.toFixed(
                1,
              )}, invalueY / ${TILE_PIXELS.toFixed(1)}));
              positionValue = decode(color);
              if(color.r > 0.0 || color.g > 0.0 || color.b > 0.0) { 
                value += positionValue;
                valueOpt += 1.0;
              }`,
            )
            .join('\n')}
          vec4 rgba = encode(value / valueOpt);
          r = rgba.r;
          g = rgba.g;
          b = rgba.b;
          a = rgba.a;
        }
      `,
    varyings: ['r', 'g', 'b', 'a'],
    sourceBuffers: {
      invalueX,
      invalueY,
    },
    elementCount: TILE_PIXELS * TILE_PIXELS,
    feedbackBuffers: {
      r,
      g,
      b,
      a,
    },
    modules: [project32],
  });
  // 加载数据
  const promises = urls.map(
    (url) =>
      new Promise<Texture2D>((resolve) => {
        fetch(url).then((res) => {
          res.blob().then((blob) => {
            createImageBitmap(blob, {
              premultiplyAlpha: 'none',
              colorSpaceConversion: 'none',
              resizeQuality: 'pixelated',
            }).then((img) => {
              const texture = new Texture2D(gl, {
                format: gl.RGBA,
                dataFormat: gl.RGBA,
                type: gl.UNSIGNED_BYTE,
                mipmaps: false,
                parameters: {
                  [gl.TEXTURE_MIN_FILTER]: gl.NEAREST,
                  [gl.TEXTURE_MAG_FILTER]: gl.NEAREST,
                },
                data: img,
                width: TILE_PIXELS,
                height: TILE_PIXELS,
              });
              resolve(texture);
            });
          });
        });
      }),
  );
  const textures = await Promise.all(promises);
  transform.run({
    uniforms: textures.reduce((acc, texture, i) => {
      const cur = acc;
      cur[`bitmapTexture${i}`] = texture;
      return acc;
    }, {} as any),
  });
  const RBuffer = transform.getBuffer('r').getData();
  const GBuffer = transform.getBuffer('g').getData();
  const BBuffer = transform.getBuffer('b').getData();
  const ABuffer = transform.getBuffer('a').getData();
  const buffer = new ArrayBuffer(TILE_PIXELS * TILE_PIXELS * 4);
  const data = new DataView(buffer);
  for (let x = 0; x < TILE_PIXELS; x += 1) {
    for (let y = 0; y < TILE_PIXELS; y += 1) {
      const pixelNumber = y * TILE_PIXELS + x;
      const idx = pixelNumber * 4;
      data.setUint8(idx, RBuffer[pixelNumber]);
      data.setUint8(idx + 1, GBuffer[pixelNumber]);
      data.setUint8(idx + 2, BBuffer[pixelNumber]);
      data.setUint8(idx + 3, ABuffer[pixelNumber]);
    }
  }
  const arr = new Uint8ClampedArray(buffer);
  return new ImageData(arr, TILE_PIXELS, TILE_PIXELS);
}
