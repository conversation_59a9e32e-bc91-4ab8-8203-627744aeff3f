export function calculateUvAngle(u: number, v: number) {
   let angle = 0;
   if (u > 0 && v > 0) {
     angle = (Math.atan(v / u) * 180) / Math.PI;
   } else if (u < 0 && v > 0) {
     angle = 180 + (Math.atan(v / u) * 180) / Math.PI;
   } else if (u < 0 && v < 0) {
     angle = (Math.atan(v / u) * 180) / Math.PI - 180;
   } else if (u > 0 && v < 0) {
     angle = (Math.atan(v / u) * 180) / Math.PI;
   } else if (u === 0 && v > 0) {
     angle = 90;
   } else if (u === 0 && v < 0) {
     angle = -90;
   } else if (u > 0 && v === 0) {
     angle = 0;
   } else if (u < 0 && v === 0) {
     angle = 180;
   }
   return angle;
 }

 export function calculateUvDirection(u: number, v: number) {
    const angle = calculateUvAngle(u, v);
    if(angle >= -180 && angle < -150) return '东风';
    if(angle >= -150 && angle < -120) return '东北风';
    if(angle >= -120 && angle < -75) return '北风';
    if(angle >= -75 && angle < -25) return '西北风';
    if(angle >= -25 && angle < 30) return '西风';
    if(angle > 30 && angle < 70) return '西南风';
    if(angle >= 70 && angle < 120) return '南风';
    if(angle >= 120 && angle < 150) return '东南风';
    if(angle >= 150 && angle <= 180) return '东风';
    return '';
 }