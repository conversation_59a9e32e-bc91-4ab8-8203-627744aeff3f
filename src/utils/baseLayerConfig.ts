import { BitmapLayer } from 'deck.gl';
import type { IconLayerProps, PathLayerProps } from '@deck.gl/layers';
import { TileLayerProps } from 'deck.gl/typed';

// 瓦片图层设置
export const tileLayerBaseConfig: TileLayerProps = {
  minZoom: 3,
  maxZoom: 18,
  pickable: true,
  loadOptions: {
    // @workaround: TileLayer 无法为image/jpg这种类型的瓦片选择loader，这是deck.gl的BUG
    // 这里通过设置指定的mimeType来绕过这个问题，具体的设置含义参考
    // https://loaders.gl/modules/core/docs/api-reference/loader-options
    mimeType: 'image/jpeg',
  },
  maxRequests: 48,
  // @ts-ignore
  renderSubLayers: (props: any) => {
    const {
      bbox: { west, south, east, north },
    } = props.tile;
    return new BitmapLayer(props, {
      data: null,
      image: props.data,
      bounds: [west, south, east, north],
    });
  },
};

// 测量距离点图层设置
export const pointsLayerBaseConfig: IconLayerProps<any> = {
  id: 'distance-points-layer',
  iconAtlas: '/assets/map/point.png',
  iconMapping: {
    marker: {
      x: 0,
      y: 0,
      width: 20,
      height: 20,
    },
  },
  getIcon: () => 'marker',
  getPosition: (d) => d.coordinate,
  pickable: false,
  getSize: 20,
  sizeScale: 0.5,
};

// 测量距离点连线图层设置
export const distancePathLayerBaseConfig: PathLayerProps<[number, number][]> = {
  id: 'distance-path-layer',
  pickable: false,
  widthScale: 20,
  widthMinPixels: 2,
  widthMaxPixels: 2,
  getColor: () => [40, 108, 255],
  getWidth: () => 15,
  getPath: (d) => {
    return d;
  },
};
