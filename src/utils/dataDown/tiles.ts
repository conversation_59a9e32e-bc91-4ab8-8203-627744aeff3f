import { loadImageData } from '@/utils/image';
import GL from '@luma.gl/constants';
import { Transform } from '@luma.gl/engine';
import { Buffer, Texture2D, Texture3D } from '@luma.gl/webgl';
import { project32 } from 'deck.gl';

export const MIN_LNG = 60;
export const MAX_LAT = 64;
export const TILE_WIDTH = 4;
export const TILE_HEIGHT = 4;
export const TILE_PIXELS = 256;
export const NORMAL_TILE_PIXELS = 256;
export const DEGREE_UNIT = 0.01;
const canvas = document.createElement('canvas');
const gl = canvas.getContext('webgl2');
/**
 * 根据指定的渲染范围计算出需要的瓦片的信息
 */
export function calculateTextureTileInfo(minLng: number, maxLng: number, minLat: number, maxLat: number) {
  const x1 = Math.floor((minLng - MIN_LNG) / TILE_WIDTH);
  const x2 = Math.floor((maxLng - MIN_LNG) / TILE_WIDTH);
  const y1 = Math.floor((MAX_LAT - maxLat) / TILE_HEIGHT);
  const y2 = Math.floor((MAX_LAT - minLat) / TILE_HEIGHT);

  const left = x1 * TILE_PIXELS * DEGREE_UNIT + MIN_LNG;
  const right = x2 * TILE_PIXELS * DEGREE_UNIT + MIN_LNG + TILE_WIDTH;
  const top = MAX_LAT - y1 * TILE_PIXELS * DEGREE_UNIT;
  const bottom = MAX_LAT - y2 * TILE_PIXELS * DEGREE_UNIT - TILE_HEIGHT;

  const tiles = [];
  // tile 按 Z 型排列
  for (let y = y1; y <= y2; y += 1) {
    for (let x = x1; x <= x2; x += 1) {
      tiles.push([x, y]);
    }
  }

  const width = (x2 - x1 + 1) * TILE_PIXELS;
  const height = (y2 - y1 + 1) * TILE_PIXELS;
  const rows = y2 - y1 + 1;
  const cols = x2 - x1 + 1;

  return { tiles, left, right, bottom, top, width, height, rows, cols };
}

export type PixelEncoder = (value: number | number[]) => [number, number, number, number];
export type PixelDecoder = (pixel: [number, number, number, number], nodata: number) => number;

/**
 * 对传入的瓦片进行平均聚合
 * TODO: 使用web worker在其他线程来完成聚合
 */
export async function aggregate(urls: string[], encoder: PixelEncoder, decoder: PixelDecoder): Promise<ImageData> {
  return new Promise((resolve) => {
    const values = new DataView(new ArrayBuffer(TILE_PIXELS * TILE_PIXELS * 8));
    const counts = new DataView(new ArrayBuffer(TILE_PIXELS * TILE_PIXELS));
    const promises: Promise<ImageData>[] = [];

    // 记录每个像素的有效值之和以及有效值数量
    urls.forEach((url) => promises.push(loadImageData(url)));
    Promise.all(promises).then((imgs: ImageData[]) => {
      imgs.forEach((img: ImageData) => {
        for (let x = 0; x < TILE_PIXELS; x += 1) {
          for (let y = 0; y < TILE_PIXELS; y += 1) {
            const pixelNumber = y * TILE_PIXELS + x;
            const idx = pixelNumber * 4;
            const r = img.data[idx];
            const g = img.data[idx + 1];
            const b = img.data[idx + 2];
            const a = img.data[idx + 3];

            const value = decoder([r, g, b, a]);
            if (value > 0) {
              counts.setUint8(pixelNumber, counts.getUint8(pixelNumber) + 1);
              const offset = pixelNumber * 8;
              values.setFloat64(offset, values.getFloat64(offset) + value);
            }
          }
        }
      });

      // 计算有效均值、编码、写入图片缓冲区
      const buffer = new ArrayBuffer(TILE_PIXELS * TILE_PIXELS * 4);
      const data = new DataView(buffer);
      for (let x = 0; x < TILE_PIXELS; x += 1) {
        for (let y = 0; y < TILE_PIXELS; y += 1) {
          const pixelNumber = y * TILE_PIXELS + x;
          const count = counts.getUint8(pixelNumber);
          if (count > 0) {
            const value = values.getFloat64(pixelNumber * 8);
            const avg = value / count;
            const [r, g, b, a] = encoder(avg);
            const idx = pixelNumber * 4;
            data.setUint8(idx, r);
            data.setUint8(idx + 1, g);
            data.setUint8(idx + 2, b);
            data.setUint8(idx + 3, a);
          }
        }
      }
      const arr = new Uint8ClampedArray(buffer);
      resolve(new ImageData(arr, TILE_PIXELS, TILE_PIXELS));
    });
  });
}

/**
 * 对传入的瓦片进行平均聚合
 * TODO: 使用web worker在其他线程来完成聚合
 */
export async function aggregateTile(urls: string[], encoder: PixelEncoder, decoder: PixelDecoder, type?: string): Promise<ImageData> {
  return new Promise((resolve) => {
    const values = new DataView(new ArrayBuffer(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 8));
    const uVals = new DataView(new ArrayBuffer(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 8));
    const vVals = new DataView(new ArrayBuffer(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 8));
    const counts = new DataView(new ArrayBuffer(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS));
    const promises: Promise<ImageData>[] = [];

    // 记录每个像素的有效值之和以及有效值数量
    urls.forEach((url) => promises.push(loadImageData(url)));
    Promise.all(promises).then((imgs: ImageData[]) => {
      imgs.forEach((img: ImageData) => {
        for (let x = 0; x < NORMAL_TILE_PIXELS; x += 1) {
          for (let y = 0; y < NORMAL_TILE_PIXELS; y += 1) {
            const pixelNumber = y * NORMAL_TILE_PIXELS + x;
            const idx = pixelNumber * 4;
            const r = img.data[idx];
            const g = img.data[idx + 1];
            const b = img.data[idx + 2];
            const a = img.data[idx + 3];

            const value = decoder([r, g, b, a]);
            if (value > 0 || type === 'wiu-wiv') {
              counts.setUint8(pixelNumber, counts.getUint8(pixelNumber) + 1);
              const offset = pixelNumber * 8;

              if (type === 'wiu-wiv' && Array.isArray(value)) {
                uVals.setFloat64(offset, uVals.getFloat64(offset) + value[0]);
                vVals.setFloat64(offset, vVals.getFloat64(offset) + value[1]);
              } else {
                values.setFloat64(offset, values.getFloat64(offset) + value);
              }
            }
          }
        }
      });

      // 计算有效均值、编码、写入图片缓冲区
      const buffer = new ArrayBuffer(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 4);
      const data = new DataView(buffer);
      for (let x = 0; x < NORMAL_TILE_PIXELS; x += 1) {
        for (let y = 0; y < NORMAL_TILE_PIXELS; y += 1) {
          const pixelNumber = y * NORMAL_TILE_PIXELS + x;
          const count = counts.getUint8(pixelNumber);
          if (count > 0) {
            if (type === 'wiu-wiv') {
              const u = uVals.getFloat64(pixelNumber * 8);
              const v = vVals.getFloat64(pixelNumber * 8);
              const [r, g, b, a] = encoder([u / count, v / count]);
              const idx = pixelNumber * 4;
              data.setUint8(idx, r);
              data.setUint8(idx + 1, g);
              data.setUint8(idx + 2, b);
              data.setUint8(idx + 3, a);
            } else {
              const value = values.getFloat64(pixelNumber * 8);

              const avg = value / count;
              const [r, g, b, a] = encoder(avg);
              const idx = pixelNumber * 4;
              data.setUint8(idx, r);
              data.setUint8(idx + 1, g);
              data.setUint8(idx + 2, b);
              data.setUint8(idx + 3, a);
            }
          }
        }
      }
      const arr = new Uint8ClampedArray(buffer);
      resolve(new ImageData(arr, NORMAL_TILE_PIXELS, NORMAL_TILE_PIXELS));
    });
  });
}
const bandsMap = {
  1: { type: 'float', defaultValue: '0.0' },
  2: { type: 'vec2', defaultValue: 'vec2(0.0, 0.0)' },
  3: { type: 'vec3', defaultValue: 'vec3(0.0, 0.0, 0.0)' },
  4: { type: 'vec4', defaultValue: 'vec4(0.0, 0.0, 0.0, 0.0)' },
};
// 使用gpu聚合数据解决聚合时带有a通道的数据自左乘问题
export async function aggregateTileWithAlpha(urls: string[], encoder: string, decoder: string, bands: 1 | 2 | 3 | 4): Promise<ImageData> {
  if (!gl) throw new Error('webgl not supported');

  // 创建位标
  const invalue = new Buffer(gl, {
    data: new Float32Array(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 2).map((_, index) => {
      if (index % 2 === 0) {
        return (index / 2) % NORMAL_TILE_PIXELS;
      } else {
        return Math.floor(index / 2 / NORMAL_TILE_PIXELS);
      }
    }),
    accessor: { size: 2, divisor: 0 },
  });

  // 初始化数据
  const buffer = new Buffer(gl, {
    byteLength: NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 4 * 4,
    accessor: { size: 4, divisor: 0 },
  });
  const transform = new Transform(gl, {
    vs: `#version 300 es
        precision mediump sampler3D;
        uniform sampler3D textureMap;
        in vec2 invalue;
        out vec4 buffer;
        ${decoder}
        ${encoder}
        void main()
        {
          ${bandsMap[bands].type} value = ${bandsMap[bands].defaultValue};
          float valueOpt = 0.0;
          ${bandsMap[bands].type} positionValue = ${bandsMap[bands].defaultValue};
          vec4 color;
          ${urls
            .map(
              (_, i) => `color = texture(textureMap, vec3(invalue.x / ${NORMAL_TILE_PIXELS.toFixed(1)}, invalue.y / ${NORMAL_TILE_PIXELS.toFixed(
                1,
              )}, ${i.toFixed(1)} / ${urls.length.toFixed(1)}));
              positionValue = decode(color);
              if(color.r > 0.0 || color.g > 0.0 || color.b > 0.0) {
                value += positionValue;
                valueOpt += 1.0;
              }`,
            )
            .join('\n')}
            if(valueOpt == 0.0) {
              buffer = encode(${bands > 1 ? bandsMap[bands].type + '(0.0)' : '0.0'});
            } else {
              buffer = encode(value / valueOpt);
            }
        }
      `,
    varyings: ['buffer'],
    sourceBuffers: {
      invalue,
    },
    elementCount: NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS,
    feedbackBuffers: { buffer },
    modules: [project32],
  });

  // 加载数据
  const promises = urls.map(
    (url) =>
      new Promise<ImageBitmap>((resolve) => {
        fetch(url).then((res) => {
          res.blob().then((blob) => {
            createImageBitmap(blob, {
              premultiplyAlpha: 'none',
              colorSpaceConversion: 'none',
              resizeQuality: 'pixelated',
            })
              .then((img) => {
                resolve(img);
              })
              .catch(() => {
                resolve(null);
              });
          });
        });
      }),
  );

  const textures = (await Promise.all(promises)).filter((img) => img !== null) as ImageBitmap[];

  const allDataArray = new Buffer(gl, {
    byteLength: NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * textures.length * 4,
  });
  // 将每个 ImageBitmap 的数据上传到 3D 纹理的不同切片
  for (let z = 0; z < textures.length; z++) {
    // 创建 Framebuffer 并绑定
    const framebuffer = gl.createFramebuffer();
    gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
    // 确保未绑定 PIXEL_UNPACK_BUFFER
    gl.bindBuffer(gl.PIXEL_UNPACK_BUFFER, null);

    // 创建 2D 纹理并绑定 ImageBitmap 数据
    const texture2D = new Texture2D(gl, {
      data: textures[z],
      format: gl.RGBA,
      type: gl.UNSIGNED_BYTE,
      width: NORMAL_TILE_PIXELS,
      height: NORMAL_TILE_PIXELS,
    });
    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture2D.handle, 0);

    // 检查帧缓冲状态
    const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);
    if (status !== gl.FRAMEBUFFER_COMPLETE) {
      console.error(`Framebuffer is not complete for layer ${z}:`, status);
      continue;
    }

    // 提取像素数据
    const pixels = new Uint8Array(NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 4);
    gl.readPixels(0, 0, NORMAL_TILE_PIXELS, NORMAL_TILE_PIXELS, gl.RGBA, gl.UNSIGNED_BYTE, pixels);
    allDataArray.subData({
      offset: NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * z * 4,
      data: pixels,
      accessor: {
        size: NORMAL_TILE_PIXELS * NORMAL_TILE_PIXELS * 4,
      },
    });
    // 清理资源
    gl.deleteFramebuffer(framebuffer);
    texture2D.delete();
  }
  const texture3D = new Texture3D(gl, {
    data: allDataArray,
    width: NORMAL_TILE_PIXELS,
    height: NORMAL_TILE_PIXELS,
    depth: textures.length,
    format: GL.RGBA,
    type: GL.UNSIGNED_BYTE,
    parameters: {
      [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
      [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
    },
    mipmaps: false,
  });

  transform.run({
    uniforms: {
      textureMap: texture3D,
    },
  });

  const arr = new Uint8ClampedArray(new Uint8Array(buffer.getData()));
  return new ImageData(arr, NORMAL_TILE_PIXELS, NORMAL_TILE_PIXELS);
}
