// 原来数据下载里面执行的 从全国大气迁移过来的

import { first, last } from "lodash";
import { array_, getDecoder, number_ } from "../format";


// 数据下载里的 原来走的是globalPoMapping
export const dataDownLoadglobalPoMapping: Record<string, string> = {
  pm25: 'PM₂.₅',
  pm10: 'PM₁₀',
  o3: 'O₃_S5P',
  hcho: 'HCHO_S5P',
  no2: 'NO₂_S5P',
  co: 'CO',
  so2: 'SO₂',
  co2: 'CO₂',
  nh3: 'NH₃',
  nox: 'NOx',
  vocs: 'VOCs',
  voc: 'VOCs',
  aod: 'AOD',
  ch4: 'CH₄',
  uva: 'UVA',
  uvb: 'UVB',
  'pm2.5': 'PM₂.₅',
  'CH₄': 'CH₄',
  'pm₂.₅': 'PM₂.₅',
  'pm₁₀': 'PM₁₀',
  'o₃': 'O₃',
  'no₂': 'NO₂',
  'so₂': 'SO₂',
  'nh₃': 'NH₃',
  scpm25: 'PM₂.₅插值',
  gk2b_no2tcd: 'NO₂柱浓度_GK2B',
  no2tcd: 'NO₂柱浓度',
  gk2b_o3tcd: 'O₃柱浓度_GK2B',
  o3tcd: 'O₃柱浓度',
  gk2b_hcho: 'HCHO_GK2B',
  gk2b_co: 'CO_GK2B',
  gk2b_so2: 'SO₂_GK2B',
  gk2b_o3: 'O₃_GK2B',
  gk2b_no2: 'NO₂_GK2B',
  cotcd: 'CO柱浓度',
  so2tcd: 'SO₂柱浓度',
};

// 解码器 给index 里是一样的
export const decoderFactory = (dataType: string) => {
  const decoders: Record<string, string> = {
    pm25: `((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})`,
    scpm25: `((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})`,
    lst: '((data.r * 255.0) - 128.00 - {min})/ ({max} - {min})',
    pm10: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    aod: '(((data.r * 255.0 + data.g) * 255.0 / 1000.0) - {min}) / ({max} - {min})',
    no2: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    gk2b_no2: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    o3: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    gk2b_o3: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    fnr: `if ( data.r * 255.0 / 10.0 >= 6.0 ) {
      return 1.0;
    } else if( data.r * 255.0 / 10.0 < 6.0 && data.r * 255.0 / 10.0 >= 4.0 ) {
      return 0.66;
    } else if( data.r * 255.0 / 10.0 < 4.0 && data.r * 255.0 / 10.0 >= 2.0 ) {
      return 0.5;
    } else if( data.r * 255.0 / 10.0 < 2.0 && data.r * 255.0 / 10.0 >= 1.0 ) {
      return 0.33;
    } else {
      return 0.0;
    }`,
    hid: `if ( data.r * 255.0 > 2.0 ) {
      return 0.667;
    } else if ( data.r * 255.0 <= 2.0 && data.r * 255.0 > 1.0 ) {
      return 0.333;
    } else if ( data.r * 255.0 <= 1.0 && data.r * 255.0 > 0.0 ) {
      return 0.0;
    }`,
    co: `((data.r * 255.0 + data.g) * 255.0 / 10000.0 * 6.022141 - {min}) / ({max} - {min})`,
    gk2b_co: `((data.r * 255.0 + data.g) * 255.0 / 10000.0 * 6.022141 - {min}) / ({max} - {min})`,
    so2: '(((data.r * 255.0 + data.g) * 255.0 / 1000.0) - {min}) / ({max} - {min})',
    gk2b_so2: `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min})/ ({max} - {min})`,
    // eslint-disable-next-line max-len
    o3tcd: `((data.r * 255.0  + data.g ) * 255.0  - {min})/ ({max} - {min})`,
    gk2b_o3tcd: `((data.r * 255.0  + data.g ) * 255.0  - {min})/ ({max} - {min})`,
    // eslint-disable-next-line max-len
    no2tcd: `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min})/ ({max} - {min})`,
    gk2b_no2tcd: `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min})/ ({max} - {min})`,
    hcho:
      `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` +
      ` + data.a * 255.0 + data.r) * 255.0 ` +
      `/ 100000000.0 - {min}) / ({max} - {min})`,
    gk2b_hcho:
      `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` +
      ` + data.a * 255.0 + data.r) * 255.0 ` +
      `/ 100000000.0 - {min}) / ({max} - {min})`,
    c2h2o2:
      `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` +
      ` + data.a * 255.0 + data.r) * 255.0 ` +
      `/ 100000000.0 - {min}) / ({max} - {min})`,
    tmp: '(data.r * 255.0 - 128.0 - {min}) / ({max} - {min})',
    tmp_max: '(data.r * 255.0 - 128.0 - {min}) / ({max} - {min})',
    pre: `
    float pre = data.r * 255.0;
    if(pre <= 0.00000001) {
      return  0.00000001;
    }
    return (pre - {min}) / ({max} - {min});
  `,
    vis: '(data.r * 0.2 * 255.0 - {min}) / ({max} - {min})',
    rh: '(data.r * 255.0 - {min}) / ({max} - {min})',
    prs: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    'wiu-wiv': `
    float u = data.r*255.0 + data.b*255.0/100.0-100.0;
    float v = data.g*255.0 + mod(data.b*255.0, 10.0)/10.0-100.0;
    float speed = sqrt(u * u + v * v);
    if(speed <= 0.00000001) {
      return 0.00000001;
    }
    return (speed - {min}) / ({max} - {min});
  `,
    uva: '(data.r * 255.0 - {min})/ ({max} - {min})',
    uvb: '((data.r / 10.0) * 255.0 - {min}) / ({max} - {min})',
    ch4: '((data.r * 255.0 + data.g) * 255.0  - {min}) / ({max} - {min})',
    co2: '((data.r * 255.0 + data.g) * 255.0/ 10.0 - {min}) / ({max} - {min}) ',
  };
  return decoders[dataType];
};

// 卫星遥感
export const remoteSensingValuesAndColors: Record<string, any> = {
  pm25: {
    unit: 'μg/m³',
    formula: 'PM₂.₅',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      { min: 100, color: 'rgb(131, 0, 36)' },
      { min: 90, max: 100, color: 'rgb(157, 0, 86)' },
      { min: 80, max: 90, color: 'rgb(198, 0, 0)' },
      { min: 70, max: 80, color: 'rgb(245, 0, 0)' },
      { min: 60, max: 70, color: 'rgb(246, 100, 0)' },
      { min: 50, max: 60, color: 'rgb(247, 137, 0)' },
      { min: 45, max: 50, color: 'rgb(249, 189, 0)' },
      { min: 40, max: 45, color: 'rgb(250, 210, 0)' },
      { min: 35, max: 40, color: 'rgb(252, 234, 0)' },
      { min: 30, max: 35, color: 'rgb(253, 255, 0)' },
      { min: 25, max: 30, color: 'rgb(218,255, 0)' },
      { min: 20, max: 25, color: 'rgb(169, 255, 0)' },
      { min: 15, max: 20, color: 'rgb(153,253,11)' },
      { min: 10, max: 15, color: 'rgb(70,232,0)' },
      { min: 5, max: 10, color: 'rgb(62,207,0)' },
      { min: 0, max: 5, color: 'rgb(52,179, 0) ' },
    ],
  },
  scpm25: {
    unit: 'μg/m³',
    formula: 'PM₂.₅插值',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      { min: 100, color: 'rgb(131, 0, 36)' },
      { min: 90, max: 100, color: 'rgb(157, 0, 86)' },
      { min: 80, max: 90, color: 'rgb(198, 0, 0)' },
      { min: 70, max: 80, color: 'rgb(245, 0, 0)' },
      { min: 60, max: 70, color: 'rgb(246, 100, 0)' },
      { min: 50, max: 60, color: 'rgb(247, 137, 0)' },
      { min: 45, max: 50, color: 'rgb(249, 189, 0)' },
      { min: 40, max: 45, color: 'rgb(250, 210, 0)' },
      { min: 35, max: 40, color: 'rgb(252, 234, 0)' },
      { min: 30, max: 35, color: 'rgb(253, 255, 0)' },
      { min: 25, max: 30, color: 'rgb(218,255, 0)' },
      { min: 20, max: 25, color: 'rgb(169, 255, 0)' },
      { min: 15, max: 20, color: 'rgb(153,253,11)' },
      { min: 10, max: 15, color: 'rgb(70,232,0)' },
      { min: 5, max: 10, color: 'rgb(62,207,0)' },
      { min: 0, max: 5, color: 'rgb(52,179, 0) ' },
    ],
  },
  lst: {
    unit: '℃',
    formula: '地表温度',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      { min: 80, color: 'rgb(58,1,5)' },
      { min: 60, max: 80, color: 'rgb(85,3,8)' },
      { min: 50, max: 60, color: 'rgb(164,2,5)' },
      { min: 40, max: 50, color: 'rgb(226,1,2)' },
      { min: 35, max: 40, color: 'rgb(255,20,2)' },
      { min: 30, max: 35, color: 'rgb(255,68,5)' },
      { min: 25, max: 30, color: 'rgb(255,120,5)' },
      { min: 20, max: 25, color: 'rgb(255,147,3)' },
      { min: 15, max: 20, color: 'rgb(254,183,20)' },
      { min: 10, max: 15, color: 'rgb(252,206,88)' },
      { min: 5, max: 10, color: 'rgb(250,222,134)' },
      { min: 0, max: 5, color: 'rgb(254,254,200)' },
      { min: -5, max: 0, color: 'rgb(54,161,223)' },
      { min: -10, max: -5, color: 'rgb(15,106,189)' },
      { min: -20, max: -10, color: 'rgb(11,56,164)' },
      { max: -20, color: 'rgb(3,16,91)' },
    ],
  },
  pm10: {
    unit: 'μg/m³',
    formula: 'PM₁₀',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      { min: 200, color: 'rgb(131, 0, 36)' },
      { min: 180, max: 200, color: 'rgb(157, 0, 86)' },
      { min: 160, max: 180, color: 'rgb(198, 0, 0)' },
      { min: 140, max: 160, color: 'rgb(245, 0, 0)' },
      { min: 120, max: 140, color: 'rgb(246, 100, 0)' },
      { min: 100, max: 120, color: 'rgb(247, 137, 0)' },
      { min: 90, max: 100, color: 'rgb(249, 189, 0)' },
      { min: 80, max: 90, color: 'rgb(250, 210, 0)' },
      { min: 70, max: 80, color: 'rgb(252, 234, 0)' },
      { min: 60, max: 70, color: 'rgb(253, 255 ,0)' },
      { min: 50, max: 60, color: 'rgb(218,255, 0)' },
      { min: 40, max: 50, color: 'rgb(169, 255, 0)' },
      { min: 30, max: 40, color: 'rgb(153,253,11)' },
      { min: 20, max: 30, color: 'rgb(70,232,0)' },
      { min: 10, max: 20, color: 'rgb(62,207,0)' },
      { min: 0, max: 10, color: 'rgb(52,179, 0) ' },
    ],
  },
  no2: {
    unit: 'μg/m³',
    formula: 'NO₂_S5P',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 250, color: 'rgb(131, 0, 36)' },
      { min: 200, max: 250, color: 'rgb(157, 0, 86)' },
      { min: 180, max: 200, color: 'rgb(181, 0, 0)' },
      { min: 160, max: 180, color: 'rgb(198, 0, 0)' },
      { min: 140, max: 160, color: 'rgb(245, 0, 0)' },
      { min: 120, max: 140, color: 'rgb(246, 100, 0)' },
      { min: 100, max: 120, color: 'rgb(247, 137, 0)' },
      { min: 80, max: 100, color: 'rgb(249, 189, 0)' },
      { min: 60, max: 80, color: 'rgb(250, 210, 0)' },
      { min: 40, max: 60, color: 'rgb(252, 234, 0)' },
      { min: 30, max: 40, color: 'rgb(253, 255 ,0)' },
      { min: 25, max: 30, color: 'rgb(218,255, 0)' },
      { min: 20, max: 25, color: 'rgb(169, 255, 0)' },
      { min: 15, max: 20, color: 'rgb(79, 255, 0)' },
      { min: 10, max: 15, color: 'rgb(70,232,0)' },
      { min: 5, max: 10, color: 'rgb(62,207,0)' },
      { min: 0, max: 5, color: 'rgb(52,179, 0) ' },
    ],
  },
  gk2b_no2: {
    unit: 'μg/m³',
    formula: 'NO₂_GK2B',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 250, color: 'rgb(131, 0, 36)' },
      { min: 200, max: 250, color: 'rgb(157, 0, 86)' },
      { min: 180, max: 200, color: 'rgb(181, 0, 0)' },
      { min: 160, max: 180, color: 'rgb(198, 0, 0)' },
      { min: 140, max: 160, color: 'rgb(245, 0, 0)' },
      { min: 120, max: 140, color: 'rgb(246, 100, 0)' },
      { min: 100, max: 120, color: 'rgb(247, 137, 0)' },
      { min: 80, max: 100, color: 'rgb(249, 189, 0)' },
      { min: 60, max: 80, color: 'rgb(250, 210, 0)' },
      { min: 40, max: 60, color: 'rgb(252, 234, 0)' },
      { min: 30, max: 40, color: 'rgb(253, 255 ,0)' },
      { min: 25, max: 30, color: 'rgb(218,255, 0)' },
      { min: 20, max: 25, color: 'rgb(169, 255, 0)' },
      { min: 15, max: 20, color: 'rgb(79, 255, 0)' },
      { min: 10, max: 15, color: 'rgb(70,232,0)' },
      { min: 5, max: 10, color: 'rgb(62,207,0)' },
      { min: 0, max: 5, color: 'rgb(52,179, 0) ' },
    ],
  },
  o3: {
    unit: 'μg/m³',
    formula: 'O₃_S5P',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 250, color: 'rgb(157, 0, 86)' },
      { min: 200, max: 250, color: 'rgb(198, 0, 0)' },
      { min: 180, max: 200, color: 'rgb(245, 0, 0)' },
      { min: 160, max: 180, color: 'rgb(246, 100 ,0)' },
      { min: 140, max: 160, color: 'rgb(247, 137, 0)' },
      { min: 120, max: 140, color: 'rgb(249, 189, 0)' },
      { min: 100, max: 120, color: 'rgb(250, 210, 0)' },
      { min: 90, max: 100, color: 'rgb(252, 234, 0)' },
      { min: 80, max: 90, color: 'rgb(253, 255 ,0)' },
      { min: 70, max: 80, color: 'rgb(169, 255, 0)' },
      { min: 60, max: 70, color: 'rgb(79, 255, 0)' },
      { min: 50, max: 60, color: 'rgb(70,232,0)' },
      { max: 50, color: 'rgb(52,179, 0)' },
    ],
  },
  gk2b_o3: {
    unit: 'μg/m³',
    formula: 'O₃_GK2B',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 250, color: 'rgb(157, 0, 86)' },
      { min: 200, max: 250, color: 'rgb(198, 0, 0)' },
      { min: 180, max: 200, color: 'rgb(245, 0, 0)' },
      { min: 160, max: 180, color: 'rgb(246, 100 ,0)' },
      { min: 140, max: 160, color: 'rgb(247, 137, 0)' },
      { min: 120, max: 140, color: 'rgb(249, 189, 0)' },
      { min: 100, max: 120, color: 'rgb(250, 210, 0)' },
      { min: 90, max: 100, color: 'rgb(252, 234, 0)' },
      { min: 80, max: 90, color: 'rgb(253, 255 ,0)' },
      { min: 70, max: 80, color: 'rgb(169, 255, 0)' },
      { min: 60, max: 70, color: 'rgb(79, 255, 0)' },
      { min: 50, max: 60, color: 'rgb(70,232,0)' },
      { max: 50, color: 'rgb(52,179, 0)' },
    ],
  },
  so2: {
    unit: 'du',
    equalMax: true,
    formula: 'SO₂',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(1);
    },
    values: [
      { min: 1.9, color: 'rgb(131, 0, 36)' },
      { min: 1.8, max: 1.9, color: 'rgb(157, 0, 86)' },
      { min: 1.7, max: 1.8, color: 'rgb(181, 0, 0)' },
      { min: 1.6, max: 1.7, color: 'rgb(198, 0, 0)' },
      { min: 1.5, max: 1.6, color: 'rgb(245, 0, 0)' },
      { min: 1.4, max: 1.5, color: 'rgb(246, 100, 0)' },
      { min: 1.3, max: 1.4, color: 'rgb(247, 137, 0)' },
      { min: 1.2, max: 1.3, color: 'rgb(249, 189, 0)' },
      { min: 1.1, max: 1.2, color: 'rgb(250, 210, 0)' },
      { min: 1, max: 1.1, color: 'rgb(252, 234, 0)' },
      { min: 0.9, max: 1, color: 'rgb(253, 255 ,0)' },
      { min: 0.75, max: 0.9, color: 'rgb(218,255, 0)' },
      { min: 0.6, max: 0.75, color: 'rgb(169, 255, 0)' },
      { min: 0.45, max: 0.6, color: 'rgb(79, 255, 0)' },
      { min: 0.3, max: 0.45, color: 'rgb(70,232,0)' },
      { min: 0.15, max: 0.3, color: 'rgb(62,207,0)' },
      { min: 0, max: 0.15, color: 'rgb(52,179, 0)' },
    ],
  },
  so2tcd: {
    unit: 'du',
    equalMax: true,
    formula: 'SO₂柱浓度',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(1);
    },
    values: [
      { min: 1.9, color: 'rgb(131, 0, 36)' },
      { min: 1.8, max: 1.9, color: 'rgb(157, 0, 86)' },
      { min: 1.7, max: 1.8, color: 'rgb(181, 0, 0)' },
      { min: 1.6, max: 1.7, color: 'rgb(198, 0, 0)' },
      { min: 1.5, max: 1.6, color: 'rgb(245, 0, 0)' },
      { min: 1.4, max: 1.5, color: 'rgb(246, 100, 0)' },
      { min: 1.3, max: 1.4, color: 'rgb(247, 137, 0)' },
      { min: 1.2, max: 1.3, color: 'rgb(249, 189, 0)' },
      { min: 1.1, max: 1.2, color: 'rgb(250, 210, 0)' },
      { min: 1, max: 1.1, color: 'rgb(252, 234, 0)' },
      { min: 0.9, max: 1, color: 'rgb(253, 255 ,0)' },
      { min: 0.75, max: 0.9, color: 'rgb(218,255, 0)' },
      { min: 0.6, max: 0.75, color: 'rgb(169, 255, 0)' },
      { min: 0.45, max: 0.6, color: 'rgb(79, 255, 0)' },
      { min: 0.3, max: 0.45, color: 'rgb(70,232,0)' },
      { min: 0.15, max: 0.3, color: 'rgb(62,207,0)' },
      { min: 0, max: 0.15, color: 'rgb(52,179, 0)' },
    ],
  },
  gk2b_so2: {
    unit: '1e16molec./cm²',
    equalMax: true,
    formula: 'SO₂_GK2B',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(1);
    },
    values: [
      { min: 1.9, color: 'rgb(131, 0, 36)' },
      { min: 1.8, max: 1.9, color: 'rgb(157, 0, 86)' },
      { min: 1.7, max: 1.8, color: 'rgb(181, 0, 0)' },
      { min: 1.6, max: 1.7, color: 'rgb(198, 0, 0)' },
      { min: 1.5, max: 1.6, color: 'rgb(245, 0, 0)' },
      { min: 1.4, max: 1.5, color: 'rgb(246, 100, 0)' },
      { min: 1.3, max: 1.4, color: 'rgb(247, 137, 0)' },
      { min: 1.2, max: 1.3, color: 'rgb(249, 189, 0)' },
      { min: 1.1, max: 1.2, color: 'rgb(250, 210, 0)' },
      { min: 1, max: 1.1, color: 'rgb(252, 234, 0)' },
      { min: 0.9, max: 1, color: 'rgb(253, 255 ,0)' },
      { min: 0.75, max: 0.9, color: 'rgb(218,255, 0)' },
      { min: 0.6, max: 0.75, color: 'rgb(169, 255, 0)' },
      { min: 0.45, max: 0.6, color: 'rgb(79, 255, 0)' },
      { min: 0.3, max: 0.45, color: 'rgb(70,232,0)' },
      { min: 0.15, max: 0.3, color: 'rgb(62,207,0)' },
      { min: 0, max: 0.15, color: 'rgb(52,179, 0)' },
    ],
  },
  co: {
    equalMax: true,
    formula: 'CO',
    unit: '1e19molec./cm²',
    roundFunc: (v: any) => {
      const correctedValue = v / 1e19;
      return Math.round(correctedValue * 1000) / 1000;
    },
    values: [
      { min: 0.29, color: '#ff0000' },
      { min: 0.28, max: 0.29, color: '#ff3700' },
      { min: 0.27, max: 0.28, color: '#ff5500' },
      { min: 0.26, max: 0.27, color: '#ff7300' },
      { min: 0.25, max: 0.26, color: '#ff8c00' },
      { min: 0.24, max: 0.25, color: '#ffa600' },
      { min: 0.23, max: 0.24, color: '#ffbf00' },
      { min: 0.22, max: 0.23, color: '#ffdd00' },
      { min: 0.21, max: 0.22, color: '#fff700' },
      { min: 0.2, max: 0.21, color: '#e6f200' },
      { min: 0.18, max: 0.2, color: '#cde800' },
      { min: 0.16, max: 0.18, color: '#bcfe58' },
      { min: 0.14, max: 0.16, color: '#52feb7' },
      { min: 0.12, max: 0.14, color: '#47cffd' },
      { min: 0.1, max: 0.12, color: '#3ba5ff' },
      { min: 0.05, max: 0.1, color: '#2b64ff' },
      { min: 0, max: 0.05, color: '#2002ff' },
    ],
  },
  cotcd: {
    unit: '1e19molec./cm²',
    equalMax: true,
    formula: 'CO柱浓度',
    roundFunc: (v: any) => {
      const correctedValue = (v * 6.022141e19) / 1e17;
      return `${Math.round(10 * correctedValue) / 1000}`;
    },
    values: [
      { min: 0.29, color: '#ff0000' },
      { min: 0.28, max: 0.29, color: '#ff3700' },
      { min: 0.27, max: 0.28, color: '#ff5500' },
      { min: 0.26, max: 0.27, color: '#ff7300' },
      { min: 0.25, max: 0.26, color: '#ff8c00' },
      { min: 0.24, max: 0.25, color: '#ffa600' },
      { min: 0.23, max: 0.24, color: '#ffbf00' },
      { min: 0.22, max: 0.23, color: '#ffdd00' },
      { min: 0.21, max: 0.22, color: '#fff700' },
      { min: 0.2, max: 0.21, color: '#e6f200' },
      { min: 0.18, max: 0.2, color: '#cde800' },
      { min: 0.16, max: 0.18, color: '#bcfe58' },
      { min: 0.14, max: 0.16, color: '#52feb7' },
      { min: 0.12, max: 0.14, color: '#47cffd' },
      { min: 0.1, max: 0.12, color: '#3ba5ff' },
      { min: 0.05, max: 0.1, color: '#2b64ff' },
      { min: 0, max: 0.05, color: '#2002ff' },
    ],
  },
  gk2b_co: {
    unit: '1e19molec./cm²',
    equalMax: true,
    formula: 'CO_GK2B',
    roundFunc: (v: any) => {
      const correctedValue = (v * 6.022141e19) / 1e17;
      return `${Math.round(10 * correctedValue) / 1000}`;
    },
    values: [
      { min: 0.29, color: '#ff0000' },
      { min: 0.28, max: 0.29, color: '#ff3700' },
      { min: 0.27, max: 0.28, color: '#ff5500' },
      { min: 0.26, max: 0.27, color: '#ff7300' },
      { min: 0.25, max: 0.26, color: '#ff8c00' },
      { min: 0.24, max: 0.25, color: '#ffa600' },
      { min: 0.23, max: 0.24, color: '#ffbf00' },
      { min: 0.22, max: 0.23, color: '#ffdd00' },
      { min: 0.21, max: 0.22, color: '#fff700' },
      { min: 0.2, max: 0.21, color: '#e6f200' },
      { min: 0.18, max: 0.2, color: '#cde800' },
      { min: 0.16, max: 0.18, color: '#bcfe58' },
      { min: 0.14, max: 0.16, color: '#52feb7' },
      { min: 0.12, max: 0.14, color: '#47cffd' },
      { min: 0.1, max: 0.12, color: '#3ba5ff' },
      { min: 0.05, max: 0.1, color: '#2b64ff' },
      { min: 0, max: 0.05, color: '#2002ff' },
    ],
  },
  aod: {
    unit: '无量纲',
    equalMax: true,
    formula: 'AOD',
    roundFunc: (v: any) => {
      const number = Number(v);
      return number ? Math.round(number * 100) / 100 : 0;
    },
    values: [
      { min: 1.4, max: 1.5, color: 'rgb(255,0,0)' },
      { min: 1.3, max: 1.4, color: 'rgb(237,103,3)' },
      { min: 1.2, max: 1.3, color: 'rgb(253,147,2)' },
      { min: 1.1, max: 1.2, color: 'rgb(253,203,2)' },
      { min: 1.0, max: 1.1, color: 'rgb(253,227,2)' },
      { min: 0.9, max: 1.0, color: 'rgb(255,255,0)' },
      { min: 0.8, max: 0.9, color: 'rgb(220,251,4)' },
      { min: 0.7, max: 0.8, color: 'rgb(186,239,1)' },
      { min: 0.6, max: 0.7, color: 'rgb(128,255,0)' },
      { min: 0.5, max: 0.6, color: 'rgb(128,255,128)' },
      { min: 0.4, max: 0.5, color: 'rgb(135,255,255)' },
      { min: 0.3, max: 0.4, color: 'rgb(95,205,255)' },
      { min: 0.2, max: 0.3, color: 'rgb(55,165,255)' },
      { min: 0.1, max: 0.2, color: 'rgb(15,100,255)' },
      { min: 0, max: 0.1, color: 'rgb(40,0,255) ' },
    ],
  },
  hcho: {
    unit: '1e16molec./cm²',
    thematicUint: '1e16molec./c㎡',
    equalMax: true,
    formula: 'HCHO_S5P',
    roundFunc: (v: any) => {
      const correctedValue = v / 1e16;
      return Math.round(correctedValue * 10) / 10
    },
    values: [
      { min: 3, color: 'rgb(131, 0, 36)' },
      { min: 2.75, max: 3, color: 'rgb(157, 0, 86)' },
      { min: 2.5, max: 2.75, color: 'rgb(178, 0, 50)' },
      { min: 2.25, max: 2.5, color: 'rgb(198, 0, 0)' },
      { min: 2, max: 2.25, color: 'rgb(245, 0, 0)' },
      { min: 1.75, max: 2, color: 'rgb(246, 100, 0)' },
      { min: 1.6, max: 1.75, color: 'rgb(247, 137, 0)' },
      { min: 1.45, max: 1.6, color: 'rgb(249, 189, 0)' },
      { min: 1.3, max: 1.45, color: 'rgb(250, 210, 0)' },
      { min: 1.15, max: 1.3, color: 'rgb(252, 234, 0)' },
      { min: 1, max: 1.15, color: 'rgb(253, 255, 0)' },
      { min: 0.75, max: 1, color: 'rgb(218,255, 0)' },
      { min: 0.6, max: 0.75, color: 'rgb(169, 255, 0)' },
      { min: 0.45, max: 0.6, color: 'rgb(153,253,11)' },
      { min: 0.3, max: 0.45, color: 'rgb(70,232,0)' },
      { min: 0.15, max: 0.3, color: 'rgb(62,207,0)' },
      { min: 0, max: 0.15, color: 'rgb(52,179, 0) ' },
    ],
  },
  gk2b_hcho: {
    unit: '1e16molec./cm²',
    thematicUint: '1e16molec./c㎡',
    equalMax: true,
    formula: 'HCHO_GK2B',
    roundFunc: (v: any) => {
      const correctedValue = v / 1e16;
      return Math.round(correctedValue * 10) / 10;
    },
    values: [
      { min: 3, color: 'rgb(131, 0, 36)' },
      { min: 2.75, max: 3, color: 'rgb(157, 0, 86)' },
      { min: 2.5, max: 2.75, color: 'rgb(178, 0, 50)' },
      { min: 2.25, max: 2.5, color: 'rgb(198, 0, 0)' },
      { min: 2, max: 2.25, color: 'rgb(245, 0, 0)' },
      { min: 1.75, max: 2, color: 'rgb(246, 100, 0)' },
      { min: 1.6, max: 1.75, color: 'rgb(247, 137, 0)' },
      { min: 1.45, max: 1.6, color: 'rgb(249, 189, 0)' },
      { min: 1.3, max: 1.45, color: 'rgb(250, 210, 0)' },
      { min: 1.15, max: 1.3, color: 'rgb(252, 234, 0)' },
      { min: 1, max: 1.15, color: 'rgb(253, 255, 0)' },
      { min: 0.75, max: 1, color: 'rgb(218,255, 0)' },
      { min: 0.6, max: 0.75, color: 'rgb(169, 255, 0)' },
      { min: 0.45, max: 0.6, color: 'rgb(153,253,11)' },
      { min: 0.3, max: 0.45, color: 'rgb(70,232,0)' },
      { min: 0.15, max: 0.3, color: 'rgb(62,207,0)' },
      { min: 0, max: 0.15, color: 'rgb(52,179, 0) ' },
    ],
  },
  ch4: {
    unit: 'ppb',
    formula: 'CH₄',
    equalMax: true,
    roundFunc: (v: any) => {
      const number = Number(v);
      return number ? number.toFixed(0) : 0;
    },
    values: [
      { min: 2300, color: 'rgb(127,33,63)' },
      { min: 2200, max: 2300, color: 'rgb(213,11,85)' },
      { min: 2100, max: 2200, color: 'rgb(253,182 ,90)' },
      { min: 2000, max: 2100, color: 'rgb(238,192,125)' },
      { min: 1900, max: 2000, color: 'rgb(253,237,177)' },
      { min: 1850, max: 1900, color: 'rgb(232,254,181)' },
      { min: 1800, max: 1850, color: 'rgb(74,227,206)' },
      { max: 1800, color: 'rgb(95,217,251)' },
    ],
  },
  c2h2o2: {
    unit: '1e16molec./cm²',
    equalMax: true,
    formula: 'C₂H₂O₂',
    roundFunc: (v: any) => {
      const correctedValue = v / 1e16;
      return `${correctedValue.toFixed(1)}`;
    },
    values: [
      { min: 1.4, max: 1.6, color: 'rgb(255,0,0)' },
      { min: 1.2, max: 1.4, color: 'rgb(241,252,20)' },
      { min: 1, max: 1.2, color: 'rgb(188,254,88)' },
      { min: 0.8, max: 1, color: 'rgb(38,254,183)' },
      { min: 0.6, max: 0.8, color: 'rgb(16,207,253)' },
      { min: 0.4, max: 0.6, color: 'rgb(55,165,255)' },
      { min: 0.2, max: 0.4, color: 'rgb(15,100,255)' },
      { min: 0, max: 0.2, color: 'rgb(0,0,255)' },
    ],
  },
  o3tcd: {
    unit: 'DU',
    equalMax: true,
    formula: 'O₃柱浓度',
    roundFunc: (v: any) => {
      const number = Number(v);
      return number ? number.toFixed(0) : 0;
    },
    values: [
      {
        min: 300,
        color: '#c20000',
      },
      {
        min: 259,
        max: 300,
        color: '#ff3700',
      },
      {
        min: 258,
        max: 259,
        color: '#ff7300',
      },
      {
        min: 257,
        max: 258,
        color: '#ff8c00',
      },
      {
        min: 256,
        max: 257,
        color: '#ffbf00',
      },
      {
        min: 255,
        max: 256,
        color: '#ffdd00',
      },
      {
        min: 254,
        max: 255,
        color: '#fff700',
      },
      {
        min: 253,
        max: 254,
        color: '#bcfe58',
      },
      {
        min: 252,
        max: 253,
        color: '#52feb7',
      },
      {
        min: 251,
        max: 252,
        color: '#3ba5ff',
      },
      {
        max: 250,
        color: '#2022ff',
      },
    ],
  },
  gk2b_o3tcd: {
    unit: 'DU',
    equalMax: true,
    formula: 'O₃柱浓度_GK2B',
    roundFunc: (v: any) => {
      const number = Number(v);
      return number ? number.toFixed(0) : 0;
    },
    values: [
      {
        min: 300,
        color: '#c20000',
      },
      {
        min: 259,
        max: 300,
        color: '#ff3700',
      },
      {
        min: 258,
        max: 259,
        color: '#ff7300',
      },
      {
        min: 257,
        max: 258,
        color: '#ff8c00',
      },
      {
        min: 256,
        max: 257,
        color: '#ffbf00',
      },
      {
        min: 255,
        max: 256,
        color: '#ffdd00',
      },
      {
        min: 254,
        max: 255,
        color: '#fff700',
      },
      {
        min: 253,
        max: 254,
        color: '#bcfe58',
      },
      {
        min: 252,
        max: 253,
        color: '#52feb7',
      },
      {
        min: 251,
        max: 252,
        color: '#3ba5ff',
      },
      {
        max: 250,
        color: '#2022ff',
      },
    ],
  },
  no2tcd: {
    unit: '1e16molec./cm²',
    equalMax: true,
    formula: 'NO₂柱浓度',
    roundFunc: (v: any) => {
      const correctedValue = v / 1e16;
      return `${correctedValue.toFixed(1)}`;
    },
    values: [
      {
        min: 1.2,
        color: '#ff0000',
      },
      {
        min: 1.18,
        max: 1.2,
        color: '#ff3700',
      },
      {
        min: 1.16,
        max: 1.18,
        color: '#ff5500',
      },
      {
        min: 1.14,
        max: 1.16,
        color: '#ff7300',
      },
      {
        min: 1.12,
        max: 1.14,
        color: '#ff8c00',
      },
      {
        min: 1.1,
        max: 1.12,
        color: '#ffa600',
      },
      {
        min: 1,
        max: 1.1,
        color: '#ffbf00',
      },
      {
        min: 0.9,
        max: 1,
        color: '#ffdd00',
      },
      {
        min: 0.8,
        max: 0.9,
        color: '#fff700',
      },
      {
        min: 0.7,
        max: 0.8,
        color: '#e6f200',
      },
      {
        min: 0.6,
        max: 0.7,
        color: '#cde800',
      },
      {
        min: 0.5,
        max: 0.6,
        color: '#bcfe58',
      },
      {
        min: 0.4,
        max: 0.5,
        color: '#52feb7',
      },
      {
        min: 0.3,
        max: 0.4,
        color: '#47cffd',
      },
      {
        min: 0.2,
        max: 0.3,
        color: '#3ba5ff',
      },
      {
        min: 0.1,
        max: 0.2,
        color: '#2b64ff',
      },
      {
        max: 0.1,
        color: '#2002ff',
      },
    ],
  },
  gk2b_no2tcd: {
    unit: '1e16molec./cm²',
    equalMax: true,
    formula: 'NO₂柱浓度_GK2B',
    roundFunc: (v: any) => {
      const correctedValue = v / 1e16;
      return `${correctedValue.toFixed(1)}`;
    },
    values: [
      {
        min: 1.2,
        color: '#ff0000',
      },
      {
        min: 1.18,
        max: 1.2,
        color: '#ff3700',
      },
      {
        min: 1.16,
        max: 1.18,
        color: '#ff5500',
      },
      {
        min: 1.14,
        max: 1.16,
        color: '#ff7300',
      },
      {
        min: 1.12,
        max: 1.14,
        color: '#ff8c00',
      },
      {
        min: 1.1,
        max: 1.12,
        color: '#ffa600',
      },
      {
        min: 1,
        max: 1.1,
        color: '#ffbf00',
      },
      {
        min: 0.9,
        max: 1,
        color: '#ffdd00',
      },
      {
        min: 0.8,
        max: 0.9,
        color: '#fff700',
      },
      {
        min: 0.7,
        max: 0.8,
        color: '#e6f200',
      },
      {
        min: 0.6,
        max: 0.7,
        color: '#cde800',
      },
      {
        min: 0.5,
        max: 0.6,
        color: '#bcfe58',
      },
      {
        min: 0.4,
        max: 0.5,
        color: '#52feb7',
      },
      {
        min: 0.3,
        max: 0.4,
        color: '#47cffd',
      },
      {
        min: 0.2,
        max: 0.3,
        color: '#3ba5ff',
      },
      {
        min: 0.1,
        max: 0.2,
        color: '#2b64ff',
      },
      {
        max: 0.1,
        color: '#2002ff',
      },
    ],
  },
  fnr: {
    unit: '',
    formula: '',
    cn: '臭氧前体指示值',
    equalMin: true,
    values: [
      {
        min: 6,
        color: 'rgb(212, 57 ,48)',
      },
      {
        min: 4,
        max: 6,
        color: 'rgb(255, 210, 112)',
      },
      {
        min: 2,
        max: 3,
        color: 'rgb(254, 254, 200)',
      },
      {
        min: 1,
        max: 2,
        color: 'rgb(15,106,189)',
      },
      {
        min: 0,
        max: 1,
        color: 'rgb(11,56, 164)',
      },
    ],
  },
  hid: {
    unit: '',
    formula: '城市热岛',
    cn: '城市热岛',
    equalMin: true,
    values: [
      {
        min: 3,
        max: 4,
        color: 'rgb(255,20,2)',
      },
      {
        min: 2,
        max: 3,
        color: 'rgb(255,120,5)',
      },
      {
        min: 1,
        max: 2,
        color: 'rgb(252,206,88)',
      },
      {
        min: 0,
        max: 1,
        color: 'rgb(250,222,134)',
      },
    ],
  },
  straw: {
    unit: '个',
    formula: 'STRAW',
    cn: '秸秆火分布',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(2);
    },
    equalMin: true,
    values: [
      { min: 6, max: 7, color: 'rgb(126, 0, 35)' },
      { min: 5, max: 6, color: 'rgb(153, 0, 35)' },
      { min: 4, max: 5, color: 'rgb(255, 0, 0)' },
      { min: 3, max: 4, color: 'rgb(255, 126, 0)' },
      { min: 2, max: 3, color: 'rgb(255, 255, 0)' },
      { min: 1, max: 2, color: 'rgb(0, 228 ,0)' },
    ],
  },
  forest: {
    unit: '个',
    formula: 'STRAW',
    cn: '林火分布',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(2);
    },
    equalMin: true,
    values: [
      { min: 6, max: 7, color: 'rgb(126, 0, 35)' },
      { min: 5, max: 6, color: 'rgb(153, 0, 35)' },
      { min: 4, max: 5, color: 'rgb(255, 0, 0)' },
      { min: 3, max: 4, color: 'rgb(255, 126, 0)' },
      { min: 2, max: 3, color: 'rgb(255, 255, 0)' },
      { min: 1, max: 2, color: 'rgb(0, 228 ,0)' },
    ],
  },
  hot_city: {
    unit: '',
    formula: '城市热岛',
    cn: '城市热岛',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(2);
    },
    equalMin: true,
    values: [
      {
        min: 3,
        max: 4,
        color: 'rgb(255,20,2)',
      },
      {
        min: 2,
        max: 3,
        color: 'rgb(255,120,5)',
      },
      {
        min: 1,
        max: 2,
        color: 'rgb(252,206,88)',
      },
      {
        min: 0,
        max: 1,
        color: 'rgb(250,222,134)',
      },
    ],
  },
  aqi: {
    unit: '',
    formula: 'AQI',
    equalMin: true,
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(1);
    },
    values: [
      {
        min: 0,
        max: 50,
        color: 'rgb(0, 228 ,0)',
      },
      {
        min: 50,
        max: 100,
        color: 'rgb(255, 255, 0)',
      },
      {
        min: 100,
        max: 150,
        color: 'rgb(255, 126, 0)',
      },
      {
        min: 150,
        max: 200,
        color: 'rgb(255, 0, 0)',
      },
      {
        min: 200,
        max: 300,
        color: 'rgb(153, 0, 35)',
      },
      {
        min: 300,
        color: 'rgb(126, 0, 35)',
      },
    ].reverse(),
  },
  uva: {
    unit: 'W/m²',
    formula: 'UVA',
    equalMin: true,
    cn: '长波紫外线',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(2);
    },
    values: [
      {
        max: 10,
        color: 'rgb(33,139, 168)',
      },
      {
        min: 10,
        max: 20,
        color: 'rgb(25,158,191)',
      },
      {
        min: 20,
        max: 30,
        color: 'rgb(90, 220, 204)',
      },
      {
        min: 40,
        max: 50,
        color: 'rgb(249, 252, 180)',
      },
      {
        min: 50,
        max: 60,
        color: 'rgb(253, 240, 163)',
      },
      {
        min: 60,
        max: 70,
        color: 'rgb(253, 182 ,90)',
      },
      {
        min: 70,
        max: 80,
        color: 'rgb(213, 11, 85)',
      },
      {
        min: 80,
        color: 'rgb(127, 33, 63)',
      },
    ].reverse(),
  },
  uvb: {
    unit: 'W/m²',
    formula: 'UVB',
    equalMin: true,
    cn: '中波紫外线',
    roundFunc: (v: any) => {
      const val = Number(v);
      return isNaN(val) ? 'N/A' : val.toFixed(2);
    },
    values: [
      {
        max: 0.25,
        color: 'rgb(33,139, 168)',
      },
      {
        min: 0.25,
        max: 0.5,
        color: 'rgb(25,158,191)',
      },
      {
        min: 0.5,
        max: 0.75,
        color: 'rgb(90, 220, 204)',
      },
      {
        min: 0.75,
        max: 1,
        color: 'rgb(249, 252, 180)',
      },
      {
        min: 1,
        max: 1.25,
        color: 'rgb(255, 204 ,79)',
      },
      {
        min: 1.25,
        max: 1.5,
        color: 'rgb(245, 130, 35)',
      },
      {
        min: 1.5,
        max: 2,
        color: 'rgb(252, 71, 1)',
      },
      {
        min: 2,
        max: 2.5,
        color: 'rgb(226, 5, 5)',
      },
      {
        min: 2.5,
        max: 3,
        color: 'rgb(184, 3, 3)',
      },
      {
        min: 3,
        max: 4,
        color: 'rgb(141, 4, 4)',
      },
      {
        min: 4,
        color: 'rgb(99, 2, 2)',
      },
    ].reverse(),
  },
  co2: {
    unit: 'PPM',
    formula: 'CO₂',
    equalMin: true,
    roundFunc: (v: any) => {
      const number = Number(v);
      return number ? number.toFixed(1) : 0;
    },
    values: [
      { min: 420, color: 'rgb(239,0,130)' },
      { min: 418, max: 420, color: 'rgb(232,68,68)' },
      { min: 414, max: 418, color: 'rgb(240,130,39)' },
      { min: 410, max: 414, color: 'rgb(230,175,45)' },
      { min: 406, max: 410, color: 'rgb(230,220,51)' },
      { min: 404, max: 406, color: 'rgb(161,230 ,50)' },
      { min: 402, max: 404, color: 'rgb(0,220,0)' },
      { min: 400, max: 402, color: 'rgb(0,200,199)' },
      { min: 390, max: 400, color: 'rgb(1,160,255)' },
      { max: 390, color: 'rgb(40,140,249) ' },
    ],
  },
};

// 气象
export const weatherValuesAndColors: Record<string, any> = {
  tmp: {
    unit: '℃',
    cn: '温度',
    equalMax: true,
    values: [
      {
        min: 40,
        color: '#DF040D',
      },
      {
        min: 37,
        max: 40,
        color: '#FB5401',
      },
      {
        min: 35,
        max: 37,
        color: '#F79780',
      },
      {
        min: 32,
        max: 35,
        color: '#FDCCA4',
      },
      {
        min: 28,
        max: 32,
        color: '#FEF3C4',
      },
      {
        min: 24,
        max: 28,
        color: '#FEFE97',
      },
      {
        min: 20,
        max: 24,
        color: '#BFFD94',
      },
      {
        min: 16,
        max: 20,
        color: '#D3FCCF',
      },
      {
        min: 12,
        max: 16,
        color: '#F2FEEE',
      },
      {
        min: 8,
        max: 12,
        color: '#D1FAFC',
      },
      {
        min: 4,
        max: 8,
        color: '#A9E8F6',
      },
      {
        min: 0,
        max: 4,
        color: '#82D1FF',
      },
      {
        min: -4,
        max: 0,
        color: '#3C9FEE',
      },
      {
        min: -8,
        max: -4,
        color: '#2573C8',
      },
      {
        min: -12,
        max: -8,
        color: '#185DA5',
      },
      {
        max: -12,
        color: '#032F81',
      },
    ],
  },
  tmp_max: {
    unit: '℃',
    cn: '高温',
    equalMax: true,
    values: [
      {
        min: 40,
        color: '#DF040D',
      },
      {
        min: 37,
        max: 40,
        color: '#FB5401',
      },
      {
        min: 35,
        max: 37,
        color: '#F79780',
      },
      {
        min: 32,
        max: 35,
        color: '#FDCCA4',
      },
      {
        min: 28,
        max: 32,
        color: '#FEF3C4',
      },
      {
        min: 24,
        max: 28,
        color: '#FEFE97',
      },
      {
        min: 20,
        max: 24,
        color: '#BFFD94',
      },
      {
        min: 16,
        max: 20,
        color: '#D3FCCF',
      },
      {
        min: 12,
        max: 16,
        color: '#F2FEEE',
      },
      {
        min: 8,
        max: 12,
        color: '#D1FAFC',
      },
      {
        min: 4,
        max: 8,
        color: '#A9E8F6',
      },
      {
        min: 0,
        max: 4,
        color: '#82D1FF',
      },
      {
        min: -4,
        max: 0,
        color: '#3C9FEE',
      },
      {
        min: -8,
        max: -4,
        color: '#2573C8',
      },
      {
        min: -12,
        max: -8,
        color: '#185DA5',
      },
      {
        max: -12,
        color: '#032F81',
      },
    ],
  },
  vis: {
    unit: 'Km',
    cn: '能见度',
    equalMax: true,
    values: [
      {
        min: 30,
        color: '#FAFCFC',
      },
      {
        min: 20,
        max: 30,
        color: '#C6EAFF',
      },
      {
        min: 10,
        max: 20,
        color: '#95DFFB',
      },
      {
        min: 5,
        max: 10,
        color: '#80F040',
      },
      {
        min: 3,
        max: 5,
        color: '#FBFC10',
      },
      {
        min: 2,
        max: 3,
        color: '#FFB046',
      },
      {
        min: 1,
        max: 2,
        color: '#F75A03',
      },
      {
        min: 0.5,
        max: 1,
        color: '#F2010D',
      },
      {
        min: 0.2,
        max: 0.5,
        color: '#9603F6',
      },
      {
        max: 0.2,
        color: '#742008',
      },
    ],
  },
  rh: {
    unit: '%',
    cn: '湿度',
    equalMax: true,
    values: [
      { min: 100, color: 'rgb(239,0,130)' },
      { min: 95, max: 100, color: 'rgb(232, 68, 68)' },
      { min: 90, max: 95, color: 'rgb(240, 130, 39)' },
      { min: 85, max: 90, color: 'rgb(230, 175, 45)' },
      { min: 75, max: 85, color: 'rgb(230, 220, 51)' },
      { min: 65, max: 75, color: 'rgb(161, 230 ,50)' },
      { min: 55, max: 65, color: 'rgb(0, 220, 0)' },
      { min: 45, max: 55, color: 'rgb(0, 200, 199)' },
      { min: 0, max: 45, color: 'rgb(1,160,255)' },
      { max: 0, color: 'rgb(40,140, 249) ' },
    ],
  },
  pre: {
    unit: 'mm',
    cn: '降水量',
    equalMax: true,
    values: [
      {
        min: 100,
        color: '#F900FE',
      },
      {
        min: 50,
        max: 100,
        color: '#1100F9',
      },
      {
        min: 25,
        max: 50,
        color: '#61B7FB',
      },
      {
        min: 10,
        max: 25,
        color: '#3FB934',
      },
      {
        min: 5,
        max: 10,
        color: '#6BD96B',
      },
      {
        min: 2.5,
        max: 5,
        color: '#B6F4A6',
      },
      {
        min: 0,
        max: 2.5,
        color: '#DCF7D1',
      },
      {
        max: 0,
        color: '#FFFFFF',
      },
    ],
  },
  prs: {
    unit: 'hPa',
    cn: '压强',
    equalMax: true,
    values: [
      { min: 900, color: 'rgb(239,0,130)' },
      { min: 850, max: 900, color: 'rgb(232,68,68)' },
      { min: 800, max: 850, color: 'rgb(240,130,39)' },
      { min: 750, max: 800, color: 'rgb(230,175,45)' },
      { min: 700, max: 750, color: 'rgb(230,220,51)' },
      { min: 650, max: 700, color: 'rgb(161,230 ,50)' },
      { min: 600, max: 650, color: 'rgb(0,220,0)' },
      { min: 550, max: 600, color: 'rgb(0,200,199)' },
      { min: 500, max: 550, color: 'rgb(1,160,255)' },
      { max: 500, color: 'rgb(40,140,249)' },
    ],
  },
  'wiu-wiv': {
    unit: 'm/s',
    cn: '风速',
    equalMax: true,
    values: [
      { min: 13.8, max: 15, color: '#5B88A1' },
      { min: 10.7, max: 13.8, color: '#5F64A0' },
      { min: 7.9, max: 10.7, color: '#974B91' },
      { min: 5.5, max: 7.9, color: '#8D3F5C' },
      { min: 4.2, max: 5.5, color: '#A26D5C' },
      { min: 3.4, max: 4.2, color: '#A28740' },
      { min: 2.6, max: 3.4, color: '#67A436' },
      { min: 1.8, max: 2.6, color: '#4CA44C' },
      { min: 1.0, max: 1.8, color: '#4D9294' },
      { min: 0.2, max: 1.0, color: '#3D6EA3' },
      { min: 0, max: 0.2, color: '#6271B8' },
    ],
  },
};

const weatherDecoder = Object.entries(weatherValuesAndColors).reduce((d, [key, val]) => {
  const { values } = val;
  return { ...d, [key]: getDecoder(values) };
}, {} as Record<string, Record<string, string>>);

const remoteSensingDecoder = Object.entries(remoteSensingValuesAndColors).reduce((d, [key, val]) => {
  const { values } = val;
  return { ...d, [key]: getDecoder(values) };
}, {} as Record<string, Record<string, string>>);

export const colorRamps: Record<string, Record<string, string>> = {
  ...remoteSensingDecoder,
  ...weatherDecoder,
  fnr: {
    '1.00': 'rgb(212, 57 ,48)',
    0.66: 'rgb(251, 210, 112)',
    0.5: 'rgb(254, 254, 200)',
    0.33: 'rgb(15,106,189)',
    0.0: 'rgb(11,56, 164)',
  },
  hid: {
    '1.00': '#ff1402',
    0.667: '#ff7805',
    0.333: '#ffd270',
    0.0: '#fade86',
  },
  o3mca: {
    '0.800': '#d43931',
    '0.667': '#ffd270',
    '0.333': '#2662d6',
    '0.000': '#2662d6',
  },
  dmask: {
    '1.0': '#faba01',
    '0.0': '#5459AB',
  },
};

const replaceDecoder = (type: string) => {
  const decoder = decoderFactory(type);
  const values = (remoteSensingValuesAndColors[type] || weatherValuesAndColors[type]).values as { min?: number; max?: number }[];
  const info = array_(values);
  const top = first(info);
  const end = last(info);
  const { min: max } = top as { min: number };
  // const { max: min } = end as { max: number };
  // 最小的色带有min和max，最大的色带没有min只有max
  // console.log(end?.min);
  const min = isNaN(end?.min) ? end?.max : end?.min;
  let decoderResult = decoder.replace(/\s/g, '');
  if (number_(min) < 0) {
    decoderResult = decoder.replaceAll('{min}', `(0.00 ${number_(min).toFixed(2)})`);
  } else {
    decoderResult = decoder.replaceAll('{min}', `${number_(min).toFixed(2)}`);
  }
  if (number_(max) < 0) {
    decoderResult = decoderResult.replaceAll('{max}', `(0.00 ${number_(max).toFixed(2)})`);
  } else {
    decoderResult = decoderResult.replaceAll('{max}', `${number_(max).toFixed(2)}`);
  }
  return decoderResult;
};

export const decoder: Record<string, string> = {
  // pm25: '(data.r * 255.0 + data.g) * 255.0 / 100.0',
  pm25: replaceDecoder('pm25'),
  scpm25: replaceDecoder('scpm25'),
  // lst: '(data.r * 255.0 + data.g) * 255.0 * 0.02 / 350.0',
  lst: replaceDecoder('lst'),
  hid: replaceDecoder('hid'),
  // pm10: '(data.r * 255.0 + data.g) * 255.0 / 200.0',
  pm10: replaceDecoder('pm10'),
  // aod: '(data.r * 255.0 + data.g) * 255.0 / 1000.0 / 1.5',
  aod: replaceDecoder('aod'),
  // no2: '(data.r * 255.0 + data.g) * 255.0 / 250.0',
  no2: replaceDecoder('no2'),
  gk2b_no2: replaceDecoder('no2'),
  // o3: '((data.r * 255.0 + data.g) * 255.0 - 0.0) / (250.0 - 0.0)',
  o3: replaceDecoder('o3'),
  gk2b_o3: replaceDecoder('o3'),
  // fnr: `if ( data.r * 255.0 / 10.0 <= 1.0 ) {
  //   return 0.0;
  // } else if( data.r * 255.0 / 10.0 > 1.0 && data.r * 255.0 / 10.0 < 6.0 ) {
  //   return 0.167;
  // } else {
  //   return 1.0;
  // }`,
  fnr: replaceDecoder('fnr'),
  // co: '(data.r * 255.0 + data.g) * 255.0 / 10000.0 * 6.022141 / 0.30',
  co: replaceDecoder('co'),
  // so2: '(data.r * 255.0 + data.g) * 255.0 / 1000.0 / 1.6',
  so2: replaceDecoder('so2'),
  // eslint-disable-next-line max-len
  // no2tcd: `(data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` + ` + data.a * 255.0 + data.r) * 255.0 ` + `/ 100000000.0 / 1.60`,
  no2tcd: replaceDecoder('no2tcd'),
  o3tcd: replaceDecoder('o3tcd'),
  // hcho: `(data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` + ` + data.a * 255.0 + data.r) * 255.0 ` + `/ 100000000.0 / 1.60`,
  hcho: replaceDecoder('hcho'),
  gk2b_hcho: replaceDecoder('hcho'),
  // tmp: '(data.r * 255.0 - 28.0) / 200.0',
  tmp: replaceDecoder('tmp'),
  // pre: `
  //   float pre = data.r * 255.0;
  //   if(pre <= 0.00000001) {
  //     return  0.00000001;
  //   }
  //   return pre / 50.0;
  // `,
  pre: replaceDecoder('pre'),
  // vis: 'data.r * 0.2 * 255.0 / 30.0',
  vis: replaceDecoder('vis'),
  // rh: 'data.r * 255.0 / 100.0',
  rh: replaceDecoder('rh'),
  // prs: '((data.r * 255.0 + data.g) * 255.0 - 500.0) / 400.0',
  prs: replaceDecoder('prs'),
  // 'wiu-wiv': `
  //   float u = data.r*255.0 + data.b*255.0/100.0-100.0;
  //   float v = data.g*255.0 + mod(data.b*255.0, 10.0)/10.0-100.0;
  //   float speed = sqrt(u * u + v * v);
  //   if(speed <= 0.00000001) {
  //     return 0.00000001;
  //   }
  //   return speed / 28.4;
  // `,
  'wiu-wiv': replaceDecoder('wiu-wiv'),
  // uva: 'data.r * 255.0 / 80.0',
  uva: replaceDecoder('uva'),
  // uvb: '(data.r / 10.0) * 255.0 / 6.0',
  uvb: replaceDecoder('uvb'),
  // ch4: `((data.r * 255.0 + data.g) * 255.0 - 1750.0) / (2400.0 - 1750.0)`,
  ch4: replaceDecoder('ch4'),
  // co2: '((data.r * 255.0 + data.g) * 255.0/ 10.0 - 380.0) / (420.0 - 380.0)',
  co2: replaceDecoder('co2'),
  dmask: `float u =  data.r * 255.0;
          if(u > 0.0) {
            return 1.0;
          }else {
            return 0.0;
          }
      `,
};