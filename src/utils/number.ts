/**
 * 有损科学计数法
 * @param n 待转的大数
 * @param integer 整数位数
 * @param fraction 小数位数
 * @returns 
 */
export function toScientificNotation(n: number, integer = 1, fraction = 2) {
   const { length } = `${n}`;
   if (length <= integer) return String(n);
   const power = length - integer;
   const scale = 10 ** fraction;
   const r = Math.round(scale * n / (10 ** power)) / scale;
   return `${r}E${power}`;
}

export const formatNumber = (num: number) => {
   if (num.toString().includes('e')) {
      return Number(num.toString().split('e')[0])
   } else {
      return num;
   }
};