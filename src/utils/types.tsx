export interface QueryListResult<Data> {
  page: number;
  total: number;
  list: Data[];
}

/**
 * 服务器接口数据返回
 */
export interface ApiResponse<Data> {
  code: number;
  data: Data | null;
  message: string;
}
export interface ApiListData<Data> {
  content: Data[];
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  pageable: {
    pageNumber: number;
    pageSize: number;
  };
  size: number;
  totalElements: number;
  totalPages: number;
}
