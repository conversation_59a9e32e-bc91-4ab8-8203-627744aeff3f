import type { Rule } from 'antd/lib/form';

export const mobileRegex = /^1[3456789]\d{9}$/;
// 污染物数值验证正则
export const poValReg = /^\d+(\.\d{1,6})?$/;
export const secondLevelRegex = /^\d{3}[^0]$/;

const formRules: Record<string, (...args: any) => Rule[]> = {
  required(message: string) {
    return [
      {
        required: true,
        message,
      },
    ];
  },
  maxLens(lens: number) {
    return [
      {
        max: lens,
        type: 'string',
      },
    ];
  },
  name(message = '用户名仅能设置2-16个字符') {
    return [
      ...this.required('请输入用户名'),
      {
        pattern: /^([a-zA-Z0-9_\u4e00-\u9fa5]{2,16})$/,
        message,
      },
    ];
  },
  mobile(message = '手机号码格式错误') {
    return [
      ...this.required('请输入手机号码'),
      {
        pattern: mobileRegex,
        message,
      },
    ];
  },
  numberWithDecimal(message = '') {
    return [
      {
        pattern: /^(0|[1-9]\d*\.?\d*)$/,
        message,
      },
    ];
  },
};

export default formRules;
