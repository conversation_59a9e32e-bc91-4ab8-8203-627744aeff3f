import CryptoJS from 'crypto-js';
import JSEncrypt from 'jsencrypt';

const AESSECRET = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTwrChUMH6BbGAr
4cKlxE4TNaRpALzIM2c2+7OXZ08pyM10d96UsKS4lANN3ZA5VGlemil9S
UpQ6O23Pkf5ImQ89kIWbGp6YCIz0POPpl50O8jAT02CpoUCtgpNkf
CMdCBGHcDscJD0tbg2E/x8XcGLnhdcHWKLFeuiLZjEbdJUwIDAQAB`;

const RSADecrypt = new JSEncrypt(); // RAS库
RSADecrypt.setPublicKey(AESSECRET); // 设置公钥（前端配置）

// 随机生成AESKey
function getKeyAES() {
  const key = [];
  for (let i = 0; i < 22; i++) {
    const num = Math.floor(Math.random() * 26);
    const chartStr = String.fromCharCode(97 + num);
    key.push(chartStr.toUpperCase());
  }
  const result = key.join('');
  return result + '==';
}

// 数据加密
export function encrypt(data: Record<string, unknown>) {
  const AESKey = getKeyAES(); // 获取随机AESKey
  const encrypted = RSADecrypt.encrypt(AESKey); // 用 RSA 加密 AESKey[通过后端给的公钥]

  // 将想传给后端的json数据转成字符串
  let dataStr = '';
  try {
    dataStr = JSON.stringify(data);
  } catch (e) {
    console.log('加密失败', e);
  }
  // 如果AESKey能够加密成功
  if (encrypted) {
    // 通过 ASEKey 给 json数据 加密
    const requestData = CryptoJS.AES.encrypt(dataStr, CryptoJS.enc.Base64.parse(AESKey), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
      formatter: CryptoJS.enc.Utf8,
    });
    // 最终将 加密后的AESKey[RSA状态下的--encrypted]以及 AESKey加密后的 json数据 作为post的body内容传给后端
    return {
      encrypted,
      requestData: CryptoJS.enc.Base64.stringify(requestData.ciphertext),
    };
  } else {
    return {
      encrypted: '',
      requestData: '',
    };
  }
}
