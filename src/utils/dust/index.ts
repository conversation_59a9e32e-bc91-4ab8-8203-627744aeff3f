import { first, last } from "lodash";
import { ConfigItem } from "./stationLegendConfig";
import { remoteSensingValuesAndColors as remoteSensingValuesAndColorsDataDown } from '@/utils/dataDown';
export function ensureArray<T>(data: T[] | undefined): T[] {
  return data instanceof Array ? data : ([] as T[]);
}
export function ensureNumber(num: unknown) {
  const num_ = Number(num);
  return num_ || 0;
}

/**
 * 格式化数字
 * @param num
 * @param decimal
 * @returns number
 */
export const roundNumber = (num: number, decimal: number = 0) => {
  return Math.round(num * Math.pow(10, decimal)) / Math.pow(10, decimal);
};

export const randomNumber = (min: number, max: number) => {
  return Math.random() * (max - min) + min;
};

export function ensureObject<T>(data: T | undefined): T {
  return data instanceof Object ? data : ({} as T);
}

export const dataConfig: Record<DUST.DataTypes, ConfigItem> = {
  PM25: {
    label: 'PM₂.₅浓度',
    iconParkLabel: 'pm25',
    value: 'PM25',
    chemicalFormula: 'PM₂.₅',
    unit: 'μg/m³',
    group: 'PM',
    groupName: '颗粒物',
    roundFn: roundNumber,
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber(data.r * 255.0 + data.g);
    },
    //注释的是沙尘迁移过来的 与全国大气有出入
    // values: [
    //   { min: 200, color: 'rgb(131, 0, 36)' },
    //   { min: 150, max: 200, color: 'rgb(157, 0, 86)' },
    //   { min: 125, max: 150, color: 'rgb(198, 0, 0)' },
    //   { min: 115, max: 125, color: 'rgb(245, 0, 0)' },
    //   { min: 110, max: 115, color: 'rgb(246, 100, 0)' },
    //   { min: 100, max: 110, color: 'rgb(247, 137, 0)' },
    //   { min: 90, max: 100, color: 'rgb(249, 189, 0)' },
    //   { min: 80, max: 90, color: 'rgb(250, 210, 0)' },
    //   { min: 70, max: 80, color: 'rgb(252, 234, 0)' },
    //   { min: 60, max: 70, color: 'rgb(253, 255, 0)' },
    //   { min: 50, max: 60, color: 'rgb(218,255, 0)' },
    //   { min: 40, max: 50, color: 'rgb(169, 255, 0)' },
    //   { min: 30, max: 40, color: 'rgb(153,253,11)' },
    //   { min: 20, max: 30, color: 'rgb(70,232,0)' },
    //   { min: 10, max: 20, color: 'rgb(62,207,0)' },
    //   { min: 0, max: 10, color: 'rgb(52,179, 0) ' },
    // ],
    // 新的是全国大气迁移过来的 目前内蒙古用的是这个
    values: remoteSensingValuesAndColorsDataDown['pm25']?.values
  },
  PM10: {
    label: 'PM₁₀浓度',
    iconParkLabel: 'pm10',
    value: 'PM10',
    chemicalFormula: 'PM₁₀',
    unit: 'μg/m³',
    group: 'PM',
    groupName: '颗粒物',
    roundFn: roundNumber,
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber(data.r * 255.0 + data.g);
    },
    //注释的是沙尘迁移过来的  与全国大气有出入
    // values: [
    //   {
    //     min: 420,
    //     color: 'rgb(131, 0, 36)',
    //   },
    //   {
    //     min: 350,
    //     max: 420,
    //     color: 'rgb(157, 0, 86)',
    //   },
    //   {
    //     min: 250,
    //     max: 350,
    //     color: 'rgb(198, 0, 0)',
    //   },
    //   {
    //     min: 200,
    //     max: 250,
    //     color: 'rgb(245, 0, 0)',
    //   },
    //   {
    //     min: 150,
    //     max: 200,
    //     color: 'rgb(246, 100, 0)',
    //   },
    //   {
    //     min: 135,
    //     max: 150,
    //     color: 'rgb(247, 137, 0)',
    //   },
    //   {
    //     min: 120,
    //     max: 135,
    //     color: 'rgb(249, 189, 0)',
    //   },
    //   {
    //     min: 105,
    //     max: 120,
    //     color: 'rgb(250, 210, 0)',
    //   },
    //   {
    //     min: 90,
    //     max: 105,
    //     color: 'rgb(252, 234, 0)',
    //   },
    //   {
    //     min: 75,
    //     max: 90,
    //     color: 'rgb(253, 255 ,0)',
    //   },
    //   {
    //     min: 60,
    //     max: 75,
    //     color: 'rgb(218,255, 0)',
    //   },
    //   {
    //     min: 45,
    //     max: 60,
    //     color: 'rgb(169, 255, 0)',
    //   },
    //   {
    //     min: 30,
    //     max: 45,
    //     color: 'rgb(153,253,11)',
    //   },
    //   {
    //     min: 15,
    //     max: 30,
    //     color: 'rgb(70,232,0)',
    //   },
    //   {
    //     min: 10,
    //     max: 15,
    //     color: 'rgb(62,207,0)',
    //   },
    //   {
    //     min: 0,
    //     max: 10,
    //     color: 'rgb(52,179, 0) ',
    //   },
    // ],
    // 新的是全国大气迁移过来的 目前内蒙古用的是这个
    values: remoteSensingValuesAndColorsDataDown['pm10']?.values
  },
  AOD: {
    label: 'AOD',
    iconParkLabel: 'AOD',
    value: 'AOD',
    chemicalFormula: 'AOD',
    unit: '无量纲',
    group: 'PM',
    groupName: '颗粒物',
    roundFn: (val: number) => roundNumber(val, 3),
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder:
      '(((data.r * 255.0 + data.g) * 255.0 / 1000.0) - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber((data.r * 255 + data.g) / 1000.0, 3);
    },
    // values: [
    //   { min: 1.4, max: 1.5, color: 'rgb(255,0,0)' },
    //   { min: 1.3, max: 1.4, color: 'rgb(237,103,3)' },
    //   { min: 1.2, max: 1.3, color: 'rgb(253,147,2)' },
    //   { min: 1.1, max: 1.2, color: 'rgb(253,203,2)' },
    //   { min: 1.0, max: 1.1, color: 'rgb(253,227,2)' },
    //   { min: 0.9, max: 1.0, color: 'rgb(255,255,0)' },
    //   { min: 0.8, max: 0.9, color: 'rgb(220,251,4)' },
    //   { min: 0.7, max: 0.8, color: 'rgb(186,239,1)' },
    //   { min: 0.6, max: 0.7, color: 'rgb(128,255,0)' },
    //   { min: 0.5, max: 0.6, color: 'rgb(128,255,128)' },
    //   { min: 0.4, max: 0.5, color: 'rgb(135,255,255)' },
    //   { min: 0.3, max: 0.4, color: 'rgb(95,205,255)' },
    //   { min: 0.2, max: 0.3, color: 'rgb(55,165,255)' },
    //   { min: 0.1, max: 0.2, color: 'rgb(15,100,255)' },
    //   { min: 0, max: 0.1, color: 'rgb(40,0,255) ' },
    // ],
    // 新的是全国大气迁移过来的 目前内蒙古用的是这个
    values: remoteSensingValuesAndColorsDataDown['aod']?.values
  },
  // 沙尘详情里好像没有用到别的污染物 如果有新增需要注意是否有出入
  O3: {
    label: 'O₃',
    iconParkLabel: 'O3',
    value: 'O3',
    chemicalFormula: 'O₃',
    unit: 'μg/m³',
    group: 'GAS',
    groupName: '气态污染物',
    roundFn: roundNumber,
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber(data.r * 255 + data.g);
    },
    values: [
      {
        min: 350,
        color: 'rgb(157, 0, 86)',
      },
      {
        min: 250,
        max: 350,
        color: 'rgb(198, 0, 0)',
      },
      {
        min: 160,
        max: 250,
        color: 'rgb(245, 0, 0)',
      },
      {
        min: 150,
        max: 160,
        color: 'rgb(246, 100 ,0)',
      },
      {
        min: 140,
        max: 150,
        color: 'rgb(247, 137, 0)',
      },
      {
        min: 130,
        max: 140,
        color: 'rgb(249, 189, 0)',
      },
      {
        min: 120,
        max: 130,
        color: 'rgb(250, 210, 0)',
      },
      {
        min: 100,
        max: 120,
        color: 'rgb(252, 234, 0)',
      },
      {
        min: 80,
        max: 100,
        color: 'rgb(253, 255 ,0)',
      },
      {
        min: 60,
        max: 80,
        color: 'rgb(169, 255, 0)',
      },
      {
        min: 40,
        max: 60,
        color: 'rgb(79, 255, 0)',
      },
      {
        min: 20,
        max: 40,
        color: 'rgb(70,232,0)',
      },
      {
        max: 20,
        color: 'rgb(52,179, 0)',
      },
    ],
  },
  O3TCD: {
    label: 'O₃柱浓度',
    iconParkLabel: 'O3',
    value: 'O3TCD',
    chemicalFormula: 'O₃',
    unit: 'DU',
    group: 'GAS',
    groupName: '气态污染物',
    roundFn: roundNumber,
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder: '((data.r * 255.0  + data.g) * 255.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber(data.r * 255 + data.g);
    },
    values: [
      {
        min: 515,
        color: '#c20000',
      },
      {
        min: 490,
        max: 515,
        color: '#ff3700',
      },
      {
        min: 465,
        max: 490,
        color: '#ff7300',
      },
      {
        min: 440,
        max: 465,
        color: '#ff8c00',
      },
      {
        min: 415,
        max: 440,
        color: '#ffbf00',
      },
      {
        min: 390,
        max: 415,
        color: '#ffdd00',
      },
      {
        min: 365,
        max: 390,
        color: '#fff700',
      },
      {
        min: 340,
        max: 365,
        color: '#bcfe58',
      },
      {
        min: 315,
        max: 340,
        color: '#52feb7',
      },
      {
        min: 290,
        max: 315,
        color: '#3ba5ff',
      },
      {
        max: 290,
        color: '#2022ff',
      },
    ],
  },
  HCHO: {
    label: 'HCHO柱浓度',
    iconParkLabel: 'HCHO',
    value: 'HCHO',
    chemicalFormula: 'HCHO',
    unit: '1e16molec./cm²',
    group: 'GAS',
    groupName: '气态污染物',
    roundFn: (val: number) => {
      return roundNumber(val, 1);
    },
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder:
      '((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0' +
      ' + data.a * 255.0 + data.r) * 255.0 ' +
      '/ 100000000.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber(
        ((data.g * 255 * 255 * 255 +
          data.b * 255 * 255 +
          data.a * 255 +
          data.r) *
          Math.pow(10, 8)) /
        Math.pow(10, 16),
        2,
      );
    },
    values: [
      {
        min: 4.0,
        color: 'rgb(131, 0, 36)',
      },
      {
        min: 3.6,
        max: 4.0,
        color: 'rgb(157, 0, 86)',
      },
      {
        min: 3.2,
        max: 3.6,
        color: 'rgb(178, 0, 50)',
      },
      {
        min: 3.0,
        max: 3.2,
        color: 'rgb(198, 0, 0)',
      },
      {
        min: 2.75,
        max: 3.0,
        color: 'rgb(245, 0, 0)',
      },
      {
        min: 2.5,
        max: 2.75,
        color: 'rgb(246, 100, 0)',
      },
      {
        min: 2.25,
        max: 2.5,
        color: 'rgb(247, 137, 0)',
      },
      {
        min: 2.0,
        max: 2.25,
        color: 'rgb(249, 189, 0)',
      },
      {
        min: 1.75,
        max: 2.0,
        color: 'rgb(250, 210, 0)',
      },
      {
        min: 1.5,
        max: 1.75,
        color: 'rgb(252, 234, 0)',
      },
      {
        min: 1.25,
        max: 1.5,
        color: 'rgb(253, 255, 0)',
      },
      {
        min: 1.0,
        max: 1.25,
        color: 'rgb(218,255, 0)',
      },
      {
        min: 0.75,
        max: 1.0,
        color: 'rgb(169, 255, 0)',
      },
      {
        min: 0.5,
        max: 0.75,
        color: 'rgb(153,253,11)',
      },
      {
        min: 0.25,
        max: 0.5,
        color: 'rgb(70,232,0)',
      },
      {
        min: 0.15,
        max: 0.25,
        color: 'rgb(62,207,0)',
      },
      {
        min: 0,
        max: 0.15,
        color: 'rgb(52,179, 0) ',
      },
    ],
  },
  SO2: {
    label: 'SO₂柱浓度',
    iconParkLabel: 'SO2',
    value: 'SO2',
    chemicalFormula: 'SO₂',
    unit: 'du',
    group: 'GAS',
    groupName: '气态污染物',
    roundFn: (val: number) => {
      return roundNumber(val, 1);
    },
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder:
      '(((data.r * 255.0 + data.g) * 255.0 / 1000.0) - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber((data.r * 255 + data.g) / 1000, 2);
    },
    values: [
      {
        min: 8.5,
        color: 'rgb(131, 0, 36)',
      },
      {
        min: 7.5,
        max: 8.5,
        color: 'rgb(157, 0, 86)',
      },
      {
        min: 6.5,
        max: 7.5,
        color: 'rgb(198, 0, 0)',
      },
      {
        min: 5.5,
        max: 6.5,
        color: 'rgb(245, 0, 0)',
      },
      {
        min: 4.5,
        max: 5.5,
        color: 'rgb(246, 100, 0)',
      },
      {
        min: 3.5,
        max: 4.5,
        color: 'rgb(247, 137, 0)',
      },
      {
        min: 2.5,
        max: 3.5,
        color: 'rgb(249, 189, 0)',
      },
      {
        min: 2.0,
        max: 2.5,
        color: 'rgb(250, 210, 0)',
      },
      {
        min: 1.8,
        max: 2.0,
        color: 'rgb(252, 234, 0)',
      },
      {
        min: 1.6,
        max: 1.8,
        color: 'rgb(253, 255 ,0)',
      },
      {
        min: 1.4,
        max: 1.6,
        color: 'rgb(218,255, 0)',
      },
      {
        min: 1.2,
        max: 1.4,
        color: '#bcfe58',
      },
      {
        min: 1.0,
        max: 1.2,
        color: '#52feb7',
      },
      {
        min: 0.8,
        max: 1.0,
        color: '#47cffd',
      },
      {
        min: 0.4,
        max: 0.8,
        color: '#3ba5ff',
      },
      {
        min: 0.2,
        max: 0.4,
        color: '#2b64ff',
      },
      {
        min: 0,
        max: 0.2,
        color: '#2002ff',
      },
    ],
  },
  NO2TCD: {
    label: 'NO₂柱浓度',
    iconParkLabel: 'NO2',
    value: 'NO2TCD',
    chemicalFormula: 'NO₂',
    unit: '1e16molec./c㎡',
    group: 'GAS',
    groupName: '气态污染物',
    roundFn: (val: number) => {
      return roundNumber(val, 3);
    },
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder:
      '((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      const value =
        (data.g * 255.0 * 255.0 * 255.0 +
          data.b * 255.0 * 255.0 +
          data.a * 255.0 +
          data.r) /
        100000000.0;

      return roundNumber(value, 2);
    },
    values: [
      {
        min: 2.8,
        color: 'rgb(131, 0, 36)',
      },
      {
        min: 2.6,
        max: 2.8,
        color: 'rgb(157, 0, 86)',
      },
      {
        min: 2.4,
        max: 2.6,
        color: 'rgb(198, 0, 0)',
      },
      {
        min: 2.2,
        max: 2.4,
        color: 'rgb(245, 0, 0)',
      },
      {
        min: 2.1,
        max: 2.2,
        color: 'rgb(246, 100, 0)',
      },
      {
        min: 1.9,
        max: 2.1,
        color: 'rgb(247, 137, 0)',
      },
      {
        min: 1.7,
        max: 1.9,
        color: 'rgb(249, 189, 0)',
      },
      {
        min: 1.5,
        max: 1.7,
        color: 'rgb(250, 210, 0)',
      },
      {
        min: 1.3,
        max: 1.5,
        color: 'rgb(252, 234, 0)',
      },
      {
        min: 1.1,
        max: 1.3,
        color: 'rgb(253, 255 ,0)',
      },
      {
        min: 0.9,
        max: 1.1,
        color: 'rgb(218,255, 0)',
      },
      {
        min: 0.7,
        max: 0.9,
        color: '#bcfe58',
      },
      {
        min: 0.5,
        max: 0.7,
        color: '#52feb7',
      },
      {
        min: 0.3,
        max: 0.5,
        color: '#47cffd',
      },
      {
        min: 0.1,
        max: 0.3,
        color: '#3ba5ff',
      },
      {
        min: 0.05,
        max: 0.1,
        color: '#2b64ff',
      },
      {
        max: 0.05,
        color: '#2002ff',
      },
    ],
  },
  CO: {
    label: 'CO柱浓度',
    iconParkLabel: 'CO',
    value: 'CO',
    chemicalFormula: 'CO',
    unit: '1e19molec./cm²',
    group: 'GAS',
    groupName: '气态污染物',
    roundFn: (val: number) => {
      return roundNumber(val, 3);
    },
    allowAggs: ['daily', 'weekly', 'monthly'],
    decoder:
      '((data.r * 255.0 + data.g) * 255.0 / 10000.0 * 6.022141 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      const value = (6.022141e19 * (data.r * 255 + data.g)) / 1e21;
      return roundNumber((value * 10) / 1000, 3);
    },
    values: [
      {
        min: 1.0,
        color: 'rgb(131, 0, 36)',
      },
      {
        min: 0.8,
        max: 1.0,
        color: 'rgb(157, 0, 86)',
      },
      {
        min: 0.6,
        max: 0.8,
        color: 'rgb(198, 0, 0)',
      },
      {
        min: 0.5,
        max: 0.6,
        color: 'rgb(245, 0, 0)',
      },
      {
        min: 0.45,
        max: 0.5,
        color: 'rgb(246, 100, 0)',
      },
      {
        min: 0.4,
        max: 0.45,
        color: 'rgb(247, 137, 0)',
      },
      {
        min: 0.35,
        max: 0.4,
        color: 'rgb(249, 189, 0)',
      },
      {
        min: 0.3,
        max: 0.35,
        color: 'rgb(250, 210, 0)',
      },
      {
        min: 0.25,
        max: 0.3,
        color: 'rgb(252, 234, 0)',
      },
      {
        min: 0.2,
        max: 0.25,
        color: 'rgb(253, 255 ,0)',
      },
      {
        min: 0.18,
        max: 0.2,
        color: 'rgb(218,255, 0)',
      },
      {
        min: 0.16,
        max: 0.18,
        color: '#bcfe58',
      },
      {
        min: 0.14,
        max: 0.16,
        color: '#52feb7',
      },
      {
        min: 0.12,
        max: 0.14,
        color: '#47cffd',
      },
      {
        min: 0.1,
        max: 0.12,
        color: '#3ba5ff',
      },
      {
        min: 0.05,
        max: 0.1,
        color: '#2b64ff',
      },
      {
        min: 0,
        max: 0.05,
        color: '#2002ff',
      },
    ],
  },
  UVA: {
    label: 'UVA',
    iconParkLabel: 'UVA',
    value: 'UVA',
    chemicalFormula: 'UVA',
    unit: 'W/m²',
    group: 'UR',
    groupName: '紫外线',
    roundFn: (val: number) => {
      return roundNumber(val, 2);
    },
    allowAggs: ['none'],
    decoder: '(data.r * 255.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return roundNumber(data.r);
    },
    values: [
      {
        min: 80,
        color: 'rgb(127, 33, 63)',
      },
      {
        min: 70,
        max: 80,
        color: 'rgb(213, 11, 85)',
      },
      {
        min: 60,
        max: 70,
        color: 'rgb(253, 182 ,90)',
      },
      {
        min: 50,
        max: 60,
        color: 'rgb(253, 240, 163)',
      },
      {
        min: 40,
        max: 50,
        color: 'rgb(249, 252, 180)',
      },
      {
        min: 20,
        max: 30,
        color: 'rgb(90, 220, 204)',
      },
      {
        min: 10,
        max: 20,
        color: 'rgb(25,158,191)',
      },
      {
        max: 10,
        color: 'rgb(33,139, 168)',
      },
    ],
  },
  UVB: {
    label: 'UVB',
    iconParkLabel: 'UVB',
    value: 'UVB',
    chemicalFormula: 'UVB',
    unit: 'W/m²',
    group: 'UR',
    groupName: '紫外线',
    roundFn: (val: number) => {
      return roundNumber(val, 2);
    },
    allowAggs: ['none'],
    decoder: '((data.r / 10.0) * 255.0 - {min}) / ({max} - {min})',
    pickingValueFn: (data) => {
      return data.r / 10;
    },
    values: [
      {
        min: 4,
        color: 'rgb(99, 2, 2)',
      },
      {
        min: 3,
        max: 4,
        color: 'rgb(141, 4, 4)',
      },
      {
        min: 2.5,
        max: 3,
        color: 'rgb(184, 3, 3)',
      },
      {
        min: 2,
        max: 2.5,
        color: 'rgb(226, 5, 5)',
      },
      {
        min: 1.5,
        max: 2,
        color: 'rgb(252, 71, 1)',
      },
      {
        min: 1.25,
        max: 1.5,
        color: 'rgb(245, 130, 35)',
      },
      {
        min: 1,
        max: 1.25,
        color: 'rgb(255, 204 ,79)',
      },
      {
        min: 0.75,
        max: 1,
        color: 'rgb(249, 252, 180)',
      },
      {
        min: 0.5,
        max: 0.75,
        color: 'rgb(90, 220, 204)',
      },
      {
        min: 0.25,
        max: 0.5,
        color: 'rgb(25,158,191)',
      },
      {
        max: 0.25,
        color: 'rgb(33,139, 168)',
      },
    ],
  },
  UVT: {
    label: '总辐射',
    iconParkLabel: 'zongfushe',
    value: 'UVT',
    chemicalFormula: 'UVT',
    unit: 'W/m²',
    group: 'UR',
    groupName: '紫外线',
    roundFn: (val: number) => {
      return roundNumber(val);
    },
    allowAggs: ['none'],
    decoder: '((data.r * 255.0 + data.g) * 255.0) / 100.0',
    pickingValueFn: (data) => {
      return data.r + data.g;
    },
    values: [
      {
        min: 100,
        color: 'rgb(127, 33, 63)',
      },
      {
        min: 85,
        max: 100,
        color: 'rgb(213, 11, 85)',
      },
      {
        min: 70,
        max: 85,
        color: 'rgb(253, 182 ,90)',
      },
      {
        min: 55,
        max: 70,
        color: 'rgb(253, 240, 163)',
      },
      {
        min: 40,
        max: 55,
        color: 'rgb(249, 252, 180)',
      },
      {
        min: 25,
        max: 40,
        color: 'rgb(90, 220, 204)',
      },
      {
        min: 10,
        max: 25,
        color: 'rgb(25,158,191)',
      },
      {
        max: 10,
        color: 'rgb(33,139, 168)',
      },
    ],
  },
  DCOLOR: {
    label: '沙尘图像',
    iconParkLabel: 'shachentuxiang',
    value: 'DCOLOR',
    chemicalFormula: 'DCOLOR',
    unit: '',
    group: 'DS',
    groupName: '沙尘',
    roundFn: undefined,
    allowAggs: ['none'],
    decoder: '',
    pickingValueFn: undefined,
    colorRamps: undefined,
    values: [],
    legend: [
      {
        id: 1,
        color: '#7e0023',
        label: '冰云 厚云 高层云',
      },
      {
        id: 2,
        color: '#000',
        label: '薄卷云 凝结尾流',
      },
      {
        id: 3,
        color: '#99703C',
        label: '厚云 中层云',
      },

      {
        id: 4,
        from: '#EA6CC3',
        to: '#E23A39',
        label: '沙尘',
      },
      {
        id: 5,
        color: '#BAAF3B',
        label: '冷气团 低层云',
      },
      {
        id: 6,
        color: '#AA64D4',
        label: '热气团 低层云',
      },
      {
        id: 7,
        color: '#3F7754',
        label: '薄云 中层云',
      },
    ],
  },
  DMASK: {
    label: '沙尘分布',
    iconParkLabel: 'shachenfenbu',
    value: 'DMASK',
    chemicalFormula: 'DMASK',
    unit: '',
    group: 'DS',
    groupName: '沙尘',
    roundFn: (val: number) => {
      return roundNumber(val);
    },
    allowAggs: ['none'],
    decoder: `float u =  data.r * 255.0;
      if(u > 0.0) {
        return 1.0;
      }else {
        return 0.0;
      }
    `,
    pickingValueFn: () => {
      return null;
    },
    values: [],
    colorRamps: {
      '1.0': '#99014D',
      '0.0': '#5459AB',
    },
    legend: [
      {
        id: 1,
        color: '#99014D',
        label: '沙尘分布',
      },
    ],
  },
  TCOLOR: {
    label: '卫星云图',
    iconParkLabel: 'weixingyuntu-cka9elph',
    value: 'TCOLOR',
    chemicalFormula: 'TCOLOR',
    unit: '',
    group: 'REST',
    groupName: '',
    roundFn: undefined,
    allowAggs: ['none'],
    decoder: '',
    pickingValueFn: undefined,
    colorRamps: undefined,
    values: [],
  },
  WINDY: {
    label: '风场',
    iconParkLabel: 'fengchang-cka9elp2',
    value: 'WINDY',
    chemicalFormula: 'WINDY',
    unit: '',
    group: 'REST',
    groupName: '',
    roundFn: undefined,
    allowAggs: ['none', 'daily', 'weekly', 'monthly'],
    decoder: '',
    pickingValueFn: undefined,
    colorRamps: undefined,
    values: [],
  },
};


export function getColorRamps(
  val: { max?: number; min?: number; color: string }[],
) {
  const info = ensureArray(val);
  if (info.length === 0) {
    return {};
  }
  const firstLevel = first(info);
  const endLevel = last(info);
  const { min: topLevelMin, color: topLevelColor } = ensureObject(firstLevel);
  const {
    max: endLevelMax,
    min: endLevelMin,
    color: endLevelColor,
  } = ensureObject(endLevel);
  // @ts-ignore
  const diff = ensureNumber(topLevelMin) - (isNaN(endLevelMin) ? ensureNumber(endLevelMax) : endLevelMin);
  if (diff === 0) {
    return {
      '1.00': topLevelColor,
      '0.00': endLevelColor,
    };
  }
  const dict = info.reduce(
    (d, model, idx) => {
      const { min: modelMin, color } = model;
      if (idx === 0) return { ...d, '1.00': color };
      if (idx === info?.length - 1) return { ...d, '-0.00': color };
      const modelDiff =
        ensureNumber(modelMin) -
        (isNaN(endLevelMin) ? ensureNumber(endLevelMax) : endLevelMin);
      const per = (modelDiff / diff).toFixed(2);
      return { ...d, [per]: color };
    },
    {} as Record<string, string>,
  );
  return dict;
}

export function getDecoder(type: DUST.DataTypes) {
  const decoder = dataConfig[type]?.decoder;
  const values = dataConfig[type]?.values || [];
  if (!decoder) {
    return '';
  }
  const info = ensureArray(values);
  if (info.length < 2) {
    return '';
  }
  const top = first(info);
  const end = last(info);
  const { min: max } = top as { min: number };
  // const { max: min } = end as { max: number };
  // 最小的色带有min和max，最大的色带没有min只有max
  // console.log(end?.min);
  const min = isNaN(end?.min) ? end?.max : end?.min;
  let decoderResult = decoder.replace(/\s/g, '');
  if (ensureNumber(min) < 0) {
    decoderResult = decoder.replaceAll(
      '{min}',
      `(0.00 ${ensureNumber(min).toFixed(2)})`,
    );
  } else {
    decoderResult = decoder.replaceAll(
      '{min}',
      `${ensureNumber(min).toFixed(2)}`,
    );
  }
  if (ensureNumber(max) < 0) {
    decoderResult = decoderResult.replaceAll(
      '{max}',
      `(0.00 ${ensureNumber(max).toFixed(2)})`,
    );
  } else {
    decoderResult = decoderResult.replaceAll(
      '{max}',
      `${ensureNumber(max).toFixed(2)}`,
    );
  }
  return decoderResult;
}

/**
 * 日期时间格式化
 * @param separator 分隔符 / | -
 * @param isInt 整点
 * @returns string
 */
export const getDatetimeFormatter = (separator = '/', isInt = false) => {
  const hm = isInt ? '00:00' : 'mm:ss';
  return `YYYY${separator}MM${separator}DD HH:${hm}`;
};

