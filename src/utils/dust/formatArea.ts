const formatAreaNumber = (area?: number) => {
  if (!area) {
    return 0;
  }
  if (area > 10000 * 10000) {
    return ((area / 10000) * 10000).toFixed(1);
  } else if (area > 10000) {
    return (area / 10000).toFixed(1);
  } else {
    return area.toFixed(1);
  }
};
const formatAreaUnit = (area?: number) => {
  if (!area) {
    return '';
  }
  if (area > 10000 * 10000) {
    return '亿';
  } else if (area > 10000) {
    return '万';
  } else {
    return '';
  }
};
export { formatAreaNumber, formatAreaUnit };
