export type ConfigItem = {
  unit: string; // 单位, 如 μg/m³
  values: {
    min?: number;
    max?: number;
    color: string;
  }[]; // 色带
  formula: string;
};

// 色带
// 国/省控
export const stationPollutionValuesAndColors: Record<string, ConfigItem> = {
  aqi: {
    unit: '',
    formula: 'AQI',
    values: [
      {
        min: 300,
        color: '#7e0023',
        lv: '严重污染',
      },
      {
        min: 200,
        max: 300,
        color: '#99004c',
        lv: '重度污染',
      },
      {
        min: 150,
        max: 200,
        color: '#ff0000',
        lv: '中度污染',
      },
      {
        min: 100,
        max: 150,
        color: '#ff7e00',
        lv: '轻度污染',
      },
      {
        min: 50,
        max: 100,
        color: '#ffff00',
        lv: '良',
      },
      {
        max: 50,
        color: '#00e400',
        lv: '优',
      },
    ],
  },
  pm25: {
    unit: 'μg/m³',
    formula: 'PM₂.₅',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      {
        min: 250,
        color: '#7e0023',
      },
      {
        min: 150,
        max: 250,
        color: '#99004c',
      },
      {
        min: 115,
        max: 150,
        color: '#ff0000',
      },
      {
        min: 75,
        max: 115,
        color: '#ff7e00',
      },
      {
        min: 35,
        max: 75,
        color: '#ffff00',
      },
      {
        max: 35,
        color: '#00e400',
      },
    ],
  },
  scpm25: {
    unit: 'μg/m³',
    formula: 'PM₂.₅插值',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      {
        min: 250,
        color: '#7e0023',
      },
      {
        min: 150,
        max: 250,
        color: '#99004c',
      },
      {
        min: 115,
        max: 150,
        color: '#ff0000',
      },
      {
        min: 75,
        max: 115,
        color: '#ff7e00',
      },
      {
        min: 35,
        max: 75,
        color: '#ffff00',
      },
      {
        max: 35,
        color: '#00e400',
      },
    ],
  },
  pm10: {
    unit: 'μg/m³',
    formula: 'PM₁₀',
    roundFunc: Math.round,
    equalMax: true,
    values: [
      {
        min: 420,
        color: '#7e0023',
      },
      {
        min: 350,
        max: 420,
        color: '#99004c',
      },
      {
        min: 250,
        max: 350,
        color: '#ff0000',
      },
      {
        min: 150,
        max: 250,
        color: '#ff7e00',
      },
      {
        min: 50,
        max: 150,
        color: '#ffff00',
      },
      {
        max: 50,
        color: '#00e400',
      },
    ],
  },
  so2: {
    unit: 'μg/m³',
    formula: 'SO₂',
    equalMax: true,
    values: [
      {
        min: 1600,
        color: '#7e0023',
      },
      {
        min: 800,
        max: 1600,
        color: '#99004c',
      },
      {
        min: 475,
        max: 800,
        color: '#ff0000',
      },
      {
        min: 150,
        max: 475,
        color: '#ff7e00',
      },
      {
        min: 50,
        max: 150,
        color: '#ffff00',
      },
      {
        max: 50,
        color: '#00e400',
      },
    ],
  },
  'so2-hour': {
    unit: 'μg/m³',
    formula: 'SO₂',
    equalMax: true,
    values: [
      {
        min: 1600,
        color: '#7e0023',
      },
      {
        min: 800,
        max: 1600,
        color: '#99004c',
      },
      {
        min: 650,
        max: 800,
        color: '#ff0000',
      },
      {
        min: 500,
        max: 650,
        color: '#ff7e00',
      },
      {
        min: 150,
        max: 500,
        color: '#ffff00',
      },
      {
        max: 150,
        color: '#00e400',
      },
    ],
  },
  no2: {
    unit: 'μg/m³',
    formula: 'NO₂',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      {
        min: 565,
        color: '#7e0023',
      },
      {
        min: 280,
        max: 565,
        color: '#99004c',
      },
      {
        min: 180,
        max: 280,
        color: '#ff0000',
      },
      {
        min: 80,
        max: 180,
        color: '#ff7e00',
      },
      {
        min: 40,
        max: 80,
        color: '#ffff00',
      },
      {
        max: 40,
        color: '#00e400',
      },
    ],
  },
  'no2-hour': {
    unit: 'μg/m³',
    formula: 'NO₂',
    equalMax: true,
    values: [
      {
        min: 2340,
        color: '#7e0023',
      },
      {
        min: 1200,
        max: 2340,
        color: '#99004c',
      },
      {
        min: 700,
        max: 1200,
        color: '#ff0000',
      },
      {
        min: 200,
        max: 700,
        color: '#ff7e00',
      },
      {
        min: 100,
        max: 200,
        color: '#ffff00',
      },
      {
        max: 100,
        color: '#00e400',
      },
    ],
  },
  'no2-day': {
    unit: 'μg/m³',
    formula: 'NO₂',
    equalMax: true,
    values: [
      {
        min: 565,
        color: '#7e0023',
      },
      {
        min: 280,
        max: 565,
        color: '#99004c',
      },
      {
        min: 180,
        max: 280,
        color: '#ff0000',
      },
      {
        min: 80,
        max: 180,
        color: '#ff7e00',
      },
      {
        min: 40,
        max: 80,
        color: '#ffff00',
      },
      {
        max: 40,
        color: '#00e400',
      },
    ],
  },
  co: {
    unit: 'mg/m³',
    formula: 'CO',
    equalMax: true,
    values: [
      {
        min: 36,
        color: '#7e0023',
      },
      {
        min: 24,
        max: 36,
        color: '#99004c',
      },
      {
        min: 14,
        max: 24,
        color: '#ff0000',
      },
      {
        min: 4,
        max: 14,
        color: '#ff7e00',
      },
      {
        min: 2,
        max: 4,
        color: '#ffff00',
      },
      {
        max: 2,
        color: '#00e400',
      },
    ],
  },
  'co-hour': {
    unit: 'mg/m³',
    formula: 'CO',
    equalMax: true,
    values: [
      {
        min: 90,
        color: '#7e0023',
      },
      {
        min: 60,
        max: 90,
        color: '#99004c',
      },
      {
        min: 35,
        max: 60,
        color: '#ff0000',
      },
      {
        min: 10,
        max: 35,
        color: '#ff7e00',
      },
      {
        min: 5,
        max: 10,
        color: '#ffff00',
      },
      {
        max: 5,
        color: '#00e400',
      },
    ],
  },
  'co-day': {
    unit: 'mg/m³',
    formula: 'CO',
    equalMax: true,
    values: [
      { min: 4, color: 'rgb(157, 0, 86)' },
      { min: 3.5, max: 4, color: 'rgb(181, 0, 0)' },
      { min: 3, max: 3.5, color: 'rgb(207, 0, 0)' },
      { min: 2.5, max: 3, color: 'rgb(245, 0, 0)' },
      { min: 2, max: 2.5, color: 'rgb(245, 68, 0)' },
      { min: 1.8, max: 2, color: 'rgb(246, 100,0)' },
      { min: 1.6, max: 1.8, color: 'rgb(247, 137, 0)' },
      { min: 1.4, max: 1.6, color: 'rgb(249, 189, 0)' },
      { min: 1.2, max: 1.4, color: 'rgb(250, 210, 0)' },
      { min: 1, max: 1.2, color: 'rgb(252, 234, 0)' },
      { min: 0.8, max: 1, color: 'rgb(253, 255, 0)' },
      { min: 0.6, max: 0.8, color: 'rgb(218,255,0)' },
      { min: 0.4, max: 0.6, color: 'rgb(169, 255, 0)' },
      { min: 0.2, max: 0.4, color: 'rgb(79, 255, 0)' },
      { min: 0, max: 0.2, color: 'rgb(52,179, 0) ' },
    ],
  },
  o3: {
    unit: 'μg/m³',
    formula: 'O₃',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      {
        min: 800,
        color: '#7e0023',
      },
      {
        min: 265,
        max: 800,
        color: '#99004c',
      },
      {
        min: 215,
        max: 265,
        color: '#ff0000',
      },
      {
        min: 160,
        max: 215,
        color: '#ff7e00',
      },
      {
        min: 100,
        max: 160,
        color: '#ffff00',
      },
      {
        max: 100,
        color: '#00e400',
      },
    ],
  },
  'o3-hour': {
    unit: 'μg/m³',
    formula: 'O₃',
    equalMax: true,
    values: [
      {
        min: 800,
        color: '#7e0023',
      },
      {
        min: 400,
        max: 800,
        color: '#99004c',
      },
      {
        min: 300,
        max: 400,
        color: '#ff0000',
      },
      {
        min: 200,
        max: 300,
        color: '#ff7e00',
      },
      {
        min: 160,
        max: 200,
        color: '#ffff00',
      },
      {
        max: 160,
        color: '#00e400',
      },
    ],
  },
  'o3-day': {
    unit: 'μg/m³',
    equalMax: true,
    formula: 'O₃',
    values: [
      { min: 250, color: 'rgb(157, 0, 86)' },
      { min: 200, max: 250, color: 'rgb(198, 0, 0)' },
      { min: 180, max: 200, color: 'rgb(245, 0, 0)' },
      { min: 160, max: 180, color: 'rgb(246, 100 ,0)' },
      { min: 140, max: 160, color: 'rgb(247, 137, 0)' },
      { min: 120, max: 140, color: 'rgb(249, 189, 0)' },
      { min: 100, max: 120, color: 'rgb(250, 210, 0)' },
      { min: 90, max: 100, color: 'rgb(252, 234, 0)' },
      { min: 80, max: 90, color: 'rgb(253, 255 ,0)' },
      { min: 70, max: 80, color: 'rgb(169, 255, 0)' },
      { min: 60, max: 70, color: 'rgb(79, 255, 0)' },
      { min: 50, max: 60, color: 'rgb(70,232,0)' },
      { max: 50, color: 'rgb(52,179, 0) ' },
    ],
  },
  hcho: {
    unit: 'molec./c㎡',
    thematicUint: '1e16molec./c㎡',
    equalMax: true,
    formula: 'HCHO',
    values: [
      { min: 80, max: 100, color: 'rgb(255,0,0)' },
      { min: 60, max: 80, color: 'rgb(241,252,20)' },
      { min: 50, max: 60, color: 'rgb(188,254,88)' },
      { min: 40, max: 50, color: 'rgb(38,254,183)' },
      { min: 30, max: 40, color: 'rgb(16,207,253)' },
      { min: 20, max: 30, color: 'rgb(55,165,255)' },
      { min: 10, max: 20, color: 'rgb(15,100,255)' },
      { min: 0, max: 10, color: 'rgb(0,0,255)' },
    ],
  },
};
