import { WebMercatorViewport } from 'deck.gl/typed';

export const calcCenterAndZoomByGeojson = (
  minLon: number,
  maxLon: number,
  minLat: number,
  maxLat: number,
  width: number,
  height: number,
  padding?: number,
  offset?: number[],
) => {
  try {
    const viewport = new WebMercatorViewport({
      width,
      height,
    });
    const result = viewport.fitBounds(
      [
        [minLon, minLat],
        [maxLon, maxLat],
      ],
      {
        width,
        height,
        padding: padding || 0,
        offset: offset,
      },
    );
    return result;
  } catch (error) {
    return null;
  }
};
