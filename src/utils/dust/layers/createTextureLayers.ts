
import { getTranslateImageData } from '@/utils/image';
import { MaskExtension } from '@deck.gl/extensions';
import { TileLayer } from 'deck.gl';
import { tileLayerBaseConfig } from '../../baseLayerConfig';
import TextureBitmapLayer from './TextureBitmapLayer';
import { EXTENT } from '../config';

export interface TextureLayerGenerateProps {
  id: string;
  dataUrl: string;
  colorRamp: Record<string, string>;
  min?: number;
  max?: number;
  decoder?: string;
  visible?: boolean;
  filters?: number[];
  filtersChannel?: number;
  maskId?: string;
  opacity?: number;
  smooth?: boolean;
  maskImageSrc?: string;
  onViewportLoad?: (tiles: any[]) => void;
}

export const createTextureLayer = ({
  id,
  dataUrl,
  colorRamp,
  min,
  max,
  decoder,
  visible,
  filters = [],
  filtersChannel,
  maskId = 'mask-area',
  opacity = 1,
  smooth = true,
  maskImageSrc,
  onViewportLoad,
}: TextureLayerGenerateProps) => {
  return new TileLayer({
    ...tileLayerBaseConfig,
    id,
    // @ts-ignore
    maskId,
    extensions: [new MaskExtension()],
    data: dataUrl,
    extent: EXTENT,
    maxZoom: 7,
    minZoom: 0,
    tileSize: 256,
    colorFormat: 'RGBA',
    pickable: true,
    zoomOffset: -1,
    // 色带更改后，重新渲染图层
    shouldUpdate: (prevProps: any, nextProps: any) => {
      return (
        prevProps.colorRamp !== nextProps.colorRamp ||
        prevProps.dataUrl !== nextProps.dataUrl ||
        prevProps.visible !== nextProps.visible ||
        prevProps.min !== nextProps.min ||
        prevProps.max !== nextProps.max ||
        prevProps.filters !== nextProps.filters ||
        prevProps.filtersChannel !== nextProps.filtersChannel
      );
    },
    visible,
    renderSubLayers: (props) => {
      const {
        bbox: { west, south, east, north },
        index: { x, y, z },
      } = props.tile;
      return props?.data
        ? new TextureBitmapLayer(props, {
            pickable: true,
            data: null,
            image: props.data,
            bounds: [west, south, east, north],
            colorRamp,
            min,
            max,
            decoder,
            filters,
            filtersChannel,
            opacity,
            maskImageSrc: maskImageSrc
              ? maskImageSrc
                  .replace('{z}', z)
                  .replace('{x}', x)
                  .replace('{y}', y)
              : undefined,
            smooth,
          })
        : null;
    },
    getTileData: (tile) => {
      return getTranslateImageData(tile.url);
    },
    onViewportLoad(tiles) {
      if (typeof onViewportLoad === 'function') {
        // eslint-disable-next-line @typescript-eslint/no-invalid-this
        onViewportLoad(tiles);
      }
    },
  });
};
