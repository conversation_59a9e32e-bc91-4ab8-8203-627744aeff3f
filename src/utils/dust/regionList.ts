export const regionList = [
  {
    code: 100000,
    name: '全国',
    children: [],
    disabled: false,
    level: 1,
  },
  {
    code: 110000,
    name: '北京市',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 120000,
    name: '天津市',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 130000,
    name: '河北省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 140000,
    name: '山西省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 150000,
    name: '内蒙古自治区',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 210000,
    name: '辽宁省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 220000,
    name: '吉林省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 230000,
    name: '黑龙江省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 310000,
    name: '上海市',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 320000,
    name: '江苏省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 330000,
    name: '浙江省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 340000,
    name: '安徽省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 350000,
    name: '福建省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 360000,
    name: '江西省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 370000,
    name: '山东省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 410000,
    name: '河南省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 420000,
    name: '湖北省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 430000,
    name: '湖南省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 440000,
    name: '广东省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 450000,
    name: '广西壮族自治区',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 460000,
    name: '海南省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 500000,
    name: '重庆市',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 510000,
    name: '四川省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 520000,
    name: '贵州省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 530000,
    name: '云南省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 540000,
    name: '西藏自治区',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 610000,
    name: '陕西省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 620000,
    name: '甘肃省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 630000,
    name: '青海省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 640000,
    name: '宁夏回族自治区',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 650000,
    name: '新疆维吾尔自治区',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 710000,
    name: '台湾省',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 810000,
    name: '香港特别行政区',
    level: 1,
    children: [],
    disabled: false,
  },
  {
    code: 820000,
    name: '澳门特别行政区',
    level: 1,
    children: [],
    disabled: false,
  },
];

export default function getAliasRegionName(name: string) {
  const threeCharacters = ['内蒙古', '黑龙江'];
  if (threeCharacters.find((item) => name.includes(item))) {
    return name.slice(0, 3);
  }
  return name.slice(0, 2);
}
