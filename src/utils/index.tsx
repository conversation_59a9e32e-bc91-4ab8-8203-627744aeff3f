/* eslint-disable consistent-return */
import type { ViewStateProps } from '@deck.gl/core/lib/deck';
import type { RGBColor } from '@deck.gl/core/utils/color';
import WebMercatorViewport from '@math.gl/web-mercator';
import bbox from '@turf/bbox';
import { TileLayer } from 'deck.gl';
import { stringify } from 'qs';
import { css } from 'styled-components';
import { tileLayerBaseConfig } from './baseLayerConfig';
import { regionData } from './region';
// @ts-ignore
import { Agg } from '@/pages/Overview/types';
import { MaskExtension } from '@deck.gl/extensions';
import { message } from 'antd';
import { TIANDITU_KEYS } from './dust/config';
import { remoteSensingValuesAndColors as remoteSensingValuesAndColorsDataDown } from './dataDown';
import { useCallback } from 'react';

// 获取规范化数字
export function number_(num: unknown) {
  const num_ = Number(num);
  return num_ || 0;
}

export function number_fixed(number: unknown, fixed: unknown = 1) {
  const num_ = number_(number);
  return Number(num_.toFixed(number_(fixed)));
}

export const dateFormatter = 'YYYY/MM/DD';

export const datetimeFormatter = 'YYYY/MM/DD HH:mm:ss';

// 整点格式化
export const hourFormatter = 'YYYY/MM/DD HH:00:00';

export { request } from './request';

export const getBase64 = (file: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const getNewViewState = (geojson: any, viewState: ViewStateProps, width: number, height: number, padding?: number) => {
  try {
    const viewport = new WebMercatorViewport({
      ...viewState,
      width,
      height,
    });
    const xy = bbox(geojson);
    const newViewport = viewport.fitBounds(
      [
        [xy[0], xy[1]],
        [xy[2], xy[3]],
      ],
      {
        padding: padding || 10,
      },
    );
    return {
      ...viewState,
      zoom: newViewport.zoom,
    };
  } catch (err) {
    return viewState;
  }
};

// 屏幕适配
export const screens = {
  md: 1500,
};

export const mediaQueries = (key: keyof typeof screens) => (styles: any) =>
  css`
    @media (max-width: ${screens[key]}px) {
      ${styles}
    }
  `;

export const routes: {
  path: string;
  menuKey: number;
}[] = [
    {
      path: '/',
      menuKey: 1,
    },
    {
      path: '/pollution-warning',
      menuKey: 3,
    },
    {
      path: '/pollution-warning-statistics',
      menuKey: 3,
    },
    {
      path: '/pollution-warning-statistics',
      menuKey: 3,
    },
  ];

interface TextureLayerPickerData {
  r: number;
  g: number;
  b: number;
  a: number;
}

export const mapDataFormatMethods: Record<string, (data: TextureLayerPickerData) => string> = {
  pm25(data) {
    return `PM₂.₅: ${data.r * 255 + data.g} μg/m³`;
  },
  scpm25(data) {
    return `PM₂.₅插值: ${data.r * 255 + data.g} μg/m³`;
  },
  lst(data) {
    return `地表温度: ${(data.r - 128).toFixed(1)} ℃`;
  },
  pm10(data) {
    return `PM₁₀: ${data.r * 255 + data.g} μg/m³`;
  },
  aod(data) {
    return `AOD: ${((data.r * 255 + data.g) / 1000.0).toFixed(3)} `;
  },
  ch4(data) {
    // ch4: '(data.r * 255.0 + data.g) * 255.0 / 1000.0',
    return `CH₄: ${data.r * 255 + data.g} ppb`;
  },
  o3(data) {
    return `O₃_S5P: ${data.r * 255 + data.g} μg/m³`;
  },
  gk2b_o3(data) {
    return `O₃_GK2B: ${data.r * 255 + data.g} μg/m³`;
  },
  o3tcd(data) {
    return `O₃柱浓度: ${data.r * 255 + data.g} DU`;
  },
  gk2b_o3tcd(data) {
    return `O₃柱浓度_GK2B: ${data.r * 255 + data.g} DU`;
  },
  co2(data) {
    return `CO₂: ${Math.trunc((data.r * 255 + data.g) / 10)}  PPM`;
  },
  hcho(data) {
    return `HCHO_S5P: ${(((data.g * 255 * 255 * 255 + data.b * 255 * 255 + data.a * 255 + data.r) * Math.pow(10, 8)) / Math.pow(10, 16)).toFixed(
      2,
    )} 1e16molec./c㎡`;
  },
  gk2b_hcho(data) {
    return `HCHO_GK2B: ${(((data.g * 255 * 255 * 255 + data.b * 255 * 255 + data.a * 255 + data.r) * Math.pow(10, 8)) / Math.pow(10, 16)).toFixed(
      2,
    )} 1e16molec./c㎡`;
  },
  humidity(data) {
    return `湿度: ${data.r}%`;
  },
  pre(data) {
    return `降水量: ${data.r} mm`;
  },
  vis(data) {
    return `能见度: ${Math.round(data.r * 2) / 10} KM`;
  },
  so2(data) {
    return `SO₂: ${((data.r * 255 + data.g) / 1000).toFixed(3)} du`;
  },
  gk2b_so2(data) {
    return `SO₂_GK2B: ${number_fixed(
      (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) / 100000000.0,
      2,
    )} 1e16molec./c㎡`;
  },
  co(data) {
    const correctedValue = (6.022141e19 * (data.r * 255 + data.g)) / 1e21;
    return `CO: ${Math.round(10 * correctedValue) / 1000} 1e19molec./c㎡`;
  },
  gk2b_co(data) {
    const correctedValue = (6.022141e19 * (data.r * 255 + data.g)) / 1e21;
    return `CO_GK2B: ${Math.round(10 * correctedValue) / 1000} 1e19molec./c㎡`;
  },
  tmp(data) {
    return `温度: ${data.r - 128} ℃`;
  },
  tmp_max(data) {
    return `高温: ${data.r - 128} ℃`;
  },
  no2(data) {
    return `NO₂_S5P: ${data.r * 255 + data.g} μg/m³`;
  },
  gk2b_no2(data) {
    return `NO₂_GK2B: ${data.r * 255 + data.g} μg/m³`;
  },
  no2tcd(data) {
    return `NO₂柱浓度: ${number_fixed(
      (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) / 100000000.0,
      2,
    )} 1e16molec./c㎡`;
  },
  gk2b_no2tcd(data) {
    return `NO₂柱浓度_GK2B: ${number_fixed(
      (data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) / 100000000.0,
      2,
    )} 1e16molec./c㎡`;
  },
  o3mca(data) {
    const text = ['', 'VOCs控制区', '共同控制区', 'NOx控制区'];
    return `${text[data.r]}`;
  },
  fnr: (data) => {
    const num = data.r / 10.0;
    if (num < 1) {
      return 'VOCs控制区';
    } else if (num >= 4 && num < 6) {
      return '共同控制区（偏NOx）';
    } else if (num >= 2 && num < 4) {
      return '共同控制区';
    } else if (num >= 1 && num < 2) {
      return '共同控制区（偏VOCs）';
    } else {
      return 'NOx控制区';
    }
  },
  hid(data) {
    return `城市热岛指数: ${data.r}`;
  },
  rh(data) {
    return `湿度: ${data.r} %`;
  },
  prs(data) {
    return `气压: ${data.r * 255 + data.g} hPa`;
  },
  'wiu-wiv': (data) => {
    const u = data.r + Math.trunc(data.b / 10) / 10.0 - 100.0;
    const v = data.g + (data.b % 10) / 10 - 100.0;
    const velocity = Math.round(10 * Math.sqrt(u * u + v * v)) / 10;
    // 暂时隐藏风向显示
    // const direction = calculateUvDirection(u, v);

    return `风速: ${velocity} m/s`;
  },
  uva: (data) => {
    return ` ${data.r} W/m²`;
  },
  uvb: (data) => {
    return ` ${data.r / 10} W/m²`;
  },
};

export const decoder: Record<string, string> = {
  pm25: '(data.r * 255.0 + data.g) * 255.0 / 350.0',
  pm10: '(data.r * 255.0 + data.g) * 255.0 / 500.0',
  aod: '(data.r * 255.0 + data.g) * 255.0 / 1000.0 / 2.8',
  // eslint-disable-next-line max-len
  no2: '(data.r * 255.0 + data.g) * 255.0 / 250.0',
  o3: '(data.r * 255.0 + data.g) * 255.0 / 400.0',
  fnr: 'data.r * 255.0 / 10.0 / 6.0',
  co: '(data.r * 255.0 + data.b) * 255.0 / 1000.0',
  so2: '(data.r * 255.0 + data.b) * 255.0 / 10000.0',
  // eslint-disable-next-line max-len
  hcho: `(data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` + ` + data.a * 255.0 + data.r) * 255.0 ` + `/ 100000000.0 / 1.60`,
  tmp: '(data.r * 255.0 - 28.0) / 200.0',
  pre: `
    float pre = data.r * 255.0;
    if(pre <= 0.00000001) {
      return  0.00000001;
    }
    return pre / 50.0;
  `,
  vis: 'data.r * 0.2 * 255.0 / 30.0',
  rh: 'data.r * 255.0 / 100.0',
  prs: '(data.r * 255.0 + data.g) * 255.0 / 1080.0',
  'wiu-wiv': `
    float u = data.r * 255.0 + data.b * 255.0 / 100.0 - 100.0;
    float v = data.g * 255.0 + mod(data.b * 255.0, 10.0) / 10.0 - 100.0;
    float speed = sqrt(u * u + v * v);
    if(speed <= 0.00000001) {
      return 0.00000001;
    }
    return speed / 28.4;
  `,
  dmask: `float u =  data.r * 255.0;
          if(u > 0.0) {
            return 1.0;
          }else {
            return 0.0;
          }
      `,
};

export const decoderFactory = (dataType: string) => {
  const decoders: Record<string, string> = {
    pm25: `((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})`,
    scpm25: `((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})`,
    lst: '((data.r * 255.0) - 128.00 - {min})/ ({max} - {min})',
    pm10: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    aod: '(((data.r * 255.0 + data.g) * 255.0 / 1000.0) - {min}) / ({max} - {min})',
    no2: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    gk2b_no2: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    o3: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    gk2b_o3: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    fnr: `if ( data.r * 255.0 / 10.0 >= 6.0 ) {
      return 1.0;
    } else if( data.r * 255.0 / 10.0 < 6.0 && data.r * 255.0 / 10.0 >= 4.0 ) {
      return 0.66;
    } else if( data.r * 255.0 / 10.0 < 4.0 && data.r * 255.0 / 10.0 >= 2.0 ) {
      return 0.5;
    } else if( data.r * 255.0 / 10.0 < 2.0 && data.r * 255.0 / 10.0 >= 1.0 ) {
      return 0.33;
    } else {
      return 0.0;
    }`,
    hid: `if ( data.r * 255.0 > 2.0 ) {
      return 0.667;
    } else if ( data.r * 255.0 <= 2.0 && data.r * 255.0 > 1.0 ) {
      return 0.333;
    } else if ( data.r * 255.0 <= 1.0 && data.r * 255.0 > 0.0 ) {
      return 0.0;
    }`,
    co: `((data.r * 255.0 + data.g) * 255.0 / 10000.0 * 6.022141 - {min}) / ({max} - {min})`,
    gk2b_co: `((data.r * 255.0 + data.g) * 255.0 / 10000.0 * 6.022141 - {min}) / ({max} - {min})`,
    so2: '(((data.r * 255.0 + data.g) * 255.0 / 1000.0) - {min}) / ({max} - {min})',
    // eslint-disable-next-line max-len
    gk2b_so2: `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min})/ ({max} - {min})`,
    // eslint-disable-next-line max-len
    o3tcd: `((data.r * 255.0  + data.g ) * 255.0  - {min})/ ({max} - {min})`,
    gk2b_o3tcd: `((data.r * 255.0  + data.g ) * 255.0  - {min})/ ({max} - {min})`,
    // eslint-disable-next-line max-len
    no2tcd: `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min})/ ({max} - {min})`,
    // eslint-disable-next-line max-len
    gk2b_no2tcd: `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0 + data.a * 255.0 + data.r) * 255.0 / 100000000.0  - {min})/ ({max} - {min})`,
    hcho:
      `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` +
      ` + data.a * 255.0 + data.r) * 255.0 ` +
      `/ 100000000.0 - {min}) / ({max} - {min})`,
    gk2b_hcho:
      `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` +
      ` + data.a * 255.0 + data.r) * 255.0 ` +
      `/ 100000000.0 - {min}) / ({max} - {min})`,
    c2h2o2:
      `((data.g * 255.0 * 255.0 * 255.0 + data.b * 255.0 * 255.0` +
      ` + data.a * 255.0 + data.r) * 255.0 ` +
      `/ 100000000.0 - {min}) / ({max} - {min})`,
    tmp: '(data.r * 255.0 - 128.0 - {min}) / ({max} - {min})',
    tmp_max: '(data.r * 255.0 - 128.0 - {min}) / ({max} - {min})',
    pre: `
    float pre = data.r * 255.0;
    if(pre <= 0.00000001) {
      return  0.00000001;
    }
    return (pre - {min}) / ({max} - {min});
  `,
    vis: '(data.r * 0.2 * 255.0 - {min}) / ({max} - {min})',
    rh: '(data.r * 255.0 - {min}) / ({max} - {min})',
    prs: '((data.r * 255.0 + data.g) * 255.0 - {min}) / ({max} - {min})',
    'wiu-wiv': `
    float u = data.r*255.0 + data.b*255.0/100.0-100.0;
    float v = data.g*255.0 + mod(data.b*255.0, 10.0)/10.0-100.0;
    float speed = sqrt(u * u + v * v);
    if(speed <= 0.00000001) {
      return 0.00000001;
    }
    return (speed - {min}) / ({max} - {min});
  `,
    uva: '(data.r * 255.0 - {min})/ ({max} - {min})',
    uvb: '((data.r / 10.0) * 255.0 - {min}) / ({max} - {min})',
    ch4: '((data.r * 255.0 + data.g) * 255.0  - {min}) / ({max} - {min})',
    co2: '((data.r * 255.0 + data.g) * 255.0/ 10.0 - {min}) / ({max} - {min}) ',
  };
  return decoders[dataType];
};

export const colorRamps: Record<string, Record<string, string>> = {
  aqi: {
    '1.000': '#7e0023',
    '0.667': '#99004c',
    '0.500': '#ff0000',
    '0.333': '#ff7e00',
    '0.167': '#ffff00',
    '0.000': '#00ff00',
  },
  pm25: {
    '1.000': '#7e0023',
    '0.600': '#99004c',
    '0.460': '#ff0000',
    '0.300': '#ff7e00',
    '0.140': '#ffff00',
    '0.000': '#00ff00',
  },
  pm10: {
    '1.000': '#7e0023',
    '0.833': '#99004c',
    '0.595': '#ff0000',
    '0.357': '#ff7e00',
    '0.119': '#ffff00',
    '0.000': '#00ff00',
  },
  aod: {
    '1.000': '#FF0000',
    '0.929': '#ED6703',
    '0.857': '#FD9302',
    '0.786': '#FDCB02',
    '0.714': '#FDE302',
    '0.643': '#FFFF00',
    '0.571': '#DCFB04',
    '0.500': '#BAEF01',
    '0.429': '#80FF00',
    '0.357': '#80FF80',
    '0.286': '#87FFFF',
    '0.214': '#5FCDFF',
    '0.143': '#37A5FF',
    '0.071': '#0F64FF',
    '0.000': '#2800FF',
  },
  no2: {
    '1.000': '#830024',
    '0.889': '#9d0056',
    '0.778': '#b50000',
    '0.667': '#c60000',
    '0.556': '#f50000',
    '0.500': '#f66400',
    '0.444': '#f78900',
    '0.389': '#f9bd00',
    '0.333': '#fad200',
    '0.278': '#fcea00',
    '0.222': '#fdff00',
    '0.167': '#daff00',
    '0.139': '#a9ff00',
    '0.111': '#4fff00',
    '0.083': '#46e800',
    '0.056': '#3ecf00',
    '0.000': '#34b300',
  },
  o3: {
    '1.0': '#830024',
    '0.800': '#830024',
    '0.600': '#9d0056',
    '0.500': '#c60000',
    '0.440': '#f50000',
    '0.400': '#f66400',
    '0.360': '#f78900',
    '0.320': '#f9bd00',
    '0.280': '#fad200',
    '0.240': '#fcea00',
    '0.200': '#fdff00',
    '0.150': '#a9ff00',
    '0.100': '#4fff00',
    '0.050': '#46e800',
    '0.000': '#34b300',
  },
  hcho: {
    '1.000': '#ff0000',
    '0.750': '#f1fc14',
    '0.625': '#bcfe58',
    '0.500': '#26feb7',
    '0.375': '#10cffd',
    '0.250': '#37a5ff',
    '0.125': '#0f64ff',
    '0.000': '#0000ff',
  },
  co: {
    '1.000': '#f50000',
    '0.833': '#f50000',
    '0.667': '#fc9900',
    '0.533': '#f5f100',
    '0.333': '#88f700',
    '0.000': '#00ff00',
  },
  so2: {
    '1.000': '#7e0023',
    '0.500': '#99004c',
    '0.297': '#ff0000',
    '0.094': '#ff7e00',
    '0.031': '#ffff00',
    '0.000': '#00ff00',
  },
  tmp: {
    '1.0': '#e84444',
    '0.675': '#e84444', // 35
    '0.65': '#f08227', // 30
    '0.625': '#e6af2d', // 25
    '0.6': '#e6dc33', // 20
    '0.575': '#a1e632', // 15
    '0.55': '#00dc00', // 10
    '0.525': '#00c8c7', // 5
    '0.50': '#01a0ff', // 0
  },
  pre: {
    '1.000': '#eb25ec',
    '0.200': '#0010dd',
    '0.040': '#67cbff',
    '0.000': '#99fe99',
  },
  vis: {
    '1.000': 'rgb(193, 238, 253)',
    '0.667': '#92d9ff',
    '0.333': '#93de2f',
    '0.167': '#7afb33',
    '0.100': '#fffe00',
    '0.067': '#fcb346',
    '0.033': '#fe5104',
    '0.017': '#b30101',
    '0.007': '#9c00ff',
    '0.000': 'rgb(115, 37, 249)',
  },
  rh: {
    '1.000': '#ef0082',
    '0.950': '#e84444',
    '0.900': '#f08227',
    '0.850': '#e6af2d',
    '0.750': '#e6dc33',
    '0.650': '#a1e632',
    '0.550': '#00dc00',
    '0.450': '#00c8c7',
    '0.000': '#01a0ff',
  },
  prs: {
    '1.000': '#ef0082',
    '0.972': '#e84444',
    '0.944': '#f08227',
    '0.917': '#e6af2d',
    '0.889': '#e6dc33',
    '0.861': '#a1e632',
    '0.833': '#00dc00',
    '0.806': '#00c8c7',
    '0.778': '#01a0ff',
    '0.000': '#288cf9',
  },
  dmask: {
    '1.0': '#faba01',
    '0.0': '#5459AB',
  },
  'wiu-wiv': {
    '1.000': '#2C0100',
    '0.859': '#2C0100',
    '0.729': '#631B1C',
    '0.602': '#901E50',
    '0.486': '#C6476E',
    '0.377': '#D77A4F',
    '0.278': '#D69A46',
    '0.190': '#CAD643',
    '0.116': '#4EBF4A',
    '0.053': '#52AC93',
    '0.007': '#4284BA',
    '0.000': '#5459AB',
  },
  fnr: {
    '1.000': '#d43931',
    '0.667': '#ffd270',
    '0.333': '#d8dbc3',
    '0.167': '#8996d0',
    '0.000': '#2662d6',
  },
};

// 深色tooltip
export const baseChartTooltipDomStyle = {
  'g2-tooltip': {
    background: '#1C1D24',
    boxShadow: 'none',
  },
  'g2-tooltip-title': {
    color: 'white',
  },
  'g2-tooltip-list-item': {
    color: 'white',
  },
};

// xAxis 虚线
export const dashedXAxisGridLine = {
  line: {
    style: {
      lineDash: [4, 4],
    },
  },
};

export const baseGeojsonLayerConfig = {
  data: '/assets/geojson/450000_full.json',
  pickable: true,
  stroked: true,
  getLineWidth: 4,
  lineWidthMinPixels: 2,
  getLineColor: [255, 255, 255],
  getFillColor: () => [255, 0, 0, Math.random() * 100 + 50],
};

export const chartColor = [
  '#5B8FF9',
  '#CDDDFD',
  '#61DDAA',
  '#CDF3E4',
  '#65789B',
  '#CED4DE',
  '#F6BD16',
  '#FCEBB9',
  '#7262FD',
  '#D3CEFD',
  '#CCCCCC',
  '#D3EEF9',
];

export const mobileRegex = /^1[3456789]\d{9}$/;

export const getMatchRegionCode: (code: number | string) => number[] = (code) => {
  return regionData.children.reduce(
    (prev, cur) => {
      if (cur.value === code) {
        return [...prev, cur.value];
      }

      const find = cur.children.find((child) => child.value === code);
      if (find) {
        return [...prev, cur.value, find.value];
      }

      return prev;
    },
    [450000],
  );
};

export const exportFile = (data: any, name: string, suffix = '.xls') => {
  if (!data) return;

  const url = window.URL.createObjectURL(new Blob([data]));
  const link = document.createElement('a');

  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `${name}${suffix}`);

  document.body.appendChild(link);
  link.click();

  document.body.removeChild(link);
  message.success('导出成功');
};

export const pageSizeOptions = ['10', '20', '50', '100'];
export const pollutionStatus: Record<string, string> = {
  1: '生产',
  2: '倒闭',
  3: '未启用',
  4: '被查封',
  5: '停产',
};

export const pollutionLevel: Record<string, string> = {
  1: '普通',
  99: '重点',
};

export const showTableTotal = (total: number | undefined, range: number[]) => `第${range[0]}-${range[1]}条，共${total}条`;

export const getRgbaColorRegex = /^rgb\((.+)\)$/;
export const rankColors: string[] = [
  'rgb(0, 228, 0)',
  'rgb(255, 255, 0)',
  'rgb(255, 126, 0)',
  'rgb(255, 0, 0)',
  'rgb(153, 0, 76)',
  'rgb(126, 0, 35)',
];
export const warningRankText = ['优', '良', '轻度', '中度', '重度', '严重'];

export const getLevelByRegionCode = (regionCode: string | number | undefined) => {
  if (!regionCode) return 0;
  if (/^[1-9]{2}0000$/.test(`${regionCode}`)) {
    return 1;
  }
  if (+`${regionCode}`.slice(4) > 0) {
    return 3;
  }
  return 2;
};

export const getRegionOptsByUserRegionCode = (regionCode: number | string | undefined) => {
  if (!regionCode) return [regionData];

  const level = getLevelByRegionCode(regionCode);
  const [, city, county] = getMatchRegionCode(regionCode);

  return [regionData].map((region) => {
    return {
      ...region,
      disabled: level !== 1,
      children: region.children.map((lv2) => {
        return {
          ...lv2,
          disabled: (level !== 2 || lv2.value !== city || !city) && level !== 1,
          children: lv2.children.map((lv3) => {
            return {
              ...lv3,
              disabled: level === 3 && lv3.value !== county,
            };
          }),
        };
      }),
    };
  });
};

export const getRegionOptsByUserRegionCode2 = (regionCode: number | undefined) => {
  if (!regionCode) return [regionData];

  const [, city] = getMatchRegionCode(regionCode);

  return [regionData].map((region) => {
    return {
      ...region,
      children: region.children.map((lv2) => {
        return lv2.value === city || !city
          ? {
            ...lv2,
            children: [],
          }
          : {
            ...lv2,
            disabled: true,
          };
      }),
    };
  });
};

export const pollutionInspectStatus: Record<number | string, string> = {
  1: '待核查',
  2: '核查中',
  3: '误报',
  99: '已核查',
};

export type AqiPollutionType = 'pm25' | 'aqi' | 'pm10' | 'no2' | 'so2' | 'o3' | 'co';

// 国控省控数据类型
export const stationTypeOptions: {
  label: string;
  value: AqiPollutionType;
}[] = [
    {
      label: 'AQI',
      value: 'aqi',
    },
    {
      label: 'PM₂.₅',
      value: 'pm25',
    },
    {
      label: 'PM₁₀',
      value: 'pm10',
    },
    {
      label: 'NO₂',
      value: 'no2',
    },
    {
      label: 'O₃',
      value: 'o3',
    },
    {
      label: 'SO₂',
      value: 'so2',
    },
    {
      label: 'CO',
      value: 'co',
    },
  ];

export const getMatchStationType = (key: string, type: 'label' | 'value') => {
  const find = stationTypeOptions.find((item) => item[type] === key);

  return find;
};

export const satRemoteOptions: {
  label: string;
  value: string;
  title: string;
  subTitle: string;
  disabledAggs?: Agg[];
}[] = [
    {
      label: 'PM₂.₅',
      value: 'pm25',
      title: 'PM₂.₅',
      subTitle: '',
      disabledAggs: [],
    },
    {
      label: 'PM₁₀',
      value: 'pm10',
      title: 'PM₁₀',
      subTitle: '',
      disabledAggs: [],
    },
    {
      label: 'AOD',
      value: 'aod',
      title: 'AOD',
      subTitle: '',
      disabledAggs: [],
    },
    {
      label: 'O3(GK2B)',
      value: 'gk2b_o3',
      title: 'O₃',
      subTitle: 'GK2B',
      disabledAggs: [],
    },
    {
      label: 'O3(S5P)',
      value: 'o3',
      title: 'O₃',
      subTitle: 'S5P',
      disabledAggs: ['hourly'],
    },
    {
      label: 'NO₂(GK2B)',
      value: 'gk2b_no2',
      title: 'NO₂',
      subTitle: 'GK2B',
      disabledAggs: [],
    },
    {
      label: 'NO₂(S5P)',
      value: 'no2',
      title: 'NO₂',
      subTitle: 'S5P',
      disabledAggs: ['hourly'],
    },
    {
      label: 'HCHO(GK2B)',
      value: 'gk2b_hcho',
      title: 'HCHO',
      subTitle: 'GK2B',
      disabledAggs: [],
    },
    {
      label: 'HCHO(S5P)',
      value: 'hcho',
      title: 'HCHO',
      subTitle: 'S5P',
      disabledAggs: ['hourly'],
    },
    {
      label: 'SO₂',
      value: 'so2',
      title: 'SO₂',
      subTitle: '',
      disabledAggs: ['hourly'],
    },
    {
      label: 'CO',
      value: 'co',
      title: 'CO',
      subTitle: '',
      disabledAggs: ['hourly'],
    },
  ];

export const heatSourceOptions: {
  label: string;
  value: string;
}[] = [
    {
      label: '秸秆火',
      value: 'straw',
    },
    {
      label: '林火',
      value: 'forest',
    },
    {
      label: '工业热源',
      value: 'industryHeat',
    },
  ];

export const levelOptionsFromApi = [
  {
    label: '轻度',
    value: 1,
  },
  {
    label: '中度',
    value: 2,
  },
  {
    label: '重度',
    value: 3,
  },
  {
    label: '严重',
    value: 4,
  },
];

// 国/省控
export const stationPollutionValuesAndColors: Record<string, any> = {
  aqi: {
    unit: '',
    formula: 'AQI',
    values: [
      { min: 300, color: 'rgb(126,0,35) ' },
      { min: 200, max: 300, color: 'rgb(153,0,76)' },
      { min: 150, max: 200, color: 'rgb(255,0,0)' },
      { min: 100, max: 150, color: 'rgb(255,126,0)' },
      { min: 50, max: 100, color: 'rgb(255,255,0)' },
      { min: 0, max: 50, color: 'rgb(0,288,0)' },
    ],
  },
  pm25: {
    unit: 'μg/m³',
    formula: 'PM₂.₅',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 250, color: 'rgb(126,0,35)' },
      { min: 150, max: 250, color: 'rgb(153,0,76)' },
      { min: 115, max: 150, color: 'rgb(255,0,0)' },
      { min: 75, max: 115, color: 'rgb(255,126,0)' },
      { min: 35, max: 75, color: 'rgb(255,255,0)' },
      { min: 0, max: 35, color: 'rgb(0,288,0)' },
    ],
  },
  pm10: {
    unit: 'μg/m³',
    formula: 'PM₁₀',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 420, color: 'rgb(126,0,35)' },
      { min: 350, max: 420, color: 'rgb(153,0,76)' },
      { min: 250, max: 350, color: 'rgb(255,0,0)' },
      { min: 150, max: 250, color: 'rgb(255,126,0)' },
      { min: 50, max: 150, color: 'rgb(255,255,0)' },
      { min: 0, max: 50, color: 'rgb(0,288,0)' },
    ],
  },
  so2: {
    unit: 'μg/m³',
    formula: 'SO₂',
    equalMax: true,
    roundFunc: function (val: number) {
      const number = Number(val);
      return number ? number.toFixed(1) : 0;
    },
    values: [
      { min: 1600, color: 'rgb(126,0,35)' },
      { min: 800, max: 1600, color: 'rgb(153,0,76)' },
      { min: 475, max: 800, color: 'rgb(255,0,0)' },
      { min: 150, max: 475, color: 'rgb(255,126,0)' },
      { min: 50, max: 150, color: 'rgb(255,255,0)' },
      { min: 0, max: 50, color: 'rgb(0,288,0)' },
    ],
  },
  no2: {
    unit: 'μg/m³',
    formula: 'NO₂',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 565, color: 'rgb(126,0,35)' },
      { min: 280, max: 565, color: 'rgb(153,0,76)' },
      { min: 180, max: 280, color: 'rgb(255,0,0)' },
      { min: 80, max: 180, color: 'rgb(255,126,0)' },
      { min: 40, max: 80, color: 'rgb(255,255,0)' },
      { min: 0, max: 40, color: 'rgb(0,288,0)' },
    ],
  },
  'no2-hour': {
    unit: 'μg/m³',
    formula: 'NO₂',
    equalMax: true,
    roundFunc: Math.trunc,
    values: [
      { min: 180, color: 'rgb(131,0,36)' },
      { min: 160, max: 180, color: 'rgb(157,0,86)' },
      { min: 140, max: 160, color: 'rgb(181,0,0)' },
      { min: 120, max: 140, color: 'rgb(198,0,0)' },
      { min: 100, max: 120, color: 'rgb(245,0,0)' },
      { min: 90, max: 100, color: 'rgb(246,100,0)' },
      { min: 80, max: 90, color: 'rgb(247,137,0)' },
      { min: 70, max: 80, color: 'rgb(249,189,0)' },
      { min: 60, max: 70, color: 'rgb(250,210,0)' },
      { min: 50, max: 60, color: 'rgb(252,234,0)' },
      { min: 40, max: 50, color: 'rgb(253,255 ,0)' },
      { min: 30, max: 40, color: 'rgb(218,255,0)' },
      { min: 25, max: 30, color: 'rgb(169,255,0)' },
      { min: 20, max: 25, color: 'rgb(79,255,0)' },
      { min: 15, max: 20, color: 'rgb(70,232,0)' },
      { min: 10, max: 15, color: 'rgb(62,207,0)' },
      { min: 0, max: 10, color: 'rgb(52,179,0) ' },
    ],
  },
  'no2-day': {
    unit: 'μg/m³',
    formula: 'NO₂',
    equalMax: true,
    roundFunc: Math.trunc,
    values: [
      { min: 180, color: 'rgb(131,0,36)' },
      { min: 160, max: 180, color: 'rgb(157,0,86)' },
      { min: 140, max: 160, color: 'rgb(181,0,0)' },
      { min: 120, max: 140, color: 'rgb(198,0,0)' },
      { min: 100, max: 120, color: 'rgb(245,0,0)' },
      { min: 90, max: 100, color: 'rgb(246,100,0)' },
      { min: 80, max: 90, color: 'rgb(247,137,0)' },
      { min: 70, max: 80, color: 'rgb(249,189,0)' },
      { min: 60, max: 70, color: 'rgb(250,210,0)' },
      { min: 50, max: 60, color: 'rgb(252,234,0)' },
      { min: 40, max: 50, color: 'rgb(253,255 ,0)' },
      { min: 30, max: 40, color: 'rgb(218,255,0)' },
      { min: 25, max: 30, color: 'rgb(169,255,0)' },
      { min: 20, max: 25, color: 'rgb(79,255,0)' },
      { min: 15, max: 20, color: 'rgb(70,232,0)' },
      { min: 10, max: 15, color: 'rgb(62,207,0)' },
      { min: 0, max: 10, color: 'rgb(52,179,0) ' },
    ],
  },
  co: {
    unit: 'molec./c㎡',
    formula: 'CO',
    equalMax: true,
    values: [
      { min: 36, color: 'rgb(126,0,35)' },
      { min: 24, max: 36, color: 'rgb(153,0,76)' },
      { min: 14, max: 24, color: 'rgb(255,0,0)' },
      { min: 4, max: 14, color: 'rgb(255,126,0)' },
      { min: 2, max: 4, color: 'rgb(255,255,0)' },
      { min: 0, max: 2, color: 'rgb(0,288,0)' },
    ],
  },
  'co-hour': {
    unit: 'mg/m³',
    formula: 'CO',
    equalMax: true,
    values: [
      { min: 36, color: 'rgb(126,0,35)' },
      { min: 24, max: 36, color: 'rgb(153,0,76)' },
      { min: 14, max: 24, color: 'rgb(255,0,0)' },
      { min: 4, max: 14, color: 'rgb(255,126,0)' },
      { min: 2, max: 4, color: 'rgb(255,255,0)' },
      { min: 0, max: 2, color: 'rgb(0,288,0)' },
    ],
  },
  'co-day': {
    unit: 'mg/m³',
    formula: 'CO',
    equalMax: true,
    values: [
      { min: 36, color: 'rgb(126,0,35)' },
      { min: 24, max: 36, color: 'rgb(153,0,76)' },
      { min: 14, max: 24, color: 'rgb(255,0,0)' },
      { min: 4, max: 14, color: 'rgb(255,126,0)' },
      { min: 2, max: 4, color: 'rgb(255,255,0)' },
      { min: 0, max: 2, color: 'rgb(0,288,0)' },
    ],
  },
  o3: {
    unit: 'μg/m³',
    formula: 'O₃',
    roundFunc: Math.trunc,
    equalMax: true,
    values: [
      { min: 800, color: 'rgb(126,0,35)' },
      { min: 400, max: 800, color: 'rgb(153,0,76)' },
      { min: 300, max: 400, color: 'rgb(255,0,0)' },
      { min: 200, max: 300, color: 'rgb(255,126,0)' },
      { min: 160, max: 200, color: 'rgb(255,255,0)' },
      { min: 0, max: 160, color: 'rgb(0,288,0)' },
    ],
  },
  'o3-hour': {
    unit: 'μg/m³',
    formula: 'O₃',
    equalMax: true,
    values: [
      { min: 800, color: 'rgb(126,0,35)' },
      { min: 400, max: 800, color: 'rgb(153,0,76)' },
      { min: 300, max: 400, color: 'rgb(255,0,0)' },
      { min: 200, max: 300, color: 'rgb(255,126,0)' },
      { min: 160, max: 200, color: 'rgb(255,255,0)' },
      { min: 0, max: 160, color: 'rgb(0,288,0)' },
    ],
  },
  'o3-day': {
    unit: 'μg/m³',
    equalMax: true,
    formula: 'O₃',
    values: [
      { min: 265, color: 'rgb(131, 0, 36),' },
      { min: 245, max: 265, color: 'rgb(157, 0, 86)' },
      { min: 230, max: 245, color: 'rgb(198, 0, 0)' },
      { min: 215, max: 230, color: 'rgb(245, 0, 0)' },
      { min: 190, max: 215, color: 'rgb(246, 100 ,0)' },
      { min: 175, max: 190, color: 'rgb(247, 137, 0)' },
      { min: 160, max: 175, color: 'rgb(249, 189, 0)' },
      { min: 140, max: 160, color: 'rgb(250, 210, 0)' },
      { min: 120, max: 140, color: 'rgb(252, 234, 0)' },
      { min: 100, max: 120, color: 'rgb(253, 255 ,0)' },
      { min: 75, max: 100, color: 'rgb(169, 255, 0)' },
      { min: 50, max: 75, color: 'rgb(79, 255, 0)' },
      { min: 25, max: 50, color: 'rgb(70,232,0)' },
      { min: 0, max: 25, color: 'rgb(52,179, 0) ' },
    ],
  },
  hcho: {
    unit: 'molec./c㎡',
    equalMax: true,
    formula: 'HCHO',
    values: [
      { min: 80, max: 100, color: 'rgb(255,0,0)' },
      { min: 60, max: 80, color: 'rgb(241,252,20)' },
      { min: 50, max: 60, color: 'rgb(188,254,88)' },
      { min: 40, max: 50, color: 'rgb(38,254,183)' },
      { min: 30, max: 40, color: 'rgb(16,207,253)' },
      { min: 20, max: 30, color: 'rgb(55,165,255)' },
      { min: 10, max: 20, color: 'rgb(15,100,255)' },
      { min: 0, max: null, color: 'rgb(0,0,255) ' },
    ],
  },
};

// 卫星遥感
export const o3RemoteSensingValuesAndColors = {
  unit: 'μg/m³',
  formula: 'O₃_S5P',
  roundFunc: Math.trunc,
  equalMax: true,
  values: [
    { min: 250, color: 'rgb(157, 0, 86)' },
    { min: 200, max: 250, color: 'rgb(198, 0, 0)' },
    { min: 180, max: 200, color: 'rgb(245, 0, 0)' },
    { min: 160, max: 180, color: 'rgb(246, 100 ,0)' },
    { min: 140, max: 160, color: 'rgb(247, 137, 0)' },
    { min: 120, max: 140, color: 'rgb(249, 189, 0)' },
    { min: 100, max: 120, color: 'rgb(250, 210, 0)' },
    { min: 90, max: 100, color: 'rgb(252, 234, 0)' },
    { min: 80, max: 90, color: 'rgb(253, 255 ,0)' },
    { min: 70, max: 80, color: 'rgb(169, 255, 0)' },
    { min: 60, max: 70, color: 'rgb(79, 255, 0)' },
    { min: 50, max: 60, color: 'rgb(70,232,0)' },
    { max: 50, color: 'rgb(52,179, 0)' },
  ],
};
export const hchoRemoteSensingValuesAndColors = {
  unit: '1e16molec./cm²',
  equalMax: true,
  formula: 'HCHO_S5P',
  roundFunc: (v: any) => {
    const correctedValue = v / 1e16;
    return `${correctedValue.toFixed(2)}`;
  },
  values: [
    { min: 1.4, max: 1.6, color: 'rgb(247,3,23)' },
    { min: 1.2, max: 1.4, color: 'rgb(238,253,21)' },
    { min: 1, max: 1.2, color: 'rgb(187,255,91)' },
    { min: 0.8, max: 1, color: 'rgb(85,253,183)' },
    { min: 0.6, max: 0.8, color: 'rgb(71,205,253)' },
    { min: 0.4, max: 0.6, color: 'rgb(68,163,253)' },
    { min: 0.2, max: 0.4, color: 'rgb(45,98,255)' },
    { min: 0, max: 0.2, color: 'rgb(31,20,250)' },
  ],
};
// 卫星遥感 原来的 这么有问题 估计需要留意一下
// export const remoteSensingValuesAndColors: Record<string, any> = {
//   pm25: stationPollutionValuesAndColors.pm25,
//   pm10: stationPollutionValuesAndColors.pm10,
//   no2: stationPollutionValuesAndColors['no2-hour'],
//   gk2b_no2: stationPollutionValuesAndColors['no2-hour'],
//   o3: o3RemoteSensingValuesAndColors,
//   gk2b_o3: o3RemoteSensingValuesAndColors,

//   so2: {
//     unit: 'du',
//     equalMax: true,
//     formula: 'SO₂',
//     roundFunc: (v: any) => (Number(v) ? Number(v).toFixed(1) : 0),
//     values: [
//       { min: 1.4, max: 1.6, color: 'rgb(252,50,0)' },
//       { min: 1.2, max: 1.4, color: 'rgb(250,172,0)' },
//       { min: 1, max: 1.2, color: 'rgb(241,252,20)' },
//       { min: 0.8, max: 1, color: 'rgb(188,254,88)' },
//       { min: 0.6, max: 0.8, color: 'rgb(38,254,183)' },
//       { min: 0.4, max: 0.6, color: 'rgb(16,207,253)' },
//       { min: 0.2, max: 0.4, color: 'rgb(54,117,255)' },
//       { min: 0, max: 0.2, color: 'rgb(22,33,198) ' },
//     ],
//   },
//   co: {
//     unit: 'molec./c㎡',
//     equalMax: true,
//     formula: 'CO',
//     roundFunc: (v: any) => {
//       const correctedValue = (v * 6.022141e19) / 1e17;
//       return `${Math.round(10 * correctedValue) / 1000}e19`;
//     },
//     values: [
//       { min: 0.25, max: 0.3, color: 'rgb(245,0,0)' },
//       { min: 0.2, max: 0.25, color: 'rgb(252,153,0)' },
//       { min: 0.16, max: 0.2, color: 'rgb(245,241,0)' },
//       { min: 0.1, max: 0.16, color: 'rgb(136, 247, 0)' },
//       { min: 0, max: 0.1, color: 'rgb(0,254,0) ' },
//     ],
//   },
//   aod: {
//     equalMax: true,
//     formula: 'AOD',
//     roundFunc: (v: any) => {
//       const number = Number(v);
//       return number ? number.toFixed(2) : 0;
//     },
//     values: [
//       { min: 2.8, max: 3, color: 'rgb(255,0,0)' },
//       { min: 2.6, max: 2.8, color: 'rgb(237,103,3)' },
//       { min: 2.4, max: 2.6, color: 'rgb(253,147,2' },
//       { min: 2.2, max: 2.4, color: 'rgb(253,203,2)' },
//       { min: 2, max: 2.2, color: 'rgb(253,227,2)' },
//       { min: 1.8, max: 2, color: 'rgb(255,255,0)' },
//       { min: 1.6, max: 1.8, color: 'rgb(220,251,4)' },
//       { min: 1.4, max: 1.6, color: 'rgb(186,239,1)' },
//       { min: 1.2, max: 1.4, color: 'rgb(128,255,0)' },
//       { min: 1, max: 1.2, color: 'rgb(128,255,128)' },
//       { min: 0.8, max: 1, color: 'rgb(135,255,255)' },
//       { min: 0.6, max: 0.8, color: 'rgb(95,205,255)' },
//       { min: 0.4, max: 0.6, color: 'rgb(55,165,255)' },
//       { min: 0.2, max: 0.4, color: 'rgb(15,100,255)' },
//       { min: 0, max: 0.2, color: 'rgb(40,0,255) ' },
//     ],
//   },
//   hcho: hchoRemoteSensingValuesAndColors,
//   gk2b_hcho: hchoRemoteSensingValuesAndColors,
//   ch4: {
//     unit: 'PPM',
//     formula: 'CH₄',
//     equalMax: true,
//     values: [
//       { min: 9, max: 10, color: 'rgb(239,0,130)' },
//       { min: 8, max: 9, color: 'rgb(232, 68, 68)' },
//       { min: 7, max: 8, color: 'rgb(240, 130, 39)' },
//       { min: 6, max: 7, color: 'rgb(230, 175, 45)' },
//       { min: 5, max: 6, color: 'rgb(230, 220, 51)' },
//       { min: 4, max: 5, color: 'rgb(161, 230 ,50)' },
//       { min: 3, max: 4, color: 'rgb(0, 220, 0)' },
//       { min: 2, max: 3, color: 'rgb(0, 200, 199)' },
//       { min: 1, max: 2, color: 'rgb(1,160,255)' },
//       { min: 0, max: 1, color: 'rgb(40,140, 249) ' },
//     ],
//   },
//   fnr: {
//     unit: '',
//     formula: '',
//     cn: '臭氧前体指示值',
//     equalMin: true,
//     values: [
//       { min: 6, max: null, color: 'rgb(212, 57 ,49)' },
//       { min: 4, max: 6, color: 'rgb(255, 210, 112)' },
//       { min: 2, max: 4, color: 'rgb(216, 219, 195)' },
//       { min: 1, max: 2, color: 'rgb(137,150,208)' },
//       { max: 2, color: 'rgb(38,98, 214)' },
//     ],
//   },
// };
export const remoteSensingValuesAndColors = remoteSensingValuesAndColorsDataDown

// 气象
export const weatherValuesAndColors: Record<string, any> = {
  tmp: {
    unit: '℃',
    cn: '温度',
    equalMax: true,
    values: [
      { min: 35, color: 'rgb(232, 68, 68)' },
      { min: 30, max: 35, color: 'rgb(240, 130, 39)' },
      { min: 25, max: 30, color: 'rgb(230, 175, 45)' },
      { min: 20, max: 25, color: 'rgb(230, 220, 51)' },
      { min: 15, max: 20, color: 'rgb(161, 230 ,50)' },
      { min: 10, max: 15, color: 'rgb(0, 220, 0)' },
      { min: 5, max: 10, color: 'rgb(0, 200, 199)' },
      { min: 0, max: 5, color: 'rgb(1,160,255)' },
      { max: 0, color: 'rgb(40,140, 249) ' },
    ],
  },
  vis: {
    unit: 'Km',
    cn: '能见度',
    equalMax: true,
    values: [
      { min: 30, color: 'rgb(193, 238, 253)' },
      { min: 20, max: 30, color: 'rgb(146, 217, 255)' },
      { min: 10, max: 20, color: 'rgb(147, 222, 47)' },
      { min: 5, max: 10, color: 'rgb(122, 251, 51)' },
      { min: 3, max: 5, color: 'rgb(255, 254, 0)' },
      { min: 2, max: 3, color: 'rgb(252, 179, 70)' },
      { min: 1, max: 2, color: 'rgb(254, 81, 4)' },
      { min: 0.5, max: 1, color: 'rgb(179, 1, 1)' },
      { min: 0.2, max: 0.5, color: 'rgb(156, 0, 255)' },
      { min: 0, max: 0.2, color: 'rgb(115, 37, 249)' },
    ],
  },
  rh: {
    unit: '%',
    cn: '湿度',
    equalMax: true,
    values: [
      { min: 100, color: 'rgb(239,0,130)' },
      { min: 95, max: 100, color: 'rgb(232, 68, 68)' },
      { min: 90, max: 95, color: 'rgb(240, 130, 39)' },
      { min: 85, max: 90, color: 'rgb(230, 175, 45)' },
      { min: 75, max: 85, color: 'rgb(230, 220, 51)' },
      { min: 65, max: 75, color: 'rgb(161, 230 ,50)' },
      { min: 55, max: 65, color: 'rgb(0, 220, 0)' },
      { min: 45, max: 55, color: 'rgb(0, 200, 199)' },
      { min: 0, max: 45, color: 'rgb(1,160,255)' },
      { max: 0, color: 'rgb(40,140, 249) ' },
    ],
  },
  pre: {
    unit: 'mm',
    cn: '降水量',
    equalMax: true,
    values: [
      { min: 50, color: 'rgb(235, 37, 236)' },
      { min: 10, max: 50, color: 'rgb(0, 16, 221)' },
      { min: 2, max: 10, color: 'rgb(103,203,255)' },
      { min: 0, max: 2, color: 'rgb(153,254, 153) ' },
    ],
  },
  prs: {
    unit: 'hPa',
    cn: '压强',
    equalMax: true,
    values: [
      { min: 1080, color: 'rgb(239,0,130)' },
      { min: 1050, max: 1080, color: 'rgb(232, 68, 68)' },
      { min: 1020, max: 1050, color: 'rgb(240, 130, 39)' },
      { min: 990, max: 1020, color: 'rgb(230, 175, 45)' },
      { min: 960, max: 990, color: 'rgb(230, 220, 51)' },
      { min: 930, max: 960, color: 'rgb(161, 230 ,50)' },
      { min: 900, max: 930, color: 'rgb(0, 220, 0)' },
      { min: 870, max: 900, color: 'rgb(0, 200, 199)' },
      { min: 840, max: 870, color: 'rgb(1,160,255)' },
      { min: 0, max: 840, color: 'rgb(40,140, 249) ' },
    ],
  },
  'wiu-wiv': {
    unit: 'm/s',
    cn: '风速',
    equalMax: true,
    values: [
      { min: 24.4, max: 28.4, color: 'rgb(44,1,0)' },
      { min: 20.7, max: 24.4, color: 'rgb(99,27,28)' },
      { min: 17.1, max: 20.7, color: 'rgb(144,30,80)' },
      { min: 13.8, max: 17.1, color: 'rgb(198,71,110)' },
      { min: 10.7, max: 13.8, color: 'rgb(215,122,79)' },
      { min: 7.9, max: 10.7, color: 'rgb(214,154,70)' },
      { min: 5.4, max: 7.9, color: 'rgb(202,214,67)' },
      { min: 3.3, max: 5.4, color: 'rgb(78,191,74)' },
      { min: 1.5, max: 3.3, color: 'rgb(82,172,147)' },
      { min: 0.2, max: 1.5, color: 'rgb(66,132,186)' },
      { min: 0, max: 0.2, color: 'rgb(84,89,171)' },
    ],
  },
};

export const getColorByValue = (
  data: {
    min: number;
    max?: number;
    color: string;
    equalMax?: boolean;
  }[],
  value: number,
) => {
  const find = data.find((item) => {
    if (typeof item.min !== 'undefined' && item.max && value >= item.min && value <= item.max) {
      return true;
    }

    if (!item.max && value > item.min) {
      return true;
    }

    return false;
  });

  return find ? find.color : 'rgb(0,0,0)';
};

export const assessmentPoTypes: {
  label: string;
  value: 'pm25' | 'pm10' | 'o3' | 'no2' | 'hcho';
  unit: string;
}[] = [
    {
      label: 'PM₂.₅',
      value: 'pm25',
      unit: 'μg/m³',
    },
    {
      label: 'PM₁₀',
      value: 'pm10',
      unit: 'μg/m³',
    },
    {
      label: 'O₃',
      value: 'o3',
      unit: 'μg/m³',
    },
    {
      label: 'HCHO',
      value: 'hcho',
      unit: 'molec./c㎡',
    },
    {
      label: 'NO₂',
      value: 'no2',
      unit: 'μg/m³',
    },
  ];

export const globalPoMapping: Record<string, string> = {
  pm25: 'PM₂.₅',
  'pm2.5': 'PM₂.₅',
  pm10: 'PM₁₀',
  o3: 'O₃',
  hcho: 'HCHO',
  no2: 'NO₂',
  co: 'CO',
  so2: 'SO₂',
  nh3: 'NH₃',
  nox: 'NOx',
};

export const downloadUseLink = (url: string, name: string) => {
  const linkEl = document.createElement('a');
  linkEl.href = url;
  linkEl.setAttribute('download', name);

  document.body.appendChild(linkEl);
  linkEl.click();
  document.body.removeChild(linkEl);
};

const menuKeys: Record<string, string[]> = {
  1: ['/overview'],
  2: ['/thematic-map', '/thematic-map-list'],
  3: ['/pollution-warning', '/pollution-warning-detail', '/pollution-early-warning'],
  4: ['/pollution-warning-statistics'],
  5: ['/cruising-route'],
  6: ['/source-of-pollution', '/details-of-pollution-source', '/pollution-form'],
  7: ['/pollution-source-statistics'],
  8: ['/comprehensive-assessment'],
  9: ['/user-management'],
  10: ['/rules-config'],
  11: ['/receive-monitoring'],
  12: ['/receiving-log'],
  13: ['/'],
  14: ['/system-log'],
};

export const getMatchMenuKey = (pathname: string) => {
  const key = Object.keys(menuKeys).reduce((prev: string, curKey: string) => {
    const current = menuKeys[curKey];

    if (current.includes(pathname)) {
      return `${curKey}`;
    }

    return prev;
  }, '1');

  return key;
};

export const getRegionLevel = (regionCode?: string | number) => {
  const code = String(regionCode);

  if (code[code.length - 1] !== '0') {
    return 3;
  }
  if (code[3] !== '0' || code[2] !== '0') {
    return 2;
  }

  return 1;
};

export const isFirespotType = (type: string) => type === 'straw' || type === 'forest' || type === 'industryHeat';

export const firespotMapping: Record<string, string> = {
  straw: '秸秆火',
  forest: '林火',
  industryHeat: '工业热源',
};

export const getProvinceCode = (regionCode: number | undefined) => {
  return regionCode ? `${String(regionCode).substr(0, 2)}0000` : '';
};

export const lonRegex = /^-?(\d{1,2}(\.\d{1,20})?|1[0-7]\d(\.\d{1,20})?|180)$/;
export const latRegex = /^([0-8]?\d{1}\.\d{0,20}|90\.0{0,20}|[0-8]?\d{1}|90)$/;

const lens = TIANDITU_KEYS.length;
const randomKey = TIANDITU_KEYS[Math.floor(Math.random() * lens)];

export const wmts = (type: 'img' | 'vec' | 'cia' | 'cva' | 'ter' | 'cta') =>
  [0, 1, 2, 3, 4, 5, 6, 7].map(
    (idx) =>
      `https://t${idx}.tianditu.gov.cn/${type}_w/wmts?tk=${randomKey}&layer=${type}&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}`,
  );

export const AlertColors: Record<number, RGBColor> = {
  4: [126, 0, 35],
  3: [153, 0, 76],
  2: [255, 0, 0],
  1: [255, 126, 0],
};
export const dataDownloadOptions = {
  pm25: {
    format: 'float',
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  pm10: {
    format: 'float',
    encoder: (v: number) => [Math.floor(v / 255), Math.floor(v % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => r * 255 + g,
  },
  so2: {
    format: 'float',
    encoder: (v: number) => [Math.floor((v * 1000) / 255), Math.floor((v * 1000) % 255), 0, 0],
    decoder: ([r, g]: [number, number]) => (r * 255 + g) / 1000,
  },
};

export const tilePrefix = 'http://tile.openrj.cn/svc/tile/texture/tile.webp';

interface TileParams {
  agg: string;
  type: string;
  time: string;
  token: string;
}
export const getTileUrl = (tileParams: TileParams, isUseTileLayer = false, z?: number) =>
  `${tilePrefix}?x={x}&y={y}${isUseTileLayer ? `&z=${z || '{z}'}` : ''}&${stringify(tileParams)}`;

type TextureParams = {
  startDate: string;
  endDate: string;
  tileToken: string;
  type: string;
};

export const getTextureUrl = (params: TextureParams, isUseTileLayer = false, z?: number) => {
  return `/api/texture/agg?x={x}&y={y}${isUseTileLayer ? `&z=${z || '{z}'}` : ''}&${stringify(params)}`;
};

export const createColorImageTileLayer = (dataUrl: string, visible: boolean, id: string, maxZoom = 7) => {
  return new TileLayer({
    ...tileLayerBaseConfig,
    id,
    data: dataUrl,
    maxZoom: 6,
    visible,
    extensions: [new MaskExtension()],
    // @ts-ignore
    // maskId: 'geojson-mask',
    // 专题图不需要
    maskId: '',
  });
};

export const useFormatDisplayName = () => {
  const formatDisplayName = useCallback((displayName: string) => {
    const find = Object.keys(globalPoMapping).find((item) => {
      return displayName.toLowerCase().includes(item)
    }
    );

    if (find) {
      const names = displayName.split('_')
      return names.length > 1 ?
        `${names[0].toLowerCase()
          .replace(new RegExp(find, 'g'), globalPoMapping[find])}_${names[1]}`
        : displayName
          .toLowerCase()
          .replace(new RegExp(find, 'g'), globalPoMapping[find]);
    }

    return displayName;
  }, []);
  return {
    formatDisplayName
  }
}
