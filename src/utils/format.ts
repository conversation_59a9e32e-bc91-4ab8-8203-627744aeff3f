import { downloadUseLink, exportFile } from '@/utils';
import html2canvas from 'html2canvas';
import { clone, first, isNil, last } from 'lodash';
import moment from 'moment';
import { stringify } from 'qs';

// 获取规范化数组
export function array_<T>(data: T[] | undefined): T[] {
  return data instanceof Array ? data.slice() : ([] as T[]);
}

// 获取规范化数组clone版本
export function array_clone<T>(data: T[] | undefined): T[] {
  return data instanceof Array ? clone(data) : ([] as T[]);
}
// 获取规范化对象
export function object_<T>(data: T | undefined): T {
  return data instanceof Object ? data : ({} as T);
}
// 获取规范化数字
export function number_(num: unknown) {
  const num_ = Number(num);
  return num_ || 0;
}
// 空值返回null
export function number_or_null(num: unknown): number | null {
  if (num === null || num === '') return null;
  const num_ = Number(num);
  return isNaN(num_) ? null : num_;
}

// 空值返回N/A
export function number_or_NA(num: unknown): number | 'N/A' {
  if (num === null) return 'N/A';
  const num_ = Number(num);
  return isNaN(num_) ? 'N/A' : num_;
}

// 空值返回'-'
export function number_or_never(num: unknown): number | '-' {
  if (num === null) return '-';
  const num_ = Number(num);
  return isNaN(num_) ? '-' : num_;
}
// 保留小数
export function number_fixed(number: unknown, fixed: unknown = 1) {
  const num_ = number_(number);
  return Number(num_.toFixed(number_(fixed)));
}

// 保留小数和_
export function number_or_never_fixed(number: unknown, fixed: unknown = 1) {
  const num_ = number_or_never(number);
  if (num_ === '-') return num_;
  return Number(num_.toFixed(number_(fixed)));
}

// 保留小数和_
export function number_or_null_fixed(number: unknown, fixed: unknown = 1) {
  const num_ = number_or_null(number);
  if (num_ === null) return num_;
  return Number(num_.toFixed(number_(fixed)));
}

// 获取有效参数
export function getFormatParams<T>(params: Partial<T>) {
  if (!params) return params;
  else if (typeof params !== 'object') return params;
  else {
    const new_params = Object.create(null);
    Object.entries(params).forEach(([key, value]) => {
      if (!isNil(value) && value !== '') Reflect.set(new_params, key, value);
    });
    return new_params as T;
  }
}

// 获取有效参数Str
export function getFormatParamsStr(params: unknown) {
  if (!params) return params;
  else if (typeof params !== 'object') return params;
  else {
    const new_params = Object.create(null);
    Object.entries(params).forEach(([key, value]) => {
      if (!isNil(value) && value !== '') Reflect.set(new_params, key, value);
    });
    return stringify(new_params);
  }
}
// 获取有效json
export function json_(data: string) {
  try {
    return JSON.parse(data);
  } catch (e) {
    return null;
  }
}

// 获取有效时间
export function date_(date: unknown, formatStr = 'YYYY/MM/DD') {
  if (typeof date === 'string') return moment(date).format(formatStr);
  return '-';
}

// ---导出回调---
export function handleExportSuccess(
  d: any,
  domId: string,
  title: string,
  cb?: () => void,
) {
  exportFile(d, title);
  const container = document.getElementById(domId);
  if (container) {
    setTimeout(() => {
      html2canvas(container).then((canvas) => {
        const url = canvas.toDataURL('image/jpeg');
        downloadUseLink(url, title);
        if (cb) cb();
      });
    }, 1000);
  }
}

// ---导出回调---
export function handleExportTable(d: any, title: string) {
  exportFile(d, title);
}

// 导出图表
export function handleExportChart(
  domId: string | HTMLElement,
  title_: string,
  cb?: unknown,
  needFull?: boolean,
) {
  const background = '#fff';
  const title = title_.replace(/\./g, '·') + '.jpg';
  const container =
    typeof domId === 'string' ? document.getElementById(domId) : domId;
  if (container) {
    const defaultBg = (container as HTMLDivElement).style.background;
    (container as HTMLDivElement).style.background = background;
    if (needFull)
      (container as HTMLDivElement).classList.add('full-height-important');
    window.requestAnimationFrame(() => {
      html2canvas(container).then((canvas) => {
        const url = canvas.toDataURL('image/jpeg');
        downloadUseLink(url, title);
        if (typeof cb === 'function') cb();
        (container as HTMLDivElement).style.background = defaultBg;
        if (needFull)
          setTimeout(
            () =>
              (container as HTMLDivElement).classList.remove(
                'full-height-important',
              ),
            500,
          );
      });
    });
  }
}

export interface IColorLevel {
  min?: number;
  max?: number;
  color: string;
}

export function getDecoder(val: IColorLevel[]) {
  // const info = array_(val);
  // const len = info.length;
  // if (len === 0) return {};
  // const firstLevel = first(info);
  // const endLevel = last(info);
  // const { min: topLevelMin, color: topLevelColor } = object_(firstLevel);
  // const { max: endLevelMax, color: endLevelColor } = object_(endLevel);
  // const diff = number_(topLevelMin) - number_(endLevelMax);
  // if (diff === 0) {
  //   return {
  //     '1.00': topLevelColor,
  //     '0.00': endLevelColor,
  //   };
  // }
  // const dict = info.reduce((d, model, idx) => {
  //   const { min: modelMin, color } = model;
  //   if (idx === 0) return { ...d, '1.00': color };
  //   if (idx === info?.length - 1) return { ...d, '-0.00': color };
  //   const modelDiff = number_(modelMin) - number_(endLevelMax);
  //   const per = (modelDiff / diff).toFixed(2);
  //   return { ...d, [per]: color };
  // }, {} as Record<string, string>);

  // return dict;

  // 走一张图里的
  const info = array_(val);
  const len = info.length;
  if (len === 0) return {};
  const firstLevel = first(info);
  const endLevel = last(info);
  const {
    min: topLevelMin,
    max: topLevelMax,
    color: topLevelColor,
  } = object_(firstLevel);
  const {
    max: endLevelMax,
    min: endLevelMin,
    color: endLevelColor,
  } = object_(endLevel);

  const realMax =
    typeof topLevelMax === 'number' ? topLevelMax : (topLevelMin as number);
  const realMin =
    typeof endLevelMin === 'number' ? endLevelMin : (endLevelMax as number);

  const diff = realMax - realMin;

  if (diff === 0) {
    return {
      '1.00': topLevelColor,
      '0.00': endLevelColor,
    };
  }
  const dict = info.reduce((d, model, idx) => {
    if (idx === 0) return { ...d, '1.00': model.color };
    if (idx === info?.length - 1) return { ...d, '0.00': model.color };
    // 去除头尾之后，此时min、max必然存在值
    const { min, max, color } = model;
    const modelDiff = (max === realMax ? min : max) - realMin;
    const per = (modelDiff / diff).toFixed(2);
    return { ...d, [per]: color };
  }, {} as Record<string, string>);
  return dict;
}

// 图表文字换行
export function formatterCreator(count: number) {
  return (e: string) => {
    const s = String(e)
      .split('')
      .reduce((str, word, idx) => {
        return str + ((idx + 1) % count ? word : word + '\n');
      }, '');
    return s;
  };
}
