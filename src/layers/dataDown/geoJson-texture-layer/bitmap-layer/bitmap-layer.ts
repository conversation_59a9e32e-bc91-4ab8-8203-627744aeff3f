import ColorRamp from '@/layers/color-ramp';
import TextureDrawer from '@/layers/texture-drawer';
import { getTranslateImageData } from '@/utils/image';
import { COORDINATE_SYSTEM, picking, project32 } from '@deck.gl/core';
import type { UpdateStateInfo } from '@deck.gl/core/lib/layer';
import { mergeShaders } from '@deck.gl/core/src/utils/shader';
import { BitmapLayer } from '@deck.gl/layers';
import GL from '@luma.gl/constants';
import { Transform } from '@luma.gl/engine';
import { Buffer } from '@luma.gl/webgl';
import fs from './bitmap-layer-fragment';
// @ts-ignore
import vs from './bitmap-layer-vertex';

/*
 * @class
 * @param {object} props
 * @param {number} props.transparentColor - color to interpret transparency to
 * @param {number} props.tintColor - color bias
 */
export default class TextureBitmapLayer extends BitmapLayer<any, any> {
  constructor(props1: any, props2?: any) {
    super(props1, {
      ...props2,
      textureParameters: {
        [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
        [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
      },
    });
  }
  initializeState(params: any) {
    this.init();
    this.avgInit();
    super.initializeState(params);
    const { maskImageSrc } = this.props;
    const { gl } = this.context;

    if (maskImageSrc) {
      getTranslateImageData(maskImageSrc).then((data) => {
        const image = new Image();
        image.src = maskImageSrc;

        this.setState({
          maskImage: TextureDrawer.createTexture(gl, {
            width: 256,
            height: 256,
            data,
          }),
        });
      });
    }
    this.setState({
      layerType: 'texture-bitmap-layer',
    });
  }
  init() {
    const r = new Buffer(this.context.gl, {
      byteLength: 4,
    });
    const g = new Buffer(this.context.gl, {
      byteLength: 4,
    });
    const b = new Buffer(this.context.gl, {
      byteLength: 4,
    });
    const a = new Buffer(this.context.gl, {
      byteLength: 4,
    });
    const transform = new Transform(this.context.gl, {
      vs: `
        uniform sampler2D bitmapTexture;
        uniform float invalueX;
        uniform float invalueY;
        varying float r;
        varying float g;
        varying float b;
        varying float a;

        void main()
        {
          vec4 feedback = texture2D(bitmapTexture, vec2(invalueX / 256.0, invalueY / 256.0));
          r = feedback.r * 255.0;
          g = feedback.g * 255.0;
          b = feedback.b * 255.0;
          a = feedback.a * 255.0;
        }
      `,
      varyings: ['r', 'g', 'b', 'a'],
      elementCount: 1 * 1,
      feedbackBuffers: {
        r,
        g,
        b,
        a,
      },
      modules: [project32],
    });
    this.setState({
      computeTransform: transform,
    });
  }
  /**
   * mask_bounds
   * 0,0 ———— 1,0
   * |         |
   * |         |
   * 0,1 ———— 1,1
   */
  avgInit() {
    const { min, max, decoder = '0.0' } = this.props;
    const invalueX = new Buffer(this.context.gl, {
      data: new Float32Array(256 * 256).map((_, index) => Math.floor(index / 256)),
    });
    const invalueY = new Buffer(this.context.gl, {
      data: new Float32Array(256 * 256).map((_, index) => index % 256),
    });
    const feedback = new Buffer(this.context.gl, {
      byteLength: 256 * 256 * 4,
    });
    const pixel = new Buffer(this.context.gl, {
      byteLength: 256 * 256 * 4,
    });
    const transform = new Transform(this.context.gl, {
      vs: `
        #define SHADER_NAME bitmap-layer-vertex-shader
        uniform sampler2D bitmapTexture;
        uniform sampler2D mask_texture;
        uniform vec4 bounds;
        uniform vec4 mask_bounds;
        uniform int mask_channel;
        uniform bool mask_enabled;
        uniform bool mask_maskByInstance;
        attribute float invalueX;
        attribute float invalueY;
        varying float feedback;
        varying float pixel;
        varying vec2 mask_texCoords;

        vec2 mask_getCoords(vec4 position) {
          return (position.xy - mask_bounds.xy) / (mask_bounds.zw - mask_bounds.xy);
        }

        bool mask_is_in_bounds(vec2 texCoords) {
          if (!mask_enabled) {
            return true;
          }
          vec4 maskColor = texture2D(mask_texture, texCoords);
          float maskValue = 1.0;
          if (mask_channel == 0) {
            maskValue = maskColor.r;
          } else if (mask_channel == 1) {
            maskValue = maskColor.g;
          } else if (mask_channel == 2) {
            maskValue = maskColor.b;
          } else if (mask_channel == 3) {
            maskValue = maskColor.a;
          }
          return maskValue < 0.5;
        }

        float decoder(vec4 data) {
          ${(decoder.includes('return') ? '' : 'return ') + decoder.replace(/{min}/g, min?.toFixed(1)).replace(/{max}/g, max?.toFixed(1))};
        }
        void main() {
          vec4 tile_common_start_position = project_position(vec4(bounds.xy, 0.0, 0.0));
          vec4 tile_common_end_position = project_position(vec4(bounds.zw, 0.0, 0.0));
          vec4 mask_common_position = vec4(vec2(invalueX, 256.0 - invalueY) / 256.0 * (tile_common_end_position.xy - tile_common_start_position.xy) + tile_common_start_position.xy, 0.0, 0.0);
          mask_texCoords = mask_getCoords(mask_common_position);
          bool inMask = mask_is_in_bounds(mask_texCoords.xy);
          vec4 data = texture2D(bitmapTexture, vec2(invalueX / 256.0, invalueY / 256.0));
          if(inMask && !(data.r == 0.0 && data.g == 0.0 && data.b == 0.0)){
            feedback = decoder(data);
            pixel = 1.0;
          } else {
            feedback = 0.0;
            pixel = 0.0;
          }
        }
      `,
      varyings: ['feedback', 'pixel'],
      elementCount: 256 * 256,
      sourceBuffers: {
        invalueX,
        invalueY,
      },
      feedbackBuffers: {
        feedback,
        pixel,
      },
      modules: [project32],
    });
    this.setState({
      avgTransform: transform,
    });
  }
  readPixelsToArray(invalueX: number, invalueY: number) {
    const { model, computeTransform } = this.state;
    computeTransform.run({
      uniforms: {
        ...model.uniforms,
        invalueX,
        invalueY,
      },
    });

    const r = computeTransform.getBuffer('r')?.getData();
    const g = computeTransform.getBuffer('g')?.getData();
    const b = computeTransform.getBuffer('b')?.getData();
    const a = computeTransform.getBuffer('a')?.getData();
    return [r[0], g[0], b[0], a[0]];
  }
  getAvgResult() {
    const { model, avgTransform } = this.state;
    const { image, bounds } = this.props;
    const { viewport } = this.context;
    const { width, height } = viewport;
    const lt = viewport.projectFlat([bounds[0], bounds[1]]);
    const rb = viewport.projectFlat([bounds[2], bounds[3]]);
    const ltPixel = viewport.project([bounds[0], bounds[3]]);
    const rbPixel = viewport.project([bounds[2], bounds[1]]);
    // 判断是否在屏幕内
    if (ltPixel[0] > width || ltPixel[1] > height || rbPixel[0] < 0 || rbPixel[1] < 0) {
      return {
        computeResult: 0,
        pixels: 0,
        kmPerPixel: 0,
      };
    } else {
      avgTransform.run({
        uniforms: {
          ...model.uniforms,
          bitmapTexture: image,
          bounds,
        },
      });
      const kmPerPixel =
        (((((rb[0] - lt[0]) / 255) * (rb[1] - lt[1])) / 255) *
          this.context?.viewport?.scale *
          this.context?.viewport?.scale *
          this.context?.viewport?.metersPerPixel *
          this.context?.viewport?.metersPerPixel) /
          1000 /
          1000 || 0;
      const computeResult = avgTransform.getBuffer('feedback')?.getData();
      const pixels = avgTransform.getBuffer('pixel')?.getData();
      let computeAll = 0;
      let pixelsNumber = 0;
      for (let i = 0; i < computeResult.length; i++) {
        if (pixels[i]) {
          computeAll += computeResult[i];
          pixelsNumber += 1;
        }
      }
      return {
        computeResult: computeAll / pixelsNumber,
        pixels: pixelsNumber,
        kmPerPixel,
      };
    }
  }
  getShaders() {
    let shaders = {};
    const { min, max, decoder, opacity, filtersChannel } = this.props;
    if (decoder) {
      shaders = {
        vs,
        fs: fs
          .replace(
            /{DECODER}/g,
            (decoder.includes('return') ? '' : 'return ') + decoder.replace(/{min}/g, min?.toFixed(5)).replace(/{max}/g, max?.toFixed(5)),
          )
          .replace(/{OPACITY_DECODER}/g, opacity ? Number(opacity).toFixed(3) : '1.0')
          .replace(/{filtersChannel}/g, filtersChannel || 0),
        modules: [project32, picking],
      };
    } else {
      shaders = {
        vs,
        fs: fs
          .replace('{DECODER}', 'return 0.0;')
          .replace(/{OPACITY_DECODER}/g, opacity ? Number(opacity).toFixed(3) : '1.0')
          .replace(/{filtersChannel}/g, filtersChannel || 0),
        modules: [project32, picking],
      };
    }
    for (const extension of this.props.extensions) {
      shaders = mergeShaders(shaders, extension.getShaders.call(this, extension));
    }

    return shaders;
  }

  updateState(opt: UpdateStateInfo<any>) {
    try {
      super.updateState(opt);

      const { props, oldProps, context } = opt;
      const {
        colorRamp: oldColorRamp,
        decoder: oldDecoder,
        min: oldMin,
        max: oldMax,
        filters: oldFilters,
        filtersChannel: oldFiltersChannel,
        gradualChange: oldGradualChange,
        visible: oldVisible,
        opacity: oldOpacity,
        maskImageSrc: oldMaskImageSrc,
      } = oldProps;
      const { colorRamp, decoder, min, max, filters, filtersChannel, gradualChange, visible, opacity, maskImageSrc } = props;
      const { gl } = context;

      if (
        decoder !== oldDecoder ||
        max !== oldMax ||
        min !== oldMin ||
        filtersChannel !== oldFiltersChannel ||
        gradualChange !== oldGradualChange ||
        visible !== oldVisible ||
        opacity !== oldOpacity
      ) {
        this.state.model?.delete();
        this.state.model = this._getModel(gl);
        this.getAttributeManager().invalidateAll();
      }
      if (colorRamp !== oldColorRamp || oldFilters !== filters) {
        this.setState({
          filters,
          colorRamp: ColorRamp.build(gl, colorRamp),
        });
      }
      if (oldMaskImageSrc !== maskImageSrc && maskImageSrc) {
        getTranslateImageData(maskImageSrc).then((data) => {
          this.setState({
            maskImage: TextureDrawer.createTexture(gl, {
              width: 256,
              height: 256,
              data,
            }),
          });
        });
      }
    } catch (e) {
      console.log(e);
    }
  }

  draw(opts: any) {
    const { uniforms, moduleParameters, context } = opts;
    const { model, coordinateConversion, bounds, disablePicking, maskImage } = this.state;
    const { image, desaturate, transparentColor, tintColor, smooth, maskImageSrc, isCompute = false, maskId = '' } = this.props;
    if (moduleParameters.pickingActive && disablePicking) {
      return;
    }
    const filters = new Array(16).fill(-1).map((data, index) => this.state.filters?.[index] || data);
    // // TODO fix zFighting
    // Render the image
    const { maskChannels } = moduleParameters;
    const { viewport } = context;
    if (maskChannels && maskChannels[maskId]) {
      const { index, bounds, coordinateOrigin: fromCoordinateOrigin } = maskChannels[maskId];
      let { coordinateSystem: fromCoordinateSystem } = maskChannels[maskId];
      uniforms.mask_enabled = true;
      uniforms.mask_channel = index;

      if (fromCoordinateSystem === COORDINATE_SYSTEM.DEFAULT) {
        fromCoordinateSystem = viewport.isGeospatial ? COORDINATE_SYSTEM.LNGLAT : COORDINATE_SYSTEM.CARTESIAN;
      }
      const opts = { modelMatrix: null, fromCoordinateOrigin, fromCoordinateSystem };
      const bl = this.projectPosition([bounds[0], bounds[1], 0], opts);
      const tr = this.projectPosition([bounds[2], bounds[3], 0], opts);
      uniforms.mask_bounds = [bl[0], bl[1], tr[0], tr[1]];
    } else {
      if (maskId) {
        // log.warn(`Could not find a mask layer with id: ${maskId}`)();
      }
      uniforms.mask_enabled = false;
    }
    if (image && model) {
      const ufs = {
        bitmapTexture: image,
        desaturate,
        transparentColor: transparentColor.map((x: number) => x / 255),
        tintColor: tintColor.slice(0, 3).map((x: number) => x / 255),
        filters: filters,
        filterCount: this.state.filters?.length || 0,
        coordinateConversion,
        bounds,
        colorRamp: this.state.colorRamp || [],
        smooth: !!smooth,
        maskImageTexture: !!maskImageSrc ? maskImage : undefined,
        shouldMaskWithImage: !!maskImageSrc,
        isCompute,
      };
      model.setUniforms(uniforms).setUniforms(ufs).draw();
    }
  }
}

TextureBitmapLayer.layerName = 'texture-bitmap-layer';
