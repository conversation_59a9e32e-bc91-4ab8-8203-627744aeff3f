export default {
  url: {
    type: 'string',
    value: '',
  },
  map: {
    type: 'object',
    value: {
      minLng: 0,
      maxLng: 0,
      minLat: 0,
      maxLat: 0,
      url: '',
    },
  },
  opacity: {
    type: 'string',
    value: '0.55'
  },
  // data: 渲染数据图层, 需要指定decoder
  // image: 渲染图片图层
  renderType: {
    type: 'string',
    value: 'data',
  },
  decoder: {
    type: 'string',
    value: 'data.r * 255.0 + data.g',
  },
  regionCode: 0,
  animation: {
    type: 'object',
    value: {
      duration: 5000,
      started: false,
    },
  },
  colorRamp: {
    type: 'object',
    value: {
      0.0: '#0f0',
      // 0.0: '#9589d3',
      // 0.05: '#8589d3',
      // 0.1: '#80cece',
      // 0.4235: '#8acece',
      // 0.4627: '#629ec1',
      // 0.502: '#5d8e35',
      // 0.5412: '#caab09',
      // 0.5804: '#cb4a13',
      // 0.6196: '#f80',
      1.0: '#f00',
    },
  },
  getPosition: { type: 'accessor', value: (x: any) => x },
  onClick: {
    type: 'function',
    value: () => {},
  },
};
