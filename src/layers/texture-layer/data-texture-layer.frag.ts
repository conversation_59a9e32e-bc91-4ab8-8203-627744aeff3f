/* eslint-disable max-len */
export default `
  #define SHADER_NAME data-layer-model-frag-shader
  #define EPSILON 0.5

  uniform sampler2D u_data_texture;
  uniform sampler2D u_map_texture;
  uniform sampler2D u_color_ramp;
  uniform int u_region_code;
  uniform sampler2D u_target_data_texture;

  varying vec2 v_uv;

  #ifdef RT_DATA
  float decode(vec4 data) {
      {DECODER};
  }
  float opacity(float value) {
    return {OPACITY_DECODER};
  }
  #endif

  bool equalsFloat(float left, float right) {
    return left > (right - EPSILON) && left < (right + EPSILON);
  }

  // 在个别GPU上有精度问题，导致渲染区域错误
  int toIntCode(float code) {
    int c = int(code);
    float f = fract(code);
    if(f > 0.9) return c + 1;
    return c;
  }

  bool shouldRender(ivec3 mask, ivec3 code) {
    if(code.r != mask.r) return false;
    if(code.g > 0 && code.g != mask.g) return false;
    if(code.b > 0 && code.b != mask.b) return false;
    return true;
  }

  void main() {
      bool render = false;
      vec4 mask = texture2D(u_map_texture, v_uv) * 255.0;
      if(u_region_code > 0) {
        ivec3 maskCode = ivec3(toIntCode(mask.r), toIntCode(mask.g), toIntCode(mask.b));
        int c1 = toIntCode(float(u_region_code / 10000));
        int c2 = toIntCode(mod(float(u_region_code / 100), 100.0));
        int c3 = toIntCode(mod(float(u_region_code), 100.0));
        ivec3 code = ivec3(c1, c2, c3);
          render = shouldRender(maskCode, code);
      } else {
        render = mask.r > 0.0;
      }
      vec4 data = texture2D(u_data_texture, v_uv);
  #ifdef RT_DATA
      if(data.r == 0.0 && data.g == 0.0 && data.b == 0.0) {
        discard;
      }
      float value = decode(data);
  #endif
    if(render) {
  #ifdef RT_DATA
      vec2 ramp_pos = vec2(
      fract(16.0 * value),
      floor(16.0 * value) / 16.0);
      gl_FragColor = texture2D(u_color_ramp, ramp_pos);
      gl_FragColor.a = opacity(value);
  #else
    gl_FragColor = data;
  #endif
      } else {
        discard;
      }
  }
`;
