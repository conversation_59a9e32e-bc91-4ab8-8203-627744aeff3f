// import { parse } from 'querystring';
import { Layer, project32 } from '@deck.gl/core';
import GL from '@luma.gl/constants';
import { Model } from '@luma.gl/engine';
import { readPixelsToArray } from '@luma.gl/webgl';
import TextureDrawer from '../texture-drawer';
import vs from './data-texture-layer.vert';
import fs from './data-texture-layer.frag';
import ColorRamp from '../color-ramp';
import LayerProps from './data-texture-layer-props';
import {
  aggregateTileWithAlpha,
  calculateTextureTileInfo,
  DEGREE_UNIT,
  TILE_PIXELS,
} from '@/utils/tiles';
import { loadImageData } from '@/utils/image';
import random from 'lodash/random';
import type { UpdateStateInfo } from '@deck.gl/core/lib/layer';
import isEqual from 'lodash/isEqual';
// import moment from 'moment';

export default class DataTextureLayer extends Layer<any, any> {
  static layerName: string;
  static defaultProps: Record<string, unknown>;
  [x: string]: any;

  updateState(opt: UpdateStateInfo<any>): void {
    const { oldProps, props, context } = opt;
    super.updateState(opt);
    const {
      url: oldUrl,
      regionCode: oldRegionCode,
      colorRamp: oldColorRamp,
      min: oldMin,
      max: oldMax,
    } = oldProps;
    const { url, map, regionCode, visible, colorRamp, min, max } = props;

    if (
      (url !== oldUrl ||
        regionCode !== oldRegionCode ||
        JSON.stringify(colorRamp) !== JSON.stringify(oldColorRamp) ||
        max !== oldMax ||
        min !== oldMin ||
        !isEqual(this.state.map, map)) &&
      visible
    ) {
      const { gl } = context;
      if (this.props.map) {
        this.setState({ map: this.props.map });
        this.initTextureMap();
      }

      if (this.props.url) {
        setTimeout(() => {
          this.setTileUrl(this.props.url);
        }, 200);
      }

      this.setState({ colorRamp: ColorRamp.build(gl, this.props.colorRamp) });
      this.getAttributeManager().add({
        position: {
          size: 2,
          type: GL.FLOAT,
          transition: true,
          accessor: 'getPosition',
        },
      });
    }
  }

  /**
   * 根据地理坐标获取纹理中的像素值
   * @param lng 经度
   * @param lat 纬度
   * @returns 像素值，如果不在纹理的边界或者无数据则返回null
   */
  public evaluate(
    lng: number,
    lat: number,
  ): {
    r: number;
    g: number;
    b: number;
    a: number;
  } | null {
    if (this.readyToDraw()) {
      const { left, right, bottom, top } = this.state.bound;
      if (lng > right || lng < left || lat > top || lat < bottom) {
        return null;
      }
      const x = Math.floor((lng - left) / DEGREE_UNIT);
      const y = Math.floor((top - lat) / DEGREE_UNIT);

      const [r0, g0, b0] = readPixelsToArray(this.state.map_texture, {
        sourceX: x,
        sourceY: y,
        sourceWidth: 1,
        sourceHeight: 1,
      });
      if (r0 === 0) return null;
      if (this.props.regionCode > 0) {
        const code = this.props.regionCode;
        const c = Math.floor(code / 100) % 100;
        const d = code % 100;
        if (c > 0 && c !== g0) return null;
        if (d > 0 && d !== b0) return null;
      }

      const [r, g, b, a] = readPixelsToArray(this.state.data_texture, {
        sourceX: x,
        sourceY: y,
        sourceWidth: 1,
        sourceHeight: 1,
      });

      if (r === 0 && g === 0 && b === 0) return null;
      return { r, g, b, a };
    }
    return null;
  }

  /**
   * 清除数据纹理，停止渲染
   */
  public clear() {
    this.state.renderable = false;
  }

  /**
   * 设置瓦片URL模板
   *
   * 模板URL中含有两个变量 {x}, {y}, 使用时需要替换
   * @param url 贴图url
   */
  public setTileUrl(templateUrl: string) {
    if (!this.props.visible) {
      return;
    }
    let timer;
    if (
      this.context &&
      this.state &&
      this.state.tiles &&
      this.state.tiles.length > 0 &&
      this.state.data_texture
    ) {
      this.state.renderable = true;
      const firstTile = this.state.tiles[0];
      this.state.tiles.forEach((tile: [number, number]) => {
        // const randomNum = random(0, 1);
        const randomNum = 0;
        // const search = templateUrl.split('?')[1];
        // const queryObj = parse(search);
        // const dateTime = moment(queryObj.dateTime).format('YYYYMMDD/HHmm');
        // const url = `http://tile${randomNum}.ytcsc.cn:20055/${`${queryObj.type}`.toUpperCase()}/${dateTime}/${queryObj.type.toUpperCase()}_${
        //   tile[0]
        // }_${tile[1]}.webp`;

        const url = templateUrl
          .replace('{x}', String(tile[0]))
          .replace('{y}', String(tile[1]))
          .replace('[]', `${randomNum}`);
        aggregateTileWithAlpha(
          [url],
          `vec4 encode(vec4 value) {
            return value;
          }`,
          `vec4 decode(vec4 data) {
            return data * 255.0;
          }`,
          4,
        ).then((img) => {
          this.state.data_texture.setSubImageData({
            x: (tile[0] - firstTile[0]) * TILE_PIXELS,
            y: (tile[1] - firstTile[1]) * TILE_PIXELS,
            data: img.data,
            width: TILE_PIXELS,
            height: TILE_PIXELS,
          });
          this.setNeedsRedraw(true);
        });
        // loadImageData(url).then((img) => {
        //   this.state.data_texture.setSubImageData({
        //     x: (tile[0] - firstTile[0]) * TILE_PIXELS,
        //     y: (tile[1] - firstTile[1]) * TILE_PIXELS,
        //     data: img.data,
        //     width: TILE_PIXELS,
        //     height: TILE_PIXELS,
        //   });
        //   this.setNeedsRedraw(true);
        // });
      });
      clearTimeout(timer);
    } else {
      timer = setTimeout(() => this.setTileUrl(templateUrl), 200);
    }
  }

  /**
   * 设置多幅瓦片实现聚合渲染
   *
   * 模板URL中含有两个变量 {x}, {y}, 使用时需要替换
   */
  public setAggregateUrls(
    templateUrls: string[],
    encoder: string,
    decoder: string,
    bands: 1 | 2 | 3 | 4,
  ) {
    let timer;
    if (
      this.context &&
      this.state &&
      this.state.tiles &&
      this.state.tiles.length > 0 &&
      this.state.data_texture
    ) {
      this.state.renderable = true;
      const firstTile = this.state.tiles[0];
      this.state.tiles.forEach((tile: [number, number]) => {
        const urls = templateUrls.map((u) => {
          const randomNum = random(0, 4);
          return u
            .replace('{x}', String(tile[0]))
            .replace('{y}', String(tile[1]))
            .replace('[]', `${randomNum}`);
        });
        aggregateTileWithAlpha(urls, encoder, decoder, bands).then((img) => {
          const x = (tile[0] - firstTile[0]) * TILE_PIXELS;
          const y = (tile[1] - firstTile[1]) * TILE_PIXELS;
          this.state.data_texture.setSubImageData({
            x,
            y,
            data: img.data,
            width: TILE_PIXELS,
            height: TILE_PIXELS,
          });
          this.setNeedsRedraw();
        });
      });
      clearTimeout(timer);
    } else {
      timer = setTimeout(
        () => this.setAggregateUrls(templateUrls, encoder, decoder, bands),
        200,
      );
    }
  }

  /**
   * 由deck.gl框架调用
   */
  initializeState() {
    this.layerInitMethod();
  }

  private layerInitMethod() {
    const { gl } = this.context;

    if (this.props.map) {
      this.setState({ map: this.props.map });
      this.initTextureMap();
    }

    if (this.props.url) {
      this.setTileUrl(this.props.url);
    }

    this.setState({ colorRamp: ColorRamp.build(gl, this.props.colorRamp) });
    this.getAttributeManager().add({
      position: {
        size: 2,
        type: GL.FLOAT,
        transition: true,
        accessor: 'getPosition',
      },
    });
  }

  private initTextureMap() {
    if (this.state.map) {
      const { gl } = this.context;

      const { minLng, maxLng, minLat, maxLat } = this.state.map;
      const { tiles, left, right, bottom, top, width, height } =
        calculateTextureTileInfo(minLng, maxLng, minLat, maxLat);

      this.rasterize(left, right, bottom, top);
      this.initShaderProgram(gl);
      this.state.bound = { left, right, bottom, top };
      loadImageData(this.state.map.url).then((img) => {
        const mapTexture = TextureDrawer.createTexture(gl, {
          width,
          height,
        });
        const x = Math.floor((minLng - left) / DEGREE_UNIT);
        const y = Math.floor((top - maxLat) / DEGREE_UNIT);
        mapTexture.setSubImageData({
          x,
          y,
          data: img.data,
          width: img.width,
          height: img.height,
        });
        this.state.map_texture = mapTexture;
        const dataTexture = TextureDrawer.createTexture(gl, {
          width,
          height,
        });

        this.state.data_texture = dataTexture;
        this.state.tiles = tiles;
        this.state.renderable = true;
      });
    }
  }

  private readyToDraw() {
    return (
      this.state &&
      !!this.state.renderable &&
      this.state.model &&
      this.state.data_texture &&
      this.state.map_texture
    );
  }

  /**
   * deck.gl 框架调用
   */
  draw(opt) {
    const { uniforms } = opt;
    if (!this.readyToDraw()) {
      return;
    }
    Object.assign(uniforms, {
      u_data_texture: this.state.data_texture,
      u_map_texture: this.state.map_texture,
      u_color_ramp: this.state.colorRamp,
      u_minlng: this.state.bound.left,
      u_maxlng: this.state.bound.right,
      u_minlat: this.state.bound.bottom,
      u_maxlat: this.state.bound.top,
      u_region_code: this.props.regionCode,
    });

    const { gl } = this.context;
    gl.disable(GL.DEPTH_TEST);
    super.draw({ ...opt, uniforms } as {
      moduleParameters: any;
      uniforms: any;
      parameters: any;
      context: WebGLRenderingContext;
    });
    gl.enable(GL.DEPTH_TEST);
  }

  private initShaderProgram(gl: WebGL2RenderingContext) {
    let { decoder } = this.props;
    const { max, min } = this.props;
    if (max !== undefined && min !== undefined) {
      decoder = decoder
        .replace(/{min}/g, min.toFixed(1))
        .replace(/{max}/g, max.toFixed(1));
    }
    if (!decoder.includes('return ')) {
      decoder = `return ${decoder}`;
    }
    const fsCode = fs
      .replaceAll('{DECODER}', decoder)
      .replaceAll('{OPACITY_DECODER}', this.props.opacity);
    const defines: Record<string, any> = {};
    if (this.props.renderType === 'data') {
      defines.RT_DATA = 1;
    }
    const model = new Model(gl, {
      vs,
      fs: fsCode,
      id: 'data-texture-layer-program',
      drawMode: gl.TRIANGLES,
      vertexCount: this.props.data.length,
      modules: [project32],
      defines,
    });
    this.setState({ model });
  }

  /**
   * 栅格化
   *
   * 按照经纬度步长将数据栅格化，避免数据整体出现位置偏移
   */
  private rasterize(
    minLng: number,
    maxLng: number,
    minLat: number,
    maxLat: number,
  ) {
    const lngUnit = 0.5;
    const latUnit = 0.5;
    const data = [] as [number, number][];
    for (let lng = minLng; lng < maxLng; lng += lngUnit) {
      for (let lat = minLat; lat < maxLat; lat += latUnit) {
        const right = lng + lngUnit > maxLng ? maxLng : lng + lngUnit;
        const top = lat + latUnit > maxLat ? maxLat : lat + latUnit;

        data.push([lng, top]);
        data.push([right, top]);
        data.push([lng, lat]);
        data.push([lng, lat]);
        data.push([right, lat]);
        data.push([right, top]);
      }
    }
    this.props.data = data;
  }
}

DataTextureLayer.layerName = 'data-texture-layer';
DataTextureLayer.defaultProps = LayerProps;
