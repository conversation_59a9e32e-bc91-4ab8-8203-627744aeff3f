/* eslint-disable max-len */
export default `
#define SHADER_NAME data-layer-model-vertex-shader

precision lowp float;

// 经纬度坐标
attribute vec2 position;

uniform float u_minlng;
uniform float u_maxlng;
uniform float u_minlat;
uniform float u_maxlat;

// 纹理坐标 uv
varying vec2 v_uv;

void main() {
    vec2 pos_common = project_position(position);
    // eslint-disable-next-line max-len
    vec4 pos_clipspace = project_common_position_to_clipspace(vec4(pos_common, 0.0, 1.0));
    gl_Position = vec4(pos_clipspace.x,pos_clipspace.y, 0.0, 1.0);
    v_uv.x = (position.x - u_minlng) / (u_maxlng - u_minlng);
    v_uv.y = (u_maxlat - position.y) / (u_maxlat - u_minlat);
}
`;
