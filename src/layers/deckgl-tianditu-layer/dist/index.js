import{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>ayer}from"deck.gl/typed";function _callSuper(r,e,t){return e=_getPrototypeOf(e),_possibleConstructorReturn(r,_isNativeReflectConstruct()?Reflect.construct(e,t||[],_getPrototypeOf(r).constructor):e.apply(r,t))}function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function(){return!!r})()}function _iterableToArrayLimit(r,e){var t=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=t){var o,n,c,l,i=[],a=!0,u=!1;try{if(c=(t=t.call(r)).next,0===e){if(Object(t)!==t)return;a=!1}else for(;!(a=(o=c.call(t)).done)&&(i.push(o.value),i.length!==e);a=!0);}catch(r){u=!0,n=r}finally{try{if(!a&&null!=t.return&&(l=t.return(),Object(l)!==l))return}finally{if(u)throw n}}return i}}function ownKeys(e,r){var t,o=Object.keys(e);return Object.getOwnPropertySymbols&&(t=Object.getOwnPropertySymbols(e),r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),o.push.apply(o,t)),o}function _objectSpread2(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(t),!0).forEach(function(r){_defineProperty(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function _toPrimitive(r,e){if("object"!=typeof r||!r)return r;var t=r[Symbol.toPrimitive];if(void 0===t)return("string"===e?String:Number)(r);t=t.call(r,e||"default");if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toPropertyKey(r){r=_toPrimitive(r,"string");return"symbol"==typeof r?r:String(r)}function _classCallCheck(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(r,e){for(var t=0;t<e.length;t++){var o=e[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(r,_toPropertyKey(o.key),o)}}function _createClass(r,e,t){return e&&_defineProperties(r.prototype,e),t&&_defineProperties(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}function _defineProperty(r,e,t){return(e=_toPropertyKey(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function _inherits(r,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&_setPrototypeOf(r,e)}function _getPrototypeOf(r){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)})(r)}function _setPrototypeOf(r,e){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,e){return r.__proto__=e,r})(r,e)}function _assertThisInitialized(r){if(void 0===r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function _possibleConstructorReturn(r,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(r)}function _superPropBase(r,e){for(;!Object.prototype.hasOwnProperty.call(r,e)&&null!==(r=_getPrototypeOf(r)););return r}function _get(){return(_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(r,e,t){var o=_superPropBase(r,e);if(o)return(o=Object.getOwnPropertyDescriptor(o,e)).get?o.get.call(arguments.length<3?r:t):o.value}).apply(this,arguments)}function _slicedToArray(r,e){return _arrayWithHoles(r)||_iterableToArrayLimit(r,e)||_unsupportedIterableToArray(r,e)||_nonIterableRest()}function _arrayWithHoles(r){if(Array.isArray(r))return r}function _unsupportedIterableToArray(r,e){var t;if(r)return"string"==typeof r?_arrayLikeToArray(r,e):"Map"===(t="Object"===(t=Object.prototype.toString.call(r).slice(8,-1))&&r.constructor?r.constructor.name:t)||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(r,e):void 0}function _arrayLikeToArray(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,o=new Array(e);t<e;t++)o[t]=r[t];return o}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var compare=function(r,o){return Object.keys(r).every(function(t){return r[t].every(function(r,e){return r===o[t][e]})})},createCustom=function(r){function e(r,e){return 0===e?"0.0":e?"".concat(e.toFixed(1)," / 255.0"):"color.".concat(r)}var t=r.land,o=r.water,o=void 0===o?[]:o,n=r.boundary,n=void 0===n?[]:n,c=r.road,c=void 0===c?[]:c,r=r.others,r=void 0===r?[]:r,t=_slicedToArray(void 0===t?[]:t,3),l=t[0],i=t[1],t=t[2],o=_slicedToArray(o,3),a=o[0],u=o[1],o=o[2],n=_slicedToArray(n,3),s=n[0],f=n[1],n=n[2],c=_slicedToArray(c,3),y=c[0],p=c[1],c=c[2],r=_slicedToArray(r,3),b=r[0],g=r[1],r=r[2];return"\n    // 水域\n    if(color.b > 230.0 / 255.0 && color.r > 165.0 / 255.0 && color.r < 180.0 / 255.0) {\n      color.r = ".concat(e("r",a),";\n      color.g = ").concat(e("g",u),";\n      color.b = ").concat(e("b",o),";\n    } else if(color.r > 240.0 / 255.0 && color.g > 240.0 / 255.0 && color.b > 230.0 / 255.0 ) {\n      // 陆地\n      color.r = ").concat(e("r",l),";\n      color.g = ").concat(e("g",i),";\n      color.b = ").concat(e("b",t),";\n    } else {\n      // 路网\n      if(color.r > 245.0 / 255.0) {\n        color.r = ").concat(e("r",y),";\n        color.g = ").concat(e("g",p),";\n        color.b = ").concat(e("b",c),";\n      } else if(color.r > 180.0 / 255.0) {\n        // 国界\n        color.r = ").concat(e("r",s),";\n        color.g = ").concat(e("g",f),";\n        color.b = ").concat(e("b",n),";\n      } else {\n        // 其他\n        color.r = ").concat(e("r",b),";\n        color.g = ").concat(e("g",g),";\n        color.b = ").concat(e("b",r),";\n      }\n    }\n")},fsFilterColor={normal:"",light:"\n    float brightness = (color.r + color.g + color.b) / 3.0;\n    color.rgb = mix(color.rgb, vec3(brightness) , 0.95);\n  ",grey:"\n    // 水域\n    if(color.b > 230.0 / 255.0 && color.r > 165.0 / 255.0 && color.r < 180.0 / 255.0) {\n      color.r = 10.0 / 255.0;\n      color.g = 20.0 / 255.0;\n      color.b = 28.0 / 255.0;\n    } else if(color.r > 240.0 / 255.0 && color.g > 240.0 / 255.0 && color.b > 230.0 / 255.0 ) {\n      // 陆地\n      color.r = 26.0 / 255.0;\n      color.g = 35.0 / 255.0;\n      color.b = 44.0 / 255.0;\n    } else {\n      // 路网\n      if(color.r > 245.0 / 255.0) {\n        color.r = 5.0 / 255.0;\n        color.g = 10.0 / 255.0;\n        color.b = 20.0 / 255.0;\n      } else if(color.r > 180.0 / 255.0) {\n        // 国界\n        color.r = 10.0 / 255.0;\n        color.g = 20.0 / 255.0;\n        color.b = 28.0 / 255.0;\n      } else {\n        // 其他\n        color.r = .1;\n        color.g = .1;\n        color.b = .1;\n      }\n    }\n",dark:"\n    // 水域\n    if(color.b > 230.0 / 255.0 && color.r < 180.0 / 255.0) {\n      color.r = 23.0 / 255.0;\n      color.g = 23.0 / 255.0;\n      color.b = 23.0 / 255.0;\n    } else if(color.r > 240.0 / 255.0 && color.g > 240.0 / 255.0 && color.b > 230.0 / 255.0 ) {\n      // 陆地\n      color.r = 42.0 / 255.0;\n      color.g = 42.0 / 255.0;\n      color.b = 42.0 / 255.0;\n    } else {\n      // color.r = 50.0 / 255.0;\n      // color.g = 50.0 / 255.0;\n      // color.b = 50.0 / 255.0;\n\n      color.rgb *= .1;\n    }\n  ",fresh:"\n    // 水域\n    if(color.b > 230.0 / 255.0 && color.r < 180.0 / 255.0) {\n      color.r = 144.0 / 255.0;\n      color.g = 204.0 / 255.0;\n      color.b = 203.0 / 255.0;\n    } else if(color.r > 240.0 / 255.0 && color.g > 240.0 / 255.0 && color.b > 230.0 / 255.0 ) {\n      // 陆地\n      color.r = 247.0 / 255.0;\n      color.g = 246.0 / 255.0;\n      color.b = 240.0 / 255.0;\n    } \n  ",darkblue:"\n    // 水域\n    if(color.b > 230.0 / 255.0 && color.r < 180.0 / 255.0) {\n      color.r = 10.0 / 255.0;\n      color.g = 20.0 / 255.0;\n      color.b = 40.0 / 255.0;\n    } else if(color.r > 240.0 / 255.0 && color.g > 240.0 / 255.0 && color.b > 230.0 / 255.0 ) {\n      // 陆地\n      color.r = 0.0 / 255.0;\n      color.g = 0.0 / 255.0;\n      color.b = 1.0 / 255.0;\n    } else {\n      // 其他\n      color.r = 10.0 / 255.0;\n      color.g = 20.0 / 255.0;\n      color.b = 40.0 / 255.0;\n    }\n  "},TdtBitmapLayer=function(){function p(){return _classCallCheck(this,p),_callSuper(this,p,arguments)}return _inherits(p,BitmapLayer),_createClass(p,[{key:"initializeState",value:function(){_get(_getPrototypeOf(p.prototype),"initializeState",this).call(this)}},{key:"draw",value:function(r){_get(_getPrototypeOf(p.prototype),"draw",this).call(this,r)}},{key:"updateState",value:function(r){_get(_getPrototypeOf(p.prototype),"updateState",this).call(this,r);try{var e,t,o=r.props,n=r.oldProps,c=r.context,l=n.style,i=n.token,a=n.colorschema,u=o.style,s=o.token,f=o.colorschema,y=c.gl;u===l&&s===i&&!compare(f,a)||(null!=(e=this.state.model)&&e.delete(),this.state.model=this._getModel(y),null==(t=this.getAttributeManager()))||t.invalidateAll()}catch(r){console.log(r)}}},{key:"getShaders",value:function(){var r=this.props,e=r.style,r=r.colorschema;return _objectSpread2(_objectSpread2({},_get(_getPrototypeOf(p.prototype),"getShaders",this).call(this)),{},{inject:{"fs:DECKGL_FILTER_COLOR":r?createCustom(r):null!=(r=fsFilterColor[null!=e?e:"normal"])?r:""}})}}]),p}(),TiandituLayer=(_defineProperty(TdtBitmapLayer,"layerName","TdtBitmapLayer"),function(){function e(){return _classCallCheck(this,e),_callSuper(this,e,arguments)}return _inherits(e,TileLayer),_createClass(e,[{key:"initializeState",value:function(){_get(_getPrototypeOf(e.prototype),"initializeState",this).call(this)}},{key:"getSubDomain",value:function(){return Math.floor(8*Math.random()+0)}},{key:"getTileUrl",value:function(r,e,t){var o=this.props.token,n=this.getSubDomain();return"https://t".concat(n,".tianditu.gov.cn/").concat("vec","_w/wmts?tk=").concat(o,"&layer=").concat("vec","&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix=").concat(t,"&TileCol=").concat(r,"&TileRow=").concat(e)}},{key:"renderSubLayers",value:function(r){var e=this.props.style,t=r.tile.bbox,o=t.west,n=t.south,c=t.east,t=t.north;return new TdtBitmapLayer(r,{data:null,image:r.data,bounds:[o,n,c,t],style:e})}},{key:"draw",value:function(r){_get(_getPrototypeOf(e.prototype),"draw",this).call(this,r)}}]),e}());_defineProperty(TiandituLayer,"layerName","TiandituLayer"),_defineProperty(TiandituLayer,"defaultProps",_objectSpread2(_objectSpread2({},TileLayer.defaultProps),{},{style:"normal",loadOptions:{mimeType:"image/jpeg"}}));export{TiandituLayer};
