import { BitmapLayer, BitmapLayerProps, UpdateParameters } from 'deck.gl/typed';
import { Color, StyleKey } from '.';
interface Props extends BitmapLayerProps {
    style?: StyleKey;
    colorschema?: Partial<{
        land: Color;
        water: Color;
        boundary: Color;
        road: Color;
        others: Color;
    }>;
}
export declare class TdtBitmapLayer extends BitmapLayer<Props> {
    static layerName: string;
    initializeState(): void;
    draw(opts: any): void;
    updateState(opt: UpdateParameters<any>): void;
    getShaders(): any;
}
export {};
