import { TileLayer, TileLayerProps } from 'deck.gl/typed';
import { TdtBitmapLayer } from './TdtBitmapLayer';
export type StyleKey = 'normal' | 'grey' | 'light' | 'dark' | 'darkblue' | 'fresh';
export type Color = [number, number, number];
export type Colorshema = Partial<{
    land: Color;
    water: Color;
    boundary: Color;
    road: Color;
    others: Color;
}>;
export interface Props extends TileLayerProps {
    token: string;
    style?: StyleKey;
    colorschema?: Colorshema;
}
export declare class TiandituLayer extends TileLayer<any, Props> {
    static layerName: string;
    static defaultProps: any;
    initializeState(): void;
    getSubDomain(): number;
    getTileUrl(x: number, y: number, z: number): string;
    renderSubLayers(props: any): TdtBitmapLayer;
    draw(opts: any): void;
}
