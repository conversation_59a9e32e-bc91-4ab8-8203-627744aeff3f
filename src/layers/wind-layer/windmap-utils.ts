// const PARTICLE_BASE_SPEED_FACTOR = 0.00055;
const PARTICLE_BASE_SPEED_FACTOR = 0.00075;

const PARTICLES_LEVELS = [
  {
    level: 1,
    lowerZoom: 0,
    upperZoom: 5,
    particleCount: 13122,
    speed: PARTICLE_BASE_SPEED_FACTOR,
  },
  {
    level: 2,
    lowerZoom: 5,
    upperZoom: 18,
    particleCount: 64000,
  },
];

export function getParticleSpeedFactor(zoom: number) {
  switch (Math.trunc(zoom)) {
    case 5:
      return PARTICLE_BASE_SPEED_FACTOR * 0.9;
    case 6:
    case 7:
    case 8:
    case 9:
      return PARTICLE_BASE_SPEED_FACTOR * 0.85;
    case 10:
      return PARTICLE_BASE_SPEED_FACTOR * 0.7;
    case 11:
      return PARTICLE_BASE_SPEED_FACTOR * 0.5;
    case 12:
      return PARTICLE_BASE_SPEED_FACTOR * 0.2;
    case 13:
      return PARTICLE_BASE_SPEED_FACTOR * 0.08;
    case 14:
      return PARTICLE_BASE_SPEED_FACTOR * 0.01;
    case 15:
      return PARTICLE_BASE_SPEED_FACTOR * 0.001;
    case 16:
      return PARTICLE_BASE_SPEED_FACTOR * 0.001;
    case 17:
    default:
      return PARTICLE_BASE_SPEED_FACTOR * 0.0009;
  }
}

export function getGridSize(zoom: number) {
  switch (Math.trunc(zoom)) {
    case 8:
      return 60000;
    case 9:
      return 40000;
    case 10:
      return 20000;
    case 11:
      return 8000;
    case 12:
      return 4000;
    case 13:
      return 2500;
    case 14:
      return 1500;
    case 15:
      return 600;
    case 16:
      return 210;
    case 17:
      return 130;
    case 18:
      return 80;
    default:
      return 500000;
  }
}

export function getParticleLevel(zoom: number) {
  // eslint-disable-next-line max-len
  const foundLevel = PARTICLES_LEVELS.find(
    (level) => level.lowerZoom <= zoom && zoom < level.upperZoom,
  );
  if (!foundLevel) {
    return PARTICLES_LEVELS[0];
  }
  return foundLevel;
}

export function getParticleSize() {
  return 1.05;
}
