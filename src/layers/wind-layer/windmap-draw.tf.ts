/* eslint-disable max-len */
export default `
#define SHADER_NAME windmap-layer-transform-feedback-shader

attribute highp vec2 a_position;
attribute highp vec2 a_ttl;

uniform sampler2D u_wind;
uniform sampler2D u_color_ramp;
uniform vec2 u_wind_max;
uniform float u_speed_factor;
uniform highp float u_time;
uniform highp float u_frame_time;
uniform highp float u_left;
uniform highp float u_top;
uniform highp float u_right;
uniform highp float u_bottom;

varying highp vec2 v_position;
varying highp vec2 v_ttl;
varying vec4 v_color;

float rand(vec2 co){
    return fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453);
}

vec2 next_pos(vec2 pos, vec2 offset) {
    // x: current age, y: max age
    if(v_ttl.x <= v_ttl.y) {
        return pos;
    }
    float x = rand(vec2(pos.x, 255.0)) * (u_right - u_left) + u_left;
    float y = rand(vec2(pos.y, 280.0)) * (u_bottom - u_top) + u_top;
    v_ttl.x = 0.0;
    return vec2(x, y);
}

vec2 decodeVelocity() {
    vec4 velocity = texture2D(u_wind, a_position) * 255.0;
    float u = velocity.r;
    float v = velocity.g;
    float fraction = velocity.b;
    float ufract = float(int(fraction / 10.0)) / 10.0;
    float vfract = mod(fraction, 10.0) / 10.0;
    u = u + ufract - 100.0;
    v = -(v + vfract - 100.0);
    return vec2(u, v);
}

void main() {
    v_ttl.x = a_ttl.x + u_frame_time;
    v_ttl.y = a_ttl.y;

    vec2 velocity = decodeVelocity();
    vec2 offset = velocity * u_speed_factor;

    v_position = a_position + offset;
    v_position = next_pos(v_position, offset);

    float speed_t = length(velocity) / length(u_wind_max);
    vec2 ramp_pos = vec2(
        fract(16.0 * speed_t),
        floor(16.0 * speed_t) / 16.0);
    v_color = texture2D(u_color_ramp, ramp_pos);
}
`;
