import { CompositeLayer } from '@deck.gl/core';
import { IconLayer } from '@deck.gl/layers';
import { readPixelsToArray } from '@luma.gl/webgl';
import { defaultProps } from './windmap-layer-props';
import WindMapParticleLayer from './windmap-particle-layer';
import turfPointGrid from '@turf/point-grid';
import TextureDrawer from '../texture-drawer';
import { getGridSize } from './windmap-utils';
import { calculateUvAngle } from '@/utils/wind';
import {
  calculateTextureTileInfo,
  DEGREE_UNIT,
  TILE_PIXELS,
} from '@/utils/tiles';
import random from 'lodash/random';

export default class WindMapLayer extends CompositeLayer<any, any> {
  [x: string]: any;
  static layerName: string;

  initializeState(params: any) {
    this.init();
    super.initializeState(params);
  }

  renderLayers() {
    return [this.renderParticleLayer(), this.renderArrowLayer()];
  }

  /**
   * zoom > 14, 渲染静态箭头风场
   * 否则, 同时渲染
   */
  filterSubLayer({ layer, viewport }) {
    if (viewport.zoom > 14 || viewport.zoom < 5.2) {
      return layer.id === `arrow-layer@${this.props.id}`;
    }

    return true;
  }

  renderSubLayers() {
    return [this.renderParticleLayer(), this.renderArrowLayer()];
  }

  /**
   * 加载风场贴图
   *
   * @param url 纹理图片的url
   */
  public setTileUrl(templateUrl: string) {
    if (
      this.context &&
      this.state &&
      this.state.tiles &&
      this.state.tiles.length > 0 &&
      this.state.wind_texture
    ) {
      const firstTile = this.state.tiles[0];
      this.state.tiles.forEach((tile: [number, number]) => {
        const randomNumber = random(0, 4);
        const url = templateUrl
          .replace('{x}', String(tile[0]))
          .replace('{y}', String(tile[1]))
          .replace('[]', `${randomNumber}`);
        this.loadImage(url).then((img) => {
          this.state.wind_texture.setSubImageData({
            x: (tile[0] - firstTile[0]) * TILE_PIXELS,
            y: (tile[1] - firstTile[1]) * TILE_PIXELS,
            data: img,
            width: TILE_PIXELS,
            height: TILE_PIXELS,
          });
        });
      });
    } else {
      setTimeout(() => this.setTileUrl(templateUrl), 200);
    }
  }

  private init() {
    const { minLng, maxLng, minLat, maxLat } = this.props.map;
    const { tiles, left, right, bottom, top, width, height } =
      calculateTextureTileInfo(minLng, maxLng, minLat, maxLat);
    this.state.bound = { left, right, bottom, top };

    const { gl } = this.context;
    const windTexture = TextureDrawer.createTexture(gl, {
      width,
      height,
    });

    this.state.wind_texture = windTexture;
    this.state.tiles = tiles;

    this.state.particleLayer = new WindMapParticleLayer({
      ...this.props,
      id: `particle-layer@${this.props.id}`,
    });
    this.state.particleLayer.setWindTexture(this.state.wind_texture);
    this.state.particleLayer.setBound(this.state.bound);

    this.loadImage(this.props.map.url).then((img) => {
      const mapTexture = TextureDrawer.createTexture(gl, {
        width,
        height,
      });
      const x = Math.floor((minLng - left) / DEGREE_UNIT);
      const y = Math.floor((top - maxLat) / DEGREE_UNIT);
      mapTexture.setSubImageData({
        x,
        y,
        data: img,
        width: img.width,
        height: img.height,
      });
      this.state.map_texture = mapTexture;
      this.state.particleLayer.setMapTexture(mapTexture);
    });
  }

  private renderParticleLayer() {
    if (this.props.style && this.props.style !== 'dynamic') return null;
    return this.state.particleLayer;
  }

  private renderArrowLayer() {
    if (this.props.style && this.props.style !== 'static') return null;
    if (!this.state || !this.state.wind_texture) return null;
    return new IconLayer({
      id: `arrow-layer@${this.props.id}`,
      data: this.calculateGridPoints(),
      getPosition: (d: any) => [d.lng, d.lat],
      getIcon: () => 'marker',
      iconMapping: {
        marker: { x: 0, y: 0, width: 64, height: 40, mask: true },
      },
      iconAtlas: '/wind-arrow.png',
      sizeMaxPixels: 64,
      sizeMinPixels: 16,
      getSize: (d: any) => 16 * d.velocity,
      getColor: [167, 194, 223],
      getAngle: (d: any) => d.angle,
      getPixelOffset: [20, 20],
    });
  }

  private calculateGridPoints() {
    const { minLng, minLat, maxLng, maxLat } = this.calculateRenderBound();
    const gridSize = getGridSize(this.context.viewport.zoom);
    const grid = turfPointGrid([minLng, minLat, maxLng, maxLat], gridSize, {
      units: 'meters',
    });
    return grid.features
      .map((f) => {
        const [lng, lat] = f.geometry.coordinates;
        const { velocity, angle } = this.evaluate(lng, lat);
        if (!velocity) return null;
        return { lng, lat, velocity, angle };
      })
      .filter((p) => !!p);
  }

  private calculateRenderBound() {
    const { viewport } = this.context;
    const [minLng1, minLat1, maxLng1, maxLat1] = (viewport as any).getBounds();
    const {
      minLng: minLng2,
      minLat: minLat2,
      maxLng: maxLng2,
      maxLat: maxLat2,
    } = this.props.map;

    // 确定实际要渲染的区域
    const minLng = Math.max(minLng1, minLng2);
    const minLat = Math.max(minLat1, minLat2);
    const maxLng = Math.min(maxLng1, maxLng2);
    const maxLat = Math.min(maxLat1, maxLat2);

    return { minLng, minLat, maxLng, maxLat };
  }

  /**
   * 获取空间点位的风向和风速
   */
  private evaluate(lng: number, lat: number) {
    const dummy = { velocity: null, angle: null };
    const { left, right, bottom, top } = this.state.bound;
    if (lng > right || lng < left || lat > top || lat < bottom) {
      return dummy;
    }
    if (!this.state.map_texture) return dummy;
    const x = Math.floor((lng - left) / DEGREE_UNIT);
    const y = Math.floor((top - lat) / DEGREE_UNIT);

    const [r0, g0, b0] = readPixelsToArray(this.state.map_texture, {
      sourceX: x,
      sourceY: y,
      sourceWidth: 1,
      sourceHeight: 1,
    });
    if (r0 === 0) return dummy;
    if (this.props.regions && this.props.regions.length === 1) {
      const code = this.props.regions[0];
      const c = Math.floor(code / 100) % 100;
      const d = code % 100;
      if (c > 0 && c !== g0) return dummy;
      if (d > 0 && d !== b0) return dummy;
    }

    const [r, g, b] = readPixelsToArray(this.state.wind_texture, {
      sourceX: x,
      sourceY: y,
      sourceWidth: 1,
      sourceHeight: 1,
    });

    if (r === 0 && g === 0 && b === 0) return dummy;
    const u = r + Math.trunc(b / 10) / 10.0 - 100.0;
    const v = g + (b % 10) / 10 - 100.0;
    const velocity = Math.sqrt(u * u + v * v);
    const angle = calculateUvAngle(u, v);

    return { velocity, angle };
  }

  shouldUpdateState({ changeFlags }) {
    return changeFlags.viewportChanged;
  }

  private loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve) => {
      const img = new Image();
      img.src = url;
      img.setAttribute('crossOrigin', 'anonymous');
      img.onload = () => {
        resolve(img);
      };
    });
  }
}

WindMapLayer.layerName = 'windmap-composite-layer';
WindMapLayer.defaultProps = defaultProps;
