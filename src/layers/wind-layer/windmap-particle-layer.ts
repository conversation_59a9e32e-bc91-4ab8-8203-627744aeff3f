import { Layer, project, project32 } from '@deck.gl/core';
import { Model, Transform } from '@luma.gl/engine';
import { Buffer } from '@luma.gl/webgl';
import drawVert from './windmap-draw.vert';
import drawFrag from './windmap-draw.frag';
import partVert from './windmap-draw.tf';
import ColorRamp from '../color-ramp';
import {
  getParticleLevel,
  getParticleSize,
  getParticleSpeedFactor,
} from './windmap-utils';
import { defaultProps } from './windmap-layer-props';

export default class WindMapParticleLayer extends Layer<any, any> {
  [x: string]: any;
  static layerName: string;

  initializeState() {
    const { gl } = this.context;
    const { speed, dropRate, dropRateBump } = this.props;
    this.setState({
      speed,
      dropRate,
      dropRateBump,
      renders: 0,
    });
    this.setState({ colorRamp: ColorRamp.build(gl, this.props.colorRamp) });
  }

  /**
   * 加载风场贴图
   *
   * @param url 纹理图片的url
   */
  public setWindTexture(texture) {
    if (this.state) {
      const isFirstRender = !this.state.wind_texture;
      this.setState({ wind_texture: texture });
      if (isFirstRender) {
        this.initialize();
      }
    } else {
      setTimeout(() => this.setWindTexture(texture), 50);
    }
  }

  public setMapTexture(texture) {
    if (this.state) {
      this.setState({ map_texture: texture });
    } else {
      setTimeout(() => this.setMapTexture(texture), 50);
    }
  }

  public setBound(bound: any) {
    if (this.state) {
      this.setState({ bound });
    } else {
      setTimeout(() => this.setBound(bound), 50);
    }
  }

  private initialize() {
    const { width, height } = this.context.viewport;
    if (width === 1 || height === 1) return;

    this.initTransformFeedback();
    this.initParticleModel();
  }

  private initParticleModel() {
    let { particleModel } = this.state;
    if (particleModel) particleModel.delete();
    const { gl } = this.context;
    particleModel = new Model(gl, {
      vs: drawVert,
      fs: drawFrag,
      id: 'windmap-layer-model-particle',
      drawMode: gl.POINTS,
      vertexCount: 1,
      modules: [project32],
      instanceCount: this.state.particleCount,
    });
    this.setState({ particleModel });
  }

  private createFeedbackBufferPair(
    gl: WebGL2RenderingContext,
    data: Float32Array,
  ) {
    // eslint-disable-next-line no-buffer-constructor
    return [new Buffer(gl, { data }), new Buffer(gl, { data })];
  }

  private createTransform(name: string, source: string, vertexCount: number) {
    const { gl } = this.context;
    const { buffer0, buffer1, ttl0, ttl1, colorBuffer } = this.state;
    const transform = this.state[name];
    if (transform) transform.delete();
    this.state[name] = new Transform(gl, {
      vs: source,
      varyings: ['v_position', 'v_color', 'v_ttl'],
      elementCount: vertexCount,
      sourceBuffers: { a_position: buffer0, a_ttl: ttl0 },
      feedbackBuffers: {
        v_position: buffer1,
        v_ttl: ttl1,
        v_color: colorBuffer,
      },
      feedbackMap: { a_position: 'v_position', a_ttl: 'v_ttl' },
      modules: [project32],
    });
  }

  private initTransformFeedback() {
    const { particles, ttls } = this.initWindParticles();
    if (!particles || !ttls) return;

    const { gl } = this.context;
    const [buffer0, buffer1] = this.createFeedbackBufferPair(gl, particles);
    const [ttl0, ttl1] = this.createFeedbackBufferPair(gl, ttls);

    // eslint-disable-next-line no-buffer-constructor
    const colorBuffer = new Buffer(gl, {
      byteLength: particles.length * 16,
    });

    this.setState({ buffer0, buffer1, ttl0, ttl1, colorBuffer });
    this.createTransform('particleTransform', partVert, particles.length / 2);
  }

  /**
   * 根据viewport和map计算当前需要渲染的区域在风场贴图中的区域
   */
  private calculateTextureBound() {
    const { viewport } = this.context;
    const [minLng1, minLat1, maxLng1, maxLat1] = (viewport as any).getBounds();
    const {
      minLng: minLng2,
      minLat: minLat2,
      maxLng: maxLng2,
      maxLat: maxLat2,
    } = this.props.map;

    // 确定实际要渲染的区域
    const minLng = Math.max(minLng1, minLng2);
    const minLat = Math.max(minLat1, minLat2);
    const maxLng = Math.min(maxLng1, maxLng2);
    const maxLat = Math.min(maxLat1, maxLat2);
    const lngSpan = maxLng2 - minLng2;
    const latSpan = maxLat2 - minLat2;

    // 将渲染区域转换为风场贴图中的区域，可能会在区域之外
    const left = (minLng - minLng2) / lngSpan;
    const top = (maxLat2 - maxLat) / latSpan;
    const right = (maxLng - minLng2) / lngSpan;
    const bottom = 1 - (minLat - minLat2) / latSpan;
    return { left, top, right, bottom };
  }

  runMovingParticlesTransform(uniforms: any) {
    const { zoom } = this.context.viewport;
    const { particleTransform } = this.state;
    const { left, top, right, bottom } = this.calculateTextureBound();
    const mergedUniforms = {
      u_speed_factor: getParticleSpeedFactor(zoom),
      u_left: left,
      u_top: top,
      u_right: right,
      u_bottom: bottom,
      ...uniforms,
    };
    this.runTransformInternal(particleTransform, mergedUniforms);
  }

  runTransformInternal(transform, uniforms) {
    const { wind_texture, colorRamp, buffer0, buffer1, ttl0, ttl1 } =
      this.state;
    buffer0.setAccessor({ size: 2, divisor: 0 });
    buffer1.setAccessor({ size: 2, divisor: 0 });
    ttl0.setAccessor({ size: 2, divisor: 0 });
    ttl1.setAccessor({ size: 2, divisor: 0 });
    const mergedUniforms = {
      u_wind_max: [50, 50],
      u_wind: wind_texture,
      u_color_ramp: colorRamp,
      ...uniforms,
    };
    transform.run({ uniforms: mergedUniforms });
    transform.swap();
  }

  runTransform(uniforms: any) {
    const now = new Date().getTime();
    if (!this.state.lastTime) {
      this.state.lastTime = now;
    }
    this.runMovingParticlesTransform({
      ...uniforms,
      u_time: now,
      u_frame_time: now - this.state.lastTime,
    });
    this.setState({ lastTime: now });
  }

  /**
   * 随机生成粒子的位置（风场的纹理坐标），x: [0,1], y:[0,1]
   */
  initWindParticles(): {
    particles: Float32Array | null;
    ttls: Float32Array | null;
  } {
    if (!this.state.wind_texture) return { particles: null, ttls: null };
    const { zoom } = this.context.viewport;
    const particleLevel = getParticleLevel(zoom);

    const resolution = Math.ceil(Math.sqrt(particleLevel.particleCount));
    const effectiveCount = resolution * resolution;

    const particles = new Float32Array(effectiveCount * 2);
    const ttls = new Float32Array(effectiveCount * 2);
    const ageRage = this.props.maxAge - this.props.minAge;
    for (let i = 0; i < particles.length; i += 2) {
      particles[i] = Math.random();
      particles[i + 1] = Math.random();
      ttls[i] = 0; // age
      ttls[i + 1] = Math.random() * ageRage + this.props.minAge; // max age
    }
    this.setState({
      particlesReady: true,
      particleCount: effectiveCount,
      particleSize: getParticleSize(),
      speed: particleLevel.speed,
      level: particleLevel.level,
    });
    return { particles, ttls };
  }

  shouldUpdateState({ changeFlags }) {
    return changeFlags.viewportChanged && this.shouldReinitParticles();
  }

  updateState() {
    this.initialize();
  }

  private shouldReinitParticles() {
    const { level } = this.state;
    const { zoom } = this.context.viewport;
    const expLevel = getParticleLevel(zoom);
    return expLevel.level !== level;
  }

  readyToDraw() {
    const { wind_texture, map_texture, particleTransform } = this.state;
    return wind_texture && map_texture && particleTransform;
  }

  getNumInstances() {
    return this.state.particleCount;
  }

  // 覆盖了父类的draw方法，实现自定义的绘制逻辑
  draw({ uniforms }) {
    if (!this.readyToDraw()) return;
    const { viewport } = this.context;
    const projectUniforms = project.getUniforms({
      viewport,
      autoWrapLongitude: false,
    });
    const allUniforms = {
      ...projectUniforms,
      ...uniforms,
    };
    this.runTransform(allUniforms);

    this.drawParticles(allUniforms);
    const { buffer0, buffer1 } = this.state;
    // 交换缓冲区
    this.setState({
      buffer0: buffer1,
      buffer1: buffer0,
    });
  }

  drawParticles(uniforms) {
    const { particleModel, buffer1, map_texture, colorBuffer, particleSize } =
      this.state;

    buffer1.setAccessor({ size: 2, divisor: 1 });
    colorBuffer.setAccessor({ size: 4, divisor: 1 });
    particleModel
      .setAttributes({
        instancePosition: buffer1,
        instanceColor: colorBuffer,
      })
      .setUniforms(uniforms)
      .setUniforms({
        u_wind_min: [50, -50],
        u_wind_max: [50, -50],
        u_map_texture: map_texture,
        u_particle_size: particleSize,
        u_minlng: this.state.bound.left,
        u_maxlng: this.state.bound.right,
        u_minlat: this.state.bound.bottom,
        u_maxlat: this.state.bound.top,
      })
      .draw();
  }
}

WindMapParticleLayer.layerName = 'windmap-particle-layer';
WindMapParticleLayer.defaultProps = defaultProps;
