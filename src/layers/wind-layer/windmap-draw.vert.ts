/* eslint-disable max-len */
export default `
#define SHADER_NAME windmap2-layer-model-particle-vertex-shader

// 粒子的纹理坐标
attribute highp vec2 instancePosition;
attribute vec4 instanceColor;

uniform sampler2D u_map_texture;
uniform lowp float u_particle_size;
uniform float u_minlng;
uniform float u_maxlng;
uniform float u_minlat;
uniform float u_maxlat;

// varying vec4 v_color;
// varying float v_render;
// 将 color 和 render 标记打包
varying vec4 v_color_render;

void main() {
    float lngspan  = u_maxlng - u_minlng;
    float latspan  = u_maxlat - u_minlat;
    
    // 将纹理坐标转为经纬度坐标
    vec2 pos_world = instancePosition * vec2(lngspan, latspan);
    pos_world.x = pos_world.x + u_minlng;
    pos_world.y = u_maxlat - pos_world.y;

    // 转为裁剪空间坐标
    vec2 pos_common = project_position(pos_world);
    vec4 pos_clipspace = project_common_position_to_clipspace(vec4(pos_common, 1.0, 1.0));

    // v_color = instanceColor;
    // v_render = texture2D(u_map_texture, instancePosition).r;
    gl_Position = vec4(pos_clipspace.xy, 0.0, 1.0);
    gl_PointSize = u_particle_size;
    v_color_render = vec4(instanceColor.rgb, texture2D(u_map_texture, instancePosition).r);
}
`