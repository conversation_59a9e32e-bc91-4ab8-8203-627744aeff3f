/*
 * Copyright (c) 2021-2023 WeatherLayers.com
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */
import { LineLayer } from '@deck.gl/layers';
import { Buffer, isWebGL2, Transform } from '@luma.gl/core';
import { readPixelsToArray } from '@luma.gl/webgl';
import ColorRamp from '@/layers/color-ramp';
import updateTransformVs from './particle-layer-update-transform.vs.glsl';
import {
  getViewportBounds,
  getViewportGlobeCenter,
  getViewportGlobeRadius,
  isViewportGlobe,
} from './utils/viewport.js';

const FPS = 30;

const DEFAULT_COLOR = [255, 255, 255, 255];

const defaultProps = {
  ...LineLayer.defaultProps,

  image: { type: 'image', value: null, async: true },
  imageUnscale: { type: 'array', value: null },

  numParticles: { type: 'number', min: 1, max: 1000000, value: 5000 },
  maxAge: { type: 'number', min: 1, max: 255, value: 100 },
  speedFactor: { type: 'number', min: 0, max: 1, value: 1 },

  color: { type: 'color', value: DEFAULT_COLOR },
  width: { type: 'number', value: 1 },
  animate: true,
  bounds: { type: 'array', value: [-180, -90, 180, 90], compare: true },
  wrapLongitude: true,
};

export default class ParticleLayer extends LineLayer {
  pointerMoveEventHandler(info) {
    const getPixelInfo = this.state.getPixelInfo || this.props.getPixelInfo;
    if (this.state.timer) {
      clearInterval(this.state.timer);
    }
    if (getPixelInfo) {
      this.state.timer = setTimeout(() => {
        try {
          const event = info.srcEvent;
          const { image, bounds } = this.props;
          const { viewport } = this.context;
          const latLon = viewport.unproject([event.offsetX, event.offsetY]);
          const x =
            image.width * ((latLon[0] - bounds[0]) / (bounds[2] - bounds[0]));
          const y =
            image.height * ((bounds[3] - latLon[1]) / (bounds[3] - bounds[1]));
          const color = readPixelsToArray(image, {
            sourceX: x,
            sourceY: y,
            sourceWidth: 1,
            sourceHeight: 1,
          });
          if (x >= 0 && y >= 0 && x < image.width && y < image.height)
            getPixelInfo({
              uv: [
                color[0] + Math.trunc(color[2] / 10) / 10.0 - 100.0,
                color[1] + (color[2] % 10) / 10 - 100.0,
              ],
              offset: [event.offsetX, event.offsetY],
              longitude: latLon[0],
              latitude: latLon[1],
              sourceX: x,
              sourceY: y,
              image: this.props.image,
              color: color,
            });
          else getPixelInfo(undefined);
        } catch (e) {
          console.log(e);
        }
      }, 300);
    }
  }
  pointerOutEventHandler() {
    if (this.state.timer) {
      clearInterval(this.state.timer);
    }

    this.state.timer = setTimeout(() => {
      const getPixelInfo = this.state.getPixelInfo || this.props.getPixelInfo;
      if (getPixelInfo) {
        getPixelInfo(undefined);
      }
    }, 300);
  }
  addEventHandlers() {
    const { eventManager } = this.context.deck;
    eventManager?.on('pointermove', this.state.pointerMoveEventHandler);
    eventManager?.on('pointerout', this.state.pointerOutEventHandler);
  }
  removeEventHandlers() {
    const { eventManager } = this.context.deck;
    eventManager?.off('pointermove', this.state.pointerMoveEventHandler);
    eventManager?.off('pointerout', this.state.pointerOutEventHandler);
  }
  getShaders() {
    return {
      ...super.getShaders(),
      inject: {
        'vs:#decl': `
          varying float drop;
          varying float speed;
          uniform float speedFactor;
          uniform vec2 imageUnscale;
          const vec2 DROP_POSITION = vec2(0);
        `,
        'vs:#main-start': `
          drop = float(instanceSourcePositions.xy == DROP_POSITION || instanceTargetPositions.xy == DROP_POSITION);
          vec2 speedOffset = (instanceTargetPositions.xy - instanceSourcePositions.xy) / speedFactor;
          speed = sqrt(speedOffset.x * speedOffset.x + speedOffset.y * speedOffset.y) / sqrt(2.0);
        `,
        'fs:#decl': `
          varying float drop;
          varying float speed;
          uniform float min;
          uniform float max;
          uniform sampler2D colorRamp;
          uniform sampler2D bitmapTexture;
        `,
        'fs:#main-start': `
          if (drop > 0.5) discard;
        `,
        'fs:#main-end': this.props.colorRamp
          ? `
          vec4 speedColor = texture2D(colorRamp, vec2((speed - min) / (max - min), 0.0));
          gl_FragColor.rgb = speedColor.rgb;
        `
          : '',
      },
    };
  }

  initializeState() {
    const { gl } = this.context;
    if (!isWebGL2(gl)) {
      throw new Error('WebGL 2 is required');
    }

    super.initializeState({});

    this._setupTransformFeedback();

    const attributeManager = this.getAttributeManager();
    attributeManager.remove([
      'instanceSourcePositions',
      'instanceTargetPositions',
      'instanceColors',
      'instanceWidths',
    ]);
    this.setState({
      pointerMoveEventHandler: (info) => this.pointerMoveEventHandler(info),
      pointerOutEventHandler: () => this.pointerOutEventHandler(),
    });
    // this.addEventHandlers();
  }

  updateState({ props, oldProps, changeFlags, context }) {
    const {
      numParticles,
      maxAge,
      color,
      width,
      colorRamp,
      visible,
      pickable,
      windyHeight,
    } = props;
    const { gl } = context;

    super.updateState({ props, oldProps, changeFlags });

    if (!numParticles || !maxAge || !width) {
      this._deleteTransformFeedback();
      return;
    }

    if (
      numParticles !== oldProps.numParticles ||
      maxAge !== oldProps.maxAge ||
      color[0] !== oldProps.color[0] ||
      color[1] !== oldProps.color[1] ||
      color[2] !== oldProps.color[2] ||
      color[3] !== oldProps.color[3] ||
      width !== oldProps.width ||
      windyHeight !== oldProps.windyHeight
    ) {
      this._setupTransformFeedback();
    }
    if (colorRamp !== oldProps.colorRamp) {
      this.setState({
        colorRamp: ColorRamp.build(gl, colorRamp),
      });
    }
    if (
      changeFlags?.updateTriggersChanged?.getPixelInfo &&
      this.props.getPixelInfo
    ) {
      this.setState({
        getPixelInfo: this.props.getPixelInfo,
      });
    }
    if (visible !== oldProps.visible || pickable !== oldProps.pickable) {
      if (visible && pickable) {
        this.removeEventHandlers();
        this.addEventHandlers();
      } else {
        this.removeEventHandlers();
      }
    }
  }

  finalizeState() {
    this._deleteTransformFeedback();
    this.removeEventHandlers();
    super.finalizeState();
  }

  draw({ uniforms }) {
    const { gl } = this.context;
    if (!isWebGL2(gl)) {
      return;
    }

    const { initialized } = this.state;
    if (!initialized) {
      return;
    }

    const { viewport } = this.context;
    const {
      animate,
      opacity,
      image,
      speedFactor,
      imageUnscale,
      min,
      max,
    } = this.props;
    const {
      sourcePositions,
      targetPositions,
      sourcePositions64Low,
      targetPositions64Low,
      colors,
      widths,
      model,
      viewportBlock,
    } = this.state;

    const currentSpeedFactor = speedFactor / 2 ** (viewport.zoom + 7);
    model.setAttributes({
      instanceSourcePositions: sourcePositions,
      instanceTargetPositions: targetPositions,
      instanceSourcePositions64Low: sourcePositions64Low,
      instanceTargetPositions64Low: targetPositions64Low,
      instanceColors: colors,
      instanceWidths: widths,
    });
    super.draw({
      uniforms: {
        ...uniforms,
        min,
        max,
        colorRamp: this.state.colorRamp,
        bitmapTexture: image,
        speedFactor: currentSpeedFactor,
        imageUnscale: imageUnscale || [0, 0],
        opacity: viewportBlock ? 0.3 * opacity : opacity,
      },
    });

    if (animate) {
      this.requestStep();
    }
  }

  _setupTransformFeedback() {
    const { gl } = this.context;
    if (!isWebGL2(gl)) {
      return;
    }

    const { initialized } = this.state;
    if (initialized) {
      this._deleteTransformFeedback();
    }

    const { numParticles, maxAge, color, width, windyHeight = 0 } = this.props;

    // sourcePositions/targetPositions buffer layout:
    // |          age0         |          age1         |          age2         |...|          ageN         |
    // |pos1,pos2,pos3,...,posN|pos1,pos2,pos3,...,posN|pos1,pos2,pos3,...,posN|...|pos1,pos2,pos3,...,posN|
    const numInstances = numParticles * maxAge;
    const numAgedInstances = numParticles * (maxAge - 1);
    const sourcePositions = new Buffer(gl, new Float32Array(numInstances * 3));
    const targetPositions = new Buffer(gl, new Float32Array(numInstances * 3));
    const sourcePositions64Low = new Float32Array([0, 0, 0]); // constant attribute
    const targetPositions64Low = new Float32Array([0, 0, 0]); // constant attribute
    const colors = new Buffer(
      gl,
      new Float32Array(
        new Array(numInstances)
          .fill(undefined)
          .map((_, i) => {
            const age = Math.floor(i / numParticles);
            return [
              color[0],
              color[1],
              color[2],
              (color[3] ?? 255) * (1 - age / maxAge),
            ].map((d) => d / 255);
          })
          .flat()
      )
    );
    const widths = new Float32Array([width]); // constant attribute

    const transform = new Transform(gl, {
      sourceBuffers: {
        sourcePosition: sourcePositions,
      },
      feedbackBuffers: {
        targetPosition: targetPositions,
      },
      feedbackMap: {
        sourcePosition: 'targetPosition',
      },
      vs: updateTransformVs,
      elementCount: numParticles,
      uniforms: { windyHeight },
    });

    this.setState({
      initialized: true,
      numInstances,
      numAgedInstances,
      sourcePositions,
      targetPositions,
      sourcePositions64Low,
      targetPositions64Low,
      colors,
      widths,
      transform,
    });
  }

  _runTransformFeedback() {
    const { gl } = this.context;
    if (!isWebGL2(gl)) {
      return;
    }

    const { initialized } = this.state;
    if (!initialized) {
      return;
    }

    const { viewport, timeline } = this.context;
    const {
      image,
      imageUnscale,
      bounds,
      numParticles,
      speedFactor,
      maxAge,
    } = this.props;
    const {
      numAgedInstances,
      transform,
      previousViewportZoom,
      previousTime,
    } = this.state;
    const time = timeline.getTime();
    if (!image || time === previousTime) {
      return;
    }

    // viewport
    const viewportGlobe = isViewportGlobe(viewport);
    const viewportGlobeCenter = getViewportGlobeCenter(viewport);
    const viewportGlobeRadius = getViewportGlobeRadius(viewport);
    const viewportBounds = getViewportBounds(viewport);
    const viewportZoomChangeFactor =
      2 ** ((previousViewportZoom - viewport.zoom) * 4);

    // speed factor for current zoom level
    const currentSpeedFactor = speedFactor / 2 ** (viewport.zoom + 7);

    // update particles age0
    const uniforms = {
      viewportGlobe,
      viewportGlobeCenter: viewportGlobeCenter || [0, 0],
      viewportGlobeRadius: viewportGlobeRadius || 0,
      viewportBounds: viewportBounds || [0, 0, 0, 0],
      viewportZoomChangeFactor: viewportZoomChangeFactor || 0,

      bitmapTexture: image,
      imageUnscale: imageUnscale || [0, 0],
      bounds,
      numParticles,
      maxAge,
      speedFactor: currentSpeedFactor,

      time,
      seed: Math.random(),
    };
    transform.run({ uniforms });

    // update particles age1-age(N-1)
    // copy age0-age(N-2) sourcePositions to age1-age(N-1) targetPositions
    const sourcePositions =
      transform.bufferTransform.bindings[transform.bufferTransform.currentIndex]
        .sourceBuffers.sourcePosition;
    const targetPositions =
      transform.bufferTransform.bindings[transform.bufferTransform.currentIndex]
        .feedbackBuffers.targetPosition;
    sourcePositions.copyData({
      sourceBuffer: targetPositions,
      readOffset: 0,
      writeOffset: numParticles * 4 * 3,
      size: numAgedInstances * 4 * 3,
    });

    transform.swap();

    // const {sourcePositions, targetPositions} = this.state;
    // console.log(uniforms, sourcePositions.getData().slice(0, 6), targetPositions.getData().slice(0, 6));

    this.state.previousViewportZoom = viewport.zoom;
    this.state.previousTime = time;
  }

  _resetTransformFeedback() {
    const { gl } = this.context;
    if (!isWebGL2(gl)) {
      return;
    }

    const { initialized } = this.state;
    if (!initialized) {
      return;
    }

    const { numInstances, sourcePositions, targetPositions } = this.state;

    sourcePositions.subData({ data: new Float32Array(numInstances * 3) });
    targetPositions.subData({ data: new Float32Array(numInstances * 3) });
  }

  _deleteTransformFeedback() {
    const { gl } = this.context;
    if (!isWebGL2(gl)) {
      return;
    }

    const { initialized } = this.state;
    if (!initialized) {
      return;
    }

    const { sourcePositions, targetPositions, colors, transform } = this.state;

    sourcePositions.delete();
    targetPositions.delete();
    colors.delete();
    transform.delete();

    this.setState({
      initialized: false,
      sourcePositions: undefined,
      targetPositions: undefined,
      sourcePositions64Low: undefined,
      targetPositions64Low: undefined,
      colors: undefined,
      widths: undefined,
      transform: undefined,
    });
  }

  requestStep() {
    const viewport = this.context.viewport;
    if (viewport && !this.state.viewportBlock) {
      if (
        this.state.oldViewState?.latitude === viewport.latitude &&
        this.state.oldViewState?.longitude === viewport.longitude &&
        this.state.oldViewState?.zoom === viewport.zoom
      ) {
        const { stepRequested } = this.state;
        if (stepRequested) {
          return;
        }
        this.state.stepRequested = true;
        setTimeout(() => {
          this.step();
          this.state.stepRequested = false;
        }, 1000 / FPS);
      } else {
        this.state.viewportBlock = true;
        this.step();
        setTimeout(() => {
          this.state.viewportBlock = false;
          this.requestStep();
        }, 500);
      }
      this.state.oldViewState = {
        latitude: viewport.latitude,
        longitude: viewport.longitude,
        zoom: viewport.zoom,
      };
    }
  }

  step() {
    this._runTransformFeedback();

    this.setNeedsRedraw();
  }

  clear() {
    this._resetTransformFeedback();

    this.setNeedsRedraw();
  }
}

ParticleLayer.layerName = 'ParticleLayer';
ParticleLayer.defaultProps = defaultProps;
