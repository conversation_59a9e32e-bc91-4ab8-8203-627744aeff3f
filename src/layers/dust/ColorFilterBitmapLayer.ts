import { BitmapLayer, Color, LayerContext } from 'deck.gl/typed';

export class ColorFilterBitmapLayer extends BitmapLayer<{
  discardColor: Color;
}> {
  initializeState() {
    super.initializeState();
  }
  getShaders() {
    return {
      ...super.getShaders(),
      inject: {
        'fs:#decl': `
          uniform vec4 discardColor;
        `,
        'fs:DECKGL_FILTER_COLOR': `
          if(color.r == discardColor.r && color.g == discardColor.g && color.b == discardColor.b){
            discard;
          }
         `,
      },
    };
  }
  finalizeState(context: LayerContext) {
    super.finalizeState(context);
  }
  draw(opts: any) {
    const { uniforms } = opts;
    const [r, g, b, a = 255] = this.props.discardColor;
    const discardColor = [r, g, b, a].map((v) => v / 255);

    uniforms.discardColor = discardColor;
    super.draw(opts);
  }
}
