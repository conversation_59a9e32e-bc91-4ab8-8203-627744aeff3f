import { PathLayer } from 'deck.gl/typed';

export class TranslatedPathLayer<DataT> extends PathLayer<
  DataT,
  {
    offsetY?: number;
  }
> {
  getShaders() {
    const shaders = super.getShaders();

    shaders.inject = {
      'vs:#decl': `uniform float offsetY;`,
      'vs:DECKGL_FILTER_GL_POSITION': `position.y += offsetY;`,
    };

    return shaders;
  }
  draw(opts: any) {
    const offsetY = this.props.offsetY ?? 0;

    opts.uniforms.offsetY = offsetY;
    super.draw(opts);
  }
}

TranslatedPathLayer.layerName = 'translated-path-layer';
