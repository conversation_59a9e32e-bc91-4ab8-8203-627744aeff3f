import { CompositeLayer } from '@deck.gl/core';
import type { UpdateStateInfo } from '@deck.gl/core/lib/layer';
import isEqual from 'lodash/isEqual';
import { calculateTextureTileInfo, MAX_LAT, MIN_LNG, TILE_HEIGHT, TILE_WIDTH } from '@/utils/tiles';
import TextureBitmapLayer from './bitmap-layer/bitmap-layer';
import GL from '@luma.gl/constants';
import { loadImageData } from '@/utils/image';

export default class GeoJsonTextureLayer extends CompositeLayer<any, any> {
  initializeState() {
    this.state.tileMap = {};
  }
  updateState(opt: UpdateStateInfo<any>): void {
    const { oldProps, props } = opt;
    const {
      url: oldUrl,
      map: oldMap,
      colorRamp: oldColorRamp,
      decoder: oldDecoder,
      min: oldMin,
      max: oldMax,
    } = oldProps;
    const { url, map, visible, colorRamp, decoder, min, max } = props;
    if (
      (oldUrl !== url ||
        JSON.stringify(colorRamp) !== JSON.stringify(oldColorRamp) ||
        max !== oldMax ||
        min !== oldMin ||
        oldDecoder !== decoder ||
        visible === visible ||
        !isEqual(oldMap, map)) &&
      visible
    ) {
      if (this.props.map) {
        this.setState({ map: this.props.map });
        this.initTextureMap();
      }
    }
    super.updateState(opt);
  }
  private initTextureMap() {
    if (this.state.map) {
      const { minLng, maxLng, minLat, maxLat } = this.state.map;
      const { tiles } = calculateTextureTileInfo(minLng, maxLng, minLat, maxLat);
      this.state.tiles = tiles;
      this.state.wait = true;
    }
  }
  renderLayers() {
    return this.state.tiles.map((tile: number[]) => {
      return new TextureBitmapLayer({
        ...this.props,
        id: `${this.props.id}-${tile[0]}-${tile[1]}`,
        bounds: [
          MIN_LNG + tile[0] * TILE_WIDTH,
          MAX_LAT - (tile[1] + 1) * TILE_HEIGHT,
          MIN_LNG + (tile[0] + 1) * TILE_WIDTH,
          MAX_LAT - tile[1] * TILE_HEIGHT,
        ],
        textureParameters: {
          [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
          [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
        },
        image: this.loadImageData(this.props.url.replace('[]', 0).replace('{x}', tile[0]).replace('{y}', tile[1])),
        colorRamp: this.props.colorRamp,
      });
    });
  }
  setMapTile(url: string, data: any) {
    if (!this.state.settings) {
      this.setState({
        tileMap: {
          ...this.state.tileMap,
          [url]: data,
        },
      });
      this.state.settings = true;
      setTimeout(() => {
        this.state.settings = false;
      }, 40);
    } else {
      setTimeout(() => {
        this.setMapTile(url, data);
        this.state.settings = true;
      }, 40);
    }
  }
  loadImageData(url: string) {
    if (this.state.tileMap[url] === undefined) {
      this.state.tileMap[url] = null;
      loadImageData(url).then((data) => {
        this.setMapTile(url, data);
      });
    }
    return this.state.tileMap[url] ? this.state.tileMap[url] : null;
  }
}

GeoJsonTextureLayer.layerName = 'geojson-texture-layer';