/**
 * Pack the top 12 bits of two normalized floats into 3 8-bit (rgb) values
 * This enables addressing 4096x4096 individual pixels
 *
 * returns vec3 encoded RGB colors
 *  result.r - top 8 bits of u
 *  result.g - top 8 bits of v
 *  result.b - next 4 bits of u and v: (u + v * 16)
 */
const packUVsIntoRGB = `
vec3 packUVsIntoRGB(vec2 uv) {
  // Extract the top 8 bits. We want values to be truncated down so we can add a fraction
  vec2 uv8bit = floor(uv * 256.);

  // Calculate the normalized remainders of u and v parts that do not fit into 8 bits
  // Scale and clamp to 0-1 range
  vec2 uvFraction = fract(uv * 256.);
  vec2 uvFraction4bit = floor(uvFraction * 16.);

  // Remainder can be encoded in blue channel, encode as 4 bits for pixel coordinates
  float fractions = uvFraction4bit.x + uvFraction4bit.y * 16.;

  return vec3(uv8bit, fractions) / 255.;
}
`;

export default `
#define SHADER_NAME bitmap-layer-fragment-shader

#ifdef GL_ES
precision highp float;
#endif

uniform sampler2D bitmapTexture;
uniform sampler2D colorRamp;

varying vec2 vTexCoord;
varying vec2 vTexPos;

uniform float desaturate;
uniform vec4 transparentColor;
uniform vec3 tintColor;
uniform float opacity;

uniform float coordinateConversion;
uniform vec4 bounds;
uniform bool smooth;

/* projection utils */
const float TILE_SIZE = 512.0;
const float PI = 3.1415926536;
const float WORLD_SCALE = TILE_SIZE / PI / 2.0;

// from degrees to Web Mercator
vec2 lnglat_to_mercator(vec2 lnglat) {
  float x = lnglat.x;
  float y = clamp(lnglat.y, -89.9, 89.9);
  return vec2(
    radians(x) + PI,
    PI + log(tan(PI * 0.25 + radians(y) * 0.5))
  ) * WORLD_SCALE;
}

// from Web Mercator to degrees
vec2 mercator_to_lnglat(vec2 xy) {
  xy /= WORLD_SCALE;
  return degrees(vec2(
    xy.x - PI,
    atan(exp(xy.y - PI)) * 2.0 - PI * 0.5
  ));
}
/* End projection utils */

// apply desaturation
vec3 color_desaturate(vec3 color) {
  float luminance = (color.r + color.g + color.b) * 0.333333333;
  return mix(color, vec3(luminance), desaturate);
}

// apply tint
vec3 color_tint(vec3 color) {
  return color * tintColor;
}

// blend with background color
vec4 apply_opacity(vec3 color, float alpha) {
  return mix(transparentColor, vec4(color, 1.0), alpha);
}

vec2 getUV(vec2 pos) {
  return vec2(
    (pos.x - bounds[0]) / (bounds[2] - bounds[0]),
    (pos.y - bounds[3]) / (bounds[1] - bounds[3])
  );
}

float decoder(vec4 data) {
    {DECODER};
}
float getOpacity(float value) {
  return {OPACITY_DECODER};
}
${packUVsIntoRGB}
bool invalid (vec4 color){
  if(color.r == 0.0 && color.g == 0.0 && color.b == 0.0){
    return false;
  }
  return true;
}
vec4 lookup_mix (vec4 val1, vec4 val2, float a){
  if(!invalid(val1) && !invalid(val2)) {
    return vec4(0.0, 0.0, 0.0, 0.0);
  } else if(!invalid(val1) && invalid(val2)) {
    return val2;
  } else if(invalid(val1) && !invalid(val2)) {
    return val1;
  } else {
    return mix(val1, val2, a);
  }
}
vec4 lookup_data(sampler2D texture, const vec2 resolution, const vec2 uv) {
  vec2 px = 1.0 / resolution;
  vec2 vc = (floor(uv * resolution)) * px;
  vec2 f = fract(uv * resolution);
  vec4 tl = texture2D(texture, vc);
  vec4 tr = texture2D(texture, vc + vec2(px.x, 0));
  vec4 bl = texture2D(texture, vc + vec2(0, px.y));
  vec4 br = texture2D(texture, vc + px);
  return lookup_mix(lookup_mix(tl, tr, f.x), lookup_mix(bl, br, f.x), f.y);
}
void main(void) {
  vec2 uv = vTexCoord;
  vec4 bitmapColor;
  if (coordinateConversion < -0.5) {
    vec2 lnglat = mercator_to_lnglat(vTexPos);
    uv = getUV(lnglat);
  } else if (coordinateConversion > 0.5) {
    vec2 commonPos = lnglat_to_mercator(vTexPos);
    uv = getUV(commonPos);
  }

  if(smooth) {
    bitmapColor = lookup_data(bitmapTexture, vec2(400.0, 400.0), uv);
  } else {
    bitmapColor = texture2D(bitmapTexture, uv);
  }

  if(bitmapColor.r != 0.0 || bitmapColor.g != 0.0 || bitmapColor.b != 0.0) {
    float value = decoder(bitmapColor);
  
    vec2 ramp_pos = vec2(
      fract(16.0 * value),
      floor(16.0 * value) / 16.0);
      
      gl_FragColor = texture2D(colorRamp, ramp_pos);
      gl_FragColor.a = getOpacity(value);
      // gl_FragColor = apply_opacity(color_tint(color_desaturate(bitmapColor.rgb)), getOpacity(value));
  } else {
    discard;
  }
  geometry.uv = uv;
  DECKGL_FILTER_COLOR(gl_FragColor, geometry);

  if (picking_uActive) {
    // Since instance information is not used, we can use picking color for pixel index
    gl_FragColor.rgb = packUVsIntoRGB(uv);
  }
}
`;
