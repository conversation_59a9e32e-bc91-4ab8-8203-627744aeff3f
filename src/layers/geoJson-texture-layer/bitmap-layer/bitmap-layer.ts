import { project32, picking } from '@deck.gl/core';

import vs from './bitmap-layer-vertex';
import fs from './bitmap-layer-fragment';
import { mergeShaders } from '@deck.gl/core/src/utils/shader';
import { BitmapLayer } from '@deck.gl/layers';
import type { UpdateStateInfo } from '@deck.gl/core/lib/layer';
import ColorRamp from '@/layers/color-ramp';
import GL from '@luma.gl/constants';

/*
 * @class
 * @param {object} props
 * @param {number} props.transparentColor - color to interpret transparency to
 * @param {number} props.tintColor - color bias
 */
export default class TextureBitmapLayer extends BitmapLayer<any, any> {
  constructor(props1: any, props2?: any) {
    super(props1, {
      ...props2,
      textureParameters: {
        [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
        [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
      },
    });
  }
  initializeState(params: any) {
    super.initializeState(params);
    this.setState({
      layerType: 'texture-bitmap-layer',
    });
  }
  getShaders() {
    let shaders = {};
    const { min, max, decoder, opacity, filtersChannel } = this.props;
    if (decoder) {
      shaders = {
        vs,
        fs: fs
          .replace(
            /{DECODER}/g,
            (decoder.includes('return') ? '' : 'return ') +
              decoder.replace(/{min}/g, min?.toFixed(1)).replace(/{max}/g, max?.toFixed(1)),
          )
          .replace(/{OPACITY_DECODER}/g, opacity ? Number(opacity).toFixed(1) : '1.0')
          .replace(/{filtersChannel}/g, filtersChannel || 0),
        modules: [project32, picking],
      };
    } else {
      shaders = { vs, fs: fs.replace('{DECODER}', 'return 0.0;'), modules: [project32, picking] };
    }
    for (const extension of this.props.extensions) {
      shaders = mergeShaders(shaders, extension.getShaders.call(this, extension));
    }

    return shaders;
  }

  updateState(opt: UpdateStateInfo<any>) {
    super.updateState(opt);
    const { props, oldProps, context } = opt;
    const {
      colorRamp: oldColorRamp,
      decoder: oldDecoder,
      min: oldMin,
      max: oldMax,
      filters: oldFilters,
      filtersChannel: oldFiltersChannel,
      gradualChange: oldGradualChange,
    } = oldProps;
    const { colorRamp, decoder, min, max, filters, filtersChannel, gradualChange } = props;
    if (
      decoder !== oldDecoder ||
      max !== oldMax ||
      min !== oldMin ||
      filtersChannel !== oldFiltersChannel ||
      gradualChange !== oldGradualChange
    ) {
      const { gl } = this.context;
      this.state.model?.delete();
      this.state.model = this._getModel(gl);
      this.getAttributeManager().invalidateAll();
    }
    if (colorRamp !== oldColorRamp || oldFilters !== filters) {
      const { gl } = context;
      this.setState({
        filters,
        colorRamp: ColorRamp.build(gl, colorRamp),
      });
    }
  }

  draw(opts) {
    const { uniforms, moduleParameters } = opts;
    const { model, coordinateConversion, bounds, disablePicking } = this.state;
    const { image, desaturate, transparentColor, tintColor, smooth } = this.props;

    if (moduleParameters.pickingActive && disablePicking) {
      return;
    }
    const filters = new Array(16).fill(-1).map((data, index) => this.state.filters?.[index] || data);
    // // TODO fix zFighting
    // Render the image
    if (image && model) {
      model
        .setUniforms(uniforms)
        .setUniforms({
          bitmapTexture: image,
          desaturate,
          transparentColor: transparentColor.map((x) => x / 255),
          tintColor: tintColor.slice(0, 3).map((x) => x / 255),
          filters: filters,
          filterCount: this.state.filters?.length || 0,
          coordinateConversion,
          bounds,
          colorRamp: this.state.colorRamp,
          smooth: smooth || false,
        })
        .draw();
    }
  }
}

TextureBitmapLayer.layerName = 'texture-bitmap-layer';
