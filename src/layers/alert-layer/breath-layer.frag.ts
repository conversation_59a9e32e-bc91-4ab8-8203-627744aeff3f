/* eslint-disable max-len */
export default `
  #define SHADER_NAME breath-layer-model-frag-shader
  #define INNER_RADIUS 0.2

  precision lowp float;

  varying vec4 vFillColor;
  varying vec2 unitPosition;
  varying float v_outer_radius;
  
  void main(void) {
    float dist = length(unitPosition);
    bool inInnerCircle = smoothstep(INNER_RADIUS - 0.001, INNER_RADIUS + 0.001, dist) < 1.0;
    gl_FragColor = vFillColor;
    if(inInnerCircle) {
      gl_FragColor.a = 1.0;
    } else {
      if(dist > INNER_RADIUS && dist < v_outer_radius) {
          gl_FragColor.a = dist * 0.55;
      }
      else {
          discard;
      }
    }

    DECKGL_FILTER_COLOR(gl_FragColor, geometry);

  }
`;
