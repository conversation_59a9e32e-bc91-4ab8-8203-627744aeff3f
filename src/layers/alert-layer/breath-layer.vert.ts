/* eslint-disable max-len */
export default `
#define SHADER_NAME breath-layer-model-vertex-shader
#define INNER_RADIUS 0.2
attribute lowp vec3 positions;

attribute lowp vec3 instancePositions;
attribute lowp vec3 instancePositions64Low;
attribute lowp float instanceRadius;
attribute lowp float instanceLineWidths;
attribute lowp vec4 instanceFillColors;
attribute lowp vec4 instanceLineColors;
attribute lowp vec3 instancePickingColors;

uniform lowp float opacity;
uniform lowp float radiusScale;
uniform lowp float radiusMinPixels;
uniform lowp float radiusMaxPixels;
uniform lowp float lineWidthScale;
uniform lowp float lineWidthMinPixels;
uniform lowp float lineWidthMaxPixels;
uniform lowp float stroked;
uniform lowp float u_time;

varying lowp vec4 vFillColor;
varying lowp vec2 unitPosition;
varying lowp float v_outer_radius;

void main(void) {
  geometry.worldPosition = instancePositions;

  // Multiply out radius and clamp to limits
  float outerRadiusPixels = clamp(
    project_size_to_pixel(radiusScale * instanceRadius),
    radiusMinPixels, radiusMaxPixels
  );
  
  // Multiply out line width and clamp to limits
  float lineWidthPixels = clamp(
    project_size_to_pixel(lineWidthScale * instanceLineWidths),
    lineWidthMinPixels, lineWidthMaxPixels
  );

  // outer radius needs to offset by half stroke width
  outerRadiusPixels += stroked * lineWidthPixels / 2.0;

  // position on the containing square in [-1, 1] space
  unitPosition = positions.xy;
  geometry.uv = unitPosition;
  geometry.pickingColor = instancePickingColors;

  vec3 offset = positions * project_pixel_size(outerRadiusPixels);
  DECKGL_FILTER_SIZE(offset, geometry);
  gl_Position = project_position_to_clipspace(instancePositions, instancePositions64Low, offset, geometry.position);
  DECKGL_FILTER_GL_POSITION(gl_Position, geometry);

  v_outer_radius = abs(sin(u_time * 1.7)) * (1.0 - INNER_RADIUS) + INNER_RADIUS;

  // Apply opacity to instance color, or return instance picking color
  vFillColor = vec4(instanceFillColors.rgb, instanceFillColors.a * opacity);
  DECKGL_FILTER_COLOR(vFillColor, geometry);
}
`