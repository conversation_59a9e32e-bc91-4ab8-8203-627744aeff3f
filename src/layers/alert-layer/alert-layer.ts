import { project32, picking, Layer } from '@deck.gl/core';
import GL from '@luma.gl/constants';
import { Model, Geometry } from '@luma.gl/core';
import vs from './breath-layer.vert';
import fs from './breath-layer.frag';
import defaultProps from './breath-layer-props';

export default class AlertLayer extends Layer<any, any> {
  getShaders() {
    return super.getShaders({ vs, fs, modules: [project32, picking] });
  }

  initializeState() {
    this.getAttributeManager().addInstanced({
      instancePositions: {
        size: 3,
        type: GL.DOUBLE,
        fp64: this.use64bitPositions(),
        transition: true,
        accessor: 'getPosition',
      },
      instanceFillColors: {
        size: this.props.colorFormat.length,
        transition: true,
        normalized: true,
        type: GL.UNSIGNED_BYTE,
        accessor: 'getFillColor',
        defaultValue: [0, 0, 0, 255],
      },
      instanceLineColors: {
        size: this.props.colorFormat.length,
        transition: true,
        normalized: true,
        type: GL.UNSIGNED_BYTE,
        accessor: 'getLineColor',
        defaultValue: [0, 0, 0, 255],
      },
      instanceLineWidths: {
        size: 1,
        transition: true,
        accessor: 'getLineWidth',
        defaultValue: 1,
      },
    });
    this.setState({
      startTime: new Date().getTime(),
    });
  }

  updateState({ props, oldProps, changeFlags }) {
    super.updateState({ props, oldProps, changeFlags });
    if (changeFlags.extensionsChanged) {
      const { gl } = this.context;
      this.state.model?.delete();
      this.state.model = this.getModel(gl);
      this.getAttributeManager().invalidateAll();
    }
  }

  draw({ uniforms }) {
    const { gl, viewport } = this.context;
    const {
      radiusUnits,
      radiusScale,
      radiusMinPixels,
      radiusMaxPixels,
      stroked,
      filled,
      lineWidthUnits,
      lineWidthScale,
      lineWidthMinPixels,
      lineWidthMaxPixels,
    } = this.props;

    const pointRadiusMultiplier =
      radiusUnits === 'pixels' ? viewport.metersPerPixel : 1;
    const lineWidthMultiplier =
      lineWidthUnits === 'pixels' ? viewport.metersPerPixel : 1;
    const elapsed = (new Date().getTime() - this.state.startTime) / 1000.0;

    gl.disable(GL.DEPTH_TEST);
    this.state.model
      .setUniforms(uniforms)
      .setUniforms({
        stroked: stroked ? 1 : 0,
        filled,
        radiusScale: radiusScale * pointRadiusMultiplier,
        radiusMinPixels,
        radiusMaxPixels,
        lineWidthScale: lineWidthScale * lineWidthMultiplier,
        lineWidthMinPixels,
        lineWidthMaxPixels,
        u_time: elapsed,
      })
      .draw();
      gl.enable(GL.DEPTH_TEST);
  }

  private getModel(gl) {
    // a square that minimally cover the unit circle
    const positions = [-1, -1, 0, 1, -1, 0, 1, 1, 0, -1, 1, 0];

    return new Model(gl, {
      ...this.getShaders(),
      id: this.props.id,
      geometry: new Geometry({
        drawMode: GL.TRIANGLE_FAN,
        vertexCount: 4,
        attributes: {
          positions: { size: 3, value: new Float32Array(positions) },
        },
      }),
      isInstanced: true,
    });
  }
}

AlertLayer.layerName = 'breath-layer';
AlertLayer.defaultProps = defaultProps;
