import { Texture2D } from '@luma.gl/webgl';

export default class TextureDrawer {
  public static loadImageTexture(
    gl: WebGL2RenderingContext,
    url: string,
  ): Promise<Texture2D> {
    return new Promise((resolve) => {
      const img = new Image();
      img.src = url;
      img.setAttribute('crossOrigin', 'anonymous');
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          return;
        }
        const { width, height } = img;
        canvas.width = width;
        canvas.height = height;
        ctx.drawImage(img, 0, 0);
        const { data } = ctx.getImageData(0, 0, width, height);
        const texture: any = TextureDrawer.createTexture(gl, {
          data,
          width,
          height,
        });
        texture.userData = {
          width,
          height,
          data,
        };
        canvas.remove();
        resolve(texture);
      };
    });
  }

  public static createTexture(
    gl: WebGL2RenderingContext | WebGLRenderingContext,
    options: any,
  ) {
    const textureOptions = {
      format: gl.RGBA,
      dataFormat: gl.RGBA,
      type: gl.UNSIGNED_BYTE,
      parameters: {
        [gl.TEXTURE_MAG_FILTER]: options.filter || gl.NEAREST,
        [gl.TEXTURE_MIN_FILTER]: options.filter || gl.NEAREST,
        [gl.TEXTURE_WRAP_S]: gl.CLAMP_TO_EDGE,
        [gl.TEXTURE_WRAP_T]: gl.CLAMP_TO_EDGE,
      },
      // pixelStore: { [gl.UNPACK_FLIP_Y_WEBGL]: true },
      mipmaps: false,
      ...options,
    };

    return new Texture2D(gl, textureOptions);
  }
}
