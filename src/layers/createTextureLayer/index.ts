import { tileLayerBaseConfig } from '@/utils/baseLayerConfig';
import { TileLayer } from 'deck.gl';
import { MaskExtension } from '@deck.gl/extensions';
import getTranslateImageData from './getTranslateImageData';
import TextureBitmapLayer from './bitmap-layer/bitmap-layer';

export interface TextureLayerGenerateProps {
  id: string;
  dataUrl: string;
  colorRamp: Record<string, string>;
  min?: number;
  max?: number;
  decoder?: string;
  visible?: boolean;
  filters?: number[];
  filtersChannel?: number;
  maskId?: string;
  opacity?: number;
  smooth?: boolean;
  maskImageSrc?: string;
  onViewportLoad?: (tiles: any[]) => void;
}

const baseExtent = [91.250096, 37.045865, 127.086488, 54.262446];

export const createTextureLayer = ({
  id,
  dataUrl,
  colorRamp,
  min,
  max,
  decoder,
  visible,
  filters = [],
  filtersChannel,
  maskId = 'geojson-mask',
  opacity = 1,
  smooth = true,
  onViewportLoad,
}: TextureLayerGenerateProps) => {
  return new TileLayer({
    ...tileLayerBaseConfig,
    id,
    // @ts-ignore
    maskId,
    extensions: [new MaskExtension()],
    data: dataUrl,
    extent: baseExtent,
    maxZoom: 7,
    minZoom: 6,
    tileSize: 256,
    colorFormat: 'RGBA',
    pickable: true,
    // 色带更改后，重新渲染图层
    shouldUpdate: (prevProps: any, nextProps: any) => {
      return (
        prevProps.colorRamp !== nextProps.colorRamp ||
        prevProps.dataUrl !== nextProps.dataUrl ||
        prevProps.visible !== nextProps.visible ||
        prevProps.min !== nextProps.min ||
        prevProps.max !== nextProps.max ||
        prevProps.filters !== nextProps.filters ||
        prevProps.filtersChannel !== nextProps.filtersChannel ||
        prevProps.decoder !== nextProps.decoder
      );
    },
    visible,
    renderSubLayers: (props) => {
      const {
        bbox: { west, south, east, north },
      } = props.tile;

      return props?.data
        ? new TextureBitmapLayer(props, {
            pickable: true,
            data: null,
            image: props.data,
            bounds: [west, south, east, north],
            colorRamp,
            min,
            max,
            decoder,
            filters,
            filtersChannel,
            opacity,
            smooth,
          })
        : null;
    },
    getTileData: (tile) => {
      return getTranslateImageData(tile.url);
    },
    onViewportLoad(tiles) {
      if (typeof onViewportLoad === 'function') {
        // eslint-disable-next-line @typescript-eslint/no-invalid-this
        onViewportLoad(tiles);
      }
    },
  });
};
