import ColorRamp from '../color-ramp';
import { getTranslateImageData } from '@/utils/image';
import { COORDINATE_SYSTEM, picking, project32 } from '@deck.gl/core';
import type { UpdateStateInfo } from '@deck.gl/core/lib/layer';
import { mergeShaders } from '@deck.gl/core/src/utils/shader';
import { BitmapLayer } from '@deck.gl/layers';
import GL from '@luma.gl/constants';
import fs from './dynamic-layer-fragment';
// @ts-ignore
import { Texture2D } from '@luma.gl/webgl';
import vs from './dynamic-layer-vertex';
/*
 * @class
 * @param {object} props
 * @param {number} props.transparentColor - color to interpret transparency to
 * @param {number} props.tintColor - color bias
 */
export default class DynamicTextureLayer extends BitmapLayer<any, any> {
  ready = () => {};
  constructor(props1: any, props2?: any) {
    super(props1, {
      ...props2,
      textureParameters: {
        [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
        [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
      },
    });
  }
  initializeState(params: any) {
    super.initializeState(params);
    this.setState({
      layerType: 'dynamic-texture-layer',
      textureInterval: null,
    });
  }
  loadImage(startFrame, firstRequestFrame) {
    const { gl, imageList } = this.state;
    let loadImages = [];
    if (!this.props.imageList || !firstRequestFrame) {
      this.ready();
      return;
    }
    if (this.props.imageList.length > firstRequestFrame) {
      const outLength =
        startFrame + firstRequestFrame - this.props.imageList.length;
      if (outLength > 0) {
        loadImages = this.props.imageList
          .concat()
          .slice(startFrame, this.props.imageList.length)
          .concat(this.props.imageList.concat().slice(0, outLength));
      } else {
        loadImages = this.props.imageList
          .concat()
          .slice(startFrame, startFrame + firstRequestFrame);
      }
    } else {
      loadImages = this.props.imageList;
    }
    return Promise.all(
      loadImages.map(
        (image: string | HTMLImageElement) =>
          new Promise((inject) => {
            // 获取WebGL上下文+
            if (typeof image === 'string') {
              getTranslateImageData(image).then((data) => {
                inject(
                  new Texture2D(gl, {
                    data,
                    format: GL.RGBA,
                    dataFormat: GL.RGBA,
                    type: GL.UNSIGNED_BYTE,
                    parameters: {
                      [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
                      [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
                      [GL.TEXTURE_WRAP_S]: GL.CLAMP_TO_EDGE,
                      [GL.TEXTURE_WRAP_T]: GL.CLAMP_TO_EDGE,
                    },
                    mipmaps: true,
                  })
                );
              });
            } else {
              const texture = new Texture2D(gl, {
                data: image,
                format: GL.RGBA,
                dataFormat: GL.RGBA,
                type: GL.UNSIGNED_BYTE,
                parameters: {
                  [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
                  [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
                  [GL.TEXTURE_WRAP_S]: GL.CLAMP_TO_EDGE,
                  [GL.TEXTURE_WRAP_T]: GL.CLAMP_TO_EDGE,
                },
                mipmaps: true,
              });
              inject(texture);
            }
          })
      ) || []
    ).then((imageRes) => {
      if (
        this.props.imageList &&
        this.props.imageList.length > firstRequestFrame
      ) {
        const outLength =
          startFrame + firstRequestFrame - this.props.imageList.length;
        if (outLength > 0) {
          this.setState({
            waitImage: false,
            imageList: imageRes
              .concat()
              .slice(firstRequestFrame - outLength, firstRequestFrame)
              .concat(imageList.slice(outLength, startFrame))
              .concat(imageRes.slice(0, firstRequestFrame - outLength)),
          });
        } else {
          this.setState({
            waitImage: false,
            imageList: imageList
              .concat()
              .slice(0, startFrame)
              .concat(imageRes)
              .concat(
                imageList
                  .concat()
                  .slice(startFrame + firstRequestFrame, imageList.length)
              ),
          });
        }
      } else {
        this.setState({
          waitImage: false,
          imageList: imageRes,
        });
      }
      this.ready();
      this.resetTimeout();
    });
  }
  loadNext(num, inject) {
    const image = this.props.imageList[num];
    const { imageList } = this.state;
    const imgs = imageList.concat();
    const gl = this.state.gl;
    if (image) {
      // 获取WebGL上下文+
      if (typeof image === 'string') {
        getTranslateImageData(image).then((data) => {
          imgs[num] = new Texture2D(gl, {
            data,
            format: GL.RGBA,
            dataFormat: GL.RGBA,
            type: GL.UNSIGNED_BYTE,
            parameters: {
              [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
              [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
              [GL.TEXTURE_WRAP_S]: GL.CLAMP_TO_EDGE,
              [GL.TEXTURE_WRAP_T]: GL.CLAMP_TO_EDGE,
            },
            mipmaps: true,
          });
          this.setState({
            imageList: imgs,
          });
          if (typeof inject === 'function') {
            inject();
          }
        });
      } else {
        const imgs = imageList.concat();
        imgs[num] = new Texture2D(gl, {
          data: image,
          format: GL.RGBA,
          dataFormat: GL.RGBA,
          type: GL.UNSIGNED_BYTE,
          parameters: {
            [GL.TEXTURE_MAG_FILTER]: GL.NEAREST,
            [GL.TEXTURE_MIN_FILTER]: GL.NEAREST,
            [GL.TEXTURE_WRAP_S]: GL.CLAMP_TO_EDGE,
            [GL.TEXTURE_WRAP_T]: GL.CLAMP_TO_EDGE,
          },
          mipmaps: true,
        });
        this.setState({
          imageList: imgs,
        });
        if (typeof inject === 'function') {
          inject();
        }
      }
    }
  }
  getShaders() {
    let shaders = {};
    const {
      min,
      max,
      decoder,
      opacity,
      filtersChannel,
      discard = '',
      clearByRGBA = 0,
      colorRamp,
    } = this.props;
    const defines = {
      CLEAR_ZERO: clearByRGBA,
      HAS_DECODER: colorRamp && decoder ? 1 : 0,
    };
    if (decoder) {
      shaders = {
        vs,
        fs: fs
          .replace(
            /{DECODER}/g,
            (decoder.includes('return') ? '' : 'return ') +
              decoder
                .replace(/{min}/g, min?.toFixed(5))
                .replace(/{max}/g, max?.toFixed(5))
          )
          .replace(/{DISCARD}/g, discard)
          .replace(
            /{OPACITY_DECODER}/g,
            opacity ? Number(opacity).toFixed(3) : '1.0'
          )
          .replace(/{filtersChannel}/g, filtersChannel || 0),
        modules: [project32, picking],
        defines,
      };
    } else {
      shaders = {
        vs,
        fs: fs
          .replace('{DECODER}', 'return 0.0;')
          .replace(/{DISCARD}/g, discard)
          .replace(
            /{OPACITY_DECODER}/g,
            opacity ? Number(opacity).toFixed(3) : '1.0'
          )
          .replace(/{filtersChannel}/g, filtersChannel || 0),
        modules: [project32, picking],
        defines,
      };
    }
    for (const extension of this.props.extensions) {
      shaders = mergeShaders(
        shaders,
        extension.getShaders.call(this, extension)
      );
    }
    return shaders;
  }
  imageLoaded(ready, startFrame, frameLenth) {
    this.ready = ready;
    this.loadImage(startFrame, frameLenth);
  }
  updateState(opt: UpdateStateInfo<any>) {
    try {
      super.updateState(opt);

      const { props, oldProps, context } = opt;
      const {
        colorRamp: oldColorRamp,
        decoder: oldDecoder,
        min: oldMin,
        max: oldMax,
        filters: oldFilters,
        filtersChannel: oldFiltersChannel,
        gradualChange: oldGradualChange,
        visible: oldVisible,
        opacity: oldOpacity,
        clearByRGBA: oldClearByRGBA,
        imageList: oldImageList,
        fps: oldFps,
        frame: oldFrame, // 显示的实际帧，小于0时随机播放
        normalFrame: oldNormalFrame,
      } = oldProps;
      const {
        colorRamp,
        decoder,
        min,
        max,
        filters,
        filtersChannel,
        gradualChange,
        visible,
        opacity,
        clearByRGBA,
        imageList,
        fps,
        frame,
        normalFrame,
        // firstRequestImage,
      } = props;
      const { gl } = context;
      if (
        decoder !== oldDecoder ||
        max !== oldMax ||
        min !== oldMin ||
        filtersChannel !== oldFiltersChannel ||
        gradualChange !== oldGradualChange ||
        visible !== oldVisible ||
        opacity !== oldOpacity ||
        clearByRGBA !== oldClearByRGBA
      ) {
        this.state.model?.delete();
        this.state.model = this._getModel(gl);
        this.getAttributeManager().invalidateAll();
      }
      if (colorRamp !== oldColorRamp || oldFilters !== filters) {
        this.setState({
          filters,
          colorRamp: ColorRamp.build(gl, colorRamp),
        });
      }
      if (oldImageList !== imageList) {
        this.setState({
          waitImage: true,
          imageList: new Array(imageList.length).fill(null),
          gl,
        });
        // const nowFrame = Math.floor(frame / (normalFrame + 1));
        // this.loadImage(nowFrame, firstRequestImage);
      } else if (
        oldFrame !== frame ||
        oldFps !== fps ||
        oldNormalFrame !== normalFrame
      ) {
        this.resetTimeout();
      }
    } catch (e) {
      console.log(e);
    }
  }
  resetTimeout() {
    const { textureInterval, timeout, imageList } = this.state;
    const { fps, frame, normalFrame } = this.props;
    const perFps = 1000 / fps;
    if (timeout) {
      clearTimeout(timeout);
    }
    if (frame >= 0) {
      this.setState({
        frame,
      });
    } else {
      const timeoutRes = setTimeout(() => {
        this.setState({
          frame:
            Math.floor(Number(new Date()) / (perFps / (normalFrame + 1))) %
            ((imageList.length - 1) * (normalFrame + 1)),
        });
        if (textureInterval) {
          clearInterval(textureInterval);
        }
        const interval = setInterval(() => {
          this.setState({
            frame:
              Math.floor(Number(new Date()) / (perFps / (normalFrame + 1))) %
              ((imageList.length - 1) * (normalFrame + 1)),
          });
        }, perFps / (normalFrame + 1));
        this.setState({
          textureInterval: interval,
        });
      }, Number(new Date()) % perFps);
      this.setState({
        timeout: timeoutRes,
      });
    }
  }
  draw(opts: any) {
    const { uniforms, moduleParameters, context } = opts;
    const {
      imageList,
      frame,
      model,
      coordinateConversion,
      bounds,
      disablePicking,
    } = this.state;
    const {
      desaturate,
      transparentColor,
      tintColor,
      smooth,
      isCompute = false,
      maskId = '',
      normalFrame,
    } = this.props;
    if (moduleParameters.pickingActive && disablePicking) {
      return;
    }
    const filters = new Array(16)
      .fill(-1)
      .map((data, index) => this.state.filters?.[index] || data);
    // // TODO fix zFighting
    // Render the image
    const { maskChannels } = moduleParameters;
    const { viewport } = context;
    if (maskChannels && maskChannels[maskId]) {
      const {
        index,
        bounds,
        coordinateOrigin: fromCoordinateOrigin,
      } = maskChannels[maskId];
      let { coordinateSystem: fromCoordinateSystem } = maskChannels[maskId];
      uniforms.mask_enabled = true;
      uniforms.mask_channel = index;

      if (fromCoordinateSystem === COORDINATE_SYSTEM.DEFAULT) {
        fromCoordinateSystem = viewport.isGeospatial
          ? COORDINATE_SYSTEM.LNGLAT
          : COORDINATE_SYSTEM.CARTESIAN;
      }
      const opts = {
        modelMatrix: null,
        fromCoordinateOrigin,
        fromCoordinateSystem,
      };
      const bl = this.projectPosition([bounds[0], bounds[1], 0], opts);
      const tr = this.projectPosition([bounds[2], bounds[3], 0], opts);
      uniforms.mask_bounds = [bl[0], bl[1], tr[0], tr[1]];
    } else {
      if (maskId) {
        // log.warn(`Could not find a mask layer with id: ${maskId}`)();
      }
      uniforms.mask_enabled = false;
    }
    const nowFrame = Math.floor(frame / (normalFrame + 1));
    let ufs = {};
    if (
      model &&
      Array.isArray(imageList) &&
      imageList[nowFrame] &&
      imageList[nowFrame + 1 === imageList.length ? nowFrame : nowFrame + 1]
    ) {
      ufs = {
        startTexture: imageList[nowFrame],
        endTexture:
          imageList[
            nowFrame + 1 === imageList.length ? nowFrame : nowFrame + 1
          ],
        frameNumber: (frame % (normalFrame + 1)) / (normalFrame + 1),
        desaturate,
        transparentColor: transparentColor.map((x: number) => x / 255),
        tintColor: tintColor.slice(0, 3).map((x: number) => x / 255),
        filters: filters,
        filterCount: this.state.filters?.length || 0,
        coordinateConversion,
        bounds,
        colorRamp: this.state.colorRamp || [],
        smooth: !!smooth,
        isCompute,
      };
      model.setUniforms(uniforms).setUniforms(ufs).draw();
    }
  }
}

DynamicTextureLayer.layerName = 'dynamic-texture-layer';
DynamicTextureLayer.defaultProps = {
  fps: 2, // 关键帧帧数
  normalFrame: 20, // 补间帧
  firstRequestFrame: 10,
};
