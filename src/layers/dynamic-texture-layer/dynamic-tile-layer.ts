import { TileLayer } from 'deck.gl';

class DynamicTileLayer extends TileLayer<any, any> {
  renderLayers() {
    const res = super.renderLayers();
    if (typeof this.loadLayer === 'function') this.loadLayer();
    return res;
  }
  loadLayer() {
    const { frame, normalFrame, firstRequestFrame, onReady, onLoading } = this.props;
    const nowFrame = Math.floor(frame / (normalFrame + 1));
    // if (this.state.nowFrame === nowFrame) return;
    // this.state.nowFrame = nowFrame;
    if (this.state.player === 'loading') return;
    // 当前显示的瓦片
    const layers = this.state?.tileset?._selectedTiles
      ?.filter((tile) => tile.isVisible)
      .reduce((prev, curr) => (curr.layers && prev.concat(curr.layers)) || prev, []);

    // 当前帧无法显示的图层
    const waitingLayer = layers.filter((layer) => {
      return (
        !layer.state ||
        !layer.state.imageList[nowFrame] ||
        !layer.state.imageList[nowFrame + 1 === layer.state.imageList.length ? nowFrame : nowFrame + 1]
      );
    });
    if (typeof this.state?.waitPromise?.cancel === 'function') {
      this.state.waitPromise.cancel();
      this.state.waitPromise = null;
    }
    // 有layer初次渲染没有state等一轮在看
    if (layers.find((tile) => !tile.state)) {
      setTimeout(() => {
        this.loadLayer();
      });
      return;
    }
    const imageLength = layers.reduce((prev, curr) => {
      return Math.max(prev, curr.state?.imageList?.length);
    }, 0);
    if (waitingLayer.length !== 0) {
      // 中断显示预读后续firstRequestFrame帧
      if (this.state.player !== 'loading') {
        if (typeof onLoading === 'function') {
          onLoading();
        }
        this.setState({
          player: 'loading',
          downloadWaiting: false,
        });
      }
      function creatPromise(self) {
        let isCanceled = false;
        let cancel;
        const promise: Promise<void> & { cancel: () => void } = new Promise<void>((resolve, reject) => {
          Promise.all(
            layers.map(
              (layer) =>
                new Promise((inject) => {
                  layer.imageLoaded(inject, nowFrame, firstRequestFrame);
                }),
            ),
          ).then(() => {
            if (!isCanceled) {
              isCanceled = true;
              if (self.state.player !== 'ready') {
                if (typeof onReady === 'function') {
                  onReady();
                }
                self.setState({
                  player: 'ready',
                  downloadWaiting: true,
                });
                self.loadMore(nowFrame + 1 === imageLength ? 0 : nowFrame + 1, imageLength);
                self.setNeedsRedraw();
              }
              resolve();
            }
          });
          cancel = () => {
            isCanceled = true;
            reject();
          };
        });
        promise.cancel = cancel;
        return promise;
      }
      const waitPromise = creatPromise(this);
      this.state.waitPromise = waitPromise;
    }
  }
  loadMore(frame, imageLength) {
    const { downloadWaiting } = this.state;
    if (!downloadWaiting) return;
    const fps = this.props.fps || 2;
    const layers = this.state?.tileset?._selectedTiles
      ?.filter((tile) => tile.isVisible)
      .reduce((prev, curr) => (curr.layers && prev.concat(curr.layers)) || prev, []);
    let loadFrame = -1;
    for (let i = 0; i < imageLength; i++) {
      if (layers?.find((layer) => !layer.state?.imageList[(i + frame) % imageLength])) {
        loadFrame = (i + frame) % imageLength;
        break;
      }
    }
    if (loadFrame < 0) return;
    const startTime = Number(new Date());
    Promise.all(
      layers
        .filter((layer) => !layer.state?.imageList[loadFrame])
        .map((layer) => {
          return new Promise((inject) => {
            layer.loadNext(loadFrame, inject);
          });
        }),
    ).then(() => {
      const loadTime = Number(new Date()) - startTime;
      if (loadTime > 1000 / fps) {
        this.loadMore(loadFrame + 1 === imageLength ? 0 : loadFrame + 1, imageLength);
      } else {
        setTimeout(() => {
          this.loadMore(loadFrame + 1 === imageLength ? 0 : loadFrame + 1, imageLength);
        }, 1000 / fps - loadTime);
      }
    });
  }
  // updateState(opt: UpdateStateInfo<any>) {
  //   super.updateState(opt);
  //   try {
  //     const { props, oldProps } = opt;
  //     const {
  //       fps: oldFps,
  //       frame: oldFrame, // 显示的实际帧，小于0时随机播放
  //       normalFrame: oldNormalFrame,
  //     } = oldProps;
  //     const { fps, frame, normalFrame, onReady } = props;
  //     if (
  //       oldFrame !== frame ||
  //       oldFps !== fps ||
  //       oldNormalFrame !== normalFrame
  //     ) {
  //       // this.state?.tileset?._tiles
  //       //   ?.filter((t) => t.isVisible)
  //       //   .forEach((t) => {
  //       //     console.log(t.layers);
  //       //   });
  //       // const layers = this.state?.tileset?._tiles?.reduce(
  //       //   (prev, curr) => (curr.layers && prev.concat(curr.layers)) || prev,
  //       //   []
  //       // );
  //       // this.internalState?.subLayers.map((layer) => console.log(layer));
  //       // console.log(
  //       //   frame,
  //       //   fps,
  //       //   normalFrame,
  //       //   Math.floor(frame / (normalFrame + 1)),
  //       //   Math.ceil(frame / (normalFrame + 1))
  //       // );
  //       // onReady();
  //     }
  //   } catch (e) {
  //     console.log(e);
  //   }
  // }
}
DynamicTileLayer.layerName = 'DynamicTileLayer';
DynamicTileLayer.defaultProps = {
  firstRequestFrame: 10,
};
export default DynamicTileLayer;
