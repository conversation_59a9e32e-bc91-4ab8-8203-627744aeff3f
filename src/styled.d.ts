// import original module declarations
import 'styled-components';

// and extend them!
declare module 'styled-components' {
  export interface DefaultTheme {
    [key: string]: any;
    colors: {
      [key: string]: any;
      black: string;
      white: string;
      primary: string;
      hoveredPrimary: string;
      danger: string;
      hoveredDanger: string;
      hoveredGray: string;
      success: string;
      warning: string;
      orange: string;
      lightOrange: string;
      lightDanger: string;
      gray: {
        '1c1d24': string;
        '25262d': string;
        '31333d': string;
        '69696d': string;
        '838485': string;
        333: string;
        400: string;
        500: string;
        666: string;
        800: string;
        900: string;
        999: string;
        b5b5b8: string;
        c1c1c4: string;
        efefef: string;
        f7f7f9: string;
        e6e6e6: string;
        ececef: string;
        [key: string]: any;
      };
    };
  }
}
