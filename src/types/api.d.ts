declare namespace APIDUST {

  type TWechatMessage =  {
			/**   */
			toUserName: string;
			/**   */
			fromUserName: string;
			/**   */
			createTime: number;
			/**   */
			msgType: string;
			/**   */
			event: string;
			/**   */
			eventKey: string;
	} ;

  type TUserParam =  {
			/** 唯一ID，修改时必填  */
			id?: number;
			/** 用户名 例：小红 */
			name: string;
			/** 区划代码 例：120000 */
			regionCode: number;
			/** 手机号 例：15022722674 */
			phoneNumber: string;
	} ;

  type TUserUpdatePasswordParam =  {
			/** 原登录密码 例：12x3456 */
			originalPassword: string;
			/** 新登录密码 例：12x3456 */
			newPassword: string;
	} ;

  type TUserLoginParam =  {
			/** 手机号 例：13320201210 */
			phoneNumber: string;
			/** 密码  */
			password: string;
	} ;

  type TUserModel =  {
			/** 唯一ID  */
			id: number;
			/** 用户名 例：小红 */
			name: string;
			/** 行政区域 例：成都市 */
			region: string;
			/** 行政区划代码 例：120000 */
			regionCode: number;
			/** 手机号 例：15022722674 */
			phoneNumber: string;
			/** 角色，1：普通用户，2：系统管理员 例：1 */
			role: number;
			/** 用户状态 例：true */
			enabled: boolean;
			/** 最后登录时间  */
			lastLoginTime: string;
			/** 后续请求需要携带的token，用户信息和用户列表该字段为空  */
			token: string;
	} ;

  type TWechatQrcodeModel =  {
			/** 过期时间，单位秒  */
			expireSeconds: number;
			/** 二维码Url  */
			qrcodeUrl: string;
	} ;

  type TPageUserModel =  {
			/**   */
			totalPages: number;
			/**   */
			totalElements: number;
			/**   */
			pageable: TPageableObject;
			/**   */
			size: number;
			/**   */
			content: TUserModel[];
			/**   */
			number: number;
			/**   */
			sort: TSortObject;
			/**   */
			first: boolean;
			/**   */
			last: boolean;
			/**   */
			numberOfElements: number;
			/**   */
			empty: boolean;
	} ;

  type TPageableObject =  {
			/**   */
			pageNumber: number;
			/**   */
			pageSize: number;
			/**   */
			offset: number;
			/**   */
			sort: TSortObject;
			/**   */
			unpaged: boolean;
			/**   */
			paged: boolean;
	} ;

  type TSortObject =  {
			/**   */
			sorted: boolean;
			/**   */
			empty: boolean;
			/**   */
			unsorted: boolean;
	} ;

  type TDustSubscribeInfoModel =  {
			/** 行政区编码  */
			regionCode: number;
			/** 面积  */
			area: number;
			/** 是否发送通知  */
			send: boolean;
	} ;

  type TTileTokenModel =  {
			/** token  */
			token: string;
	} ;

  type TTextureTimePointModel =  {
			/** 纹理时间点数据，yyyy/MM/dd HH:mm  */
			timePoints: string;
			/** 数据名称 例：PM₂.₅_2021年10月10日_日均 */
			name: string;
			/** 大小  */
			size: number;
	} ;

  type TStationModel =  {
			/** 站点编号 例：510100001 */
			stationCode: string;
			/** 站点名称 例：成华区环保局 */
			stationName: string;
			/** 站点级别 例：国控站 */
			stationLevel: string;
			/** 经度  */
			lon: number;
			/** 纬度  */
			lat: number;
	} ;

  type TStationDetailModel =  {
			/** 站点编号 例：1001A */
			stationCode: string;
			/** 站点名称 例：万寿西宫 */
			stationName: string;
			/** 站点级别 例：国控站 */
			stationLevel: string;
			/** 监测时间  */
			dataTime: string;
			/** 经度 例：116.3621 */
			lon: number;
			/** 纬度 例：39.8784 */
			lat: number;
			/** aqi 例：43 */
			aqi: number;
			/** PM25浓度 例：23 */
			pm25: number;
			/** PM10浓度 例：43 */
			pm10: number;
			/** O3浓度 例：135 */
			o3: number;
			/** NO2浓度 例：16 */
			no2: number;
			/** CO浓度 例：0.4 */
			co: number;
			/** SO2浓度 例：3 */
			so2: number;
	} ;

  type TDustStatsEventRegionModel =  {
			/** 行政区编码 例：110000 */
			regionCode: number;
			/** 行政区名称 例：北京 */
			regionName: string;
			/** 沙尘影响面积 例：10 */
			area: number;
			/** 沙尘影响占比 例：0.01 */
			ratio: number;
			/** 沙尘事件数 例：10 */
			count: number;
	} ;

  type TDustStatsSendSiteModel =  {
			/** 境外沙尘事件数量  */
			jwCount: number;
			/** 境内沙尘事件数量  */
			jnCount: number;
			/** 行政区统计集合  */
			regionModels: TDustStatsEventRegionModel[];
	} ;

  type TDustStatsRegionCountModel =  {
			/** 行政区编码  */
			regionCode: number;
			/** 行政区名称  */
			regionName: string;
			/** 沙尘天数  */
			count: number;
	} ;

  type TDustStatsEventTotalModel =  {
			/** 沙尘事件总数  */
			count: number;
			/** 沙尘事件总面积  */
			area: number;
	} ;

  type TDustStatsEventDurationModel =  {
			/** 沙尘事件最长持续时间  */
			maxDuration: number;
			/** 沙尘事件持续时间分段集合  */
			models: TKeyValuePairModelStringLong[];
	} ;

  type TKeyValuePairModelStringLong =  {
			/** 名称 例：key */
			key: string;
			/** 值  */
			value: number;
	} ;

  type TDustStatsDateCountModule =  {
			/** 时间  */
			date: string;
			/** 沙尘天数  */
			count: number;
			/** 沙尘日期集合  */
			dustDataTimes: string[];
	} ;

  type TDustSourceModel =  {
			/** 编码 例：101 */
			code: number;
			/** 名称 例：塔克拉玛干沙漠 */
			name: string;
			/** 矢量  */
			geom: string;
			/** 面积  */
			area: number;
			/** 所属行政区编码  */
			regionCodes: number[];
			/** 所属行政区名称  */
			regions: string[];
	} ;

  type TDustDataTimePointModel =  {
			/** 数据时间  */
			dataTime: string;
			/** 是否为起沙地，0：是，1：否  */
			sandSite: boolean;
			/** 沙团数量  */
			count: number;
	} ;

  type TDustRecordRegionInfoModel =  {
			/** 沙团ID 例：202405211030_10 */
			dustId: string;
			/** 沙尘事件Id 例：1 */
			eventId: number;
			/** 数据时间  */
			dataTime: string;
			/** 行政区编码 例：110000 */
			regionCode: number;
			/** 行政区名称 例：北京 */
			region: string;
			/** 沙尘影响面积 例：10 */
			area: number;
	} ;

  type TDustRecordRegionModel =  {
			/** 数据时间  */
			dataTime: string;
			/** 沙尘开始时间  */
			startTime: string;
			/** 影响面积集合  */
			infoModels: TDustRecordRegionInfoModel[];
			infoModel?: TDustRecordRegionInfoModel;
	} ;

  type TDustRecordInfoModel =  {
			/** 沙团ID 例：202405211030_10 */
			dustId: string;
			/** 沙尘事件ID 例：1 */
			eventID: number;
			/** 是否为起沙地，0：是，1：否 例：1 */
			sendSite: number;
			/** 沙团矢量  */
			geom: string;
			/** 沙团面积 例：10 */
			area: number;
			/** 沙团影响行政区信息  */
			regions: TDustRecordRegionInfoModel[];
			/**   */
			warningModel: TDustWarningModel;
	} ;

  type TDustWarningModel =  {
			/** 沙团ID 例：202405211030_10 */
			dustId: string;
			/** 角度信息  */
			direction: number;
			/** 影响行政区  */
			regions: TKeyValuePairModelIntegerString[];
			/**   */
			warnTime: string;
	} ;

  type TKeyValuePairModelIntegerString =  {
			/** 名称  */
			key: number;
			/** 值 例：value */
			value: string;
	} ;

  type TDateLabelsModelDustRecordRegionInfoModel =  {
			/** 时间  */
			date: string;
			/** 数据集合  */
			dataList: TDustRecordRegionInfoModel[];
	} ;

  type TDateValueModel =  {
			/** 时间  */
			date: string;
			/** 数值  */
			value: number;
	} ;

  type TDustEventStatsCountModel =  {
			/** 事件总数  */
			totalCount: number;
			/** 本年事件总数  */
			yearCount: number;
			/** 本季事件总数  */
			quarterCount: number;
			/** 本月事件总数  */
			monthCount: number;
	} ;

  type TDustEventRegionModel =  {
			/** 沙尘事件Id 例：1 */
			eventId: number;
			/** 沙尘开始时间  */
			startTime: string;
			/** 沙尘结束时间  */
			endTime: string;
			/** 行政区划编码 例：110000 */
			regionCode: number;
			/** 行政区划编码 例：北京 */
			regionName: string;
			/** 沙尘影响面积 例：44 */
			area: number;
	} ;

  type TDustEventModel =  {
			/** 沙尘事件Id 例：1 */
			id: number;
			/** 沙尘事件编码 例：JW2024050001 */
			code: string;
			/** 沙尘开始时间  */
			startTime: string;
			/** 沙尘结束时间  */
			endTime: string;
			/** 沙尘持续时间（秒） 例：3600 */
			duration: number;
			/** 总影响面积 例：40 */
			area: number;
			/** 缩略图路径 例：data:image/jpeg;base */
			image: string;
			/** 起沙地  */
			sendSites: TKeyValuePairModelIntegerString[];
			/** 沙源地  */
			sources: TKeyValuePairModelIntegerString[];
			/** 影响行政区  */
			regions: TKeyValuePairModelIntegerString[];
	} ;

  type TPageDustEventModel =  {
			/**   */
			totalPages: number;
			/**   */
			totalElements: number;
			/**   */
			pageable: TPageableObject;
			/**   */
			size: number;
			/**   */
			content: TDustEventModel[];
			/**   */
			number: number;
			/**   */
			sort: TSortObject;
			/**   */
			first: boolean;
			/**   */
			last: boolean;
			/**   */
			numberOfElements: number;
			/**   */
			empty: boolean;
	} ;

  type TDustEventInfoModel =  {
			/** 沙尘事件Id 例：1 */
			id: number;
			/** 沙尘事件编码 例：JW2024050001 */
			code: string;
			/** 沙尘开始时间  */
			startTime: string;
			/** 沙尘结束时间  */
			endTime: string;
			/** 沙尘持续时间（秒） 例：3600 */
			duration: number;
			/** 总影响面积 例：40 */
			area: number;
			/** 缩略图路径 例：data:image/jpeg;base */
			image: string;
			/** 起沙地  */
			sendSites: TKeyValuePairModelIntegerString[];
			/** 沙源地  */
			sources: TKeyValuePairModelIntegerString[];
			/** 影响行政区  */
			regions: TKeyValuePairModelIntegerString[];
			/** 矢量  */
			geoJson: string;
			/** 起沙地中心坐标点  */
			sendSitePoints: TPointModel[];
	} ;

  type TPointModel =  {
			/** 经度  */
			lon: number;
			/** 纬度  */
			lat: number;
	} ;

/** 用户修改 */
  type TPostUserUpdateParams =  {
			/**   */
			token: string;
	} ;

/** 用户修改登录密码 */
  type TPostUserPasswordUpdateParams =  {
			/**   */
			token: string;
	} ;

/** 新增用户 */
  type TPostUserCreateParams =  {
			/**   */
			token: string;
	} ;

/** 获取二维码 */
  type TGetWechatQrcodeParams =  {
			/**   */
			token: string;
	} ;

/** 用户重置密码 */
  type TGetUserPasswordResetParams =  {
			/** 用户id  */
			id: number;
			/**   */
			token: string;
	} ;

/** 用户列表 */
  type TGetUserListParams =  {
			/** 区划代码  */
			regionCode?: number;
			/** 用户姓名  */
			name?: string;
			/** 页码，默认为0  */
			pageNum?: number;
			/** 页大小，默认为10  */
			pageSize?: number;
			/**   */
			token: string;
	} ;

/** 获取当前用户信息 */
  type TGetUserInfoParams =  {
			/**   */
			token: string;
	} ;

/** 用户启用 */
  type TGetUserEnableParams =  {
			/** 用户id  */
			id: number;
			/**   */
			token: string;
	} ;

/** 沙尘订阅 */
  type TGetUserDustSubscribeParams =  {
			/** 行政区编码  */
			regionCode: number;
			/** 沙尘面积  */
			area: number;
			/** 是否推送通知  */
			isSend: boolean;
			/**   */
			token: string;
	} ;

/** 沙尘订阅信息 */
  type TGetUserDustSubscribeInfoParams =  {
			/**   */
			token: string;
	} ;

/** 用户停用 */
  type TGetUserDisableParams =  {
			/** 用户id  */
			id: number;
			/**   */
			token: string;
	} ;

/** 用户删除 */
  type TGetUserDeleteParams =  {
			/** 用户id  */
			id: number;
			/**   */
			token: string;
	} ;

/** 纹理瓦片token */
  type TGetTextureTokenParams =  {
			/** 域名,多个英文逗号间隔,默认所有域名都可访问  */
			domains?: string;
			/** 类型:PM25...  */
			type?: string;
	} ;

/** 纹理瓦片时间节点接口 */
  type TGetTextureTimePointParams =  {
			/** 周期类型，{none|daily|weekly|monthly|quarterly|yearly}  */
			agg?: string;
			/** 类型:pm25...  */
			type: string;
			/** 开始时间 yyyy/MM/dd  */
			startDate: string;
			/** 结束时间 yyyy/MM/dd  */
			endDate: string;
	} ;

/** 纹理瓦片聚合接口 */
  type TGetTextureAggParams =  {
			/** 类型:PM25|PM10...  */
			type: string;
			/** 开始时间 yyyy/MM/dd  */
			startDate: string;
			/** 结束时间 yyyy/MM/dd  */
			endDate: string;
			/** 缩放级别  */
			z: number;
			/** 瓦片行号  */
			x: number;
			/** 瓦片列号  */
			y: number;
			/** token  */
			tileToken: string;
	} ;

/** 站点列表 */
  type TGetStationMonitorListParams =  {
			/** 区划代码  */
			regionCode: number;
	} ;

/** 站点数据 */
  type TGetStationMonitorDataParams =  {
			/** 站点编号  */
			stationCode: string;
			/** 数据时间  yyyy/MM/dd HH:mm:ss  */
			dataTime: string;
	} ;

/** 站点数据列表 */
  type TGetStationMonitorDataListParams =  {
			/** 区划代码  */
			regionCode: number;
			/** 数据时间  yyyy/MM/dd HH:mm:ss  */
			dataTime: string;
	} ;

/** 获取起沙地统计 */
  type TGetDustStatsSendSiteParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取行政区沙尘天数统计 */
  type TGetDustStatsRegionCountParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取起沙地统计 */
  type TGetDustStatsExportSendSiteParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取行政区沙尘天数统计 */
  type TGetDustStatsExportRegionCountParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘事件行政区面积统计 */
  type TGetDustStatsExportEventRegionAreaParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘事件持续时间统计 */
  type TGetDustStatsExportEventDurationParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘事件总数统计 */
  type TGetDustStatsEventTotalParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘事件行政区面积统计 */
  type TGetDustStatsEventRegionAreaParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘事件持续时间统计 */
  type TGetDustStatsEventDurationParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘天数统计 */
  type TGetDustStatsDateCountParams =  {
			/** 开始时间 yyyy/MM/dd  */
			startTime: string;
			/** 开始时间 yyyy/MM/dd  */
			endTime: string;
	} ;

/** 获取沙尘时间节点 */
  type TGetDustRecordTimePointParams =  {
			/** 行政区划代码  */
			regionCode?: number;
	} ;

/** 获取当前时间点的监测结果 */
  type TGetDustRecordRegionInfosParams =  {
			/** 数据时间 yyyy/MM/dd HH:mm:ss  */
			dataTime: string;
      /** 行政区划代码  */
      regionCode: number;
	} ;

/** 获取当前时间点的所有沙团信息 */
  type TGetDustRecordInfosParams =  {
			/** 行政区划代码  */
			regionCode?: number;
			/** 沙尘事件ID  */
			eventId?: number;
			/** 数据时间 yyyy/MM/dd HH:mm:ss  */
			dataTime: string;
	} ;

/** 获取沙尘事件影响行政区 */
  type TGetDustRecordDateRegionsParams =  {
			/** 沙尘事件ID  */
			dustEventId: number;
	} ;

/** 获取沙尘事件沙团面积 */
  type TGetDustRecordDateAreaParams =  {
			/** 沙尘事件ID  */
			dustEventId: number;
	} ;

/** 获取沙尘事件次数统计-沙源地 */
  type TGetDustEventStatsCountSourceParams =  {
			/** 沙源地编码  */
			sourceCode?: number;
	} ;

/** 获取沙尘事件影响行政区信息 */
  type TGetDustEventRegionListParams =  {
			/** 沙尘事件ID  */
			dustEventId: number;
	} ;

/** 获取沙尘事件列表 */
  type TGetDustEventPageParams =  {
			/** 开始时间 yyyy/MM/dd HH:mm:ss  */
			startTime?: string;
			/** 开始时间 yyyy/MM/dd HH:mm:ss  */
			endTime?: string;
			/** 行政区编码  */
			regionCode?: number;
			/** 起沙地编码  */
			sendSiteRegionCode?: number;
			/** 沙源地编码  */
			sourceCode?: number;
			/** 排序规则  */
			sort?: number;
			/** 页码  */
			pageNum?: number;
			/** 条数  */
			pageSize?: number;
	} ;

/** 获取沙尘事件详情 */
  type TGetDustEventInfoParams =  {
			/** 沙尘事件ID  */
			dustEventId: number;
	} ;

/** 同步沙尘事件 */
  type TGetDataProcessSyncDustEventParams =  {
			/** 开始时间 yyyy/MM/dd HH:mm:ss  */
			startTime: string;
			/** 结束事件 yyyy/MM/dd HH:mm:ss  */
			endTime: string;
	} ;
}
