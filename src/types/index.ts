export interface User {
  enabled: boolean;
  id: number;
  lastLoginTime: string;
  name: string;
  permissions: string[];
  phoneNumber: string;
  region: string;
  regionCode: number;
  role: number;
  token: null | string;
  totalLoginTimes: number;
}

export interface LoginRespData {
  enabled: boolean;
  id: number;
  lastLoginTime: string;
  name: string;
  permissions: string[];
  phoneNumber: string;
  region: string;
  regionCode: number;
  role: number;
  token: string;
  totalLoginTimes: number;
  province: string;
}

export interface CheckSopParams {
  alertId: number;
  auditor: string;
  bc: number;
  checkType: number;
  co: number;
  co2: number;
  content: string;
  date: string;
  images: string[];
  nh3: number;
  nox: number;
  oc: number;
  pm10: number;
  pm25: number;
  pollutionSourceId: number;
  pollutionSourceStatus: number;
  related: boolean;
  tsp: number;
  voc: number;
}

export type CreateOrEditCheckParams = Omit<
  CheckSopParams,
  'alertId' | 'checkType' | 'related'
> & {
  id?: number;
  checkStatus: number;
};

export interface RecordItemInterface {
  alertCheckRecordModels: {
    actualValue: number;
    pollutionType: string;
  }[];
  alertNumber: string;
  auditor: string;
  content: string;
  count: number;
  datetime: string;
  id: number;
  images: string[];
  status: number;
  type: number;
  checkType?: number;
  related?: boolean;
}

export interface TextureMap {
  minLng: number;
  maxLng: number;
  minLat: number;
  maxLat: number;
  url: string;
}

export interface SopFormParams {
  address: string;
  contact: string[];
  foundTime: string;
  id?: number;
  industry: number;
  lat: number;
  level: number;
  lon: number;
  name: string;
  region: number;
  status: number;
  type: string;
}

export interface Pollution {
  id: number;
  code: string;
  name: string;
  province: string;
  city: string;
  county: string;
  address: string;
  type: string;
  industry: string;
  status: 1 | 2 | 3 | 4 | 5;
  level: 1 | 99;
  lon: number;
  lat: number;
  muted: boolean;
  regionCode: number;
  create: string;
  foundTime: string;
  contact: string[];
  stationNames: string[];
  blowdownInfoModels: {
    type: string;
    value: number;
  }[];
  station: boolean;
}

export interface Industry {
  id: number;
  firstLevelCode: number;
  firstLevelName: string;
  secondLevelCode: number;
  secondLevelName: string;
  pollutions: string[];
}

export interface PollutionDetails {
  address: string;
  code: string;
  create: string;
  city: string;
  blowdownInfoModels: {
    type: string;
    value: number;
  }[];
  county: string;
  contact: [string, null | string];
  foundTime: string;
  id: number;
  industry: string;
  lat: number;
  level: 1 | 99;
  lon: number;
  muted: boolean;
  name: string;
  region: string;
  regionCode: number;
  status: number;
  type: string[];
  station: boolean;
  stationNames: string[];
}

export interface StatusOrLevel {
  displayName: string;
  code: number;
}

export interface SopType {
  displayName: string;
  internalName: string;
}

export interface DicData {
  industryList: Industry[];
  statusList: StatusOrLevel[];
  levelList: StatusOrLevel[];
  typeList: SopType[];
}

export interface FetchPollutionListParams {
  regionCode: number | undefined | string;
  page: number;
  size: number;
  industry?: number[] | null;
  level?: number[] | null;
  status?: number[] | null;
  type?: string[] | number[] | null;
  name?: string | null;
}

export interface PoWarningItem {
  actualValue: number;
  address: string;
  alertNumber: string;
  date: string;
  id: number;
  lat: number;
  level: number;
  lon: number;
  percent: number;
  pollutionType: string;
  region: number;
  city: number;
  county: number;
  status: number;
  thresholdValue: number;
  radius: number;
  fromDate: string;
  toDate: string;
  process: {
    total: number;
    completes: number;
  };
}

interface TreeDataItem {
  title: string;
  key: number;
}

export interface TreeData extends TreeDataItem {
  title: string;
  key: number;
  children: TreeDataItem[];
}

export interface EarlyWarningItem {
  aqi: number;
  id: number;
  level: number;
  pollutant: string;
  range: string;
  region: string;
  regionCode: number;
  value: number;
}

export interface IndustryAlertItem {
  alerts: number;
  industryName: string;
  industryCode: number;
  levelText: string;
  merge: number;
  percent: number;
}

export type MapLayerStatus = {
  label: string;
  type: 'vec' | 'img';
  visible: boolean;
};
