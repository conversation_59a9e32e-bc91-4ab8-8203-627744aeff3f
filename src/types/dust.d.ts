declare namespace DUST {
  type DataTypes =
    | 'PM25'
    | 'PM10'
    | 'AOD'
    | 'O3'
    | 'O3TCD'
    | 'NO2TCD'
    | 'HCHO'
    | 'SO2'
    | 'CO'
    | 'UVT'
    | 'UVA'
    | 'UVB'
    | 'WINDY'
    | 'DMASK'
    | 'TCOLOR'
    | 'DCOLOR'
    | 'DMASK_COUNT';
  type Agg = 'none' | 'daily' | 'weekly' | 'monthly';
  type RGBColor = {
    r: number;
    g: number;
    b: number;
    a: number;
  };
  interface ColorRamp {
    min?: number;
    max?: number;
    // AQI显示级别
    levelLabel?: string;
    color: string;
  }
  interface ConfigItem {
    label: string; // 显示的名称, 如 PM₂.₅
    value: string; // 值, 如 PM25
    unit: string; // 单位, 如 μg/m³
    roundFn?: (num: number, decimal: number) => number; // 四舍五入函数
    pickingValueFn?: (data: RGBColor) => number | string; // 根据像素选取值函数
    colorRamps: ColorRamp[]; // 色带
    value: DUST.DataTypes; // 值, 如 PM25
    unit: string; // 单位, 如 μg/m³
    colorRamps: ColorRamp[]; // 色带
  }

  interface TimePoint {
    time: string;
  }

  interface PointGroup {
    groupName: string;
    points: {
      label: string;
      value: string;
    }[];
  }
  // 污染级别
  type pollutionLevelType =
    | 'seriousCount'
    | 'heavyCount'
    | 'moderateCount'
    | 'lightCount';

  interface DistancePoint {
    id: string;
    coordinate: [number, number];
    x: number;
    y: number;
    distance: number;
    value?: string | number;
  }
  interface DistanceGroup {
    id: string;
    points: DistancePoint[];
    isEditing: boolean;
  }

  interface AreaPoint {
    id: string;
    coordinate: [number, number];
    x: number;
    y: number;
  }

  interface BoxPoint {
    id: string;
    coordinate: [number, number];
    x: number;
    y: number;
  }

  interface AreaGroup {
    id: string;
    points: AreaPoint[];
    isEditing: boolean;
  }

  interface boxGroup {
    id: string;
    points: AreaPoint[];
    isEditing: boolean;
  }

  type PickInfo = {
    value?: number;
    unit?: string;
  };

  type RemoteSensingType = 'PM10' | 'PM25' | 'AOD';

  type RegionInfo = {
    code: number;
    name: string;
  };
}