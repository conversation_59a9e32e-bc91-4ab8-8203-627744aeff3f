@font-face {
  font-weight: normal;
  font-family: 'icomoon';
  font-style: normal;
  src: url('/assets/fonts/icomoon.ttf?v2de3d') format('truetype'),
    url('/assets/fonts/icomoon.woff?v2de3d') format('woff'),
    url('/assets/fonts/icomoon.svg?v2de3d#icomoon') format('svg');
  font-display: block;
}

.icomoon {
  font-weight: normal;
  font-size: inherit;
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  font-style: normal;
  font-variant: normal;
  line-height: 1;
  text-transform: none;
  speak: never;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-create-map:before {
  content: '\e960';
}
.icon-image-layer:before {
  content: '\e95f';
}
.icon-earth:before {
  content: '\e95e';
}
.icon-more:before {
  content: '\e95a';
}
.icon-column:before {
  content: '\e95b';
}
.icon-tag:before {
  content: '\e95c';
}
.icon-four-block:before {
  content: '\e95d';
}
.icon-edit:before {
  content: '\e956';
}
.icon-fillin:before {
  content: '\e957';
}
.icon-pause-thin:before {
  content: '\e958';
}
.icon-copy:before {
  content: '\e959';
}
.icon-circle:before {
  content: '\e955';
}
.icon-rect:before {
  content: '\e954';
}
.icon-pic:before {
  content: '\e902';
}
.icon-arrow:before {
  content: '\e952';
}
.icon-fire:before {
  content: '\e951';
}
.icon-big-screen:before {
  content: '\e950';
}
.icon-back:before {
  content: '\e94f';
}
.icon-save-line:before {
  content: '\e94e';
}
.icon-visible:before {
  content: '\e945';
}
.icon-export-line:before {
  content: '\e946';
}
.icon-layer-title:before {
  content: '\e947';
}
.icon-handle:before {
  content: '\e948';
}
.icon-data-layer:before {
  content: '\e949';
}
.icon-folder:before {
  content: '\e94a';
}
.icon-hidden:before {
  content: '\e94b';
}
.icon-trash-line:before {
  content: '\e94c';
}
.icon-text-layer:before {
  content: '\e94d';
}
.icon-person:before {
  content: '\e944';
}
.icon-time:before {
  content: '\e941';
}
.icon-company:before {
  content: '\e942';
}
.icon-phone:before {
  content: '\e943';
}
.icon-excel-file:before {
  content: '\e93e';
}
.icon-trash-outlined:before {
  content: '\e93f';
}
.icon-attachment:before {
  content: '\e940';
}
.icon-upload:before {
  content: '\e93d';
}
.icon-add-fill:before {
  content: '\e93c';
}
.icon-brush:before {
  content: '\e937';
}
.icon-draw:before {
  content: '\e938';
}
.icon-bethel:before {
  content: '\e939';
}
.icon-export:before {
  content: '\e93a';
}
.icon-save:before {
  content: '\e93b';
}
.icon-information:before {
  content: '\e936';
}
.icon-up:before {
  content: '\e934';
}
.icon-down:before {
  content: '\e935';
}
.icon-download:before {
  content: '\e933';
}
.icon-trash:before {
  content: '\e90a';
}
.icon-bullseys:before {
  color: #666;
  content: '\e901';
}
.icon-compass:before {
  content: '\e953';
}
.icon-detail .path1:before {
  color: rgb(232, 232, 232);
  content: '\e903';
}
.icon-detail .path2:before {
  margin-left: -1em;
  color: rgb(153, 153, 153);
  content: '\e904';
}
.icon-d-search:before {
  content: '\e905';
}
.icon-filled-next .path1:before {
  color: rgb(28, 29, 36);
  content: '\e906';
}
.icon-filled-next .path2:before {
  margin-left: -1em;
  color: rgb(255, 255, 255);
  content: '\e907';
}
.icon-qst:before {
  content: '\e908';
}
.icon-area:before {
  content: '\e90b';
}
.icon-building:before {
  content: '\e90c';
}
.icon-calendar:before {
  content: '\e90d';
}
.icon-cct:before {
  content: '\e90e';
}
.icon-chart:before {
  content: '\e90f';
}
.icon-clear:before {
  content: '\e910';
}
.icon-coordinate:before {
  content: '\e911';
}
.icon-ff:before {
  content: '\e912';
}
.icon-full-screen:before {
  content: '\e913';
}
.icon-gridding:before {
  content: '\e914';
}
.icon-layer-1:before {
  content: '\e915';
}
.icon-layer-2:before {
  content: '\e916';
}
.icon-layer-3:before {
  content: '\e917';
}
.icon-layer-4:before {
  content: '\e918';
}
.icon-list:before {
  content: '\e919';
}
.icon-location:before {
  content: '\e91a';
}
.icon-lock:before {
  content: '\e91c';
}
.icon-menu-1:before {
  content: '\e91d';
}
.icon-menu-2:before {
  content: '\e91e';
}
.icon-menu-3:before {
  content: '\e91f';
}
.icon-menu-4:before {
  content: '\e920';
}
.icon-menu-5:before {
  content: '\e921';
}
.icon-menu-6:before {
  content: '\e922';
}
.icon-next:before {
  content: '\e923';
}
.icon-pause:before {
  content: '\e924';
}
.icon-play:before {
  content: '\e925';
}
.icon-prev:before {
  content: '\e926';
}
.icon-ruler:before {
  content: '\e928';
}
.icon-screenshot:before {
  content: '\e929';
}
.icon-screen-shot:before {
  content: '\e92a';
}
.icon-search:before {
  content: '\e92b';
}
.icon-ss:before {
  content: '\e92c';
}
.icon-triangle-down:before {
  content: '\e92d';
}
.icon-triangle-up:before {
  content: '\e92e';
}
.icon-unlock:before {
  content: '\e92f';
}
.icon-x:before {
  content: '\e930';
}
.icon-zoomin:before {
  content: '\e931';
}
.icon-zoomout:before {
  content: '\e932';
}
.icon-alarm:before {
  content: '\e900';
}
.icon-location-icon:before {
  content: '\e91b';
}
.icon-arrow-down:before {
  content: '\e909';
}
.icon-close:before {
  content: '\e927';
}
