//@ts-ignore
import { codeInspectorPlugin } from 'code-inspector-plugin';
import { defineConfig } from 'umi';
import DEV_CONFIG from './.dev.config';

const isProduction = process.env.NODE_ENV === 'production';
const PROXY_ENV = process.env.PROXY_ENV as 'dev' | 'test';
const { proxyUrl } = DEV_CONFIG[PROXY_ENV || 'dev'];

console.log('当前环境:', proxyUrl);
export default defineConfig({
  proxy: {
    '/api': {
      target: proxyUrl,
      // target: 'http://test.dust.ruijingrs.cn:20055',
      changeOrigin: true,
    },
  },
  history: {
    type: 'hash',
  },
  helmet: true,
  jsMinifier: 'terser',
  scripts: ['https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_19893_583.c651e639416976cb03494945d226dc7d.es5.js'],

  define: {
    'process.env.API_PREFIX': isProduction ? '' : '',
    'process.env.PRIVATE_Key': `MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCTmoP7+JZeB5Be
    1ZH1IhqW1abW+ruSkhiURDvgj4VGT7uvG0Tg82FXkF60h7/BtXVtzQyg3M8Ee4EJ
    2N8YHj7oVma8V8TXSzXf1MGlA4m6IRDoAfAhSdv/bguKZPriNVmdnPSF9Sodws/z
    QwyaiVxw1LRzoNdCWLt9pO0jZEPilEjgnzo1e6AQmh+P0GERA+AOIKIrPrjJKyJv
    FSj6Bnc6louzKDdUaDDTL1PY53245M6cKFrCwlwbHvGshj0rdPEN0/lGVh2/cDou
    f+aAo2+npObsaA52B2hixSVxcrBlpsjbn4UqjzwgzbEMNSeVZ7+g5ZplFXCrTVFk
    M3aWn6qlAgMBAAECggEAB1m5hZHH7d995AsfXVAZIjZ6PC3OViqVCMUryi9mLq3d
    JdzmHYxhO5rCl0ZErraE+4vWO33jh/tH3iABCX1wGo0689mBwaBK4DOt1QmLu/Yr
    llzfrXi1I6Ts9DO6frYIxB2YhmyYGqqkmnOrVq131LKdrehuRS06tl4Wj8hNd89h
    Q2hbX98IAfmm5DKL0hSY8PvMZ5bAdjmlTGQT0vp9L4etF+Ef4mxMHqqtL6HbVIK3
    zO9AXvFbH3v81uSTLbvzoU6qJuLyuGD7x997T/CEkpxnBDP4vJHxVBy6YvU4QGKp
    n2zIs5FhGhsrZVeg5mp5925C8GI0mCE93Prhj2ayvQKBgQDFbImgSz6utmpqJTqj
    UtJmf2ZSziyTZPKvDE4YCGr1b4jvLitqaIgq0edWuVFb8KY8y+YdB8za1x+oW3af
    9aOrGlXZcpkui2oDACVRlFcziSbtUD661t1ZHUKdgmjylJc50USouDqMAZhh90u6
    NZr0RO4BW1+TdiwhGNHP6yVPXwKBgQC/ZdaURo43Ka9rHrMg3ghzBxL+l8iXkyvO
    m6SP6Bd7C8AXLmZEhk7XhPiD26eTGSn3yStCtYQM38Tm79eTncqukafYHc2YKYKv
    s4yp8Boz33R196YaMBEmaCZF9vcVJpECXZl4078Gf3VNXkAwNtM4+PVzcOYvOpS6
    ++qQWEJ4ewKBgBSyv28d1HGO0HNWeeJbsZFrc/fkg8nkQIlXuexNHaGj0vB4n7al
    RPlwTlfKUBxai6M+IV8TzUd4XGpTRImawFEVDsEggazekvvDjSYTbl0J2RnyWtFN
    66EHL0/CN2yPZZd7OD6UKhc5p5dFzRu1uQw5GdbslIWqUxAZQQ4ScjPzAoGAAVEv
    F9ggdGq6BU+xxeEhR8FdtRjQTZK6s3JIwKDFQJiFGuO72m7eLgeBOk8RqSZ8LnNY
    892R6hDEJx6xahek5GymLguOdaSbfKAMsMKxO3gnMgShRP7dr1j6InMfoiqgxtZk
    DvvODbpk0UJ7yJkn/PVhowi83MhXa63+ID4rPu8CgYAOkYhjGLHq4sJQwZThbKj5
    pxi1EvEO7r3kvh2feV5+Xx8iR0+8FCCetMwOs+iDmHHjud00KwK+zu9WL6rlD311
    RB3yIuGz2N9CBtzcK8BJtk0SVDGTMf6stXZgq87bshubrDOuRBS6J2dpWEHrDiv4
    +48723RnNgA0ioHm6DixnQ==`,
    'process.env.BUILD_ENV': process.env.BUILD_ENV,
  },
  theme: {
    '@primary-color': '#255BDA',
  },
  favicons: ['/favicon.png'],
  copy: ['public'],
  extraBabelPlugins: isProduction ? [] : ['babel-plugin-styled-components', './babel-plugin/jotai-debug-label', './babel-plugin/jotai-react-refresh'],
  publicPath: '/',
  ignoreMomentLocale: true,
  plugins: [],
  chainWebpack(config: any) {
    config.merge({
      optimization: {
        splitChunks: {
          chunks: 'all',
          automaticNameDelimiter: '.',
          name: true,
          minSize: 30000,
          minChunks: 1,
          cacheGroups: {
            vendors: {
              name: 'vendors',
              chunks: 'all',
              test: /[\\/]node_modules[\\/]/,
              priority: -12,
            },
          },
        },
      },
    });
    if (!isProduction) {
      config.plugin('code-inspector-plugin').use(
        codeInspectorPlugin({
          bundler: 'webpack',
          editor: 'code',
          showSwitch: false,
          hotKeys: ['altKey'],
        }),
      );
    }
  },
  routes: [
    {
      path: '/',
      routes: [
        {
          path: '/login',
          exact: true,
          component: 'Login',
          title: '登录',
        },
        {
          path: '',
          regionCode: '',
          component: 'Overview',
          title: '首页监控一张图',
          key: '2',
          icon: 'daqiyizhangtu',
        },
        {
          path: 'dust-event',
          regionCode: '',
          component: 'DustEvent',
          title: '沙尘事件',
          key: '9',
          icon: 'fengchang-gjmbn420',
        },
        {
          path: 'dust-event-detail',
          regionCode: '',
          component: 'DustEventDetail',
          title: '沙尘事件详情',
          key: '9-1',
          hiddenInMenu: true,
        },
        {
          path: 'report',
          regionCode: '',
          component: 'Report',
          title: '分析报告',
          key: '11',
          icon: 'shujuguanli',
        },
        {
          path: 'report-detail',
          regionCode: '',
          component: 'ReportDetail',
          title: '查看报告',
          key: '11-1',
          hiddenInMenu: true,
        },
        {
          path: 'thematic-map',
          regionCode: '',
          component: 'ThematicMap',
          title: '专题图',
          key: '3',
          icon: 'chuangjianzhuantitu',
          hiddenInMenu: true,
        },
        {
          path: 'thematic-map-tpl',
          regionCode: '',
          component: 'ThematicMapTpl',
          title: '专题图模板',
          key: '3',
          icon: 'chuangjianzhuantitu',
          hiddenInMenu: true,
        },
        {
          path: 'thematic-map-list',
          regionCode: '',
          component: 'ThematicMapList',
          title: '专题图列表',
          key: '3',
        },
        {
          path: 'thematic-map-tpl-list',
          regionCode: '',
          component: 'ThematicMapTplList',
          title: '模板列表',
          key: '3',
          hiddenInMenu: true,
        },
        {
          // path: 'comprehensive-assessment',
          regionCode: '',
          // component: 'ComprehensiveAssessment',
          title: '综合评估',
          key: '6',
          icon: 'zonghefenxi',
          routes: [
            {
              path: 'comprehensive-assessment',
              regionCode: '',
              component: 'ComprehensiveAssessment',
              title: '大气综合评估',
              key: '6-1',
            },
            {
              path: 'dust-analyze',
              regionCode: '',
              component: 'DustAnalyze',
              title: '沙尘综合评估',
              key: '6-2',
            },
          ],
        },
        {
          title: '系统管理',
          key: '8',
          regionCode: '',
          icon: 'xitongguanli',
          routes: [
            {
              path: 'user-management',
              regionCode: '',
              component: 'UserManagement',
              title: '用户管理',
              key: '8-1',
            },
            {
              path: 'receive-monitoring',
              regionCode: '',
              component: 'ReceiveMonitoring',
              title: '数据接收监控',
              key: '8-2',
            },
            {
              regionCode: '',
              path: 'data-download',
              component: 'DataDownload',
              title: '数据下载',
              key: '8-3',
            },
            {
              regionCode: '',
              path: 'receiving-log',
              component: 'ReceivingLog',
              title: '数据接收日志',
              key: '8-4',
            },
            {
              regionCode: '',
              path: 'system-log',
              component: 'SystemLog',
              title: '系统日志',
              key: '8-5',
            },
          ],
        },
        {
          path: '*',
          exact: true,
          component: '404',
          title: '',
        },
      ],
    },
  ],
  tailwindcss: {},
});
