const FILTER_PIXELS="FILTER_PIXELS",FILTER_BY_CODECS="FILTER_BY_CODECS",CODEC={NDVI:{format:"float",bands:1,factor:10,encoder:t=>{if("number"===typeof t){let e=t+CODEC.NDVI.factor;e*=100;const o=Math.floor(e/255),r=Math.floor(e%255);return[o,r,0,255]}return[0,0,0,0]},decoder:([t,e])=>(255*t+e)/100-CODEC.NDVI.factor},NER:{format:"float",bands:1,factor:6e4,encoder:t=>{if("number"===typeof t){let e=t;e<=-CODEC.NER.factor&&(e=0),e+=CODEC.NER.factor,e*=100;const o=Math.floor(e/255/255),r=Math.floor(e/255%255),a=Math.floor(e%255);return[o,r,a,255]}return[0,0,0,0]},decoder:([t,e,o])=>(255*t*255+255*e+o)/100-CODEC.NER.factor},NEP:{format:"float",bands:2,factor:6e4,encoder:t=>{if(Array.isArray(t)){let[e]=t;const[,o]=t;e<=-CODEC.NEP.factor&&(e=0),e+=CODEC.NEP.factor,e*=100;const r=Math.floor(e/255/255),a=Math.floor(e/255%255),f=Math.floor(e%255),n=o;return[r,a,f,n]}return[0,0,0,0]},decoder:([t,e,o,r])=>{const a=(255*t*255+255*e+o)/100-CODEC.NEP.factor,f=r-240;return[a,f]}},GPP:{format:"float",bands:1,factor:6e4,encoder:t=>{if(Array.isArray(t)){let[e]=t;const[,o]=t;e<=-CODEC.GPP.factor&&(e=0),e+=CODEC.GPP.factor,e*=100;const r=Math.floor(e/255/255),a=Math.floor(e/255%255),f=Math.floor(e%255),n=o;return[r,a,f,n]}return[0,0,0,0]},decoder:([t,e,o])=>(255*t*255+255*e+o)/100-CODEC.GPP.factor},SSRA:{format:"float",factor:0,bands:1,encoder:t=>{if("number"===typeof t){const e=Math.floor(t/255/255),o=Math.floor(t/255%255),r=Math.floor(t%255);return[e,o,r,255]}return[0,0,0,0]},decoder:([t,e,o])=>255*t*255+255*e+o},TMP:{format:"float",bands:1,factor:128,encoder:t=>"number"===typeof t?[t-273+CODEC.TMP.factor,0,0,0]:[0,0,0,0],decoder:([t])=>t-CODEC.TMP.factor+273}};function filterPixels(t){const e=t.data,o=e.length,r=new Uint8ClampedArray(o);for(let f=0;f<o;f+=4){const t=e[f],o=e[f+1],a=e[f+2];0===t&&0===o&&0===a?(r[f]=0,r[f+1]=0,r[f+2]=0,r[f+3]=0):(r[f]=t,r[f+1]=o,r[f+2]=a,r[f+3]=e[f+3])}const a=new ImageData(r,t.width,t.height);return a}function filterPixelsByCodecs(t,e){const o=t.data,r=o.length,a=new Uint8ClampedArray(r),f=CODEC[e].decoder,n=CODEC[e].encoder;for(let c=0;c<r;c+=4){const t=o[c],e=o[c+1],r=o[c+2],C=o[c+3],d=f([t,e,r,C]),[s,E,l,i]=n(d);a[c]=s,a[c+1]=E,a[c+2]=l,a[c+3]=i}return new ImageData(a,t.width,t.height)}onmessage=t=>{if(t.data.type===FILTER_PIXELS){const e=filterPixels(t.data.image);self.postMessage({type:FILTER_PIXELS,image:e})}if(t.data.type===FILTER_BY_CODECS){const{encoder:e,decoder:o}=CODEC[t.data.dataType];if(e&&o){const{image:e}=t.data;self.postMessage({type:FILTER_BY_CODECS,image:filterPixelsByCodecs(e,t.data.dataType)})}else self.postMessage({type:FILTER_BY_CODECS,image:t.data.image})}};