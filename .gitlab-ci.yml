variables:
  NODE_MODULES_PATH: /home/<USER>/runner-cache/frondend/$CI_PROJECT_NAME/node_modules
  BUILD_OUTPUT_PATH: $CI_PROJECT_DIR/dist

stages:
  - install
  - deploy

安装依赖并构建:
  stage: install
  before_script:
    - mkdir -p $NODE_MODULES_PATH
    - ln -s $NODE_MODULES_PATH .
    - yarn config set registry https://registry.npmmirror.com/
    - yarn config get registry
    - yarn config set strict-ssl false
  script:
    - yarn install --no-progress --ignore-engines
    - yarn build
    - cd $CI_PROJECT_DIR
    - rm -rf /home/<USER>/archives/apb/apb-site-nm
    - mv dist /home/<USER>/archives/apb/apb-site-nm
  tags:
    - pack-web
  only:
    - test/v2

部署测试环境:
  stage: deploy
  before_script:
    - export PROFILE=test
  script:
    - ssh ********* "rm -rf /home/<USER>/archives/apb/apb-site-nm"
    - scp -r /home/<USER>/archives/apb/apb-site-nm/ *********:/home/<USER>/archives/apb/apb-site-nm/
  when: manual
  only:
    - test/v2
  tags:
    - pack-web
