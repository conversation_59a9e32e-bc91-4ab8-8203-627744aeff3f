{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}, "allowSyntheticDefaultImports": true}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts", "windi.config.ts", "iconPark.d.ts", "src/api/**/*"], "exclude": ["node_modules", "lib", "es", "dist", "typings", "**/__test__", "test", "docs", "tests"]}