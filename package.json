{"private": true, "scripts": {"start": "npm run dev", "start:test": "concurrently 'npm:dev:test'", "dev": "cross-env PROXY_ENV=dev max dev", "dev:test": "cross-env PROXY_ENV=test max dev", "dev:prod": "cross-env PROXY_ENV=test BUILD_ENV=production max dev", "max:build": "max build", "build": "max build", "build:prod": "cross-env BUILD_ENV=production yarn run build", "analyze": "cross-env ANALYZE=1 umi build", "postinstall": "max setup", "prettier": "prettier --write 'src/**/*.{tsx,ts}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "lint": "eslint --color --ext .ts,.tsx src", "fix": "eslint src/**/*.tsx --fix", "api": "npx @ruijingrs/swagger-exporter@1.2.6 -u http://ocean.ruijingrs.cn:20055/v3/api-docs/1.0.0 -p api-v3", "format-svg": "node svg-formatter/index.js remove --dir src/assets/svg", "font": "npm run format-svg && svgtofont --sources ./src/assets/svg --output ./src/assets/svgFont --fontName svg-font"}, "svgtofont": {"css": {"fontSize": "12px"}}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.ts?(x)": ["eslint --color --ext .ts,.tsx src"]}, "dependencies": {"@ant-design/charts": "^1.2.5", "@ant-design/icons": "^4.6.2", "@ant-design/moment-webpack-plugin": "^0.0.3", "@ant-design/plots": "^1.2.5", "@ant-design/pro-layout": "^6.5.0", "@antv/g2": "5.0.2", "@antv/graphlib": "^1.2.0", "@deck.gl/core": "8.8.20", "@deck.gl/react": "8.8.20", "@emotion/react": "^11.11.1", "@loadable/component": "^5.15.3", "@loaders.gl/core": "^3.2.12", "@loaders.gl/images": "^3.2.12", "@loaders.gl/polyfills": "^3.2.12", "@material-ui/core": "^4.12.4", "@material-ui/lab": "^4.0.0-alpha.61", "@math.gl/web-mercator": "^3.4.3", "@ruijingrs/deckgl-annotation-layer": "0.5.10", "@ruijingrs/deckgl-breath-layer": "^0.9.11", "@ruijingrs/deckgl-circle-layer": "^0.5.0", "@ruijingrs/deckgl-cluster-layer": "^0.5.2", "@ruijingrs/deckgl-icon-layer": "^0.5.1", "@ruijingrs/deckgl-map": "0.5.7", "@ruijingrs/deckgl-raster-layer": "^0.4.7", "@ruijingrs/deckgl-vector-layer": "^0.5.3", "@tanstack/react-table": "^8.9.3", "@turf/area": "^6.4.0", "@turf/bbox": "^6.4.0", "@turf/bbox-polygon": "^6.4.0", "@turf/center": "^6.5.0", "@turf/centroid": "^6.5.0", "@turf/circle": "^6.5.0", "@turf/distance": "^6.4.0", "@turf/helpers": "^6.5.0", "@turf/point-grid": "^6.5.0", "@turf/turf": "^6.5.0", "@types/crypto-js": "^4.1.1", "@types/echarts": "^4.9.16", "@types/loadable__component": "^5.13.5", "@types/mockjs": "^1.0.3", "@types/moment": "^2.13.0", "@types/qs": "^6.9.7", "@types/styled-components": "^5.1.10", "@umijs/max": "^4.0.0", "@umijs/preset-react": "1.x", "@use-gesture/react": "^10.2.23", "ahooks": "^3.7.2", "antd": "^5.26.2", "array-move": "^4.0.0", "axios": "^1.7.7", "clsx": "^1.2.1", "code-inspector-plugin": "^0.20.12", "crypto-js": "^4.1.1", "dagre-compound": "^0.0.13", "dayjs": "^1.11.7", "deck.gl": "8.8.20", "docx": "^9.5.1", "dom-to-image": "^2.6.0", "echarts": "^5.5.1", "file-saver": "^2.0.5", "geodesy-fn": "1.0.1", "geojson-vt": "^4.0.2", "geotiff": "^2.1.3", "gif.js": "^0.2.0", "github-markdown-css": "^5.8.1", "html-to-docx": "^1.8.0", "html-to-image": "^1.11.11", "html2canvas": "1.4.1", "immer": "^10.0.2", "jotai": "^2.4.0", "jotai-devtools": "^0.6.2", "jsencrypt": "^3.3.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "markdown-to-jsx": "^7.7.10", "math.js": "^1.1.46", "mathjs": "^11.8.0", "mockjs": "^1.1.0", "moment": "^2.29.1", "pbf": "^4.0.1", "proj4": "^2.19.7", "qs": "^6.10.1", "rc-slider": "^9.7.2", "rc-virtual-list": "^3.4.13", "react-captcha-code": "^1.0.7", "react-color": "^2.19.3", "react-dnd": "^14.0.3", "react-dnd-html5-backend": "^14.0.1", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-moveable": "^0.29.0", "react-query": "^3.18.1", "react-resizable": "^3.0.4", "react-resize-detector": "^8.0.4", "react-rnd": "^10.3.5", "react-router-dom": "^5.2.0", "react-sortable-hoc": "^2.0.0", "react-spring": "^9.2.3", "react-svg": "^14.0.2", "react-virtuoso": "^4.10.4", "remark-gfm": "^4.0.1", "slate": "^0.87.0", "slate-react": "^0.88.0", "styled-components": "^5.3.0", "tailwind-merge": "^1.14.0", "terraformer-wkt-parser": "^1.2.1", "use-count-up": "^3.0.1", "use-immer": "^0.9.0", "usehooks-ts": "^3.1.0", "vt-pbf": "^3.1.3", "windicss-webpack-plugin": "1.5.8", "wkt-parser": "^1.3.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "@danmarshall/deckgl-typings": "^4.9.7", "@types/dom-to-image": "^2.6.3", "@types/geojson": "^7946.0.8", "@types/geojson-vt": "^3.2.5", "@types/lodash": "^4.14.170", "@types/react": "^18.2.21", "@types/react-color": "^3.0.5", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^4.28.0", "@umijs/fabric": "^2.6.2", "@umijs/test": "^3.4.25", "babel-eslint": "10.0.0", "babel-plugin-styled-components": "^1.12.0", "concurrently": "^8.0.1", "cross-env": "^7.0.3", "eslint": "^8.38.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "lint-staged": "^10.0.7", "nunjucks": "^3.2.4", "prettier": "^2.3.1", "react": "17.x", "react-dom": "17.x", "svgtofont": "^6.0.0", "tailwindcss": "^3", "ts-node": "^10.9.1", "typescript": "^4.1.2", "windicss": "^3.5.6", "yargs": "^17.7.2", "yorkie": "^2.0.0"}, "resolutions": {"dayjs": "1.11.13"}}