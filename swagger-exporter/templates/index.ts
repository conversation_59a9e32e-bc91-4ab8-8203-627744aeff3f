export const serviceHead = `import { request } from '@/utils';
	{% if shouldImportFromQs %}import { stringify } from 'qs';\n{% endif %}
`;

export const getTpl = `
	{% if summary %} /** {{summary}} */ {% endif %}\n
	export const {{name}} = ({% if paramsType %}params: APIV2.{{paramsType}} {% endif %}) => {
		return request(\`{{url}}{% if paramsType %}?\${stringify(params)}{% endif %}\`) as Promise<{{respModel}}>
	}
`;

export const postTpl = `
	{% if summary %}/** {{summary}} */{% endif %}\n
	export const {{name}} = ({% if paramsType %}params: APIV2.{{paramsType}} {% endif %}) => {
		return request('{{url}}', {
			method: 'POST',
			body:JSON.stringify(params) ,
		}) as Promise<{{respModel}}>
	}
`;
export const typeTpl = `
  {% if summary %}/** {{summary}} */\n{% endif %}
  type {{name}} = {% if length > 0 %} {
		{% for property in properties %}
			/** {{property.description}} {% if property.example %}例：{{property.example}}{% endif %} */
			{{property.name}}{% if property.optional %}?{% endif %}: {{property.type}};
		{% endfor %}
	} {% else %}Record<string, unknown>{% endif %};\n`;
