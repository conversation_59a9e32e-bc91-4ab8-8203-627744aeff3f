import { resolve } from 'path';

export const log = (message: string) => {
  console.log(`====** ${message} **====`);
};

export const apiPath = resolve(__dirname, '../src/api-v2');

export const formatModelName = (name: string = '') => {
  const replaced = name.replace(/[»«,，、\-{}]*/g, '');
  return `T${replaced}`;
};

export const arrayToCamelCase = (arr: string[]) => {
  return arr
    .map((item, index) => {
      if (index === 0) {
        return item;
      }
      return item[0].toUpperCase() + item.slice(1);
    })
    .join('');
};

export const formatPropertyType = (type: string) => {
  if (type === 'object') {
    return 'unknown';
  }
  if (type === 'integer') {
    return 'number';
  }

  if (type === 'file') {
    return 'File';
  }

  return type;
};

export const generateSvcName = (pathName: string, method: string) => {
  const pathArr = pathName.replace(/[{}]/g, '').replace('/api/', '').split('/');
  const str = arrayToCamelCase(pathArr);
  const result = method + str[0].toUpperCase() + str.slice(1);

  return result;
};

export const generateSvcParamsTypeName = (pathName: string, method: string) => {
  const pathArr = pathName.replace(/[{}]/g, '').replace('/api/', '').split('/');
  const str = arrayToCamelCase(pathArr);
  const result = method + str[0].toUpperCase() + str.slice(1);

  return 'T' + result + 'Params';
};
