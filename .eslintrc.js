module.exports = {
  // <PERSON>i Max 项目
  extends: [require.resolve('@umijs/max/eslint'), 'plugin:react-hooks/recommended'],
  parser: '@typescript-eslint/parser',
  globals: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: true,
    page: true,
    REACT_APP_ENV: true,
    charts: true,
  },
  plugins: ['react-hooks'],
  rules: {
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': 0,
    '@typescript-eslint/no-empty-interface': 0,
    '@typescript-eslint/no-use-before-define': 0,
    'import/no-dynamic-require': 'off',
    'no-case-declarations': 0,
    'no-self-compare': 0,
    'react/no-unknown-property': 0,
    'react/button-has-type': 0,
    'array-callback-return': 0,
    'no-self-assign': 0,
    'max-len': [
      'error',
      {
        code: 200,
        ignoreUrls: true,
      },
    ],
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'error',
  },
};
