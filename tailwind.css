@tailwind base;
@tailwind components;
@tailwind utilities;

img,
video {
  max-width: unset;
  height: auto;
}

/* injected */
.has-triangle-down::after {
  position: absolute;
  bottom: -6px;
  left: 50%;
  display: block;
  border-top: 8px solid white;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  transform: translate(-50% 0);
  content: '';
}

.scroll-bar {
  overflow: auto;
}
.scroll-bar::-webkit-scrollbar {
  width: 4px;
  height: 7px;
}
.scroll-bar::-webkit-scrollbar-track {
  background: transparent;
}
.scroll-bar::-webkit-scrollbar-thumb {
  width: 60px;
  background: rgba(216, 216, 216, 0.29);
  border-radius: 2px;
}
.scroll-bar-hidden {
  overflow: auto;
}
.scroll-bar-hidden::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.scroll-bar-hidden::-webkit-scrollbar-track {
  background: #f7f7f9;
}
.scroll-bar-hidden::-webkit-scrollbar-thumb {
  background: #c6c6c6;
}
.long-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-overflow-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.square {
  width: 100%;
  aspect-ratio: 1 / 1;
}
.grid-center {
  display: grid;
  place-items: center;
}

.flex-center {
  @apply flex items-center justify-center;
}

.absolute-center-horizontal {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.absolute-center-vertical {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.backdrop-filter-blur-card {
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

/* 横向滚动条美化 */
.horizontal-scroll-bar {
  @apply overflow-x-auto;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #25262d;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(105, 105, 109, 0.5);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(105, 105, 109, 1);
  }
}

@keyframes bubble {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.7);
    opacity: 0;
  }
}

.backdrop-filter-blur-card {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.custom-legend .ant-btn-color-primary {
  box-shadow: none;
}
